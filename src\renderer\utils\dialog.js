import { createApp, nextTick } from 'vue'
import CustomDialog from '../components/CustomDialog.vue'

/**
 * 创建对话框实例
 * @param {Object} options - 对话框配置选项
 * @returns {Promise} 返回Promise，resolve时表示用户确认，reject时表示用户取消
 */
function createDialog(options) {
  return new Promise((resolve, reject) => {
    // 创建容器元素
    const container = document.createElement('div')
    document.body.appendChild(container)
    
    // 创建Vue应用实例
    const app = createApp(CustomDialog, {
      ...options,
      visible: true,
      onConfirm: () => {
        cleanup()
        resolve(true)
      },
      onCancel: () => {
        cleanup()
        reject(false)
      },
      onClose: () => {
        cleanup()
        if (options.type === 'confirm') {
          reject(false)
        } else {
          resolve(true)
        }
      }
    })
    
    // 清理函数
    function cleanup() {
      nextTick(() => {
        app.unmount()
        if (container.parentNode) {
          container.parentNode.removeChild(container)
        }
      })
    }
    
    // 挂载应用
    app.mount(container)
  })
}

/**
 * 显示信息提示对话框（替代原生alert）
 * @param {string} message - 提示消息
 * @param {Object} options - 可选配置
 * @returns {Promise<boolean>}
 */
export function showAlert(message, options = {}) {
  return createDialog({
    type: 'alert',
    message,
    title: '提示',
    variant: 'info',
    confirmText: '确定',
    ...options
  })
}

/**
 * 显示确认对话框（替代原生confirm）
 * @param {string} message - 确认消息
 * @param {Object} options - 可选配置
 * @returns {Promise<boolean>}
 */
export function showConfirm(message, options = {}) {
  return createDialog({
    type: 'confirm',
    message,
    title: '确认',
    variant: 'warning',
    confirmText: '确定',
    cancelText: '取消',
    ...options
  })
}

/**
 * 显示成功提示对话框
 * @param {string} message - 成功消息
 * @param {Object} options - 可选配置
 * @returns {Promise<boolean>}
 */
export function showSuccess(message, options = {}) {
  return createDialog({
    type: 'alert',
    message,
    title: '成功',
    variant: 'success',
    confirmText: '确定',
    ...options
  })
}

/**
 * 显示错误提示对话框
 * @param {string} message - 错误消息
 * @param {Object} options - 可选配置
 * @returns {Promise<boolean>}
 */
export function showError(message, options = {}) {
  return createDialog({
    type: 'alert',
    message,
    title: '错误',
    variant: 'error',
    confirmText: '确定',
    ...options
  })
}

/**
 * 显示警告提示对话框
 * @param {string} message - 警告消息
 * @param {Object} options - 可选配置
 * @returns {Promise<boolean>}
 */
export function showWarning(message, options = {}) {
  return createDialog({
    type: 'alert',
    message,
    title: '警告',
    variant: 'warning',
    confirmText: '确定',
    ...options
  })
}

/**
 * 显示删除确认对话框
 * @param {string} message - 删除确认消息
 * @param {Object} options - 可选配置
 * @returns {Promise<boolean>}
 */
export function showDeleteConfirm(message = '确定要删除这个项目吗？此操作不可撤销。', options = {}) {
  return createDialog({
    type: 'confirm',
    message,
    title: '删除确认',
    variant: 'error',
    confirmText: '删除',
    cancelText: '取消',
    ...options
  })
}

/**
 * 全局对话框方法（可以挂载到Vue实例上）
 */
export const DialogPlugin = {
  install(app) {
    app.config.globalProperties.$alert = showAlert
    app.config.globalProperties.$confirm = showConfirm
    app.config.globalProperties.$success = showSuccess
    app.config.globalProperties.$error = showError
    app.config.globalProperties.$warning = showWarning
    app.config.globalProperties.$deleteConfirm = showDeleteConfirm
  }
}

// 默认导出
export default {
  showAlert,
  showConfirm,
  showSuccess,
  showError,
  showWarning,
  showDeleteConfirm,
  DialogPlugin
} 
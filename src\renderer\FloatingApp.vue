<template>
  <div class="floating-app">
    <!-- Loading 遮罩层 -->
    <div class="loading-overlay" v-if="isLoading">
      <div class="loading-container">
        <img :src="logoUrl" class="loading-logo" alt="Logo" />
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingMessage }}</div>
        <div class="loading-status">{{ loadingStatus }}</div>
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
      </div>
    </div>
    
    <FloatingCharacter v-if="isLoggedIn" />
    
    <!-- 未登录提示 -->
    <div v-if="!isLoading && !isLoggedIn" class="login-required">
      <div class="login-required-container">
        <div class="login-icon">🔐</div>
        <div class="login-title">请先登录</div>
        <div class="login-message">需要登录后才能使用AI助手</div>
        <button @click="openMainWindow" class="login-button">
          前往登录
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import FloatingCharacter from './components/FloatingCharacter.vue'
import { useAuthStore } from './stores/auth.js'
import logoUrl from '/assets/logo.png?url'

export default {
  name: 'FloatingApp',
  components: {
    FloatingCharacter
  },
  setup() {
    console.log('FloatingApp setup函数被调用')
    
    const authStore = useAuthStore()
    const isLoading = ref(true)
    const loadingMessage = ref('正在初始化助手...')
    const loadingStatus = ref('准备中')
    const welcomeVoicePlayed = ref(false)
    const appInitialized = ref(false)
    const loadingClosed = ref(false) // 🔄 【新增】防止重复关闭loading的标志位
    
    // 登录状态
    const isLoggedIn = computed(() => authStore.isLoggedIn)
    
    // 打开主窗口
    const openMainWindow = () => {
      console.log('🔓 未登录用户点击前往登录，打开主窗口')
      if (window.electronAPI && window.electronAPI.openMainWindow) {
        window.electronAPI.openMainWindow('login')
      }
    }
    
    // 函数：请求播放欢迎语音
    const requestWelcomeVoice = () => {
      // 只有在用户已登录的情况下才播放欢迎语音
      if (!authStore.isLoggedIn) {
        console.log('🔄 悬浮窗：用户未登录，跳过欢迎语音播放')
        return
      }
      
      if (!welcomeVoicePlayed.value && window.electronAPI && window.electronAPI.speakWelcomeMessage) {
        console.log('🔄 悬浮窗：请求播放欢迎语音...')
        
        window.electronAPI.speakWelcomeMessage({
          text: '',
          callback: 'welcome-voice-completed'
        })
          .then((result) => {
            console.log('🔄 悬浮窗：欢迎语音请求结果:', result)
            welcomeVoicePlayed.value = true
          })
          .catch((error) => {
            console.error('🔄 悬浮窗：欢迎语音播放失败:', error)
            welcomeVoicePlayed.value = true
          })
      } else {
        console.log('🔄 悬浮窗：欢迎语音API不可用，跳过播放')
        welcomeVoicePlayed.value = true
      }
    }
    
    // 函数：检查初始化是否完成
    const checkInitComplete = () => {
      // 🔄 【修复】防止重复关闭loading
      if (loadingClosed.value) {
        console.log('🔄 悬浮窗：loading已关闭，跳过重复关闭')
        return
      }
      
      // 🔄 【修改】改为监听智能语音启动完成消息来关闭loading
      if (authStore.isLoggedIn && appInitialized.value) {
        console.log('✅ 悬浮窗：用户已登录，应用初始化已完成，等待智能语音启动完成')
        // 不立即关闭loading，等待智能语音启动完成消息
      } else {
        console.log('🔄 悬浮窗：等待完成 - 登录状态=', authStore.isLoggedIn, '应用初始化=', appInitialized.value)
      }
    }
    
    onMounted(() => {
      console.log('FloatingApp组件已挂载')
      console.log('当前时间:', new Date().toLocaleString())
      console.log('窗口大小:', window.innerWidth, 'x', window.innerHeight)
      
      // 检查登录状态
      authStore.checkLoginStatus().then(() => {
        console.log('🔍 悬浮窗登录状态检查完成，当前登录状态:', authStore.isLoggedIn)
        
        // 根据登录状态决定是否显示加载状态
        if (authStore.isLoggedIn) {
          isLoading.value = true
          loadingMessage.value = '正在初始化助手...'
          loadingStatus.value = '准备中'
          console.log('🔄 悬浮窗：用户已登录，显示服务初始化加载状态')
        } else {
          isLoading.value = false
          console.log('🔄 悬浮窗：用户未登录，不显示加载状态')
        }
      }).catch(error => {
        console.error('🔍 悬浮窗登录状态检查失败:', error)
        isLoading.value = false
      })
      
      // 监听登录状态变化
      if (window.electronAPI && window.electronAPI.onLoginStatusChanged) {
        window.electronAPI.onLoginStatusChanged((status) => {
          console.log('🔄 悬浮窗收到登录状态变化:', status)
          console.log('🔄 悬浮窗当前登录状态:', authStore.isLoggedIn, '→', status)
          authStore.isLoggedIn = status
          
          // 根据登录状态控制加载状态
          if (status) {
            console.log('✅ 悬浮窗：用户已登录，将显示AI助手')
            // 用户登录后显示加载状态
            isLoading.value = true
            loadingMessage.value = '正在初始化助手...'
            loadingStatus.value = '准备中'
            welcomeVoicePlayed.value = false
            appInitialized.value = false
            loadingClosed.value = false // 🔄 【新增】重置loading关闭标志
            console.log('🔄 悬浮窗：用户登录，显示加载状态')
          } else {
            console.log('❌ 悬浮窗：用户未登录，将显示登录提示')
            // 用户退出登录后隐藏加载状态
            isLoading.value = false
            console.log('🔄 悬浮窗：用户退出登录，隐藏加载状态')
          }
        })
        console.log('✅ 悬浮窗已设置登录状态变化监听器')
      } else {
        console.error('❌ 悬浮窗无法设置登录状态监听器')
      }
      
      // 监听主进程的状态消息
      if (window.electronAPI && window.electronAPI.onStatusMessage) {
        console.log('设置状态消息监听器...')
        window.electronAPI.onStatusMessage((message) => {
          console.log('📣 悬浮窗收到状态消息:', message)
          loadingStatus.value = message
          
          // 只有在用户已登录的情况下才处理初始化完成消息
          if (authStore.isLoggedIn && isLoading.value) {
            // 如果收到应用初始化完成的消息，则标记为已初始化
            if (message === '应用初始化完成' || message.includes('初始化完成')) {
              console.log('✅ 悬浮窗：应用标记为已初始化')
              appInitialized.value = true
              
              // 🔄 【修改】不立即关闭loading，等待智能语音启动完成
              checkInitComplete()
              
              // 请求播放欢迎语音（异步，不影响loading关闭）
              setTimeout(() => {
                requestWelcomeVoice()
              }, 100)
            }
            // 🔄 【新增】如果收到智能语音启动完成的消息，则关闭loading
            else if (message === '智能语音启动完成') {
              console.log('🔄 悬浮窗：收到智能语音启动完成消息，准备关闭loading')
              loadingClosed.value = true // 🔄 【新增】标记loading已关闭
              setTimeout(() => {
                isLoading.value = false
                console.log('✅ 悬浮窗：关闭loading遮罩')
              }, 500)
            }
          }
        })
        console.log('✅ 状态消息监听器设置完成')
      } else {
        console.error('❌ electronAPI.onStatusMessage 不可用')
      }
      
      // 监听欢迎语音播放完成事件
      if (window.electronAPI && window.electronAPI.onSpeakText) {
        window.electronAPI.onSpeakText((speechData) => {
          console.log('收到语音事件:', speechData)
          if (speechData.callback === 'welcome-voice-completed') {
            console.log('欢迎语音播放完成')
            welcomeVoicePlayed.value = true
            
            // 检查是否可以关闭loading
            checkInitComplete()
          }
        })
      }
      
      // 监听主进程console日志并转发到浏览器控制台
      if (window.electronAPI && window.electronAPI.onMainConsoleLog) {
        window.electronAPI.onMainConsoleLog((logData) => {
          const timestamp = new Date(logData.timestamp).toLocaleTimeString()
          const message = `[主进程 ${timestamp}] ${logData.message}`
          
          // 根据日志级别调用相应的console方法
          switch (logData.level) {
            case 'error':
              console.error(message)
              break
            case 'warn':
              console.warn(message)
              break
            case 'info':
              console.info(message)
              break
            case 'debug':
              console.debug(message)
              break
            default:
              console.log(message)
              break
          }
        })
        console.log('✅ 悬浮窗console转发已启动')
      }
      
      // 如果30秒后还没有完成初始化，则自动关闭loading（防止卡死）
      // setTimeout(() => {
      //   if (isLoading.value) {
      //     console.warn('⚠️ 30秒超时，强制关闭loading遮罩')
      //     isLoading.value = false
      //   }
      // }, 30000)
    })
    
    return {
      isLoading,
      loadingMessage,
      loadingStatus,
      logoUrl,
      isLoggedIn,
      openMainWindow
    }
  }
}
</script>

<style lang="scss">
.floating-app {
  width: 100%;
  height: 100%;
  position: relative;
}

/* Loading样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(150deg, #4768DB 0%, #B06AB3 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background-color: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  min-width: 280px;
  max-width: 350px;
  animation: fadeIn 0.5s ease;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid rgba(100, 100, 255, 0.2);
  border-top-color: #6464ff;
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: 16px;
}

.loading-logo {
  width: 60px;
  height: 60px;
  margin-bottom: 16px;
  border-radius: 50%;
  object-fit: cover;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 18px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 12px;
}

.loading-status {
  font-size: 14px;
  color: #5a5a5a;
  text-align: center;
  margin-bottom: 6px;
  min-height: 16px;
}

.loading-progress {
  width: 100%;
  height: 3px;
  background-color: rgba(100, 100, 255, 0.2);
  border-radius: 3px;
  margin-top: 12px;
  overflow: hidden;
  position: relative;
}

.loading-progress-bar {
  height: 100%;
  background-color: #6464ff;
  position: absolute;
  left: 0;
  top: 0;
  animation: progressAnimation 2s ease infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(100, 100, 255, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(100, 100, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(100, 100, 255, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressAnimation {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

/* 未登录提示样式 */
.login-required {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(0, 0, 0, 0.8);
  z-index: 9999;
}

.login-required-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.3);
  min-width: 260px;
  max-width: 320px;
  animation: fadeIn 0.5s ease;
  text-align: center;
}

.login-icon {
  font-size: 48px;
  margin-bottom: 16px;
  animation: pulse 2s infinite;
}

.login-title {
  font-size: 20px;
  font-weight: bold;
  color: white;
  margin-bottom: 10px;
}

.login-message {
  font-size: 14px;
  color: rgba(255, 255, 255, 0.9);
  margin-bottom: 20px;
  line-height: 1.4;
}

.login-button {
  background: rgba(255, 255, 255, 0.2);
  border: 2px solid rgba(255, 255, 255, 0.3);
  color: white;
  padding: 10px 20px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);

  &:hover {
    background: rgba(255, 255, 255, 0.3);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(0, 0, 0, 0.2);
  }

  &:active {
    transform: translateY(0);
  }
}
</style> 
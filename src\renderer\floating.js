import { createApp } from 'vue'
import { createPinia } from 'pinia'
import FloatingApp from './FloatingApp.vue'
import './assets/styles/floating.scss'

console.log('Floating.js is loading...')

// 确保CryptoJS加载成功
const ensureCryptoJSLoaded = () => {
  return new Promise((resolve, reject) => {
    // 如果CryptoJS已经可用，直接返回
    if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
      console.log('✅ CryptoJS已加载并可用')
      resolve(true)
      return
    }
    
    console.log('🔄 CryptoJS未加载，尝试手动加载...')
    
    // 创建script标签加载CryptoJS
    const script = document.createElement('script')
    script.src = '/utils/cryptojs.js'
    script.async = false
    
    script.onload = () => {
      console.log('✅ CryptoJS手动加载成功')
      if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
        resolve(true)
      } else {
        console.error('❌ CryptoJS加载后仍然不可用')
        reject(new Error('CryptoJS加载失败'))
      }
    }
    
    script.onerror = (e) => {
      console.error('❌ CryptoJS加载失败:', e)
      // 尝试CDN加载
      const cdnScript = document.createElement('script')
      cdnScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'
      cdnScript.async = false
      
      cdnScript.onload = () => {
        console.log('✅ 从CDN加载CryptoJS成功')
        if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
          resolve(true)
        } else {
          reject(new Error('从CDN加载CryptoJS后仍不可用'))
        }
      }
      
      cdnScript.onerror = () => {
        reject(new Error('CryptoJS本地和CDN加载均失败'))
      }
      
      document.head.appendChild(cdnScript)
    }
    
    document.head.appendChild(script)
  })
}

// 初始化应用
const initApp = async () => {
  try {
    // 尝试加载CryptoJS
    await ensureCryptoJSLoaded()
    
    // 创建Vue应用
    const app = createApp(FloatingApp)
    const pinia = createPinia()
    
    app.use(pinia)
    app.mount('#floating-app')
    
    console.log('Floating Vue app mounted successfully')
  } catch (error) {
    console.error('初始化应用失败:', error)
  }
}

// 启动应用
initApp() 
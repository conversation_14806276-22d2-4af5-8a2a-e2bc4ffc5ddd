const { ipcMain } = require('electron')
const Store = require('electron-store')

// === Outlook日历同步服务管理器 ===
class OutlookCalendarService {
  constructor(mcpManager, mainWindow) {
    this.mcpManager = mcpManager
    this.mainWindow = mainWindow
    this.isInitialized = false
    this.syncedTodos = new Map() // 记录已同步到日历的待办事项

    // 添加持久化存储
    this.syncStore = new Store({
      name: 'calendar-sync',
      defaults: {
        syncedTodos: {},
        lastSyncTime: null
      }
    })

    // 初始化时加载同步记录
    this.loadSyncedTodosFromStore()

    this.setupIPC()
  }

  async initialize() {
    console.log('📅 [OUTLOOK_CALENDAR] 初始化Outlook日历服务...')
    
    try {
      // 检查或初始化Outlook日历MCP服务
      console.log('📅 [OUTLOOK_CALENDAR] 检查或初始化Outlook日历MCP服务...')
      await this.waitForMCPReady()
      
      this.isInitialized = true
      console.log('📅 [OUTLOOK_CALENDAR] Outlook日历服务初始化完成')
      
    } catch (error) {
      console.error('❌ [OUTLOOK_CALENDAR] Outlook日历服务初始化失败:', error)
      this.isInitialized = true // 即使失败也标记为已初始化，避免阻塞其他服务
      throw error
    }
  }

  async waitForMCPReady() {
    try {
      console.log('📅 初始化 Outlook 日历 MCP 服务...')
      await this.mcpManager.initializeOutlookCalendarMCP()
      console.log('✅ Outlook 日历 MCP 服务初始化完成')
    } catch (error) {
      console.error('❌ Outlook日历MCP服务器初始化失败:', error)
      console.log('📅 [OUTLOOK_CALENDAR] Outlook日历MCP按需初始化失败:', error.message)
      console.log('📅 [OUTLOOK_CALENDAR] Outlook日历MCP服务不可用')
      console.log('📅 [OUTLOOK_CALENDAR] 请确保Microsoft Outlook已安装并正在运行')
      throw error
    }
  }

  setupIPC() {
    // 获取同步状态
    ipcMain.handle('get-calendar-sync-status', () => {
      const outlookClient = this.mcpManager.clients.get('outlook-calendar')
      return {
        isAvailable: !!(outlookClient && outlookClient.isConnected),
        syncedCount: this.syncedTodos.size,
        lastSyncTime: this.syncStore.get('lastSyncTime'),
        clientStatus: outlookClient ? {
          isConnected: outlookClient.isConnected,
          configSource: outlookClient.configSource,
          availableTools: outlookClient.availableTools?.length || 0
        } : null
      }
    })

    // 手动同步待办事项到日历
    ipcMain.handle('sync-todos-to-calendar', async (event, todos) => {
      try {
        console.log('📅 [OUTLOOK_CALENDAR] 收到手动同步请求')
        
        const outlookClient = this.mcpManager.clients.get('outlook-calendar')
        if (!outlookClient || !outlookClient.isConnected) {
          return { 
            success: false, 
            error: 'Outlook日历MCP服务不可用，请确保Microsoft Outlook已安装并正在运行' 
          }
        }

        let syncedCount = 0
        const errors = []

        for (const todo of todos) {
          try {
            // 检查是否已经同步过
            if (this.syncedTodos.has(todo.uid)) {
              console.log(`📅 [OUTLOOK_CALENDAR] 跳过已同步的待办事项: ${todo.subject}`)
              continue
            }

            // 构建日历事件参数
            const eventParams = this.buildCalendarEventParams(todo)

            console.log(`📅 [OUTLOOK_CALENDAR] 同步待办事项到日历: ${todo.subject}`)

            // 调用MCP工具创建日历事件
            const result = await this.mcpManager.callRealMCPTool('create_event', eventParams)

            if (result && result.success) {
              console.log(`✅ [OUTLOOK_CALENDAR] 日历事件创建成功: ${todo.subject}`)
              
              // 记录同步状态
              this.syncedTodos.set(todo.uid, {
                todoId: todo.uid,
                eventId: result.eventId || 'unknown',
                syncTime: new Date().toISOString(),
                subject: todo.subject
              })
              
              syncedCount++
            } else {
              const errorMsg = `日历事件创建失败: ${todo.subject} - ${result?.error || '未知错误'}`
              console.warn(`⚠️ [OUTLOOK_CALENDAR] ${errorMsg}`)
              errors.push(errorMsg)
            }

          } catch (syncError) {
            const errorMsg = `同步待办事项失败: ${todo.subject} - ${syncError.message}`
            console.error(`❌ [OUTLOOK_CALENDAR] ${errorMsg}`)
            errors.push(errorMsg)
          }
        }

        // 保存同步记录
        this.saveSyncedTodosToStore()
        this.syncStore.set('lastSyncTime', new Date().toISOString())

        return {
          success: true,
          syncedCount,
          totalCount: todos.length,
          errors: errors.length > 0 ? errors : null,
          message: `成功同步 ${syncedCount}/${todos.length} 个待办事项到Outlook日历`
        }

      } catch (error) {
        console.error('❌ [OUTLOOK_CALENDAR] 手动同步失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 清除同步记录
    ipcMain.handle('clear-calendar-sync-records', () => {
      try {
        this.syncedTodos.clear()
        this.syncStore.set('syncedTodos', {})
        this.syncStore.set('lastSyncTime', null)
        console.log('📅 [OUTLOOK_CALENDAR] 同步记录已清除')
        return { success: true }
      } catch (error) {
        console.error('❌ [OUTLOOK_CALENDAR] 清除同步记录失败:', error)
        return { success: false, error: error.message }
      }
    })
  }

  buildCalendarEventParams(todo) {
    // 计算事件时间
    const now = new Date()
    const eventDate = new Date(now.getTime() + 24 * 60 * 60 * 1000) // 默认明天
    
    // 如果邮件中包含时间信息，尝试解析
    const timeMatch = todo.content?.match(/(\d{1,2})[:\：](\d{2})|(\d{1,2})点/g)
    if (timeMatch) {
      const timeStr = timeMatch[0]
      const hourMatch = timeStr.match(/(\d{1,2})/)
      if (hourMatch) {
        const hour = parseInt(hourMatch[1])
        if (hour >= 0 && hour <= 23) {
          eventDate.setHours(hour, 0, 0, 0)
        }
      }
    }

    // 构建事件参数
    return {
      subject: `[邮件待办] ${todo.subject}`,
      body: `来自邮件的待办事项\n\n发件人: ${todo.sender}\n邮件内容: ${todo.content?.substring(0, 200)}...`,
      start: eventDate.toISOString(),
      end: new Date(eventDate.getTime() + 60 * 60 * 1000).toISOString(), // 1小时后结束
      isAllDay: false,
      reminder: todo.isUrgent ? 15 : 60 // 紧急事项15分钟提醒，普通事项1小时提醒
    }
  }

  loadSyncedTodosFromStore() {
    try {
      const storedSyncedTodos = this.syncStore.get('syncedTodos', {})
      this.syncedTodos = new Map(Object.entries(storedSyncedTodos))
      console.log(`📅 [OUTLOOK_CALENDAR] 从存储加载了 ${this.syncedTodos.size} 个同步记录`)
    } catch (error) {
      console.error('📅 [OUTLOOK_CALENDAR] 加载同步记录失败:', error)
      this.syncedTodos = new Map()
    }
  }

  saveSyncedTodosToStore() {
    try {
      const syncedTodosObj = Object.fromEntries(this.syncedTodos)
      this.syncStore.set('syncedTodos', syncedTodosObj)
    } catch (error) {
      console.error('📅 [OUTLOOK_CALENDAR] 保存同步记录失败:', error)
    }
  }

  cleanup() {
    console.log('🧹 清理Outlook日历服务资源...')
    // 目前没有需要清理的资源
    console.log('✅ Outlook日历服务资源清理完成')
  }
}

module.exports = OutlookCalendarService

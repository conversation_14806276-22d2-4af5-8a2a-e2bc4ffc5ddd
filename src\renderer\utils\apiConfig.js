/**
 * 统一API配置中心
 * 集中管理所有API相关的配置
 */

// API基础配置
export const API_CONFIG = {
  // 生产环境API地址
  PROD_API_BASE_URL: 'http://114.67.112.88:9603/prod-api',
  
  // API路径配置
  ENDPOINTS: {
    // 认证相关（不需要token）
    LOGIN: '/login',
    LOGIN_BY_CODE: '/login-by-code',
    VERSION_CHECK: '/common/defaultVersion',
    
    // 用户相关（需要token）
    USER_INFO: '/getInfo',
    
    // AI服务相关（需要token）
    AI_CHAT: '/api/chat/completions',
    AI_SEARCH: '/api/tool/ai_search/chat/completions',
    
    // 知识库服务相关（需要token）
    KNOWLEDGE_EMBEDDINGS: '/api/embeddings',
    KNOWLEDGE_RERANK: '/api/rerank',
    
    // 其他API路径可以在这里添加
  },
  
  // 请求配置
  REQUEST_CONFIG: {
    TIMEOUT: 30000,
    SEARCH_TIMEOUT: 120000, // 搜索请求超时时间更长
    KNOWLEDGE_TIMEOUT: 60000, // 知识库请求超时时间
    CONTENT_TYPE: 'application/json'
  },
  
  // 不需要token的接口列表
  NO_TOKEN_ENDPOINTS: [
    '/login',
    '/login-by-code', 
    '/common/defaultVersion'
  ]
}

/**
 * 获取完整的API URL
 * @param {string} endpoint - API端点路径
 * @returns {string} 完整的API URL
 */
export function getApiUrl(endpoint) {
  return `${API_CONFIG.PROD_API_BASE_URL}${endpoint}`
}

/**
 * 检查接口是否需要token
 * @param {string} url - 请求URL
 * @returns {boolean} 是否需要token
 */
export function needsToken(url) {
  return !API_CONFIG.NO_TOKEN_ENDPOINTS.some(endpoint => url.includes(endpoint))
}

/**
 * 获取API基础URL
 * @returns {string} API基础URL
 */
export function getApiBaseUrl() {
  return API_CONFIG.PROD_API_BASE_URL
}

/**
 * 获取请求超时时间
 * @param {string} endpoint - API端点
 * @returns {number} 超时时间（毫秒）
 */
export function getTimeout(endpoint) {
  if (endpoint.includes('ai_search')) {
    return API_CONFIG.REQUEST_CONFIG.SEARCH_TIMEOUT
  }
  if (endpoint.includes('embeddings') || endpoint.includes('rerank')) {
    return API_CONFIG.REQUEST_CONFIG.KNOWLEDGE_TIMEOUT
  }
  return API_CONFIG.REQUEST_CONFIG.TIMEOUT
}

// 导出默认配置
export default API_CONFIG 
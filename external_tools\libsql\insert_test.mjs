import {createClient} from '@libsql/client';
import OpenAI from 'openai';

const client = createClient({
    url: 'file:local.db',
});

const openai = new OpenAI({
    baseURL: 'https://api.siliconflow.cn/v1',
    apiKey: 'sk-tbcbjgzqnnyzdqrxmnparmvibzwteydpknmhiyvmuvlpmram',
});

async function getEmbedding(prompt) {
    const response = await openai.embeddings.create({
        model: 'BAAI/bge-large-zh-v1.5',
        input: prompt,
        encoding_format: 'float'
    });

    // 将返回的数组转换为 Float32Array
    return new Float32Array(response.data[0].embedding);
}

async function main() {
    const fileType = 1;
    const fileName = "测试文档.txt";
    const filePath = "/tmp/20250629/";
    const sourceFilePath = "/root/20250629/";
    const filePreview = "测试一下啊";
    const remark = "测试了";
    const fileContentList = ["一场谋杀案使银行家安迪（蒂姆•罗宾斯 Tim Robbins 饰）蒙冤入狱，谋杀妻子及其情人的指控将囚禁他终生。在肖申克监狱的首次现身就让监狱“大哥”瑞德（摩根•弗里曼 Morgan Freeman 饰）对他另眼相看。瑞德帮助他搞到一把石锤和一幅女明星海报，两人渐成患难 之交。很快，安迪在监狱里大显其才，担当监狱图书管理员，并利用自己的金融知识帮助监狱官避税，引起了典狱长的注意，被招致麾下帮助典狱长洗黑钱。偶然一次，他得知一名新入狱的小偷能够作证帮他洗脱谋杀罪。燃起一丝希望的安迪找到了典狱长，希望他能帮自己翻案。阴险伪善的狱长假装答应安迪，背后却派人杀死小偷，让他唯一能合法出狱的希望泯灭。沮丧的安迪并没有绝望，在一个电闪雷鸣的风雨夜，一场暗藏几十年的越狱计划让他自我救赎，重获自由！老朋友瑞德在他的鼓舞和帮助下，也勇敢地奔向自由。", "阿甘（汤姆·汉克斯 饰）于二战结束后不久出生在美国南方阿拉巴马州一个闭塞的小镇，他先天弱智，智商只有75，然而他的妈妈是一个性格坚强的女性，她常常鼓励阿甘“傻人有傻福”，要他自强不息。\n" +
    "　　阿甘像普通孩子一样上学，并且认识了一生的朋友和至爱珍妮（罗宾·莱特·潘 饰），在珍妮 和妈妈的爱护下，阿甘凭着上帝赐予的“飞毛腿”开始了一生不停的奔跑。\n" +
    "　　阿甘成为橄榄球巨星、越战英雄、乒乓球外交使者、亿万富翁，但是，他始终忘不了珍妮，几次匆匆的相聚和离别，更是加深了阿甘的思念。\n" +
    "　　有一天，阿甘收到珍妮的信，他们终于又要见面", "1912年4月10日，号称 “世界工业史上的奇迹”的豪华客轮泰坦尼克号开始了自己的处女航，从英国的南安普顿出发驶往美国纽约。富家少女罗丝（凯特•温丝莱特）与母亲及未婚夫卡尔坐上了头等舱；另一边，放荡不羁的少年画家杰克（莱昂纳多·迪卡普里奥）也在码头的一场赌博中赢得了下等舱的船票。\n" +
    "　　罗丝厌倦了上流社会虚伪的生活，不愿嫁给卡尔，打算投海自尽，被杰克救起。很快，美丽活泼的罗丝与英俊开朗的杰克相爱，杰克带罗丝参加下等舱的舞会、为她画像，二人的感情逐渐升温。\n" +
    "　　1912年4月14日，星期天晚上，一个风平浪静的夜晚。泰坦尼克号撞上了冰山，“永不沉没的”泰坦尼克号面临沉船的命运，罗丝和杰克刚萌芽的爱情也将经历生死的考验。"];
    // 插入数据并返回自增ID
    const result = await client.execute({
        sql: `INSERT INTO user_file (file_type, file_name, file_path, source_file_path, file_preview, remark)
              VALUES (?, ?, ?, ?, ?, ?) RETURNING id`, // 添加RETURNING子句获取新插入行的ID
        args: [fileType, fileName, filePath, sourceFilePath, filePreview, remark]
    });
    // 从结果中提取自增ID
    const fileId = result.rows[0].id;
    console.log(`✅ 插入成功，自增ID为: ${fileId}`);
    for (const fileContent of fileContentList) {
        // 生成描述文本的嵌入向量
        const embedding = await getEmbedding(fileContent);
        // 插入数据到数据库
        await client.execute({
            sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                  VALUES (?, ?, ?)`,
            args: [fileId, fileContent, embedding]
        });
        console.log('✅ 文件数据插入成功：' + fileContent);
    }
}

main().catch(console.error);
import { ref, computed } from 'vue'
import { getCurrentVersion, needsUpdate, formatVersion } from '../utils/versionUtils.js'
import { logVersionCheck, debugVersionState } from '../utils/versionDebug.js'
import { apiGet } from '../utils/apiManager.js'
import { getApiUrl } from '../utils/apiConfig.js'

/**
 * 版本检查 composable
 */
export function useVersionCheck() {
  // 响应式状态
  const checking = ref(false)
  const error = ref('')
  const versionData = ref(null)
  const currentVersionValue = ref('')
  
  // 计算属性
  const currentVersion = computed(() => currentVersionValue.value)
  const latestVersion = computed(() => versionData.value?.versionNumber || '')
  const isMandatoryUpdate = computed(() => versionData.value?.mandatoryUpdate === '0')
  const downloadUrl = computed(() => {
    if (!versionData.value?.filePath) return ''
    return getApiUrl(versionData.value.filePath)
  })
  const hasUpdate = computed(() => {
    if (!latestVersion.value || !currentVersion.value) return false
    return needsUpdate(currentVersion.value, latestVersion.value)
  })
  const formattedCurrentVersion = computed(() => formatVersion(currentVersion.value))
  const formattedLatestVersion = computed(() => formatVersion(latestVersion.value))

  /**
   * 检查版本更新
   */
  const checkVersion = async () => {
    try {
      logVersionCheck('开始检查版本更新')
      checking.value = true
      error.value = ''
      versionData.value = null
      
      // 先获取当前版本号
      currentVersionValue.value = await getCurrentVersion()
      logVersionCheck('当前版本:', currentVersionValue.value)
      
      const result = await apiGet('/common/defaultVersion')
      
      logVersionCheck('版本检查API响应:', result)
      
      if (result.success && result.data) {
        versionData.value = result.data
        logVersionCheck('版本数据设置成功:', result.data)
        logVersionCheck('版本比较结果:', {
          current: currentVersionValue.value,
          latest: result.data.versionNumber,
          hasUpdate: hasUpdate.value
        })
      } else {
        throw new Error(result.error || '版本检查失败')
      }
    } catch (err) {
      logVersionCheck('版本检查失败:', err.message)
      console.error('版本检查失败:', err)
      error.value = err.message || '网络连接失败，请检查网络后重试'
    } finally {
      checking.value = false
      logVersionCheck('版本检查完成，状态:', {
        hasUpdate: hasUpdate.value,
        error: error.value,
        checking: checking.value
      })
    }
  }

  /**
   * 处理升级
   */
  const handleUpdate = () => {
    if (downloadUrl.value) {
      if (window.electronAPI && window.electronAPI.openUrl) {
        window.electronAPI.openUrl(downloadUrl.value)
      } else {
        window.open(downloadUrl.value, '_blank')
      }
    }
  }

  /**
   * 重置状态
   */
  const reset = () => {
    checking.value = false
    error.value = ''
    versionData.value = null
    currentVersionValue.value = ''
  }

  return {
    // 状态
    checking,
    error,
    versionData,
    currentVersionValue,
    
    // 计算属性
    currentVersion,
    latestVersion,
    isMandatoryUpdate,
    downloadUrl,
    hasUpdate,
    formattedCurrentVersion,
    formattedLatestVersion,
    
    // 方法
    checkVersion,
    handleUpdate,
    reset
  }
} 
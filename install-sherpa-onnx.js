#!/usr/bin/env node

/**
 * Sherpa-ONNX 专用安装脚本
 * 只安装 sherpa-onnx-node，跳过可能有编译问题的 naudiodon2
 * 系统会自动回退到 Web Audio API 模式
 */

const { spawn } = require('child_process')
const fs = require('fs')
const path = require('path')

console.log('🚀 Sherpa-ONNX 专用安装程序')
console.log('='.repeat(50))

// 检查是否在正确的项目目录
if (!fs.existsSync('package.json')) {
  console.error('❌ 请在项目根目录运行此脚本')
  process.exit(1)
}

console.log('📦 开始安装 Sherpa-ONNX 核心包...')
console.log('💡 跳过 naudiodon2，使用 Web Audio API 模式')

// 只安装 sherpa-onnx-node
const npmInstall = spawn('npm', ['install', 'sherpa-onnx-node@^1.12.6'], {
  stdio: 'inherit',
  shell: true
})

npmInstall.on('close', (code) => {
  if (code === 0) {
    console.log('\n✅ Sherpa-ONNX 核心包安装成功！')
    
    console.log('\n🎵 音频模式说明：')
    console.log('🌐 Web Audio API 模式：在浏览器环境中获取麦克风音频')
    console.log('💡 优点：无需编译原生模块，跨平台兼容性好')
    console.log('📝 注意：需要用户授权麦克风权限')
    
    console.log('\n🧪 运行测试：')
    console.log('node test-sherpa-integration.js')
    
    console.log('\n🚀 启动项目：')
    console.log('npm run dev')
    
    console.log('\n💡 如果您想要原生音频支持（更高性能）：')
    console.log('1. 安装 Visual Studio Build Tools 2022')
    console.log('2. 选择 "Desktop development with C++" 工作负载')
    console.log('3. 运行 npm install naudiodon2')
    
    console.log('\n' + '='.repeat(50))
    console.log('🎉 安装完成！系统将使用 Web Audio API 模式。')
    
  } else {
    console.error('\n❌ Sherpa-ONNX 安装失败')
    console.error('请检查网络连接和 Node.js 版本')
    process.exit(1)
  }
})

npmInstall.on('error', (error) => {
  console.error('❌ 安装过程中出错:', error.message)
  process.exit(1)
}) 
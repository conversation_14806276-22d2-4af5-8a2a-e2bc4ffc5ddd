/**
 * 阿里云ASR音频录制器
 * 处理音频数据的获取、处理和降噪
 * 
 * 🎯 已优化配置用于检测较大声音并过滤环境杂音
 */

import { ALIYUN_ASR_CONFIG } from './aliyun-asr-config.js'

// 🔧 音频检测配置 - 可根据需要调整
const AUDIO_DETECTION_CONFIG = {
  // 本地音频检测阈值
  silenceThreshold: 0.02,     // 静音阈值：0.001(敏感) ~ 0.05(严格)
  peakThreshold: 0.15,        // 峰值阈值：0.05(敏感) ~ 0.3(严格)
  
  // 增益控制
  smallVolumeGain: 1.2,       // 小音量放大倍数：1.0 ~ 3.0
  mediumVolumeGain: 1.5,      // 中音量放大倍数：1.0 ~ 3.0
  largeVolumeGain: 1.0,       // 大音量放大倍数：1.0 ~ 1.5
  
  // 压缩设置
  compressionRatio: 0.7,      // 压缩比例：0.5(强压缩) ~ 1.0(无压缩)
  compressionThreshold: 0.25   // 压缩触发阈值：0.2 ~ 0.5
}

// 音频录制器类
class AliyunWebAudioRecorder {
  constructor(requestId, params = {}, isLog = false) {
    this.requestId = requestId
    this.params = {
      ...ALIYUN_ASR_CONFIG.audioConfig,
      ...ALIYUN_ASR_CONFIG.recognitionConfig,
      ...params
    }
    this.isLog = isLog
    
    // 音频相关
    this.audioContext = null
    this.mediaStream = null
    this.source = null
    this.analyser = null
    this.processor = null
    this.gainNode = null
    this.compressor = null
    this.filter = null
    
    // 缓冲区
    this.audioBuffer = []
    this.bufferSize = 4096
    this.sampleRate = this.params.sampleRate || 16000
    
    // 状态
    this.isRecording = false
    this.isProcessing = false
    
    // 回调函数
    this.OnReceivedData = null
    this.OnError = null
    this.OnStop = null
    this.OnVolumeChange = null
    
    // 音频处理统计
    this.audioStats = {
      totalFrames: 0,
      droppedFrames: 0,
      averageVolume: 0,
      peakVolume: 0,
      noiseLevel: 0,
      signalToNoiseRatio: 0
    }
    
    this.isLog && console.log('🎙️ 阿里云音频录制器已创建:', {
      requestId: this.requestId,
      sampleRate: this.sampleRate,
      bufferSize: this.bufferSize,
      params: this.params
    })
  }
  
  // 开始录音
  async start() {
    try {
      this.isLog && console.log('🎙️ 开始阿里云音频录制...')
      
      // 检查浏览器支持
      if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
        throw new Error('浏览器不支持麦克风录音功能')
      }
      
      // 优化音频配置以提高识别准确性和杂音过滤
      const constraints = {
        audio: {
          channelCount: 1,
          sampleRate: this.sampleRate,
          echoCancellation: true,              // 回声消除
          noiseSuppression: true,              // 降噪 - 重要！
          autoGainControl: false,              // 自动增益控制
          
          // 🔥 强化降噪配置 - 针对环境杂音过滤
          googEchoCancellation: true,          // Google回声消除
          googNoiseSuppression: true,          // Google降噪 - 核心功能
          googAutoGainControl: false,          // Google自动增益
          googHighpassFilter: true,            // 高通滤波器 - 过滤低频噪音
          googTypingNoiseDetection: true,      // 键盘噪音检测
          googNoiseSuppression2: true,         // Google降噪2.0（如果支持）
          
          // 新增配置
          googAudioMirroring: false,           // 关闭音频镜像
          googAutoGainControl2: false,         // 关闭Google自动增益2.0
          
          // 新增配置以优化声音检测
          volume: 1.0,                         // 音量级别
          sampleSize: 16,                      // 采样深度
          latency: 0                           // 延迟设置
        }
      }
      
      // 获取媒体流
      this.mediaStream = await navigator.mediaDevices.getUserMedia(constraints)
      
      // 创建音频上下文
      this.audioContext = new (window.AudioContext || window.webkitAudioContext)({
        sampleRate: this.sampleRate
      })
      
      // 创建音频源
      this.source = this.audioContext.createMediaStreamSource(this.mediaStream)
      
      // 创建分析器
      this.analyser = this.audioContext.createAnalyser()
      this.analyser.fftSize = 2048
      this.analyser.smoothingTimeConstant = 0.8
      
      // 创建增益节点
      this.gainNode = this.audioContext.createGain()
      this.gainNode.gain.value = 1.0
      
      // 创建压缩器
      this.compressor = this.audioContext.createDynamicsCompressor()
      this.compressor.threshold.value = -24
      this.compressor.knee.value = 30
      this.compressor.ratio.value = 12
      this.compressor.attack.value = 0.003
      this.compressor.release.value = 0.25
      
      // 创建高通滤波器
      this.filter = this.audioContext.createBiquadFilter()
      this.filter.type = 'highpass'
      this.filter.frequency.value = 85 // 过滤低频噪音
      this.filter.Q.value = 1
      
      // 创建音频处理器 - 使用 AudioWorkletNode 替代已弃用的 ScriptProcessorNode
      try {
        // 尝试使用 AudioWorkletNode
        if (this.audioContext.audioWorklet) {
          // 注册 AudioWorklet 处理器
          await this.audioContext.audioWorklet.addModule(URL.createObjectURL(new Blob([`
            class AudioProcessor extends AudioWorkletProcessor {
              constructor() {
                super()
                this.buffer = []
              }
              
              process(inputs, outputs, parameters) {
                const input = inputs[0]
                if (input && input.length > 0) {
                  const inputData = input[0]
                  this.buffer.push(...inputData)
                  
                  // 当缓冲区达到一定大小时发送数据
                  if (this.buffer.length >= 1024) {
                    this.port.postMessage({
                      type: 'audioData',
                      data: this.buffer.slice()
                    })
                    this.buffer = []
                  }
                }
                return true
              }
            }
            registerProcessor('audio-processor', AudioProcessor)
          `], { type: 'application/javascript' })))
          
          this.processor = new AudioWorkletNode(this.audioContext, 'audio-processor')
          this.processor.port.onmessage = (event) => {
            if (event.data.type === 'audioData') {
              this.handleAudioProcess({ inputBuffer: { getChannelData: () => event.data.data } })
            }
          }
        } else {
          // 降级到 ScriptProcessorNode（已弃用但兼容性更好）
          console.warn('⚠️ AudioWorklet 不可用，使用已弃用的 ScriptProcessorNode')
          this.processor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1)
          this.processor.onaudioprocess = this.handleAudioProcess.bind(this)
        }
      } catch (error) {
        console.warn('⚠️ AudioWorklet 初始化失败，降级到 ScriptProcessorNode:', error)
        this.processor = this.audioContext.createScriptProcessor(this.bufferSize, 1, 1)
        this.processor.onaudioprocess = this.handleAudioProcess.bind(this)
      }
      
      // 连接音频节点
      this.source
        .connect(this.filter)           // 高通滤波器
        .connect(this.compressor)       // 压缩器
        .connect(this.gainNode)         // 增益节点
        .connect(this.analyser)         // 分析器
        .connect(this.processor)        // 处理器
        .connect(this.audioContext.destination)
      
      // 设置状态
      this.isRecording = true
      this.isProcessing = true
      
      // 开始音频统计
      this.startAudioStats()
      
      console.log('✅ [RECORDER] 阿里云音频录制已启动:', {
        isRecording: this.isRecording,
        isProcessing: this.isProcessing,
        hasCallback: !!this.OnReceivedData,
        sampleRate: this.audioContext.sampleRate,
        bufferSize: this.bufferSize
      })
      
      this.isLog && console.log('✅ 阿里云音频录制已启动')
      
    } catch (error) {
      console.error('❌ 启动阿里云音频录制失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
      throw error
    }
  }
  
  // 处理音频数据
  handleAudioProcess(event) {
    if (!this.isRecording || !this.isProcessing) {
      console.log('⚠️ [RECORDER] 音频处理被跳过:', {
        isRecording: this.isRecording,
        isProcessing: this.isProcessing
      })
      return
    }
    
    try {
      const inputBuffer = event.inputBuffer
      const inputData = inputBuffer.getChannelData(0)
      
      // 计算音量
      const volume = this.calculateVolume(inputData)
      
      // 更新音频统计
      this.updateAudioStats(inputData, volume)
      
      // 动态增益控制
      this.applyDynamicGainControl(volume)
      
      // 噪音门限检测
      if (volume < AUDIO_DETECTION_CONFIG.silenceThreshold) {
        this.isLog && console.log('🔇 音频音量过低，跳过此帧')
        return
      }
      
      // 转换为PCM数据
      const pcmData = this.convertToPCM(inputData)
      
      // 只在调试模式下输出详细日志
      if (this.isLog) {
        console.log('🎤 [RECORDER] 处理音频帧:', {
          inputLength: inputData.length,
          volume: volume.toFixed(3),
          pcmLength: pcmData ? pcmData.byteLength : 0,
          hasCallback: !!this.OnReceivedData,
          pcmSuccess: !!pcmData
        })
      }
      
      // 发送音频数据
      if (this.OnReceivedData && pcmData && pcmData.byteLength > 0) {
        // 只在调试模式下输出发送日志
        if (this.isLog) {
          console.log('🎵 [RECORDER] 发送PCM数据到识别器:', {
            size: pcmData.byteLength
          })
        }
        this.OnReceivedData(pcmData)
      } else if (pcmData === null) {
        console.warn('⚠️ [RECORDER] PCM转换失败，跳过音频帧')
      }
      
      // 通知音量变化
      if (this.OnVolumeChange) {
        this.OnVolumeChange(volume)
      }
      
    } catch (error) {
      console.error('❌ 处理音频数据失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 计算音量
  calculateVolume(audioData) {
    let sum = 0
    let max = 0
    
    for (let i = 0; i < audioData.length; i++) {
      const abs = Math.abs(audioData[i])
      sum += abs * abs
      max = Math.max(max, abs)
    }
    
    const rms = Math.sqrt(sum / audioData.length)
    return Math.max(rms, max * 0.1) // 结合RMS和峰值
  }
  
  // 更新音频统计
  updateAudioStats(audioData, volume) {
    this.audioStats.totalFrames++
    this.audioStats.averageVolume = (this.audioStats.averageVolume * 0.9) + (volume * 0.1)
    this.audioStats.peakVolume = Math.max(this.audioStats.peakVolume * 0.95, volume)
    
    // 计算噪音水平
    const noiseLevel = this.calculateNoiseLevel(audioData)
    this.audioStats.noiseLevel = (this.audioStats.noiseLevel * 0.95) + (noiseLevel * 0.05)
    
    // 计算信噪比
    this.audioStats.signalToNoiseRatio = this.audioStats.averageVolume / (this.audioStats.noiseLevel + 0.001)
  }
  
  // 计算噪音水平
  calculateNoiseLevel(audioData) {
    // 简化的噪音估计：计算高频分量
    let highFreqSum = 0
    const step = Math.floor(audioData.length / 32)
    
    for (let i = 0; i < audioData.length; i += step) {
      highFreqSum += Math.abs(audioData[i])
    }
    
    return highFreqSum / 32
  }
  
  // 动态增益控制
  applyDynamicGainControl(volume) {
    if (!this.gainNode) return
    
    let gainValue = 1.0
    
    if (volume < 0.1) {
      // 小音量放大
      gainValue = AUDIO_DETECTION_CONFIG.smallVolumeGain
    } else if (volume < 0.3) {
      // 中音量适度放大
      gainValue = AUDIO_DETECTION_CONFIG.mediumVolumeGain
    } else {
      // 大音量保持或轻微放大
      gainValue = AUDIO_DETECTION_CONFIG.largeVolumeGain
    }
    
    // 平滑增益变化
    this.gainNode.gain.setTargetAtTime(gainValue, this.audioContext.currentTime, 0.1)
  }
  
  // 转换为PCM数据
  convertToPCM(audioData) {
    try {
      if (!audioData || audioData.length === 0) {
        console.warn('⚠️ 音频数据为空，跳过PCM转换')
        return null
      }
      
      const pcmData = new Int16Array(audioData.length)
      
      for (let i = 0; i < audioData.length; i++) {
        // 限制音频范围到[-1, 1]
        let sample = Math.max(-1, Math.min(1, audioData[i]))
        
        // 转换为16位PCM
        pcmData[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF
      }
      
      const buffer = pcmData.buffer
      // 只在调试模式下输出PCM转换日志
      if (this.isLog) {
        console.log('🎵 [PCM_CONVERT] PCM转换成功:', {
          inputLength: audioData.length,
          outputLength: buffer.byteLength,
          pcmLength: pcmData.length
        })
      }
      
      return buffer
    } catch (error) {
      console.error('❌ 转换PCM数据失败:', error)
      return null
    }
  }
  
  // 开始音频统计
  startAudioStats() {
    setInterval(() => {
      if (this.isLog && this.audioStats.totalFrames > 0) {
        console.log('📊 音频统计:', {
          totalFrames: this.audioStats.totalFrames,
          averageVolume: this.audioStats.averageVolume.toFixed(4),
          peakVolume: this.audioStats.peakVolume.toFixed(4),
          noiseLevel: this.audioStats.noiseLevel.toFixed(4),
          signalToNoiseRatio: this.audioStats.signalToNoiseRatio.toFixed(2)
        })
      }
    }, 5000) // 每5秒输出一次统计
  }
  
  // 停止录音
  stop() {
    try {
      this.isLog && console.log('🛑 停止阿里云音频录制')
      
      this.isRecording = false
      this.isProcessing = false
      
      // 断开音频节点
      if (this.source) {
        this.source.disconnect()
        this.source = null
      }
      
      if (this.processor) {
        this.processor.disconnect()
        this.processor = null
      }
      
      if (this.analyser) {
        this.analyser.disconnect()
        this.analyser = null
      }
      
      if (this.gainNode) {
        this.gainNode.disconnect()
        this.gainNode = null
      }
      
      if (this.compressor) {
        this.compressor.disconnect()
        this.compressor = null
      }
      
      if (this.filter) {
        this.filter.disconnect()
        this.filter = null
      }
      
      // 关闭音频上下文
      if (this.audioContext && this.audioContext.state !== 'closed') {
        this.audioContext.close()
        this.audioContext = null
      }
      
      // 停止媒体流
      if (this.mediaStream) {
        this.mediaStream.getTracks().forEach(track => track.stop())
        this.mediaStream = null
      }
      
      // 清理缓冲区
      this.audioBuffer = []
      
      // 重置统计
      this.audioStats = {
        totalFrames: 0,
        droppedFrames: 0,
        averageVolume: 0,
        peakVolume: 0,
        noiseLevel: 0,
        signalToNoiseRatio: 0
      }
      
      if (this.OnStop) {
        this.OnStop()
      }
      
      this.isLog && console.log('✅ 阿里云音频录制已停止')
      
    } catch (error) {
      console.error('❌ 停止阿里云音频录制失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 获取音频状态
  getAudioStats() {
    return {
      ...this.audioStats,
      isRecording: this.isRecording,
      isProcessing: this.isProcessing,
      sampleRate: this.sampleRate,
      bufferSize: this.bufferSize
    }
  }
  
  // 设置增益
  setGain(gainValue) {
    if (this.gainNode) {
      this.gainNode.gain.setTargetAtTime(gainValue, this.audioContext.currentTime, 0.1)
      this.isLog && console.log('🔊 设置增益:', gainValue)
    }
  }
  
  // 设置压缩器参数
  setCompressorParams(threshold, ratio, attack, release) {
    if (this.compressor) {
      this.compressor.threshold.setTargetAtTime(threshold, this.audioContext.currentTime, 0.1)
      this.compressor.ratio.setTargetAtTime(ratio, this.audioContext.currentTime, 0.1)
      this.compressor.attack.setTargetAtTime(attack, this.audioContext.currentTime, 0.1)
      this.compressor.release.setTargetAtTime(release, this.audioContext.currentTime, 0.1)
      this.isLog && console.log('🔧 设置压缩器参数:', { threshold, ratio, attack, release })
    }
  }
  
  // 设置滤波器参数
  setFilterParams(frequency, Q) {
    if (this.filter) {
      this.filter.frequency.setTargetAtTime(frequency, this.audioContext.currentTime, 0.1)
      this.filter.Q.setTargetAtTime(Q, this.audioContext.currentTime, 0.1)
      this.isLog && console.log('🔧 设置滤波器参数:', { frequency, Q })
    }
  }
  
  // 暂停录音
  pause() {
    this.isProcessing = false
    this.isLog && console.log('⏸️ 暂停阿里云音频录制')
  }
  
  // 恢复录音
  resume() {
    this.isProcessing = true
    this.isLog && console.log('▶️ 恢复阿里云音频录制')
  }
  
  // 销毁录制器
  destroy() {
    this.stop()
    
    // 清理回调
    this.OnReceivedData = null
    this.OnError = null
    this.OnStop = null
    this.OnVolumeChange = null
    
    this.isLog && console.log('🗑️ 阿里云音频录制器已销毁')
  }
}

export default AliyunWebAudioRecorder

console.log('✅ 阿里云音频录制器已加载') 
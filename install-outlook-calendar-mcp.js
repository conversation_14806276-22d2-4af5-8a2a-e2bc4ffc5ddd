#!/usr/bin/env node

// 安装并配置Outlook日历MCP服务
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🛠️ 开始安装和配置Outlook日历MCP...');

// 检查Windows环境
function checkWindowsEnvironment() {
  console.log('🔍 检查Windows环境...');
  
  if (process.platform !== 'win32') {
    console.error('❌ Outlook日历MCP仅支持Windows系统');
    return false;
  }
  
  console.log('✅ Windows环境检查通过');
  return true;
}

// 检查Outlook是否已安装
function checkOutlookInstallation() {
  console.log('🔍 检查Microsoft Outlook安装状态...');
  
  try {
    // 检查常见的Outlook安装路径
    const outlookPaths = [
      'C:\\Program Files\\Microsoft Office\\root\\Office16\\OUTLOOK.EXE',
      'C:\\Program Files (x86)\\Microsoft Office\\root\\Office16\\OUTLOOK.EXE',
      'C:\\Program Files\\Microsoft Office\\Office16\\OUTLOOK.EXE',
      'C:\\Program Files (x86)\\Microsoft Office\\Office16\\OUTLOOK.EXE'
    ];
    
    const outlookExists = outlookPaths.some(path => fs.existsSync(path));
    
    if (outlookExists) {
      console.log('✅ Microsoft Outlook已安装');
      return true;
    } else {
      console.warn('⚠️ 未找到Microsoft Outlook安装，请确保已安装Microsoft Office');
      console.warn('⚠️ Outlook日历MCP需要Microsoft Outlook桌面客户端');
      return false;
    }
  } catch (error) {
    console.warn('⚠️ 检查Outlook安装时出错:', error.message);
    return false;
  }
}

// 安装outlook-calendar-mcp包
function installOutlookCalendarMCP() {
  console.log('📦 安装outlook-calendar-mcp包...');
  
  try {
    // 检查是否已安装
    try {
      require.resolve('outlook-calendar-mcp');
      console.log('✅ outlook-calendar-mcp已安装');
      return true;
    } catch (err) {
      console.log('📦 安装outlook-calendar-mcp...');
      execSync('npm install outlook-calendar-mcp@latest --save', { 
        stdio: 'inherit',
        timeout: 120000 // 2分钟超时
      });
      console.log('✅ outlook-calendar-mcp安装完成');
      return true;
    }
  } catch (error) {
    console.error('❌ outlook-calendar-mcp安装失败:', error.message);
    return false;
  }
}

// 创建outlook-calendar-mcp.cmd批处理文件
function createCmdFile() {
  console.log('🔧 创建outlook-calendar-mcp.cmd批处理文件...');
  
  try {
    const cmdPath = path.join(process.cwd(), 'outlook-calendar-mcp.cmd');
    
    // 获取node_modules中的outlook-calendar-mcp路径
    const outlookMcpPath = path.join(process.cwd(), 'node_modules', 'outlook-calendar-mcp', 'src', 'index.js');
    
    const cmdContent = `@echo off
REM Outlook Calendar MCP Server Startup Script
REM Auto-generated by install-outlook-calendar-mcp.js

echo 📅 启动Outlook日历MCP服务器...

REM 检查Node.js是否可用
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Node.js，请确保Node.js已安装并添加到PATH
    exit /b 1
)

REM 检查outlook-calendar-mcp是否存在
if not exist "${outlookMcpPath}" (
    echo ❌ 未找到outlook-calendar-mcp，请运行 npm install outlook-calendar-mcp
    exit /b 1
)

REM 启动MCP服务器
echo 🚀 正在启动Outlook日历MCP服务器...
node "${outlookMcpPath}" %*

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Outlook日历MCP服务器启动失败
    exit /b %ERRORLEVEL%
)
`;

    fs.writeFileSync(cmdPath, cmdContent);
    console.log('✅ outlook-calendar-mcp.cmd已创建:', cmdPath);
    return true;
  } catch (error) {
    console.error('❌ 创建CMD文件失败:', error.message);
    return false;
  }
}

// 测试outlook-calendar-mcp连接
async function testOutlookConnection() {
  console.log('🧪 测试Outlook日历连接...');
  
  try {
    // 检测Outlook是否正在运行
    const { exec } = require('child_process');
    
    return new Promise((resolve) => {
      exec('tasklist | findstr OUTLOOK', (error, stdout, stderr) => {
        if (stdout && stdout.includes('OUTLOOK.EXE')) {
          console.log('✅ Outlook正在运行');
          console.log('💡 提示: 首次使用时，Outlook可能会显示安全提示，请选择"允许"');
          resolve(true);
        } else {
          console.warn('⚠️ Outlook未运行，请先启动Outlook并登录您的账户');
          console.warn('⚠️ Outlook日历MCP需要Outlook桌面客户端运行才能正常工作');
          resolve(false);
        }
      });
    });
  } catch (error) {
    console.warn('⚠️ 测试Outlook连接时出错:', error.message);
    return false;
  }
}

// 创建配置说明文件
function createConfigDocumentation() {
  console.log('📋 创建配置说明文件...');
  
  try {
    const docPath = path.join(process.cwd(), 'outlook-calendar-mcp-setup.md');
    
    const docContent = `# Outlook日历MCP配置说明

## 安装完成

✅ outlook-calendar-mcp包已安装
✅ outlook-calendar-mcp.cmd启动脚本已创建

## 使用要求

1. **Windows操作系统**: 仅支持Windows系统
2. **Microsoft Outlook**: 必须安装Microsoft Outlook桌面客户端
3. **Outlook登录**: Outlook必须已登录您的账户
4. **安全设置**: 首次使用时需要允许脚本访问Outlook

## 可用功能

- 📅 查看日历事件
- ➕ 创建新的日历事件
- 📝 更新现有事件
- 👥 管理会议和参会者
- 🔍 查找空闲时间段
- 📊 获取参会者状态

## 安全提示

- 首次使用时，Outlook会显示安全对话框
- 请选择"允许"以启用MCP功能
- 所有操作仅在本地Outlook客户端中进行
- 不会向外部服务器发送日历数据

## 故障排除

如果遇到问题：

1. 确保Outlook正在运行并已登录
2. 重启Outlook和应用程序
3. 检查Outlook安全设置
4. 确认Microsoft Office版本兼容性

## 测试连接

运行以下命令测试连接：
\`\`\`bash
node calendar-sync-console.js
\`\`\`

然后使用：
\`\`\`javascript
await testCalendar()
\`\`\`
`;

    fs.writeFileSync(docPath, docContent);
    console.log('✅ 配置说明已创建:', docPath);
    return true;
  } catch (error) {
    console.error('❌ 创建配置说明失败:', error.message);
    return false;
  }
}

// 运行所有安装步骤
async function main() {
  console.log('🚀 开始安装Outlook日历MCP...');
  
  // 检查Windows环境
  const windowsOk = checkWindowsEnvironment();
  if (!windowsOk) {
    console.error('⛔ 环境检查失败，中止安装');
    process.exit(1);
  }
  
  // 检查Outlook安装
  const outlookOk = checkOutlookInstallation();
  if (!outlookOk) {
    console.warn('⚠️ Outlook检查失败，但将继续安装');
  }
  
  // 安装MCP包
  const mcpInstalled = installOutlookCalendarMCP();
  if (!mcpInstalled) {
    console.error('⛔ MCP包安装失败，中止安装');
    process.exit(1);
  }
  
  // 创建CMD文件
  const cmdCreated = createCmdFile();
  if (!cmdCreated) {
    console.warn('⚠️ CMD文件创建失败，但将继续安装');
  }
  
  // 测试连接
  const connectionOk = await testOutlookConnection();
  if (!connectionOk) {
    console.warn('⚠️ Outlook连接测试失败，请手动启动Outlook');
  }
  
  // 创建配置说明
  const docCreated = createConfigDocumentation();
  if (!docCreated) {
    console.warn('⚠️ 配置说明创建失败');
  }
  
  console.log('🎉 Outlook日历MCP安装完成！');
  console.log('📋 使用前请确保：');
  console.log('   1. Microsoft Outlook已安装并正在运行');
  console.log('   2. 已登录您的Outlook账户');
  console.log('   3. 允许脚本访问Outlook（首次使用时会提示）');
  console.log('');
  console.log('💡 查看详细配置说明: outlook-calendar-mcp-setup.md');
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('💥 安装过程中发生未捕获的错误:', error.message);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 安装过程中发生未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动安装
main().catch(err => {
  console.error('💥 致命错误:', err.message);
  process.exit(1);
}); 
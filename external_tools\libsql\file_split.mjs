import parser from './core/parser.js';
import splitter from './core/splitter.js';
import fs from 'fs';
import mammoth from "mammoth";
import TurndownService from "turndown";


function splitMarkdown(markdownText, minSplitLength = 1500, maxSplitLength = 2000) {
    // 解析文档结构
    const outline = parser.extractOutline(markdownText);

    // 按标题分割文档
    const sections = parser.splitByHeadings(markdownText, outline);

    // 处理段落，确保满足分割条件
    const res = splitter.processSections(sections, outline, minSplitLength, maxSplitLength);

    return res.map(r => ({
        result: `> **📑 Summarization：** *${r.summary}*\n\n---\n\n${r.content}`,
        ...r
    }));
}

export async function getContentByPath(filePath) {
    try {
        try {
            const htmlResult = await mammoth.convertToHtml(
                {path: filePath},
                {
                    convertImage: (image) => {
                        try {
                            return mammoth.docx.paragraph({
                                children: [
                                    mammoth.docx.textRun({
                                        text: ''
                                    })
                                ]
                            });
                        } catch (error) {
                            console.error(`[getContentByPath] 图片转换错误: ${filePath}`, error);
                            // 返回一个简单的替代文本，避免转换失败
                            return {
                                altText: '[图片]'
                            };
                        }
                    }
                }
            );
            
            try {
                const turndownService = new TurndownService();
                return turndownService.turndown(htmlResult.value);
            } catch (markdownError) {
                console.error(`[getContentByPath] HTML转Markdown错误: ${filePath}`, markdownError);
                // 回退到原始HTML
                return htmlResult.value;
            }
        } catch (conversionError) {
            console.error(`[getContentByPath] 文档转换失败: ${filePath}`, conversionError);
            // 回退方案：使用extractRawText
            console.log(`[getContentByPath] 尝试提取纯文本...`);
            const textResult = await mammoth.extractRawText({path: filePath});
            return textResult.value;
        }
    } catch (error) {
        console.error(`[getContentByPath] 获取文件内容失败: ${filePath}`, error);
        throw error;
    }
}

// 使用示例
// 修改后的调用方式示例
(async () => {
    try {
        const filename = "D:\\Users\\L\\Documents\\WeChat Files\\wxid_027173b4ipoq22\\FileStorage\\File\\2025-06\\数字员工需求-20250617.docx";
        // const filename = "E:\\临时文件\\测试文章.txt";
        let fileContent;
        if (filename.endsWith('.docx') || filename.endsWith('.doc')) {
            fileContent = await getContentByPath(filename);
        } else {
            fileContent = await fs.promises.readFile(filename, 'utf8');
        }
        const result = splitMarkdown(fileContent);
        console.log(`result: ${JSON.stringify(result)}`);
    } catch (error) {
        console.error('查询失败:', error);
    }
})();
import parser from './core/parser.js';
import splitter from './core/splitter.js';
import fs from 'fs';
import XLSX from 'xlsx';
import mammoth from "mammoth";
import TurndownService from "turndown";
import {createClient} from '@libsql/client';
import OpenAI from 'openai';

const client = createClient({
    url: 'file:local.db',
});

const openai = new OpenAI({
    baseURL: 'https://api.siliconflow.cn/v1',
    apiKey: 'sk-cubtiycbsbczznzdpxfwsomvhnnpzoikstnubshkpimhqhzy',
});

async function getEmbedding(content) {
    const response = await openai.embeddings.create({
        model: 'BAAI/bge-m3',
        input: content,
        encoding_format: 'float'
    });

    // 将返回的数组转换为 Float32Array
    return new Float32Array(response.data[0].embedding);
}

export async function getContentByPath(filePath) {
    if (filePath.endsWith('.docx') || filePath.endsWith('.doc')) {
        try {
            const htmlResult = await mammoth.convertToHtml(
                {path: filePath},
                {
                    convertImage: (image) => {
                        try {
                            return mammoth.docx.paragraph({
                                children: [
                                    mammoth.docx.textRun({
                                        text: ''
                                    })
                                ]
                            });
                        } catch (error) {
                            console.error(`[getContentByPath] 图片转换错误: ${filePath}`, error);
                            // 返回一个简单的替代文本，避免转换失败
                            return {
                                altText: '[图片]'
                            };
                        }
                    }
                }
            );
            const turndownService = new TurndownService();
            return turndownService.turndown(htmlResult.value);
        } catch (error) {
            console.error(`[getContentByPath] 处理文件失败: ${filePath}`, {
                error: error.message,
                stack: error.stack
            });
            throw new Error(`文件处理失败: ${filePath}`, {cause: error});
        }
    } else if (filePath.endsWith('.txt') || filePath.endsWith('.md')) {
        return await fs.promises.readFile(filePath, 'utf8')
    } else if (filePath.endsWith('.xlsx')) {
        const workbook = XLSX.readFile(filePath);
        let mdLines = [];
        workbook.SheetNames.forEach(sheetName => {
            const sheetData = workbook.Sheets[sheetName];
            const jsonData = XLSX.utils.sheet_to_json(sheetData, {header: 1});
            jsonData.forEach((row, i) => {
                // 处理空单元格
                const filledRow = row.map(cell => cell === undefined ? '' : cell);
                mdLines.push(`| ${filledRow.join(' | ')} |`);
                // 添加表头分隔符
                if (i === 0) {
                    mdLines.push(`|${filledRow.map(() => '---').join('|')}|`);
                }
            });
        });
        return mdLines.join('\n');
    } else {
        throw new Error(`文件处理失败: 格式暂不支持`);
    }
}

function splitMarkdown(markdownText, minSplitLength = 1500, maxSplitLength = 2000) {
    // 解析文档结构
    const outline = parser.extractOutline(markdownText);

    // 按标题分割文档
    const sections = parser.splitByHeadings(markdownText, outline);

    // 处理段落，确保满足分割条件
    const res = splitter.processSections(sections, outline, minSplitLength, maxSplitLength);

    return res.map(r => ({
        result: `> **📑 Summarization：** *${r.summary}*\n\n---\n\n${r.content}`,
        ...r
    }));
}

export async function uploadFile(fileAbsPath, fileType = 1, remark = 'word文档') {
    try {
        const fileContent = await getContentByPath(fileAbsPath);
        const chunks = splitMarkdown(fileContent);
        if (!chunks || chunks.length === 0 || !chunks[0].content) {
            console.log('没有需要处理的内容');
            return;
        }
        const fileName = fileAbsPath.split('\\').pop()
        console.log(fileName)
        const filePath = fileAbsPath.split('\\').slice(0, -1).join('\\') + "\\"
        console.log(filePath)
        const sourceFilePath = fileAbsPath;
        const filePreview = chunks[0].content;
        // 插入数据并返回自增ID
        const result = await client.execute({
            sql: `INSERT INTO user_file (file_type, file_name, file_path, source_file_path, file_preview, remark)
                  VALUES (?, ?, ?, ?, ?, ?) RETURNING id`, // 添加RETURNING子句获取新插入行的ID
            args: [fileType, fileName, filePath, sourceFilePath, filePreview, remark]
        });
        // 从结果中提取自增ID
        const fileId = result.rows[0].id;
        console.log(`✅ 文件插入成功，自增ID为: ${fileId}`);
        for (const chunk of chunks) {
            // 生成描述文本的嵌入向量
            const embedding = await getEmbedding(chunk.content);
            // 插入数据到数据库
            await client.execute({
                sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                      VALUES (?, ?, ?)`,
                args: [fileId, chunk.content, embedding]
            });
            console.log('✅ 文本块数据插入成功：' + chunk.content);
        }
    } catch (error) {
        console.error('上传失败:', error);
    }
}

// 使用示例
(async () => {
    // const filename = "./doc/专家信息2.txt";
    const filename = "./doc/指标.xlsx";
    await uploadFile(filename)
})();
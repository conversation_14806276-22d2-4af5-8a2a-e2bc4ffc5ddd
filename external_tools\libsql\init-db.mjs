import { createClient } from '@libsql/client';

async function initDatabase() {
    try {
        const client = createClient({
            url: 'file:local.db',  // 在当前目录创建 SQLite 数据库文件
        });

        // 初始化数据库结构
        await client.batch([
            `CREATE TABLE IF NOT EXISTS user_file (
                id INTEGER PRIMARY KEY,
                file_type INTEGER NOT NULL,
                file_name TEXT NOT NULL,
                file_path TEXT NOT NULL,
                source_file_path TEXT NOT NULL,
                file_preview TEXT NOT NULL,
                remark TEXT NOT NULL,
                create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            `CREATE TABLE IF NOT EXISTS user_file_embd (
                id INTEGER PRIMARY KEY,
                file_id INTEGER NOT NULL,
                file_content TEXT NOT NULL,
                embedding F32_BLOB(1024),
                create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            `CREATE INDEX IF NOT EXISTS file_embedding_idx 
             ON user_file_embd(libsql_vector_idx(embedding))`
        ], 'write');

        console.log('✅ 数据库初始化成功！');
        console.log('  已创建 文件 表和向量索引');
    } catch (error) {
        console.error('❌ 初始化失败:', error);
    }
}

// 执行初始化
initDatabase();
const axios = require('axios')

// API 配置常量
const MAIN_API_CONFIG = {
  PROD_API_BASE_URL: 'http://114.67.112.88:9603/prod-api',
  REQUEST_CONFIG: {
    TIMEOUT: 30000,
    SEARCH_TIMEOUT: 120000
  }
}

/**
 * 创建主进程API客户端
 * @param {string} baseURL - API基础URL
 * @param {string} userToken - 用户token
 * @returns {Object} axios客户端实例
 */
function createMainApiClient(baseURL = null, userToken = '') {
  // 使用配置中心的API地址
  const apiBaseUrl = baseURL || `${MAIN_API_CONFIG.PROD_API_BASE_URL}/api`
  const headers = {
    'Content-Type': 'application/json'
  }
  
  // 如果有token则添加Authorization头
  if (userToken) {
    headers['Authorization'] = `Bearer ${userToken}`
  }
  
  const client = axios.create({
    baseURL: apiBaseUrl,
    headers,
    timeout: MAIN_API_CONFIG.REQUEST_CONFIG.TIMEOUT
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      console.log('🌐 主进程API请求:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        hasToken: !!userToken,
        tokenLength: userToken.length
      })
      return config
    },
    (error) => {
      console.error('❌ 主进程API请求错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器 - 统一错误处理
  client.interceptors.response.use(
    (response) => {
      console.log('✅ 主进程API响应成功:', {
        status: response.status,
        url: response.config.url
      })
      return response
    },
    (error) => {
      console.error('❌ 主进程API响应错误:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.response?.data?.msg || error.message
      })
      
      return handleMainApiError(error)
    }
  )

  return client
}

/**
 * 处理主进程API错误
 * @param {Error} error - axios错误对象
 * @returns {Promise} 处理后的错误
 */
function handleMainApiError(error) {
  if (!error.response) {
    // 网络错误
    console.error('🚨 主进程API网络错误: 网络连接失败，请检查网络状态')
    return Promise.reject(error)
  }

  const { status, data } = error.response
  
  switch (status) {
    case 200:
      // 200是正常返回，不应该进入错误处理
      return Promise.resolve(error.response)
      
    case 401:
      // Token过期，记录日志但不执行退出操作（主进程不处理UI）
      console.error('🔑 主进程API Token已过期，需要重新登录')
      break
      
    case 403:
      console.error('🚨 主进程API权限不足，无法访问该资源')
      break
      
    case 404:
      console.error('🚨 主进程API请求的资源不存在')
      break
      
    case 500:
      console.error('🚨 主进程API服务器内部错误，请稍后重试')
      break
      
    default:
      // 其他状态码，显示服务器返回的消息
      const message = data?.msg || data?.message || `请求失败 (${status})`
      console.error('🚨 主进程API错误:', message)
      break
  }

  return Promise.reject(error)
}

/**
 * 主进程AI服务请求
 * @param {string} userToken - 用户token
 * @param {Object} requestData - 请求数据
 * @param {string} model - 模型名称
 * @param {boolean} isEmailTodoRequest - 是否为邮件待办请求
 * @returns {Promise} 请求结果
 */
async function callMainAIService(userToken, requestData, model = '', isEmailTodoRequest = false) {
  try {
    // 如果没有传入isEmailTodoRequest参数，则自动检测
    if (isEmailTodoRequest === false) {
      isEmailTodoRequest = requestData.messages && 
        requestData.messages.some(msg => 
          msg.content && msg.content.includes('邮件分析助手') && 
          msg.content.includes('待办事项')
        )
    }
    
    let client
    let endpoint
    
    if (isEmailTodoRequest) {
      // 邮件待办筛选使用代理模式
      console.log('🤖 [EMAIL_TODO] 使用代理模式处理邮件待办筛选请求')
      client = createMainApiClient('http://114.67.112.88:9603/prod-api', userToken)
      endpoint = '/api/tool/email/handle'
    } else {
      // 其他AI请求使用原有模式
      client = createMainApiClient(null, userToken)
      endpoint = '/chat/completions'
    }
    
    // 根据模型类型设置不同的参数
    const finalRequestData = { ...requestData }
    if (model) {
      finalRequestData.model = model
    }
    
    console.log('🤖 主进程AI服务请求:', {
      model: model || '系统默认',
      hasToken: !!userToken,
      messagesCount: finalRequestData.messages?.length || 0,
      endpoint: endpoint,
      isEmailTodo: isEmailTodoRequest
    })

    const response = await client.post(endpoint, finalRequestData)
    
    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data
    
    return {
      success: true,
      data: responseData,
      status: response.status
    }
    
  } catch (error) {
    console.error('🤖 主进程AI服务请求失败:', error)
    return {
      success: false,
      error: error.response?.data?.msg || error.message,
      status: error.response?.status
    }
  }
}

module.exports = {
  createMainApiClient,
  handleMainApiError,
  callMainAIService,
  MAIN_API_CONFIG
} 
const { ipcMain, dialog } = require('electron')

/**
 * MCP相关的IPC处理程序
 */
class MCPIPCHandler {
  constructor(appManager) {
    this.appManager = appManager
  }

  /**
   * 注册所有MCP相关的IPC处理程序
   */
  register() {
    // 执行MCP工具
    ipcMain.handle('execute-mcp-tool', async (event, toolName, args) => {
      try {
        console.log(`🚀 执行MCP工具: ${toolName}`)
        console.log(`📋 工具参数:`, JSON.stringify(args, null, 2))

        let result

        // 直接使用MCP管理器的callRealMCPTool方法
        result = await this.appManager.mcpManager.callRealMCPTool(toolName, args)

        console.log(`✅ MCP工具执行完成: ${toolName}`)
        console.log(`📋 执行结果:`, JSON.stringify(result, null, 2))

        return {
          success: true,
          result: result,
          toolName: toolName,
          timestamp: new Date().toISOString()
        }
      } catch (error) {
        console.error(`❌ MCP工具执行失败: ${toolName}`, error)
        return {
          success: false,
          error: error.message,
          toolName: toolName,
          timestamp: new Date().toISOString()
        }
      }
    })

    // 获取MCP状态
    ipcMain.handle('get-mcp-status', async () => {
      try {
        const status = this.appManager.mcpManager.getConnectionStatus()
        const tools = await this.appManager.mcpManager.listAvailableTools()

        return {
          success: true,
          status: status,
          tools: tools,
          timestamp: new Date().toISOString()
        }
      } catch (error) {
        console.error('获取MCP状态失败:', error)
        return {
          success: false,
          error: error.message,
          timestamp: new Date().toISOString()
        }
      }
    })

    // 重启浏览器MCP服务
    ipcMain.handle('restart-browser-mcp', async () => {
      try {
        console.log('🔄 接收到重启浏览器MCP请求')
        return await this.appManager.mcpManager.restartBrowserMCP()
      } catch (error) {
        console.error('重启浏览器MCP失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 重启Outlook日历MCP服务
    ipcMain.handle('restart-outlook-calendar-mcp', async () => {
      try {
        console.log('🔄 接收到重启Outlook日历MCP请求')
        return await this.appManager.mcpManager.restartOutlookCalendarMCP()
      } catch (error) {
        console.error('重启Outlook日历MCP失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取文件路径配置
    ipcMain.handle('get-file-paths-config', async () => {
      try {
        if (this.appManager && this.appManager.mcpManager) {
          const userConfig = this.appManager.mcpManager.getUserConfig()
          return userConfig?.filePaths || {
            downloadsDir: 'downloads',
            documentsDir: 'documents',
            desktopDir: 'desktop',
            customPaths: []
          }
        } else {
          console.warn('AppManager 或 MCPManager 未初始化')
          return {
            downloadsDir: 'downloads',
            documentsDir: 'documents',
            desktopDir: 'desktop',
            customPaths: []
          }
        }
      } catch (error) {
        console.error('获取文件路径配置失败:', error)
        return {
          downloadsDir: 'downloads',
          documentsDir: 'documents',
          desktopDir: 'desktop',
          customPaths: []
        }
      }
    })

    // 更新文件路径配置
    ipcMain.handle('update-file-paths-config', async (event, config) => {
      try {
        if (this.appManager && this.appManager.mcpManager) {
          // 获取当前配置
          const userConfig = this.appManager.mcpManager.getUserConfig() || {}
          // 更新文件路径配置
          userConfig.filePaths = { ...userConfig.filePaths, ...config }
          // 保存配置
          this.appManager.mcpManager.saveUserConfig(userConfig)
          
          console.log('✅ 文件路径配置已更新:', config)
          return { success: true }
        } else {
          console.error('AppManager 或 MCPManager 未初始化')
          return { success: false, error: 'AppManager 或 MCPManager 未初始化' }
        }
      } catch (error) {
        console.error('更新文件路径配置失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 选择自定义路径
    ipcMain.handle('select-custom-path', async () => {
      try {
        const result = await dialog.showOpenDialog({
          properties: ['openDirectory'],
          title: '选择MCP文件操作允许访问的目录'
        })

        if (result.canceled) {
          return { success: false, canceled: true }
        }

        const selectedPath = result.filePaths[0]
        console.log('🧠 用户选择的自定义路径:', selectedPath)
        return { success: true, path: selectedPath }
      } catch (error) {
        console.error('选择自定义路径失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 配置管理相关的IPC处理
    ipcMain.handle('update-chat-config', async (event, config) => {
      try {
        // 更新知识库配置
        if (config.embedding) {
          const knowledge = require('../knowledge')
          const currentConfig = knowledge.getKnowledgeConfig()
          const newEmbeddingConfig = { ...currentConfig.embedding, ...config.embedding }
          
          knowledge.updateKnowledgeConfig({
            ...currentConfig,
            embedding: newEmbeddingConfig
          })
          
          console.log('✅ 知识库配置已更新:', newEmbeddingConfig)
        }

        // 更新MCP配置
        if (config.mcp) {
          // 这里可以添加MCP配置更新逻辑
          console.log('✅ MCP配置已更新:', config.mcp)
        }

        return { success: true }
      } catch (error) {
        console.error('更新聊天配置失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ MCP相关IPC处理程序已注册')
  }


}

module.exports = MCPIPCHandler

/**
 * 阿里云实时语音识别(ASR) API参数配置
 * 
 * === 主要API参数范围和说明 ===
 * 
 * 1. format: 音频格式
 *    - 可选值: "pcm", "wav", "opus", "speex"
 *    - 推荐: "pcm" (无压缩，质量最好)
 * 
 * 2. sample_rate: 采样率
 *    - 可选值: 8000, 16000, 44100, 48000
 *    - 推荐: 16000 (语音识别标准采样率)
 * 
 * 3. enable_punctuation_prediction: 标点符号预测
 *    - 可选值: true/false
 *    - 说明: 自动添加标点符号
 * 
 * 4. enable_intermediate_result: 中间结果
 *    - 可选值: true/false
 *    - 说明: 实时返回识别过程中的结果
 * 
 * 5. enable_inverse_text_normalization: 逆文本规范化
 *    - 可选值: true/false
 *    - 说明: 将"一九九八年"转为"1998年"
 * 
 * 6. enable_voice_detection: 语音检测
 *    - 可选值: true/false
 *    - 说明: 检测是否有人声输入
 * 
 * 7. max_end_silence: 最大结束静音时间
 *    - 范围: 200-10000ms
 *    - 说明: 静音多久后认为说话结束
 *    - 重要: 控制句子结束的关键参数
 * 
 * 8. max_single_segment_time: 最大单段时间
 *    - 范围: 5000-300000ms (5秒-5分钟)
 *    - 说明: 单次识别的最长时间
 * 
 * 9. vocabulary_id: 热词ID
 *    - 类型: 字符串
 *    - 说明: 自定义词汇表ID，提高特定词汇识别率
 * 
 * 10. enable_semantic_sentence_detection: 语义断句
 *     - 可选值: true/false
 *     - 说明: 基于语义智能断句
 * 
 * === 重要提示 ===
 * - max_end_silence 是控制响应速度的关键参数
 * - 降低此值可以更快响应，但可能会截断长句
 * - 提高此值可以完整识别长句，但响应会较慢
 * - 建议范围：500-2000ms，默认1000ms
 */

// 阿里云ASR配置
export const ALIYUN_ASR_CONFIG = {
  // 阿里云访问密钥
  accessKeyId: 'LTAI5tLYqLP9ep6X4kkYPJVk',
  accessKeySecret: '******************************',
  
  // 项目配置
  appkey: 'uappWLUEjGcvRLZP',
  
  // 热词配置
  hotwordId: 'c8a07d7c7d3f451cab30624519c218f7',
  
  // 大模型API Key
  llmApiKey: 'sk-64244749e4db4ec2ad347a7ee0e7334e',
  
  // 阿里云ASR实时语音识别WebSocket地址
  websocketUrl: 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1',
  
  // 区域配置
  region: 'cn-shanghai',
  
  // 音频配置
  audioConfig: {
    sampleRate: 16000,      // 采样率
    format: 'pcm',          // 音频格式
    channels: 1,            // 单声道
    bitsPerSample: 16       // 位深度
  },
  
  // 识别配置
  recognitionConfig: {
    // === 基础识别参数 ===
    enablePunctuationPrediction: true,    // 启用标点符号预测 [true/false]
    enableIntermediateResult: true,       // 启用中间结果输出 [true/false] - 实时显示识别过程
    enableInverseTextNormalization: true, // 启用逆文本规范化 [true/false] - 将语音转为标准文本格式
    enableDisfluency: false,              // 是否启用顺滑/过滤语气词 [true/false] - false保留"嗯啊"等
    enableVoiceDetection: true,           // 启用语音检测 [true/false] - 检测是否有人声
    
    // === 关键时间参数 ===
    maxEndSilence: 800,                   // 最大结束静音时间 [200-10000ms] - 静音多久后认为说话结束
    maxSingleSegmentTime: 10000,          // 最大单段时间 [5000-300000ms] - 单次识别最长时间
    
    // === 高级识别参数 ===
    enableTimestampAlignment: false,      // 启用时间戳对齐 [true/false] - 为每个词添加时间戳
    enableFirstChannelOnly: false,        // 仅处理第一个声道 [true/false] - 多声道音频时使用
    enableSemanticSentenceDetection: false, // 启用语义断句 [true/false] - 智能断句
    enableAutoSplit: false,               // 启用自动分轨 [true/false] - 自动分离不同段落
    enableSpeakerDiarization: false,      // 启用说话人分离 [true/false] - 区分不同说话人
    
    // === 音频处理参数（本地浏览器处理）===
    enableVoiceActivity: true,            // 启用语音活动检测 [true/false]
    enableNoiseSuppression: true,         // 启用噪声抑制 [true/false]
    enableEchoCancellation: true,         // 启用回声消除 [true/false]
    enableAutoGainControl: false,         // 启用自动增益控制 [true/false]
    enableHighpassFilter: true,           // 启用高通滤波器 [true/false] - 过滤低频噪音
    enableTypingNoiseDetection: true,     // 启用打字噪声检测 [true/false]
    
    // === Google增强处理参数 ===
    enableGoogleNoiseSuppression: true,   // 启用Google噪声抑制 [true/false]
    enableGoogleEchoCancellation: true,   // 启用Google回声消除 [true/false]
    enableGoogleHighpassFilter: true,     // 启用Google高通滤波器 [true/false]
    enableGoogleTypingNoiseDetection: true, // 启用Google打字噪声检测 [true/false]
    enableGoogleAutoGainControl: false,   // 启用Google自动增益控制 [true/false]
    enableGoogleNoiseSuppression2: true,  // 启用Google噪声抑制2.0 [true/false]
    enableGoogleAudioMirroring: false,    // 启用Google音频镜像 [true/false]
    enableGoogleAutoGainControl2: false,  // 启用Google自动增益控制2.0 [true/false]
    
    // === 音频级别参数 ===
    volume: 1.0,                          // 音量级别 [0.0-2.0] - 1.0为正常音量
    latency: 0,                           // 延迟设置 [0-1000ms] - 音频延迟补偿
    compressionRatio: 0.7,                // 压缩比率 [0.1-1.0] - 动态范围压缩
    compressionThreshold: 0.25,           // 压缩触发阈值 [0.0-1.0] - 音量超过此值开始压缩
    peakThreshold: 0.15,                  // 峰值阈值 [0.0-1.0] - 防止音频削峰
    silenceThreshold: 0.02,               // 静音阈值 [0.0-1.0] - 低于此值认为是静音
    smallVolumeGain: 1.2,                 // 小音量增益 [0.5-3.0] - 小声时的放大倍数
    mediumVolumeGain: 1.5,                // 中等音量增益 [0.5-3.0] - 中等音量的放大倍数
    largeVolumeGain: 1.0,                 // 大音量增益 [0.5-3.0] - 大声时的放大倍数（通常为1.0不放大）
    noiseThreshold: 0.4                   // 噪音阈值 [0.0-1.0] - 低于此值的音频被认为是背景噪音
  }
}

// 验证配置
export const validateAliyunConfig = () => {
  const errors = []
  
  if (!ALIYUN_ASR_CONFIG.accessKeyId) {
    errors.push('缺少AccessKey ID')
  }
  
  if (!ALIYUN_ASR_CONFIG.accessKeySecret) {
    errors.push('缺少AccessKey Secret')
  }
  
  if (!ALIYUN_ASR_CONFIG.appkey) {
    errors.push('缺少项目AppKey')
  }
  
  if (!ALIYUN_ASR_CONFIG.websocketUrl) {
    errors.push('缺少WebSocket连接地址')
  }
  
  return {
    valid: errors.length === 0,
    errors: errors
  }
}

// 调试配置
export const ALIYUN_DEBUG_CONFIG = {
  enableDebugLog: true,
  enablePerformanceMonitor: true,
  enableErrorReport: true,
  enableNetworkMonitor: true,
  enableAudioMonitor: true,
  enableRecognitionMonitor: true,
  enableConnectionMonitor: true,
  enableHeartbeatMonitor: true,
  enableReconnectMonitor: true,
  enableTokenMonitor: true,
  enableResultMonitor: true,
  enableStateMonitor: true,
  enableVolumeMonitor: true,
  enableSilenceMonitor: true,
  enableNoiseMonitor: true,
  enableLatencyMonitor: true,
  enableThroughputMonitor: true,
  enableQualityMonitor: true,
  enableStabilityMonitor: true,
  enableCompatibilityMonitor: true
}

// 常用配置预设
export const ALIYUN_PRESETS = {
  // 高质量配置
  highQuality: {
    ...ALIYUN_ASR_CONFIG.recognitionConfig,
    enablePunctuationPrediction: true,
    enableIntermediateResult: true,
    enableInverseTextNormalization: true,
    enableDisfluency: false,
    enableVoiceDetection: true,
    maxEndSilence: 600,
    maxSingleSegmentTime: 60000,
    enableNoiseSuppression: true,
    enableEchoCancellation: true,
    enableAutoGainControl: false,
    enableHighpassFilter: true,
    enableTypingNoiseDetection: true,
    enableGoogleNoiseSuppression: true,
    enableGoogleEchoCancellation: true,
    enableGoogleHighpassFilter: true,
    enableGoogleTypingNoiseDetection: true,
    enableGoogleAutoGainControl: false,
    enableGoogleNoiseSuppression2: true,
    enableGoogleAudioMirroring: false,
    enableGoogleAutoGainControl2: false,
    volume: 1.0,
    latency: 0,
    compressionRatio: 0.7,
    compressionThreshold: 0.25,
    peakThreshold: 0.15,
    silenceThreshold: 0.02,
    smallVolumeGain: 1.2,
    mediumVolumeGain: 1.5,
    largeVolumeGain: 1.0,
    noiseThreshold: 0.4
  },
  
  // 快速响应配置
  fastResponse: {
    ...ALIYUN_ASR_CONFIG.recognitionConfig,
    enablePunctuationPrediction: true,
    enableIntermediateResult: true,
    enableInverseTextNormalization: true,
    enableDisfluency: false,
    enableVoiceDetection: true,
    maxEndSilence: 400,
    maxSingleSegmentTime: 30000,
    enableNoiseSuppression: true,
    enableEchoCancellation: true,
    enableAutoGainControl: false,
    enableHighpassFilter: true,
    enableTypingNoiseDetection: true,
    enableGoogleNoiseSuppression: true,
    enableGoogleEchoCancellation: true,
    enableGoogleHighpassFilter: true,
    enableGoogleTypingNoiseDetection: true,
    enableGoogleAutoGainControl: false,
    enableGoogleNoiseSuppression2: true,
    enableGoogleAudioMirroring: false,
    enableGoogleAutoGainControl2: false,
    volume: 1.0,
    latency: 0,
    compressionRatio: 0.8,
    compressionThreshold: 0.2,
    peakThreshold: 0.1,
    silenceThreshold: 0.03,
    smallVolumeGain: 1.5,
    mediumVolumeGain: 1.8,
    largeVolumeGain: 1.2,
    noiseThreshold: 0.3
  },
  
  // 低延迟配置
  lowLatency: {
    ...ALIYUN_ASR_CONFIG.recognitionConfig,
    enablePunctuationPrediction: true,
    enableIntermediateResult: true,
    enableInverseTextNormalization: true,
    enableDisfluency: false,
    enableVoiceDetection: true,
    maxEndSilence: 300,
    maxSingleSegmentTime: 20000,
    enableNoiseSuppression: true,
    enableEchoCancellation: true,
    enableAutoGainControl: false,
    enableHighpassFilter: true,
    enableTypingNoiseDetection: true,
    enableGoogleNoiseSuppression: true,
    enableGoogleEchoCancellation: true,
    enableGoogleHighpassFilter: true,
    enableGoogleTypingNoiseDetection: true,
    enableGoogleAutoGainControl: false,
    enableGoogleNoiseSuppression2: true,
    enableGoogleAudioMirroring: false,
    enableGoogleAutoGainControl2: false,
    volume: 1.0,
    latency: 0,
    compressionRatio: 0.9,
    compressionThreshold: 0.15,
    peakThreshold: 0.08,
    silenceThreshold: 0.04,
    smallVolumeGain: 1.8,
    mediumVolumeGain: 2.0,
    largeVolumeGain: 1.5,
    noiseThreshold: 0.2
  }
}

// 默认配置
export const getDefaultAliyunConfig = () => {
  const configValidation = validateAliyunConfig()
  if (!configValidation.valid) {
    console.error('❌ 阿里云ASR配置验证失败:', configValidation.errors)
  }
  
  return {
    ...ALIYUN_ASR_CONFIG,
    debug: ALIYUN_DEBUG_CONFIG.enableDebugLog || false
  }
}

console.log('✅ 阿里云ASR配置已加载') 
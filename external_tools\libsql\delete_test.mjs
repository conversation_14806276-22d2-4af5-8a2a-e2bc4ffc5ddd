import {createClient} from '@libsql/client';

const client = createClient({
    url: 'file:local.db',
});


async function deleteUserFilesWithEmbeddings(ids) {
    if (!Array.isArray(ids) || ids.length === 0) {
        throw new Error('必须提供至少一个文件ID');
    }
    // 验证所有ID都是正整数
    const invalidIds = ids.filter(id => !Number.isInteger(id) || id <= 0);
    if (invalidIds.length > 0) {
        throw new Error(`无效的文件ID: ${invalidIds.join(', ')}`);
    }
    try {
        // 使用事务保证数据一致性
        // const results = await client.batch([
        //     `DELETE
        //      FROM user_file_embd
        //      WHERE file_id IN (${ids.map(() => '?').join(', ')})`,
        //     `DELETE
        //      FROM user_file
        //      WHERE id IN (${ids.map(() => '?').join(', ')})`
        // ], 'write', ids.concat(ids));  // 传递参数两次
        const embdResult = await client.execute({
            sql: `DELETE FROM user_file_embd WHERE file_id IN (${ids.map(() => '?').join(',')})`,
            args: ids
        });

        const fileResult = await client.execute({
            sql: `DELETE FROM user_file WHERE id IN (${ids.map(() => '?').join(',')})`,
            args: ids
        });

        return {
            embdResult: embdResult.rowsAffected,
            fileResult: fileResult.rowsAffected
        };
    } catch (error) {
        console.error('级联删除失败:', error);
        throw error;
    }
}


// 使用示例
(async () => {
    try {
        const deletedCount = await deleteUserFilesWithEmbeddings([1, 2, 3]);
        console.log(`成功删除 ${JSON.stringify(deletedCount)} 条记录`);
    } catch (error) {
        console.error('删除操作异常:', error);
    }
})();

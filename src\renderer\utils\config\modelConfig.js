/**
 * 统一的大模型配置管理器
 * 集中管理所有API配置，避免重复定义
 */
import modelManager from '../modelManager.js'

// 默认配置
const DEFAULT_CONFIG = {
  // 聊天API配置（通过代理服务器）
  chat: {
    // 使用代理服务器，与项目架构保持一致
    useProxy: true,
    proxyEndpoint: '/api/chat/completions', // 代理端点
    model: 'qwen-max-latest', // 默认模型，实际由modelManager管理
    maxTokens: 1000,
    temperature: 0.7,
    timeout: 120000  // 2分钟超时
  },

  // 向量化API配置（通过代理服务器）
  embedding: {
    // 使用代理服务器，不直接调用外部API
    useProxy: true,
    proxyEndpoint: '/api/embeddings', // 代理端点
    model: 'BAAI/bge-m3',
    encoding_format: 'float',
    maxTokens: 8000
  },

  // 文件路径配置
  filePaths: {
    // 默认为用户下载目录
    downloadsDir: 'downloads', // 这里存储的是相对标识符，实际路径会在运行时解析
    allowCustomPaths: false // 是否允许自定义路径（安全限制）
  },

  // 聊天参数配置
  chatSettings: {
    maxHistoryLength: 20,
    systemPrompt: `你是犇犇，一个可爱、活泼、聪明的AI助手。你的特点是：
1. 性格活泼开朗，喜欢用简洁明了的话语回答问题
2. 乐于助人，总是积极地帮助用户解决问题
3. 回答要简洁明了，通常控制在100字以内
4. 保持友好和耐心的态度

请用符合犇犇性格的方式回答用户的问题。`
  },

  // 知识库配置
  knowledge: {
    database: {
      url: 'file:knowledge.db',
      timeout: 30000
    },
    document: {
      minSplitLength: 1500,
      maxSplitLength: 2000,
      supportedFormats: ['.txt', '.md', '.docx', '.doc', '.pdf']
    },
    search: {
      defaultLimit: 4,
      similarityThreshold: 0.47,
      minSimilarityThreshold: 0.47,
      maxResultsPerDocument: 2
    },
    fileTypes: {
      DOCUMENT: 1,
      PDF: 2,
      MARKDOWN: 3,
      TEXT: 4
    }
  }
}

// 本地存储键名
const STORAGE_KEY = 'nezha-model-config'

/**
 * 配置管理器类
 */
class ModelConfigManager {
  constructor() {
    this.config = this.loadConfig()
    this.listeners = new Set()
  }

  /**
   * 从本地存储加载配置
   */
  loadConfig() {
    try {
      const saved = localStorage.getItem(STORAGE_KEY)
      if (saved) {
        const parsedConfig = JSON.parse(saved)
        // 深度合并默认配置和保存的配置
        return this.deepMerge(DEFAULT_CONFIG, parsedConfig)
      }
    } catch (error) {
      console.error('加载配置失败:', error)
    }
    return JSON.parse(JSON.stringify(DEFAULT_CONFIG)) // 深拷贝默认配置
  }

  /**
   * 保存配置到本地存储
   */
  saveConfig() {
    try {
      localStorage.setItem(STORAGE_KEY, JSON.stringify(this.config))
      this.notifyListeners()
      return true
    } catch (error) {
      console.error('保存配置失败:', error)
      return false
    }
  }

  /**
   * 深度合并对象
   */
  deepMerge(target, source) {
    const result = { ...target }
    for (const key in source) {
      if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
        result[key] = this.deepMerge(result[key] || {}, source[key])
      } else {
        result[key] = source[key]
      }
    }
    return result
  }

  /**
   * 获取聊天API配置
   */
  getChatConfig() {
    // 获取基础配置
    const baseConfig = { ...this.config.chat }

    // 获取模型管理器中的模型配置
    const modelConfig = modelManager.getModelConfig()

    // 实际使用modelManager的配置（已经是代理模式）
    return {
      ...baseConfig,
      model: modelConfig.id,
      baseURL: modelConfig.baseURL, // 总是使用modelManager的代理地址
      // 不需要apiKey，使用用户Token认证
    }
  }

  /**
   * 获取向量化API配置
   */
  getEmbeddingConfig() {
    return { ...this.config.embedding }
  }

  /**
   * 获取聊天设置
   */
  getChatSettings() {
    return { ...this.config.chatSettings }
  }

  /**
   * 获取知识库配置
   */
  getKnowledgeConfig() {
    return { ...this.config.knowledge }
  }

  /**
   * 获取文件路径配置
   */
  getFilePathsConfig() {
    return { ...this.config.filePaths }
  }

  /**
   * 获取完整配置
   */
  getFullConfig() {
    return JSON.parse(JSON.stringify(this.config))
  }

  /**
   * 更新聊天API配置
   */
  updateChatConfig(updates) {
    this.config.chat = { ...this.config.chat, ...updates }
    return this.saveConfig()
  }

  /**
   * 更新向量化API配置
   */
  updateEmbeddingConfig(updates) {
    this.config.embedding = { ...this.config.embedding, ...updates }
    return this.saveConfig()
  }

  /**
   * 更新聊天设置
   */
  updateChatSettings(updates) {
    this.config.chatSettings = { ...this.config.chatSettings, ...updates }
    return this.saveConfig()
  }

  /**
   * 更新知识库配置
   */
  updateKnowledgeConfig(updates) {
    this.config.knowledge = this.deepMerge(this.config.knowledge, updates)
    return this.saveConfig()
  }

  /**
   * 更新文件路径配置
   */
  updateFilePathsConfig(updates) {
    this.config.filePaths = { ...this.config.filePaths, ...updates }
    return this.saveConfig()
  }

  /**
   * 批量更新配置
   */
  updateConfig(updates) {
    this.config = this.deepMerge(this.config, updates)
    return this.saveConfig()
  }

  /**
   * 重置配置为默认值
   */
  resetToDefaults() {
    this.config = JSON.parse(JSON.stringify(DEFAULT_CONFIG))
    return this.saveConfig()
  }

  /**
   * 重置特定部分配置
   */
  resetSection(section) {
    if (DEFAULT_CONFIG[section]) {
      this.config[section] = JSON.parse(JSON.stringify(DEFAULT_CONFIG[section]))
      return this.saveConfig()
    }
    return false
  }

  /**
   * 添加配置变更监听器
   */
  addListener(callback) {
    this.listeners.add(callback)
    return () => this.listeners.delete(callback)
  }

  /**
   * 通知所有监听器
   */
  notifyListeners() {
    this.listeners.forEach(callback => {
      try {
        callback(this.config)
      } catch (error) {
        console.error('配置监听器执行失败:', error)
      }
    })
  }

  /**
   * 验证配置完整性
   */
  validateConfig() {
    const errors = []

    // 验证聊天配置（代理模式）
    if (!this.config.chat.useProxy) {
      errors.push('聊天服务必须使用代理模式')
    }
    if (!this.config.chat.proxyEndpoint) {
      errors.push('聊天代理端点不能为空')
    }
    if (!this.config.chat.model) {
      errors.push('聊天模型不能为空')
    }

    // 验证向量化配置（代理模式）
    if (!this.config.embedding.useProxy) {
      errors.push('向量化服务必须使用代理模式')
    }
    if (!this.config.embedding.proxyEndpoint) {
      errors.push('向量化代理端点不能为空')
    }
    if (!this.config.embedding.model) {
      errors.push('向量化模型不能为空')
    }

    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 测试聊天API连接（通过代理）
   */
  async testChatConnection() {
    try {
      // 使用代理服务进行测试
      const { sendMCPChatRequest } = await import('../mcpChatAPI.js')

      const testMessages = [
        { role: 'user', content: '你好，这是一个连接测试。' }
      ]

      const testResult = await sendMCPChatRequest(testMessages, {
        maxTokens: 50,
        temperature: 0.7
      })

      if (testResult && testResult.success) {
        return {
          success: true,
          message: '聊天代理服务连接测试成功！',
          data: { response: testResult.message }
        }
      } else {
        return {
          success: false,
          message: '聊天代理服务返回错误：' + (testResult?.error || '未知错误')
        }
      }
    } catch (error) {
      return {
        success: false,
        message: '聊天代理服务连接测试失败：' + error.message
      }
    }
  }

  /**
   * 测试向量化API连接（通过代理）
   */
  async testEmbeddingConnection() {
    try {
      // 使用代理服务进行测试
      const { knowledgeProxyService } = await import('../knowledgeProxyService.js')

      const config = this.getEmbeddingConfig()
      const testResult = await knowledgeProxyService.getEmbedding('测试文本', {
        model: config.model,
        encoding_format: config.encoding_format
      })

      if (testResult && testResult.length > 0) {
        return {
          success: true,
          message: '向量化代理服务连接测试成功！',
          data: { dimension: testResult.length }
        }
      } else {
        return {
          success: false,
          message: '向量化代理服务返回数据格式不正确'
        }
      }
    } catch (error) {
      return {
        success: false,
        message: '向量化代理服务连接测试失败：' + error.message
      }
    }
  }

  /**
   * 导出配置
   */
  exportConfig() {
    return JSON.stringify(this.config, null, 2)
  }

  /**
   * 导入配置
   */
  importConfig(configJson) {
    try {
      const importedConfig = JSON.parse(configJson)
      this.config = this.deepMerge(DEFAULT_CONFIG, importedConfig)
      return this.saveConfig()
    } catch (error) {
      console.error('导入配置失败:', error)
      return false
    }
  }
}

// 创建全局实例
const modelConfig = new ModelConfigManager()

// 导出实例和类
export default modelConfig
export { ModelConfigManager, DEFAULT_CONFIG }

// 便捷的导出函数
export const getChatConfig = () => modelConfig.getChatConfig()
export const getEmbeddingConfig = () => modelConfig.getEmbeddingConfig()
export const getChatSettings = () => modelConfig.getChatSettings()
export const getKnowledgeConfig = () => modelConfig.getKnowledgeConfig()
export const getFilePathsConfig = () => modelConfig.getFilePathsConfig()
export const updateChatConfig = (updates) => modelConfig.updateChatConfig(updates)
export const updateEmbeddingConfig = (updates) => modelConfig.updateEmbeddingConfig(updates)
export const updateChatSettings = (updates) => modelConfig.updateChatSettings(updates)
export const updateKnowledgeConfig = (updates) => modelConfig.updateKnowledgeConfig(updates)
export const updateFilePathsConfig = (updates) => modelConfig.updateFilePathsConfig(updates)
export const resetConfigToDefaults = () => modelConfig.resetToDefaults()
export const testChatConnection = () => modelConfig.testChatConnection()
export const testEmbeddingConnection = () => modelConfig.testEmbeddingConnection() 
/**
 * 版本检查调试工具
 */

// 调试开关
const DEBUG_VERSION_CHECK = true

export function logVersionCheck(message, data = null) {
  if (DEBUG_VERSION_CHECK) {
    const timestamp = new Date().toLocaleTimeString()
    console.log(`[版本检查 ${timestamp}] ${message}`, data || '')
  }
}

export function debugVersionState(state) {
  if (DEBUG_VERSION_CHECK) {
    logVersionCheck('当前状态:', {
      checking: state.checking,
      hasUpdate: state.hasUpdate,
      error: state.error,
      currentVersion: state.currentVersion,
      latestVersion: state.latestVersion,
      isMandatoryUpdate: state.isMandatoryUpdate
    })
  }
}

export function debugVersionCheckResult(result) {
  if (DEBUG_VERSION_CHECK) {
    logVersionCheck('检查结果:', result)
  }
}

<template>
  <VersionCheck 
    ref="versionCheckRef"
    :autoCheck="true"
    @version-check-complete="handleVersionCheckComplete"
    @version-update-found="handleVersionUpdateFound" 
  />
</template>

<script setup>
import { ref } from 'vue'
import VersionCheck from './VersionCheck.vue'

const versionCheckRef = ref(null)
let timeoutId = null

// 版本检查完成处理
const handleVersionCheckComplete = (result) => {
  console.log('应用版本检查完成:', result)
  
  // 清除超时计时器
  if (timeoutId) {
    clearTimeout(timeoutId)
    timeoutId = null
  }
  
  // 直接转发结果给父组件
  emit('version-check-complete', result)
}

// 版本更新发现处理
const handleVersionUpdateFound = (data) => {
  console.log('发现版本更新，停止超时计时:', data)
  // 清除超时计时器，因为需要用户交互
  if (timeoutId) {
    clearTimeout(timeoutId)
    timeoutId = null
    console.log('🔄 已停止版本检查超时计时')
  }
}

// 定义事件
const emit = defineEmits(['version-check-complete'])

// 开始版本检查
const startVersionCheck = async () => {
  console.log('开始应用版本检查')

  // 设置超时，防止版本检查无限期显示
  timeoutId = setTimeout(() => {
    console.log('版本检查超时，强制隐藏')
    emit('version-check-complete', { needsUpdate: false, timeout: true })
  }, 30000) // 30秒超时，给API更多时间响应

  // 注意：版本检查会通过 VersionCheck 组件的 autoCheck="true" 自动开始
  // 超时保护会在 handleVersionCheckComplete 中被清除
}

// 暴露方法给父组件
defineExpose({
  startVersionCheck
})
</script>

 
<template>
  <div class="knowledge-page">
    <div class="page-header">
      <h1>知识库管理</h1>
      <div class="header-actions">
        <button @click="refreshStats" class="btn btn-secondary" :disabled="loading">
          <i class="icon">🔄</i>
          刷新
        </button>
        <button @click="showAddDocuments = true" class="btn btn-primary" :disabled="loading">
          <i class="icon">➕</i>
          添加文档
        </button>
      </div>
    </div>

    <!-- 统计信息 -->
    <div class="stats-grid">
      <div class="stat-card">
        <div class="stat-icon">📚</div>
        <div class="stat-info">
          <h3>{{ stats.totalFiles }}</h3>
          <p>文档数量</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">📝</div>
        <div class="stat-info">
          <h3>{{ stats.totalSegments }}</h3>
          <p>知识片段</p>
        </div>
      </div>
      <div class="stat-card">
        <div class="stat-icon">🎯</div>
        <div class="stat-info">
          <h3>{{ searchResults.length }}</h3>
          <p>搜索结果</p>
        </div>
      </div>
    </div>

    <!-- 搜索知识库 -->
    <div class="search-section">
      <h2>搜索知识库</h2>
      <div class="search-form">
        <div class="search-input-group">
          <input 
            v-model="searchQuery" 
            @keyup.enter="searchKnowledge"
            type="text" 
            placeholder="输入关键词搜索知识库..."
            class="search-input"
            :disabled="loading"
          >
          <button @click="searchKnowledge" class="btn btn-primary" :disabled="loading || !searchQuery.trim()">
            <i class="icon">🔍</i>
            搜索
          </button>
        </div>
        <div class="search-options">
          <label>
            搜索限制:
            <select v-model="searchLimit">
              <option value="3">3 个结果</option>
              <option value="5">5 个结果</option>
              <option value="10">10 个结果</option>
            </select>
          </label>
        </div>
      </div>

      <!-- 搜索结果 -->
      <div v-if="searchResults.length > 0" class="search-results">
        <h3>搜索结果 ({{ searchResults.length }} 个)</h3>
        <div class="result-list">
          <div v-for="(result, index) in searchResults" :key="index" class="result-item">
            <div class="result-header">
              <span class="result-index">{{ index + 1 }}</span>
              <span class="result-file">{{ result.file_name || '未知文档' }}</span>
              <span class="result-similarity">{{ (result.similarity * 100).toFixed(1) }}%</span>
            </div>
            <div class="result-content">{{ result.content }}</div>
          </div>
        </div>
      </div>
      <div v-else-if="hasSearched && searchResults.length === 0" class="no-results">
        <p>没有找到相关的知识内容</p>
      </div>
    </div>

    <!-- 文档管理 -->
    <div class="documents-section">
      <h2>文档管理</h2>
      <div class="documents-actions">
        <button @click="refreshDocuments" class="btn btn-secondary" :disabled="loading">
          <i class="icon">🔄</i>
          刷新列表
        </button>
        <button @click="showClearDialog = true" class="btn btn-danger" :disabled="loading || stats.totalFiles === 0">
          <i class="icon">🗑️</i>
          清空知识库
        </button>
        <button @click="rebuildKnowledgeBase" class="btn btn-warning" :disabled="loading">
          <i class="icon">🔨</i>
          重建知识库
        </button>
      </div>

      <!-- 文档列表 -->
      <div v-if="documents.length > 0" class="documents-list">
        <div class="document-item" v-for="doc in documents" :key="doc.id">
          <div class="document-info">
            <div class="document-name">{{ doc.fileName }}</div>
            <div class="document-meta">
              <span>创建时间: {{ formatDate(doc.createTime) }}</span>
              <span>备注: {{ doc.remark }}</span>
            </div>
            <div class="document-preview">{{ doc.filePreview }}</div>
          </div>
          <div class="document-actions">
            <button @click="deleteDocument(doc.id)" class="btn btn-sm btn-danger" :disabled="loading">
              删除
            </button>
          </div>
        </div>
      </div>
      <div v-else-if="!loading" class="no-documents">
        <p>暂无文档，点击"添加文档"开始构建您的知识库</p>
      </div>
    </div>

    <!-- 添加文档对话框 -->
    <div v-if="showAddDocuments" class="modal-overlay" @click="closeAddDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>添加文档到知识库</h3>
          <button @click="closeAddDialog" class="btn-close">×</button>
        </div>
        <div class="modal-body">
          <div class="add-options">
            <button @click="selectFiles" class="btn btn-primary btn-block">
              <i class="icon">📁</i>
              选择文件
            </button>
            <button @click="selectDirectory" class="btn btn-secondary btn-block">
              <i class="icon">📂</i>
              选择文件夹
            </button>
          </div>
          
          <div v-if="selectedFiles.length > 0" class="selected-files">
            <h4>已选择文件 ({{ selectedFiles.length }} 个)</h4>
            <div class="file-list">
              <div v-for="(file, index) in selectedFiles" :key="index" class="file-item">
                <span class="file-name">{{ getFileName(file) }}</span>
                <button @click="removeFile(index)" class="btn btn-sm btn-danger">移除</button>
              </div>
            </div>
          </div>

          <div v-if="indexProgress.show" class="index-progress">
            <h4>正在索引文档...</h4>
            <div class="progress-bar">
              <div 
                class="progress-fill" 
                :style="{ width: indexProgress.percentage + '%' }"
              ></div>
            </div>
            <p class="progress-text">
              {{ indexProgress.current }} / {{ indexProgress.total }} 
              - {{ indexProgress.currentFile }}
            </p>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeAddDialog" class="btn btn-secondary" :disabled="indexing">
            取消
          </button>
          <button 
            @click="startIndexing" 
            class="btn btn-primary" 
            :disabled="selectedFiles.length === 0 || indexing"
          >
            开始索引 ({{ selectedFiles.length }} 个文件)
          </button>
        </div>
      </div>
    </div>

    <!-- 清空确认对话框 -->
    <div v-if="showClearDialog" class="modal-overlay" @click="showClearDialog = false">
      <div class="modal-content small" @click.stop>
        <div class="modal-header">
          <h3>确认清空知识库</h3>
        </div>
        <div class="modal-body">
          <p>此操作将删除所有文档和知识片段，不可恢复。确定要继续吗？</p>
        </div>
        <div class="modal-footer">
          <button @click="showClearDialog = false" class="btn btn-secondary">
            取消
          </button>
          <button @click="clearKnowledgeBase" class="btn btn-danger">
            确认清空
          </button>
        </div>
      </div>
    </div>

    <!-- 加载中指示器 -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-spinner"></div>
      <p>{{ loadingMessage }}</p>
    </div>
  </div>
</template>

<script>
import { 
  initKnowledgeDatabase,
  getKnowledgeStats,
  searchKnowledge as searchKnowledgeAPI,
  indexDocuments,
  selectDocumentDirectory,
  getDirectoryFiles,
  clearKnowledgeBase as clearKnowledgeBaseAPI,
  rebuildKnowledgeBase as rebuildKnowledgeBaseAPI,
  listFiles,
  deleteFile
} from '../utils/knowledge/knowledgeClient.js'

export default {
  name: 'Knowledge',
  data() {
    return {
      loading: false,
      loadingMessage: '',
      
      // 统计信息
      stats: {
        totalFiles: 0,
        totalSegments: 0
      },
      
      // 搜索
      searchQuery: '',
      searchLimit: 5,
      searchResults: [],
      hasSearched: false,
      
      // 文档管理
      documents: [],
      
      // 添加文档
      showAddDocuments: false,
      selectedFiles: [],
      indexing: false,
      indexProgress: {
        show: false,
        current: 0,
        total: 0,
        percentage: 0,
        currentFile: ''
      },
      
      // 清空确认
      showClearDialog: false
    }
  },
  
  async mounted() {
    await this.initialize()
  },
  
  methods: {
    async initialize() {
      this.loading = true
      this.loadingMessage = '初始化知识库...'
      
      try {
        // 初始化知识库
        await initKnowledgeDatabase()
        
        // 获取统计信息
        await this.refreshStats()
        
        // 获取文档列表
        await this.refreshDocuments()
        
      } catch (error) {
        console.error('知识库初始化失败:', error)
        this.$message?.error?.('知识库初始化失败')
      } finally {
        this.loading = false
      }
    },
    
    async refreshStats() {
      try {
        this.stats = await getKnowledgeStats()
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    },
    
    async refreshDocuments() {
      try {
        const result = await listFiles({ pageSize: 50 })
        this.documents = result.rows || []
      } catch (error) {
        console.error('获取文档列表失败:', error)
      }
    },
    
    async searchKnowledge() {
      if (!this.searchQuery.trim()) return
      
      this.loading = true
      this.loadingMessage = '搜索知识库...'
      
      try {
        this.searchResults = await searchKnowledgeAPI(this.searchQuery, parseInt(this.searchLimit))
        this.hasSearched = true
      } catch (error) {
        console.error('搜索失败:', error)
        this.$message?.error?.('搜索失败')
      } finally {
        this.loading = false
      }
    },
    
    async selectFiles() {
      // 这里需要实现文件选择功能
      // 可以通过Electron的dialog API或者HTML input实现
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = '.txt,.md,.docx,.doc'
      
      input.onchange = (e) => {
        const files = Array.from(e.target.files)
        this.selectedFiles.push(...files)
      }
      
      input.click()
    },
    
    async selectDirectory() {
      try {
        const result = await selectDocumentDirectory()
        if (result.success && result.directory) {
          const files = await getDirectoryFiles(result.directory)
          this.selectedFiles.push(...files.map(filePath => ({ path: filePath })))
        }
      } catch (error) {
        console.error('选择目录失败:', error)
        this.$message?.error?.('选择目录失败')
      }
    },
    
    removeFile(index) {
      this.selectedFiles.splice(index, 1)
    },
    
    async startIndexing() {
      if (this.selectedFiles.length === 0) return
      
      this.indexing = true
      this.indexProgress.show = true
      this.indexProgress.current = 0
      this.indexProgress.total = this.selectedFiles.length
      this.indexProgress.percentage = 0
      
      try {
        const filePaths = this.selectedFiles.map(file => file.path || file.name)
        
        await indexDocuments(filePaths, 1, (current, total, currentFile) => {
          this.indexProgress.current = current
          this.indexProgress.total = total
          this.indexProgress.percentage = Math.round((current / total) * 100)
          this.indexProgress.currentFile = this.getFileName(currentFile)
        })
        
        this.$message?.success?.('文档索引完成')
        await this.refreshStats()
        await this.refreshDocuments()
        this.closeAddDialog()
        
      } catch (error) {
        console.error('文档索引失败:', error)
        this.$message?.error?.('文档索引失败')
      } finally {
        this.indexing = false
        this.indexProgress.show = false
      }
    },
    
    async deleteDocument(id) {
      if (!confirm('确定要删除这个文档吗？')) return
      
      this.loading = true
      this.loadingMessage = '删除文档...'
      
      try {
        await deleteFile(id)
        this.$message?.success?.('文档删除成功')
        await this.refreshStats()
        await this.refreshDocuments()
      } catch (error) {
        console.error('删除文档失败:', error)
        this.$message?.error?.('删除文档失败')
      } finally {
        this.loading = false
      }
    },
    
    async clearKnowledgeBase() {
      this.showClearDialog = false
      this.loading = true
      this.loadingMessage = '清空知识库...'
      
      try {
        await clearKnowledgeBaseAPI()
        this.$message?.success?.('知识库已清空')
        await this.refreshStats()
        await this.refreshDocuments()
        this.searchResults = []
        this.hasSearched = false
      } catch (error) {
        console.error('清空知识库失败:', error)
        this.$message?.error?.('清空知识库失败')
      } finally {
        this.loading = false
      }
    },
    
    async rebuildKnowledgeBase() {
      if (!confirm('重建知识库将删除所有现有数据，确定继续吗？')) return
      
      this.loading = true
      this.loadingMessage = '重建知识库...'
      
      try {
        await rebuildKnowledgeBaseAPI()
        this.$message?.success?.('知识库重建完成')
        await this.refreshStats()
        await this.refreshDocuments()
        this.searchResults = []
        this.hasSearched = false
      } catch (error) {
        console.error('重建知识库失败:', error)
        this.$message?.error?.('重建知识库失败')
      } finally {
        this.loading = false
      }
    },
    
    closeAddDialog() {
      this.showAddDocuments = false
      this.selectedFiles = []
      this.indexProgress.show = false
    },
    
    getFileName(file) {
      if (typeof file === 'string') {
        return file.split(/[\\\/]/).pop()
      }
      return file.name || file.path?.split(/[\\\/]/).pop() || '未知文件'
    },
    
    formatDate(dateString) {
      return new Date(dateString).toLocaleString('zh-CN')
    }
  }
}
</script>

<style scoped>
.knowledge-page {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
}

.page-header h1 {
  margin: 0;
  color: #333;
}

.header-actions {
  display: flex;
  gap: 10px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-card {
  background: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
  display: flex;
  align-items: center;
  gap: 15px;
}

.stat-icon {
  font-size: 32px;
}

.stat-info h3 {
  margin: 0;
  font-size: 24px;
  color: #333;
}

.stat-info p {
  margin: 5px 0 0 0;
  color: #666;
  font-size: 14px;
}

.search-section, .documents-section {
  background: white;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.search-section h2, .documents-section h2 {
  margin: 0 0 20px 0;
  color: #333;
}

.search-form {
  margin-bottom: 20px;
}

.search-input-group {
  display: flex;
  gap: 10px;
  margin-bottom: 10px;
}

.search-input {
  flex: 1;
  padding: 10px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 14px;
}

.search-options {
  display: flex;
  gap: 20px;
  align-items: center;
}

.search-options label {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.search-options select {
  padding: 4px 8px;
  border: 1px solid #ddd;
  border-radius: 4px;
}

.search-results h3 {
  margin: 0 0 15px 0;
  color: #333;
}

.result-list {
  space-y: 10px;
}

.result-item {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
}

.result-header {
  display: flex;
  align-items: center;
  gap: 10px;
  margin-bottom: 10px;
  font-size: 14px;
}

.result-index {
  background: #007bff;
  color: white;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 12px;
  font-weight: bold;
}

.result-file {
  font-weight: 500;
  color: #333;
}

.result-similarity {
  background: #28a745;
  color: white;
  padding: 2px 6px;
  border-radius: 4px;
  font-size: 12px;
  margin-left: auto;
}

.result-content {
  line-height: 1.5;
  color: #555;
  background: #f8f9fa;
  padding: 10px;
  border-radius: 4px;
  font-size: 14px;
}

.no-results, .no-documents {
  text-align: center;
  color: #666;
  padding: 40px 20px;
}

.documents-actions {
  display: flex;
  gap: 10px;
  margin-bottom: 20px;
}

.documents-list {
  space-y: 10px;
}

.document-item {
  border: 1px solid #eee;
  border-radius: 6px;
  padding: 15px;
  margin-bottom: 10px;
  display: flex;
  justify-content: space-between;
  align-items: start;
}

.document-info {
  flex: 1;
}

.document-name {
  font-weight: 500;
  color: #333;
  margin-bottom: 5px;
}

.document-meta {
  font-size: 12px;
  color: #666;
  margin-bottom: 8px;
}

.document-meta span {
  margin-right: 15px;
}

.document-preview {
  font-size: 14px;
  color: #555;
  line-height: 1.4;
  background: #f8f9fa;
  padding: 8px;
  border-radius: 4px;
}

.document-actions {
  margin-left: 15px;
}

/* 模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: white;
  border-radius: 8px;
  width: 90%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-content.small {
  max-width: 400px;
}

.modal-header {
  padding: 20px 20px 10px 20px;
  border-bottom: 1px solid #eee;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-header h3 {
  margin: 0;
  color: #333;
}

.btn-close {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
}

.modal-body {
  padding: 20px;
  flex: 1;
  overflow-y: auto;
}

.modal-footer {
  padding: 10px 20px 20px 20px;
  border-top: 1px solid #eee;
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

.add-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 10px;
  margin-bottom: 20px;
}

.selected-files h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid #eee;
  border-radius: 4px;
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid #f0f0f0;
}

.file-item:last-child {
  border-bottom: none;
}

.file-name {
  font-size: 14px;
  color: #333;
}

.index-progress {
  margin-top: 20px;
}

.index-progress h4 {
  margin: 0 0 10px 0;
  color: #333;
}

.progress-bar {
  background: #f0f0f0;
  border-radius: 4px;
  height: 8px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  background: #007bff;
  height: 100%;
  transition: width 0.3s ease;
}

.progress-text {
  font-size: 14px;
  color: #666;
  margin: 0;
}

/* 按钮样式 */
.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  display: inline-flex;
  align-items: center;
  gap: 5px;
  transition: background-color 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-primary {
  background: #007bff;
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: #0056b3;
}

.btn-secondary {
  background: #6c757d;
  color: white;
}

.btn-secondary:hover:not(:disabled) {
  background: #545b62;
}

.btn-danger {
  background: #dc3545;
  color: white;
}

.btn-danger:hover:not(:disabled) {
  background: #c82333;
}

.btn-warning {
  background: #ffc107;
  color: #212529;
}

.btn-warning:hover:not(:disabled) {
  background: #e0a800;
}

.btn-block {
  width: 100%;
  justify-content: center;
}

.btn-sm {
  padding: 4px 8px;
  font-size: 12px;
}

.icon {
  font-size: 16px;
}

/* 加载指示器 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255,255,255,0.8);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1001;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 4px solid #f3f3f3;
  border-top: 4px solid #007bff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 10px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 
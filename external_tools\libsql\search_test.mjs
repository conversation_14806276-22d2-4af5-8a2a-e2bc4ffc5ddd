import {createClient} from '@libsql/client';
import OpenAI from "openai";

const client = createClient({
    url: 'file:local.db',
});

const openai = new OpenAI({
    baseURL: 'https://api.siliconflow.cn/v1',
    apiKey: 'sk-tbcbjgzqnnyzdqrxmnparmvibzwteydpknmhiyvmuvlpmram',
});

// 获取嵌入向量函数（需要单独定义）
async function getEmbedding(prompt) {
    const response = await openai.embeddings.create({
        model: 'BAAI/bge-m3',
        input: prompt,
        encoding_format: 'float'
    });

    // 将返回的数组转换为 Float32Array
    return new Float32Array(response.data[0].embedding);
}

// 使用重排序模型并过滤相似度低的片段
async function rerank(similarChunks, queryEmbedding) {
    const documents = similarChunks.map(chunk => (chunk.fileContent));
    const body = {
        query: queryEmbedding,
        documents: documents,
        model: "BAAI/bge-reranker-v2-m3"
    }
    const options = {
        method: 'POST',
        headers: {
            Authorization: 'Bearer sk-tbcbjgzqnnyzdqrxmnparmvibzwteydpknmhiyvmuvlpmram',
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(body),
    };
    try {
        const response = await fetch('https://api.siliconflow.cn/v1/rerank', options);
        const data = await response.json();
        if (data.results) {
            similarChunks = getTopChunks(data, similarChunks);
        }
    } catch (err) {
        console.error('Rerank error:', err);
    }
    return similarChunks;
}

async function getTopChunks(response, chunks, topN = 10, minScore = 0.01) {
    // 提取并排序结果（按相关性分数降序）
    const sortedResults = response.results
        .slice() // 创建副本避免修改原数组
        .sort((a, b) => b.relevance_score - a.relevance_score);
    // 计算统计指标
    const scores = sortedResults.map(res => res.relevance_score);
    const mean = scores.reduce((sum, val) => sum + val, 0) / scores.length;
    const finalThreshold = Math.max(minScore, mean);
    // 筛选满足条件的chunks
    const indexList = sortedResults
        .filter(res => res.relevance_score >= finalThreshold)
        .slice(0, topN) // 限制最大返回数量
        .map(res => (res.index));
    return chunks.filter((chunk, index) => indexList.includes(index));
}

async function chat(message, knowledge) {
    return openai.chat.completions.create({
        model: 'Qwen/Qwen2.5-72B-Instruct',
        messages: [
            {
                role: 'system',
                content: '你是一个智能助手，请总结知识库的内容来回答问题，请列举知识库中的数据回答。当知识库为空或者所有知识库内容都与问题无关时，你的回答必须包括“知识库中未找到您要的答案！”这句话。回答需要考虑聊天历史。以下是知识库：' + knowledge + '以上是知识库。',
            },
            {
                role: 'user',
                content: message,
            },
        ],
        stream: false,
        enable_thinking: false,
        // thinking_budget: 1,
    });
}


// 搜索函数
async function findSimilarChunks(description, fileType, limit = 3) {
    const queryEmbedding = await getEmbedding(description);
    // 查询指定fileType的file
    const files = await client.execute({
        sql: `
            SELECT id
            FROM user_file
            WHERE file_type = ?
        `,
        args: [fileType]
    })
    if (!files.rows.length) {
        return [];
    }
    const fileIds = files.rows.map(row => row.id);
    const results = await client.execute({
        sql: `WITH vector_scores AS (SELECT rowid                                           AS id,
                                            file_id,
                                            file_content,
                                            embedding,
                                            1 - vector_distance_cos(embedding, vector32(?)) AS similarity
                                     FROM user_file_embd
                                     WHERE file_id IN (${Array(fileIds.length).fill('?').join(',')})
                                     ORDER BY similarity DESC
                  LIMIT ?
                  )
        SELECT v.id,
               v.file_id          AS fileId,
               v.file_content     AS fileContent,
               v.similarity,
               f.source_file_path AS filePath
        FROM vector_scores v
                 LEFT JOIN user_file f ON v.file_id = f.id;
        `,
        args: [JSON.stringify(Array.from(queryEmbedding)), ...fileIds, limit],
    });
    return results.rows;
}

// 使用示例
(async () => {
    try {
        // 计算耗时多少秒
        const startTime = Date.now();
        const message = "我们有几位模型调优专家";
        let similarChunks = await findSimilarChunks(
            message,
            1,
            15
        );
        console.log(similarChunks.length)
        similarChunks = await rerank(similarChunks, message);
        console.log(similarChunks.length)
        const knowledge = similarChunks.map(chunk => chunk.fileContent).join('\n');
        console.log(knowledge);
        // const answer = await chat(message, knowledge);
        console.log(`耗时: ${(Date.now() - startTime) / 1000}秒`);
        // console.log(JSON.stringify(answer));
    } catch (error) {
        console.error('搜索失败:', error);
    }
})();
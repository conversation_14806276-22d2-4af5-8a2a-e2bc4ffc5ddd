<template>
  <div class="main-layout" :class="{ 'dark-theme': themeStore.isDarkMode }">
    <!-- Loading 遮罩层 -->
    <div class="loading-overlay" v-if="isLoading">
      <div class="loading-container">
        <img :src="logoUrl" class="loading-logo" alt="Logo" />
        <div class="loading-spinner"></div>
        <div class="loading-text">{{ loadingMessage }}</div>
        <div class="loading-status">{{ loadingStatus }}</div>
        <div class="loading-progress">
          <div class="loading-progress-bar"></div>
        </div>
      </div>
    </div>
    
    <!-- 侧边栏 -->
    <aside class="sidebar">
      <div class="sidebar-header">
        <img :src="logoUrl" alt="Logo" class="sidebar-logo" />
      </div>

      <nav class="sidebar-nav">
        <button v-for="item in mainMenuItems" :key="item.id" :class="['nav-item', { active: activeView === item.id }]"
          @click="setActiveView(item.id)" :title="item.label">
          <img :src="getIconUrl(item, activeView === item.id)" :alt="item.label" class="nav-icon" />
        </button>
      </nav>

      <div class="sidebar-footer" v-if="true">
        <!-- 主题切换按钮 -->
        <button @click="themeStore.toggleTheme" class="theme-toggle"
          :title="themeStore.isDarkMode ? '切换到明亮模式' : '切换到黑暗模式'">
          <i :class="themeStore.isDarkMode ? 'icon-sun' : 'icon-moon'"></i>
        </button>

        <!-- 用户信息 -->
        <div class="user-info" @click="toggleUserMenu">
          <div class="user-avatar">
            {{ authStore.user?.username?.charAt(0)?.toUpperCase() || 'U' }}
          </div>
          <div class="user-name">{{ authStore.user?.username || 'User' }}</div>
        </div>

        <!-- 用户菜单 -->
        <div v-if="showUserMenu" class="user-menu">
          <div class="user-menu-item" @click="handleLogout">
            <i class="icon-logout"></i>
            <span>退出登录</span>
          </div>
        </div>

        <!-- 设置按钮 -->
        <button :class="[ 'settings-item']"
          @click="setActiveView('settings')" title="设置">
          <img :src="settingsIconUrl" alt="设置" class="nav-icon" />
        </button>
      </div>
    </aside>

    <!-- 主内容区域 -->
    <main class="main-content">
      <div class="content-view">
        <!-- 知识库视图 -->
        <KnowledgeView v-if="activeView === 'knowledge'" />

        <!-- 待办事项视图 -->
        <TodoView v-if="activeView === 'todo'" />

        <!-- 设置视图 -->
        <SettingsView v-if="activeView === 'settings'" />
      </div>
    </main>
  </div>
</template>

<script>
import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { useThemeStore } from '../stores/theme.js'
import KnowledgeView from '../views/KnowledgeView.vue'
import TodoView from '../views/TodoView.vue'
import SettingsView from '../views/SettingsView.vue'
import logoUrl from '/assets/logo.png?url'

// 导入图标资源 - 确保打包后正确
import iconZskImg from '/assets/menu/icon-1-zsk.png'
import iconZskOnImg from '/assets/menu/icon-1-zsk-on.png'
import iconDbImg from '/assets/menu/icon-1-db.png'
import iconDbOnImg from '/assets/menu/icon-1-db-on.png'
import iconSettingsImg from '/assets/menu/icon-settions.png'

export default {
  name: 'MainLayout',
  components: {
    KnowledgeView,
    TodoView,
    SettingsView
  },
  props: {
    initialPage: {
      type: String,
      default: null
    }
  },
  setup(props) {
    const authStore = useAuthStore()
    const themeStore = useThemeStore()
    const activeView = ref('knowledge')
    const isLoading = ref(false)
    const loadingMessage = ref('正在初始化服务...')
    const loadingStatus = ref('准备中')
    const loadingClosed = ref(false) // 🔄 【新增】防止重复关闭loading的标志位
    const welcomeVoicePlayed = ref(false)
    const initCompleted = ref(false)
    const showUserMenu = ref(false)

    const mainMenuItems = [
      {
        id: 'knowledge',
        label: '知识库',
        icon: iconZskImg,
        activeIcon: iconZskOnImg
      },
      {
        id: 'todo',
        label: '待办',
        icon: iconDbImg,
        activeIcon: iconDbOnImg
      }
    ]

    const settingsIconUrl = iconSettingsImg

    const getIconUrl = (item, isActive) => {
      return isActive ? item.activeIcon : item.icon
    }

    const setActiveView = (viewId) => {
      activeView.value = viewId
    }

    const toggleUserMenu = () => {
      showUserMenu.value = !showUserMenu.value
    }

    const handleLogout = async () => {
      try {
        console.log('🚪 开始退出登录')
        showUserMenu.value = false
        await authStore.logout()
        console.log('🚪 退出登录成功')
      } catch (error) {
        console.error('🚪 退出登录失败:', error)
      }
    }

    onMounted(() => {
      // 应用主题
      themeStore.applyTheme()
      
      // 登录后显示全屏加载状态
      if (authStore.isLoggedIn) {
        isLoading.value = true
        loadingMessage.value = '正在初始化服务...'
        loadingStatus.value = '准备中'
        loadingClosed.value = false // 🔄 【新增】重置loading关闭标志
        initCompleted.value = false // 🔄 【新增】重置初始化完成标志
        welcomeVoicePlayed.value = false // 🔄 【新增】重置欢迎语音播放标志
        console.log('🔄 主窗口：用户已登录，显示服务初始化加载状态')
      } else {
        // 用户未登录，不显示加载状态
        isLoading.value = false
        console.log('🔄 主窗口：用户未登录，不显示加载状态')
      }
      
      // 监听主进程的状态消息
      if (window.electronAPI && window.electronAPI.onStatusMessage) {
        window.electronAPI.onStatusMessage((message) => {
          loadingStatus.value = message
          console.log('主窗口状态更新:', message)
          
          // 只有在用户已登录且正在加载时才处理状态更新
          if (authStore.isLoggedIn && isLoading.value && !loadingClosed.value) {
            // 如果收到应用初始化完成的消息，则标记为已初始化但不立即关闭loading
            if (message === '应用初始化完成' || message.includes('初始化完成')) {
              console.log('🔄 主窗口：收到应用初始化完成消息，标记为已初始化')
              initCompleted.value = true
              // 🔄 【修改】不立即关闭loading，等待智能语音启动完成
            }
            // 🔄 【修改】改为监听智能语音启动完成消息来关闭loading
            else if (message === '智能语音启动完成') {
              console.log('🔄 主窗口：收到智能语音启动完成消息，准备关闭loading')
              loadingClosed.value = true // 🔄 【新增】标记loading已关闭
              setTimeout(() => {
                isLoading.value = false
                console.log('✅ 主窗口：关闭loading遮罩')
                
                // 🔄 【新增】加载完成后自动隐藏主窗口
                setTimeout(() => {
                  console.log('🔄 主窗口：服务初始化完成，自动隐藏主窗口')
                  if (window.electronAPI && window.electronAPI.hideMainWindow) {
                    window.electronAPI.hideMainWindow()
                  }
                }, 1000) // 等待1秒让用户看到加载完成的效果
              }, 500)
            }
          }
        })
      }
      
      // 监听登录状态变化
      authStore.$subscribe((mutation, state) => {
        if (state.isLoggedIn) {
          // 用户登录后立即显示加载状态
          isLoading.value = true
          loadingMessage.value = '正在初始化服务...'
          loadingStatus.value = '准备中'
          console.log('🔄 主窗口：用户登录，显示服务初始化加载状态')
        } else {
          // 用户退出登录后隐藏加载状态
          isLoading.value = false
          console.log('🔄 主窗口：用户退出登录，隐藏加载状态')
        }
      })
      
      // 如果有初始页面，导航到该页面
      if (props.initialPage) {
        console.log('Navigating to initial page:', props.initialPage)
        setActiveView(props.initialPage)
      }
      
      // 监听点击外部关闭用户菜单
      const handleClickOutside = (event) => {
        const userInfo = event.target.closest('.user-info')
        const userMenu = event.target.closest('.user-menu')
        
        if (!userInfo && !userMenu && showUserMenu.value) {
          showUserMenu.value = false
        }
      }
      
      document.addEventListener('click', handleClickOutside)
      
      // 组件卸载时移除事件监听器
      onUnmounted(() => {
        document.removeEventListener('click', handleClickOutside)
      })
    })

    return {
      authStore,
      themeStore,
      activeView,
      mainMenuItems,
      settingsIconUrl,
      showUserMenu,
      getIconUrl,
      setActiveView,
      toggleUserMenu,
      handleLogout,
      logoUrl,
      isLoading,
      loadingMessage,
      loadingStatus
    }
  }
}
</script>

<style lang="scss" scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background: var(--bg-primary);
}

/* Loading样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: linear-gradient(150deg, #4768DB 0%, #B06AB3 100%);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 9999;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 40px;
  background-color: var(--bg-secondary);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  min-width: 300px;
  animation: fadeIn 0.5s ease;
}

.loading-spinner {
  width: 60px;
  height: 60px;
  border: 6px solid rgba(100, 100, 255, 0.2);
  border-top-color: var(--primary-color);
  border-radius: 50%;
  animation: spin 1.2s linear infinite;
  margin-bottom: 24px;
}

.loading-logo {
  width: 80px;
  height: 80px;
  margin-bottom: 20px;
  border-radius: 50%;
  object-fit: cover;
  animation: pulse 2s infinite;
}

.loading-text {
  font-size: 22px;
  font-weight: bold;
  color: var(--text-secondary);
  margin-bottom: 16px;
}

.loading-status {
  font-size: 16px;
  color: var(--text-secondary);
  text-align: center;
  margin-bottom: 8px;
  min-height: 20px;
}

.loading-progress {
  width: 100%;
  height: 4px;
  background-color: rgba(100, 100, 255, 0.2);
  border-radius: 4px;
  margin-top: 16px;
  overflow: hidden;
  position: relative;
}

.loading-progress-bar {
  height: 100%;
  background-color: var(--primary-color);
  position: absolute;
  left: 0;
  top: 0;
  animation: progressAnimation 2s ease infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(100, 100, 255, 0.7);
  }
  70% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(100, 100, 255, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(100, 100, 255, 0);
  }
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes progressAnimation {
  0% {
    width: 0%;
  }
  50% {
    width: 70%;
  }
  100% {
    width: 100%;
  }
}

.sidebar {
  width: 60px;
  // border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
}

.sidebar-header {
  padding: 12px 10px;
  display: flex;
  align-items: center;
  gap: 12px;
}

.sidebar-logo {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  object-fit: cover;
}

.app-title {
  font-size: 20px;
  font-weight: 600;
  color: var(--text-primary);
}

.sidebar-nav {
  flex: 1;
  padding: 20px 0;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 6px;
  border: none;
  background: none;
  cursor: pointer;
  transition: all 0.2s ease;
  width: 60%;
  border-radius: 8px;
  margin: 0 auto;
  margin-bottom: 20px;
  &:hover {
    background-color: var(--sidebar-hover);
  }

  &.active {
    background-color: var(--sidebar-hover);
  }
}

.nav-icon {
  width: 24px;
  height: 24px;
  object-fit: contain;
}

.sidebar-footer {
  padding: 10px;
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.theme-toggle {
  width: 40px;
  height: 40px;
  border: 1px solid var(--border-color);
  border-radius: 8px;
  background-color: var(--bg-secondary);
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--sidebar-hover);
    color: var(--text-primary);
  }

  i {
    font-size: 18px;
  }
}

.user-info {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10px;
  border-radius: 10px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
  margin-bottom: 15px;
  
  &:hover {
    background: rgba(255, 255, 255, 0.1);
  }
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  margin-bottom: 5px;
}

.user-name {
  font-size: 12px;
  color: var(--text-primary);
  max-width: 60px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.user-menu {
  position: absolute;
  bottom: 80px;
  left: 80px;
  background: var(--bg-primary);
  border-radius: 8px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  min-width: 120px;
  z-index: 1000;
  
  .user-menu-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    cursor: pointer;
    transition: all 0.3s ease;
    border-radius: 8px;
    
    &:hover {
      background: var(--primary-color);
      color: white;
    }
    
    .icon-logout {
      width: 16px;
      height: 16px;
      margin-right: 8px;
      
      &::before {
        content: '🚪';
        font-size: 16px;
      }
    }
    
    span {
      font-size: 14px;
    }
  }
}

.settings-item {
  margin-top: 8px;
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.logout-btn {
  width: 36px;
  height: 36px;
  border: none;
  border-radius: 6px;
  background-color: transparent;
  color: var(--text-secondary);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;

  &:hover {
    background-color: var(--sidebar-hover);
    color: var(--text-primary);
  }

  i {
    font-size: 16px;
  }
}

.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.content-view {
  flex: 1;
  overflow: auto;
}

// 图标字体样式
.icon-book::before {
  content: '📚';
}

.icon-todo::before {
  content: '📋';
}

.icon-settings::before {
  content: '⚙️';
}

.icon-sun::before {
  content: '☀️';
}

.icon-moon::before {
  content: '🌙';
}

.icon-logout::before {
  content: '🚪';
}
</style>
# PowerShell脚本：自动化构建流程
# 用法：在项目根目录运行 .\build-dist.ps1

Write-Host ""
Write-Host "===================================" -ForegroundColor Cyan
Write-Host "    犇犇数字员工助手 - 构建脚本" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan
Write-Host ""

# 1. 删除dist文件夹
Write-Host "[1/3] 删除dist文件夹..." -ForegroundColor Yellow
if (Test-Path "dist") {
    try {
        Remove-Item -Recurse -Force "dist"
        Write-Host "✅ dist文件夹已删除" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  删除dist文件夹时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ℹ️  dist文件夹不存在，跳过删除" -ForegroundColor Gray
}

Write-Host ""

# 2. 杀掉electron进程
Write-Host "[2/3] 杀掉electron进程..." -ForegroundColor Yellow
$electronProcesses = Get-Process -Name "electron", "犇犇数字员工助手", "nezha-ai-desktop" -ErrorAction SilentlyContinue

if ($electronProcesses) {
    Write-Host "🔍 发现electron进程，正在终止..." -ForegroundColor Cyan
    try {
        $electronProcesses | Stop-Process -Force
        Write-Host "✅ electron进程已终止" -ForegroundColor Green
    } catch {
        Write-Host "⚠️  终止electron进程时出错: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "ℹ️  没有发现electron进程" -ForegroundColor Gray
}

Write-Host ""

# 3. 运行构建命令
Write-Host "[3/3] 开始构建应用..." -ForegroundColor Yellow
Write-Host "🚀 运行 npm run electron:dist..." -ForegroundColor Cyan
Write-Host ""

try {
    # 使用Start-Process来运行npm命令并等待完成
    $process = Start-Process -FilePath "npm" -ArgumentList "run", "electron:dist" -NoNewWindow -Wait -PassThru
    
    Write-Host ""
    if ($process.ExitCode -eq 0) {
        Write-Host "✅ 构建完成！" -ForegroundColor Green
        Write-Host "📦 生成的文件位于 dist 文件夹" -ForegroundColor Green
        Write-Host ""
        
        # 询问是否打开dist文件夹
        $openFolder = Read-Host "是否打开dist文件夹？(y/n)"
        if ($openFolder -eq "y" -or $openFolder -eq "Y") {
            if (Test-Path "dist") {
                Start-Process -FilePath "explorer" -ArgumentList "dist"
            } else {
                Write-Host "❌ dist文件夹不存在" -ForegroundColor Red
            }
        }
    } else {
        Write-Host "❌ 构建失败！退出代码: $($process.ExitCode)" -ForegroundColor Red
        Write-Host "请检查错误信息并修复后重试" -ForegroundColor Red
    }
} catch {
    Write-Host "❌ 运行构建命令时出错: $($_.Exception.Message)" -ForegroundColor Red
}

Write-Host ""
Write-Host "===================================" -ForegroundColor Cyan
Write-Host "       构建脚本执行完成" -ForegroundColor Cyan
Write-Host "===================================" -ForegroundColor Cyan

# 暂停以查看结果
Write-Host ""
Write-Host "按任意键继续..." -ForegroundColor Gray
Read-Host 
// Email MCP Module
const fs = require('fs')
const { join } = require('path')
const Store = require('electron-store')

let initialized = false
let mcpClient = null
let mcpTransport = null
let availableTools = []

// Check if development environment
const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged

async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('Initializing Email MCP server...')

    // Check email configuration
    const store = new Store()
    const emailConfig = store.get('emailConfig')

    console.log('Email config check:', {
      emailConfig: emailConfig,
      hasUser: emailConfig && emailConfig.user,
      hasPass: emailConfig && emailConfig.pass,
      shouldUseSimulation: !emailConfig || !emailConfig.user || !emailConfig.pass
    })

    if (!emailConfig || !emailConfig.user || !emailConfig.pass) {
      console.log('⚠️ 未找到邮件配置，跳过邮件MCP服务初始化')
      // 不注册任何客户端，让EmailService知道没有MCP服务可用
      return null
    }

    // Read MCP config file
    const mcpConfigPath = isDev
      ? join(process.cwd(), 'mcp-config.json')
      : join(process.resourcesPath, 'mcp-config.json')
    console.log('Reading Email MCP config file:', mcpConfigPath)

    if (!fs.existsSync(mcpConfigPath)) {
      console.error('MCP config file does not exist:', mcpConfigPath)
      throw new Error('MCP config file does not exist')
    }

    const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'))
    console.log('Email MCP config content:', JSON.stringify(mcpConfig, null, 2))

    // Get email-server-mcp configuration
    const emailServerConfig = mcpConfig.mcpServers['email-server-mcp']
    if (!emailServerConfig) {
      console.error('email-server-mcp configuration not found')
      throw new Error('email-server-mcp configuration not found')
    }

    console.log('email-server-mcp config:', emailServerConfig)
    console.log('Command:', emailServerConfig.command)
    console.log('Args:', emailServerConfig.args)

    // 加载MCP SDK
    console.log('Loading MCP SDK...')
    try {
      // 使用动态导入方式加载MCP SDK
      const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
      const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
      const { StdioClientTransport } = stdioModule
      const { Client } = clientModule

      console.log('MCP SDK loaded successfully')

      // Resolve command and args paths
      const resolvedCommand = isDev
        ? emailServerConfig.command
        : join(process.resourcesPath, emailServerConfig.command)

      const resolvedArgs = emailServerConfig.args.map(arg =>
        isDev ? arg : join(process.resourcesPath, arg)
      )

      console.log('Starting Email MCP server:')
      console.log('  - Original command:', emailServerConfig.command)
      console.log('  - Resolved command:', resolvedCommand)
      console.log('  - Original args:', emailServerConfig.args)
      console.log('  - Resolved args:', resolvedArgs)
      console.log('  - Environment:', isDev ? 'Development' : 'Production')

      // Validate Python executable exists
      if (!fs.existsSync(resolvedCommand)) {
        console.error('Python executable does not exist:', resolvedCommand)
        throw new Error(`Python executable does not exist: ${resolvedCommand}`)
      }

      // Validate MCP server script exists
      if (resolvedArgs.length > 0 && !fs.existsSync(resolvedArgs[0])) {
        console.error('Email MCP server script does not exist:', resolvedArgs[0])
        throw new Error(`Email MCP server script does not exist: ${resolvedArgs[0]}`)
      }

      // Handle environment variables
      const resolvedEnv = {}
      if (emailServerConfig.env) {
        for (const [key, value] of Object.entries(emailServerConfig.env)) {
          if (key === 'PYTHONPATH' && typeof value === 'string') {
            resolvedEnv[key] = isDev ? value : join(process.resourcesPath, value)
            console.log(`Environment variable ${key}: "${value}" → "${resolvedEnv[key]}"`)
          } else {
            resolvedEnv[key] = value
          }
        }
      }

      // Set email configuration environment variables
      console.log('Setting email configuration environment variables')
      resolvedEnv.EMAIL_USER = emailConfig.user
      resolvedEnv.EMAIL_PASS = emailConfig.pass
      resolvedEnv.EMAIL_SMTP_SERVER = emailConfig.smtpServer || 'smtp.qq.com'
      resolvedEnv.EMAIL_SMTP_PORT = String(emailConfig.smtpPort || 465)
      resolvedEnv.EMAIL_SMTP_SSL = String(emailConfig.smtpSsl !== false)
      resolvedEnv.EMAIL_IMAP_SERVER = emailConfig.imapServer || 'imap.qq.com'
      resolvedEnv.EMAIL_IMAP_PORT = String(emailConfig.imapPort || 993)
      resolvedEnv.EMAIL_IMAP_SSL = String(emailConfig.imapSsl !== false)
      console.log('Email configuration environment variables set')

      // Skip Email Python dependency check to avoid popup
      console.log('Skipping Email Python dependency check (avoid popup)')
      console.log('Assuming Email Python dependencies are installed, continuing initialization')

      // Create Email MCP client connection
      console.log('Creating Email MCP transport with:')
      console.log('Command:', resolvedCommand)
      console.log('Args:', resolvedArgs)
      console.log('Environment variables:', Object.keys(resolvedEnv))

      mcpTransport = new StdioClientTransport({
        command: resolvedCommand,
        args: resolvedArgs,
        env: {
          ...process.env,
          ...resolvedEnv
        }
      })

      console.log('Email MCP transport created successfully')

      mcpClient = new Client({
        name: 'nezha-email-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // Connect to Email MCP server
      console.log('Connecting to Email MCP server...')
      await mcpClient.connect(mcpTransport)
      console.log('Connected to email-server MCP server')

      // 等待连接稳定，然后发送 initialized 通知
      console.log('📧 等待连接稳定...')
      await new Promise(resolve => setTimeout(resolve, 1000))

      // 手动发送 initialized 通知（修复MCP协议兼容性问题）
      console.log('📧 发送 initialized 通知...')
      console.log('📧 检查MCP客户端和传输层对象结构...')
      console.log('📧 mcpClient keys:', Object.keys(mcpClient))
      console.log('📧 mcpTransport keys:', Object.keys(mcpTransport))

      try {
        // 检查MCP客户端是否有sendNotification方法
        if (typeof mcpClient.sendNotification === 'function') {
          await mcpClient.sendNotification('notifications/initialized', {})
          console.log('📧 initialized 通知发送成功')
        } else if (typeof mcpClient.notification === 'function') {
          // 尝试使用notification方法
          await mcpClient.notification('notifications/initialized', {})
          console.log('📧 通过notification方法发送 initialized 通知成功')
        } else {
          console.log('📧 MCP客户端不支持sendNotification，检查传输层...')

          // 检查传输层的结构
          if (mcpTransport) {
            console.log('📧 传输层存在，检查可用方法...')
            if (typeof mcpTransport.send === 'function') {
              const initMessage = {
                jsonrpc: '2.0',
                method: 'notifications/initialized'
              }
              await mcpTransport.send(initMessage)
              console.log('📧 通过transport.send发送 initialized 通知成功')
            } else if (typeof mcpTransport.write === 'function') {
              const initMessage = {
                jsonrpc: '2.0',
                method: 'notifications/initialized'
              }
              await mcpTransport.write(JSON.stringify(initMessage) + '\n')
              console.log('📧 通过transport.write发送 initialized 通知成功')
            } else {
              console.warn('📧 传输层不支持send或write方法')
              console.log('📧 跳过 initialized 通知，依赖MCP SDK自动处理')
            }
          } else {
            console.warn('📧 传输层不可用')
          }
        }
      } catch (notificationError) {
        console.warn('📧 发送 initialized 通知失败:', notificationError.message)
        console.log('📧 继续初始化，可能MCP SDK会自动处理协议')
      }

      // 连接后设置Python进程输出监听
      console.log('📧 连接后设置Python进程输出监听...')

      if (mcpTransport.process) {
        console.log('📧 找到Python进程，设置输出监听')

        if (mcpTransport.process.stderr) {
          mcpTransport.process.stderr.setEncoding('utf8')
          mcpTransport.process.stderr.on('data', (data) => {
            console.error('📧 [EMAIL_MCP_STDERR]:', data.toString().trim())
          })
          console.log('📧 stderr监听已设置')
        } else {
          console.warn('📧 stderr不可用')
        }

        if (mcpTransport.process.stdout) {
          mcpTransport.process.stdout.setEncoding('utf8')
          mcpTransport.process.stdout.on('data', (data) => {
            console.log('📧 [EMAIL_MCP_STDOUT]:', data.toString().trim())
          })
          console.log('📧 stdout监听已设置')
        } else {
          console.warn('📧 stdout不可用')
        }

        mcpTransport.process.on('error', (error) => {
          console.error('📧 [EMAIL_MCP_PROCESS_ERROR]:', error)
        })

        mcpTransport.process.on('exit', (code, signal) => {
          console.log(`📧 [EMAIL_MCP_PROCESS_EXIT]: code=${code}, signal=${signal}`)
        })
      } else {
        console.warn('📧 连接后Python进程仍不可用，无法设置输出监听')
      }

      // Get available tools list
      console.log('Getting Email tools list...')
      const tools = await mcpClient.listTools()

      console.log('Email tools list obtained successfully:', tools)
      console.log('Available Email tools:', tools.tools?.map(t => t.name) || [])

      availableTools = tools.tools || []
      initialized = true

      console.log('Real Email MCP client initialized')
      console.log('Available tools count:', availableTools.length)
      console.log('Config source: mcp-config.json')
      console.log('MCP transport protocol: STDIO')

      // 注册到管理器
      mcpManager.clients.set('email-server', {
        isConnected: true,
        mcpClient: mcpClient,
        mcpTransport: mcpTransport,
        availableTools: availableTools,
        isRealMCP: true,
        configSource: 'mcp-config.json'
      })

      console.log('✅ 邮件MCP服务器初始化完成并注册到管理器')

    } catch (importError) {
      console.error('MCP SDK import failed:', importError)
      throw importError
    }

  } catch (error) {
    console.error('Email MCP initialization failed:', error)
    // Fallback to simulation mode
    console.log('Falling back to simulation mode...')

    availableTools = [
      { name: 'send_email', description: 'Send email (simulation)' },
      { name: 'list_email', description: 'List emails (simulation)' },
      { name: 'mark_email_as_read', description: 'Mark email as read (simulation)' }
    ]
    initialized = true

    // 注册到管理器（模拟模式）
    mcpManager.clients.set('email-server', {
      isConnected: true,
      mcpClient: null,
      mcpTransport: null,
      availableTools: availableTools,
      isRealMCP: false,
      configSource: 'Error fallback mode'
    })

    console.log('✅ 邮件MCP服务器初始化完成（模拟模式）并注册到管理器')
  }
}

function getConnectionStatus() {
  return {
    connected: initialized,
    service: 'email',
    isRealMCP: mcpClient !== null,
    availableTools: availableTools.length
  }
}

function getAvailableTools() {
  return availableTools
}

async function callTool(toolName, args) {
  if (!initialized) {
    throw new Error('Email service not initialized')
  }

  if (!mcpClient) {
    // Simulation mode
    console.log(`Simulating email tool call: ${toolName}`, args)
    return {
      success: true,
      message: `Simulated execution of ${toolName}`,
      data: { tool: toolName, args: args }
    }
  }

  try {
    console.log(`Calling real email tool: ${toolName}`, args)

    // 添加更强制的超时处理，防止无限等待
    let timeoutId
    const timeoutPromise = new Promise((_, reject) => {
      timeoutId = setTimeout(() => {
        console.error(`📧 邮件工具调用超时 (15秒): ${toolName}`)
        reject(new Error(`Email tool call timeout after 15 seconds: ${toolName}`))
      }, 15000) // 15秒超时，更短的超时时间
    })

    const callPromise = mcpClient.callTool({
      name: toolName,
      arguments: args
    }).then(result => {
      clearTimeout(timeoutId) // 清除超时定时器
      return result
    }).catch(error => {
      clearTimeout(timeoutId) // 清除超时定时器
      throw error
    })

    // 使用Promise.race来实现超时
    const result = await Promise.race([callPromise, timeoutPromise])

    console.log(`Email tool call completed: ${toolName}`, result)

    // 转换MCP结果为统一格式
    if (result.isError) {
      return {
        success: false,
        error: result.content?.[0]?.text || 'Unknown error'
      }
    }

    // 处理成功的结果
    if (toolName === 'list_email') {
      // 解析邮件列表结果
      try {
        const emails = result.structuredContent?.result || []
        return {
          success: true,
          emails: emails
        }
      } catch (parseError) {
        console.error('📧 解析邮件列表结果失败:', parseError)
        return {
          success: false,
          error: '解析邮件列表结果失败'
        }
      }
    } else {
      // 其他工具的通用处理
      return {
        success: true,
        data: result.content?.[0]?.text || result.structuredContent || result
      }
    }
  } catch (error) {
    console.error(`Email tool call failed: ${toolName}`, error)

    // 如果是超时错误，强制重新初始化连接
    if (error.message.includes('timeout')) {
      console.warn('📧 邮件工具调用超时，强制重新初始化连接')
      // 清理现有连接
      if (mcpTransport && mcpTransport.process) {
        try {
          mcpTransport.process.kill('SIGTERM')
          console.log('📧 已终止超时的Python进程')
        } catch (killError) {
          console.error('📧 终止Python进程失败:', killError)
        }
      }

      // 标记为未初始化，下次调用时会重新初始化
      initialized = false
      mcpClient = null
      mcpTransport = null
    }

    // 返回统一的错误格式而不是抛出异常
    return {
      success: false,
      error: error.message || 'Unknown error'
    }
  }
}

async function cleanup() {
  console.log('Cleaning up Email MCP resources...')
  
  if (mcpClient) {
    try {
      await mcpClient.close()
      console.log('Email MCP client closed')
    } catch (error) {
      console.error('Failed to close Email MCP client:', error)
    }
  }

  if (mcpTransport) {
    try {
      await mcpTransport.close()
      console.log('Email MCP transport closed')
    } catch (error) {
      console.error('Failed to close Email MCP transport:', error)
    }
  }

  mcpClient = null
  mcpTransport = null
  availableTools = []
  initialized = false
}

// 重置初始化状态（用于重启服务）
function resetInitialization() {
  console.log('📧 重置邮件MCP服务初始化状态...')
  initialized = false
  mcpClient = null
  mcpTransport = null
  availableTools = []
  console.log('✅ 邮件MCP服务初始化状态已重置')
}

module.exports = {
  initialize,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup,
  resetInitialization
}
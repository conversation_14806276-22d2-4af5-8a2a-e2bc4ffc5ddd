const { createMainApiClient } = require('../apiClient')
const { getKnowledgeConfig } = require('./config')

// 获取用户token的函数（需要从main.js中获取）
let getCurrentUserToken = null

// 设置获取用户token的函数
function setGetCurrentUserTokenFunction(tokenFunction) {
  getCurrentUserToken = tokenFunction
}

/**
 * 获取知识库嵌入向量
 * @param {string} text - 要向量化的文本
 * @returns {Promise<Float32Array>} 向量数组
 */
async function getKnowledgeEmbedding(text) {
  try {
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    
    // 检查文本长度，如果过长则截断
    let processedText = text
    if (text.length > KNOWLEDGE_CONFIG.embedding.maxTokens) {
      console.log(`⚠️ 文本过长 (${text.length} 字符)，截断到 ${KNOWLEDGE_CONFIG.embedding.maxTokens} 字符`)
      processedText = text.substring(0, KNOWLEDGE_CONFIG.embedding.maxTokens)
    }

    console.log(`🔗 正在通过代理向量化文本，长度: ${processedText.length} 字符`)

    // 获取用户token并创建API客户端
    const userToken = await getCurrentUserToken()
    console.log('🔑 向量化请求使用的token:', userToken ? userToken.substring(0, 20) + '...' : '无token')
    const client = createMainApiClient(null, userToken)
    
    const requestData = {
      model: KNOWLEDGE_CONFIG.embedding.model,
      input: processedText,
      encoding_format: KNOWLEDGE_CONFIG.embedding.encoding_format
    }

    const response = await client.post('/embeddings', requestData)
    
    // 打印完整的响应信息用于调试
    console.log('🔍 完整响应对象:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    })
    
    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data
    console.log('🔍 响应数据结构:', {
      hasResponseData: !!responseData,
      responseDataType: typeof responseData,
      isArray: Array.isArray(responseData),
      hasData: responseData && responseData.data,
      dataLength: responseData && responseData.data ? responseData.data.length : 0
    })
    
    // 根据实际响应格式解析数据
    let embeddingData
    if (responseData && responseData.data && Array.isArray(responseData.data)) {
      // 新格式：response.data.data.data[0].embedding
      embeddingData = responseData.data[0]
    } else if (responseData && Array.isArray(responseData)) {
      // 旧格式：response.data.data[0].embedding
      embeddingData = responseData[0]
    } else {
      throw new Error('代理返回的向量数据格式不正确')
    }
    
    if (!embeddingData || !embeddingData.embedding) {
      throw new Error('代理返回的向量数据格式不正确')
    }

    const embedding = new Float32Array(embeddingData.embedding)
    console.log(`✅ 代理向量化成功，维度: ${embedding.length}`)
    return embedding
  } catch (error) {
    console.error('❌ 代理向量化失败:', error)
    console.error('❌ 输入文本长度:', text.length)
    console.error('❌ 错误详情:', error.message)
    throw error
  }
}

/**
 * 使用重排序模型并过滤相似度低的片段（使用代理）
 * @param {Array} similarChunks - 相似片段数组
 * @param {string} queryText - 查询文本
 * @returns {Promise<Array>} 重排序后的片段数组
 */
async function rerank(similarChunks, queryText) {
  try {
    const documents = similarChunks.map(chunk => chunk.content);
    
    console.log('🔄 正在通过代理重排序文档片段:', {
      documentsCount: documents.length,
      queryLength: queryText.length
    });

    // 获取用户token并创建API客户端
    const userToken = await getCurrentUserToken()
    console.log('🔑 重排序请求使用的token:', userToken ? userToken.substring(0, 20) + '...' : '无token')
    const client = createMainApiClient(null, userToken)
    
    const requestData = {
      query: queryText,
      documents: documents,
      model: "BAAI/bge-reranker-v2-m3"
    };

    const response = await client.post('/rerank', requestData);
    
    // 打印完整的响应信息用于调试
    console.log('🔍 重排序完整响应对象:', {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers,
      data: response.data
    })
    
    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data
    console.log('🔍 重排序响应数据结构:', {
      hasResponseData: !!responseData,
      responseDataType: typeof responseData,
      isArray: Array.isArray(responseData),
      hasResults: responseData && responseData.results,
      resultsLength: responseData && responseData.results ? responseData.results.length : 0
    })
    
    if (responseData && responseData.results) {
      console.log(`✅ 代理重排序成功，返回 ${responseData.results.length} 个结果`);
      return getTopChunks(responseData, similarChunks);
    }
  } catch (error) {
    console.error('❌ 重排序失败:', error);
    console.error('❌ 错误详情:', error.message);
    // 如果重排序失败，返回原始片段
    console.log('🔄 重排序失败，返回原始片段');
    return similarChunks;
  }
}

/**
 * 动态阈值筛选优质片段
 * @param {Object} response - 重排序响应
 * @param {Array} chunks - 原始片段数组
 * @param {number} topN - 返回数量
 * @param {number} minScore - 最小相似度阈值
 * @returns {Array} 过滤后的片段数组
 */
async function getTopChunks(response, chunks, topN = 4, minScore = 0.3) {
  try {
    if (!response || !response.results || !Array.isArray(response.results)) {
      console.log('⚠️ 重排序响应为空，返回原始片段');
      return chunks.slice(0, topN);
    }

    // 提取并排序结果（按相关性分数降序）
    const sortedResults = response.results
      .slice() // 创建副本避免修改原数组
      .sort((a, b) => b.relevance_score - a.relevance_score);

    // 计算统计指标
    const scores = sortedResults.map(res => res.relevance_score);
    const mean = scores.reduce((sum, val) => sum + val, 0) / scores.length;
    const stdDev = Math.sqrt(
      scores.reduce((sum, val) => sum + Math.pow(val - mean, 2), 0) / scores.length
    );

    // 改进的动态阈值计算
    // 使用更宽松的阈值：均值减去0.5个标准差，或者直接使用前N个结果
    const dynamicThreshold = Math.max(minScore, mean - 0.5 * stdDev);
    const finalThreshold = Math.min(dynamicThreshold, 0.05); // 设置上限，避免过于严格

    console.log(`📊 重排序统计: 均值=${mean.toFixed(3)}, 标准差=${stdDev.toFixed(3)}, 动态阈值=${finalThreshold.toFixed(3)}`);
    console.log(`📊 相关性分数分布:`, scores.map(s => s.toFixed(3)));

    // 如果动态阈值过滤掉太多结果，则直接返回前N个
    const filteredResults = sortedResults.filter(res => res.relevance_score >= finalThreshold);

    if (filteredResults.length === 0) {
      console.log(`⚠️ 动态阈值过滤掉所有结果，返回前${topN}个结果`);
      const indexList = sortedResults.slice(0, topN).map(res => res.index);
      return chunks.filter((chunk, index) => indexList.includes(index));
    }

    console.log(`📊 阈值过滤后剩余: ${filteredResults.length} 个结果`);

    // 筛选满足条件的chunks
    const indexList = filteredResults
      .slice(0, topN) // 限制最大返回数量
      .map(res => res.index);

    return chunks.filter((chunk, index) => indexList.includes(index));
  } catch (error) {
    console.error('❌ 获取相似度最高的片段失败:', error);
    // 如果处理失败，返回原始片段的前N个
    return chunks.slice(0, topN);
  }
}

module.exports = {
  setGetCurrentUserTokenFunction,
  getKnowledgeEmbedding,
  rerank,
  getTopChunks
} 
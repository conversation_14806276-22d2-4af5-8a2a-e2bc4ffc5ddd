# -*- coding: utf-8 -*-
"""
Author: Mr.Car
Date: 2025-03-20 20:18:33
"""
import sys
import os
import traceback

# 强制刷新输出缓冲区
sys.stdout.flush()
sys.stderr.flush()

# 设置输出编码 - 注释掉，避免与MCP库冲突
# if sys.platform.startswith('win'):
#     import codecs
#     sys.stdout = codecs.getwriter('utf-8')(sys.stdout.detach())
#     sys.stderr = codecs.getwriter('utf-8')(sys.stderr.detach())

print("[WEATHER-SERVER] 开始导入模块...")
sys.stdout.flush()

try:
    from mcp.server.fastmcp import FastMCP
    print("[WEATHER-SERVER] FastMCP导入成功")
    sys.stdout.flush()
except Exception as e:
    print(f"[WEATHER-SERVER] FastMCP导入失败: {e}")
    print(f"[WEATHER-SERVER] 错误详情: {traceback.format_exc()}")
    sys.stdout.flush()
    sys.exit(1)

try:
    import httpx
    print("[WEATHER-SERVER] httpx导入成功")
    sys.stdout.flush()
except Exception as e:
    print(f"[WEATHER-SERVER] httpx导入失败: {e}")
    print(f"[WEATHER-SERVER] 错误详情: {traceback.format_exc()}")
    sys.stdout.flush()
    sys.exit(1)

try:
    import json
    print("[WEATHER-SERVER] json导入成功")
    sys.stdout.flush()
except Exception as e:
    print(f"[WEATHER-SERVER] json导入失败: {e}")
    print(f"[WEATHER-SERVER] 错误详情: {traceback.format_exc()}")
    sys.stdout.flush()
    sys.exit(1)

try:
    from dotenv import load_dotenv
    print("[WEATHER-SERVER] dotenv导入成功")
    sys.stdout.flush()
except Exception as e:
    print(f"[WEATHER-SERVER] dotenv导入失败: {e}")
    print(f"[WEATHER-SERVER] 错误详情: {traceback.format_exc()}")
    sys.stdout.flush()
    sys.exit(1)

print("[WEATHER-SERVER] 所有模块导入成功")
sys.stdout.flush()

# 加载环境变量
try:
    load_dotenv()
    print("[WEATHER-SERVER] 环境变量加载成功")
    sys.stdout.flush()
except Exception as e:
    print(f"[WEATHER-SERVER] 环境变量加载失败: {e}")
    print(f"[WEATHER-SERVER] 错误详情: {traceback.format_exc()}")
    sys.stdout.flush()
    sys.exit(1)

# 初始化 FastMCP 服务器
try:
    server = FastMCP("weather")
    print("[WEATHER-SERVER] FastMCP服务器初始化成功")
    sys.stdout.flush()
except Exception as e:
    print(f"[WEATHER-SERVER] FastMCP服务器初始化失败: {e}")
    print(f"[WEATHER-SERVER] 错误详情: {traceback.format_exc()}")
    sys.stdout.flush()
    sys.exit(1)


def get_user_auth_token():
    """
    获取用户认证Token
    只从主进程传递的环境变量获取实时用户Token
    """
    # 从环境变量获取用户Token
    user_token = os.getenv('USER_AUTH_TOKEN')
    
    if user_token:
        print("[AUTH] 从环境变量获取用户Token成功")
        sys.stdout.flush()
        return user_token
    else:
        error_msg = "未找到用户认证Token，请确保用户已登录"
        print(f"[AUTH] {error_msg}")
        sys.stdout.flush()
        raise Exception(error_msg)


@server.tool()
async def get_weather_forecast(
        adm: str,
        city: str,
        days: str
) -> dict:
    """
    获取指定城市的天气预报信息

    Args:
        adm (str): 城市的上级行政区划，例如省份或直辖市名称。
        city (str): 城市或地区名。
        days (str): 预报天数，支持最多30天预报，可选值：
            - "3d"：3天预报
            - "7d"：7天预报
            - "10d"：10天预报
            - "15d"：15天预报
            - "30d"：30天预报

    Returns:
        dict: 包含天气预报信息的字典，通常包括日期、天气状况、气温、风速等信息。
    """
    print(f"[WEATHER] 开始天气查询...")
    print(f"[WEATHER] 参数: adm={adm}, city={city}, days={days}")
    sys.stdout.flush()
    
    # 参数验证
    valid_days = {"3d", "7d", "10d", "10d", "15d", "30d"}
    if days not in valid_days:
        error_msg = f"无效的天数参数，支持的值: {', '.join(valid_days)}"
        print(f"[WEATHER] 参数验证失败: {error_msg}")
        sys.stdout.flush()
        return {"error": error_msg}

    if not adm or not city:
        error_msg = "城市和行政区划参数不能为空"
        print(f"[WEATHER] 参数验证失败: {error_msg}")
        sys.stdout.flush()
        return {"error": error_msg}

    print(f"[WEATHER] 参数验证通过")
    sys.stdout.flush()

    try:
        # 获取用户认证Token
        print(f"[WEATHER] 开始获取用户认证Token...")
        user_token = get_user_auth_token()
        print(f"[WEATHER] 用户认证Token获取成功")
        sys.stdout.flush()

        # 通过代理服务器调用天气API
        proxy_url = "http://114.67.112.88:9603/prod-api/api/tool/weather"
        print(f"[WEATHER] 代理服务器URL: {proxy_url}")
        
        # 构建请求参数
        params = {
            "adm": adm,
            "city": city,
            "day": days
        }
        print(f"[WEATHER] 请求参数: {params}")
        
        # 设置请求头（使用用户认证Token）
        headers = {
            "Authorization": f"Bearer {user_token}",
            "Content-Type": "application/json"
        }
        print(f"[WEATHER] 请求头设置完成")
        sys.stdout.flush()

        # 通过代理服务器获取天气信息
        print(f"[WEATHER] 开始通过代理服务器获取天气信息...")
        print(f"[WEATHER] 代理请求URL: {proxy_url}")
        print(f"[WEATHER] 代理请求参数: {params}")
        print(f"[WEATHER] 代理请求头: {headers}")
        sys.stdout.flush()
        
        async with httpx.AsyncClient(timeout=10.0) as client:
            try:
                print(f"[WEATHER] 发送HTTP请求...")
                proxy_res = await client.get(
                    proxy_url,
                    headers=headers,
                    params=params
                )
                print(f"[WEATHER] 代理请求成功，状态码: {proxy_res.status_code}")
                sys.stdout.flush()
                
                proxy_res.raise_for_status()
                weather_data = proxy_res.json()
                print(f"[WEATHER] 代理服务器响应原始数据:")
                print(json.dumps(weather_data, ensure_ascii=False, indent=2))
                print(f"[WEATHER] 响应数据类型: {type(weather_data)}")
                print(f"[WEATHER] 响应数据键: {list(weather_data.keys()) if isinstance(weather_data, dict) else '非字典类型'}")
                sys.stdout.flush()

                # 检查代理服务器返回结果
                print(f"[WEATHER] 检查响应状态码...")
                print(f"[WEATHER] 响应code字段: {weather_data.get('code')} (类型: {type(weather_data.get('code'))})")
                print(f"[WEATHER] 响应message字段: {weather_data.get('message')}")
                sys.stdout.flush()
                
                # 支持数字和字符串的code值
                response_code = weather_data.get("code")
                if response_code != 200 and response_code != "200":
                    error_msg = f"天气信息查询失败: {weather_data.get('message', '未知错误')}"
                    print(f"[WEATHER] {error_msg}")
                    sys.stdout.flush()
                    return {"error": error_msg}

                # 处理嵌套的返回结构
                print(f"[WEATHER] 检查是否有data字段...")
                print(f"[WEATHER] data字段存在: {'data' in weather_data}")
                print(f"[WEATHER] data字段内容: {weather_data.get('data')}")
                sys.stdout.flush()
                
                if "data" in weather_data and weather_data["data"]:
                    actual_weather_data = weather_data["data"]
                    print(f"[WEATHER] 提取实际天气数据:")
                    print(json.dumps(actual_weather_data, ensure_ascii=False, indent=2))
                    
                    # 检查实际天气数据的code
                    print(f"[WEATHER] 检查实际数据的code字段: {actual_weather_data.get('code')} (类型: {type(actual_weather_data.get('code'))})")
                    actual_code = actual_weather_data.get("code")
                    if actual_code != 200 and actual_code != "200":
                        error_msg = f"天气信息查询失败: {actual_weather_data.get('message', '未知错误')}"
                        print(f"[WEATHER] {error_msg}")
                        sys.stdout.flush()
                        return {"error": error_msg}
                    
                    print(f"[WEATHER] 天气查询成功完成")
                    sys.stdout.flush()
                    return actual_weather_data
                else:
                    # 如果没有嵌套结构，直接返回原始数据
                    print(f"[WEATHER] 天气查询成功完成，直接返回原始数据")
                    sys.stdout.flush()
                    return weather_data
                
            except httpx.TimeoutException as e:
                error_msg = f"代理请求超时: {str(e)}"
                print(f"[WEATHER] {error_msg}")
                sys.stdout.flush()
                return {"error": error_msg}
            except httpx.HTTPStatusError as e:
                error_msg = f"代理HTTP请求失败: {e.response.status_code} - {e.response.text}"
                print(f"[WEATHER] {error_msg}")
                print(f"[WEATHER] 错误响应内容: {e.response.text}")
                sys.stdout.flush()
                return {"error": error_msg}
            except httpx.RequestError as e:
                error_msg = f"代理网络请求错误: {str(e)}"
                print(f"[WEATHER] {error_msg}")
                sys.stdout.flush()
                return {"error": error_msg}

    except Exception as e:
        error_msg = f"天气查询过程中发生未知错误: {str(e)}"
        print(f"[WEATHER] {error_msg}")
        import traceback
        print(f"[WEATHER] 错误堆栈: {traceback.format_exc()}")
        sys.stdout.flush()
        return {"error": error_msg}


# 运行服务器
if __name__ == "__main__":
    print("[WEATHER-SERVER] 天气服务器启动中...")
    print("[WEATHER-SERVER] 使用stdio传输模式")
    sys.stdout.flush()
    server.run(transport="stdio") 
const { spawn } = require('child_process')
const path = require('path')
const fs = require('fs')
const os = require('os')

console.log('🔗 正在注册 ai-cognidesk:// 协议处理器...')

// 获取当前应用的路径
const appPath = process.platform === 'win32' 
  ? path.join(process.cwd(), 'dist', 'win-unpacked', '犇犇数字员工助手.exe')
  : process.execPath

console.log('🔗 应用路径:', appPath)

if (process.platform === 'win32') {
  // Windows: 通过注册表注册协议
  const registryScript = `Windows Registry Editor Version 5.00

[HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk]
@="URL:ai-cognidesk Protocol"
"URL Protocol"=""

[HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\DefaultIcon]
@="${appPath.replace(/\\/g, '\\\\')},1"

[HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\shell]

[HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\shell\\open]

[HKEY_CURRENT_USER\\Software\\Classes\\ai-cognidesk\\shell\\open\\command]
@="\\"${appPath.replace(/\\/g, '\\\\')}\\" \\"%1\\""
`
  
  const tempRegFile = path.join(os.tmpdir(), 'ai-cognidesk-protocol.reg')
  
  try {
    fs.writeFileSync(tempRegFile, registryScript)
    console.log('🔗 创建注册表文件:', tempRegFile)
    
    // 导入注册表
    const regProcess = spawn('reg', ['import', tempRegFile], { 
      shell: true, 
      stdio: 'inherit' 
    })
    
    regProcess.on('close', (code) => {
      console.log('🔗 注册表导入完成，退出码:', code)
      if (code === 0) {
        console.log('✅ 协议处理器注册成功！')
        console.log('🔗 现在可以使用 ai-cognidesk:// 协议链接启动应用')
      } else {
        console.error('❌ 注册表导入失败')
      }
      
      // 清理临时文件
      try {
        fs.unlinkSync(tempRegFile)
      } catch (e) {
        console.warn('🔗 清理临时文件失败:', e.message)
      }
    })
    
    regProcess.on('error', (error) => {
      console.error('🔗 注册表导入失败:', error)
    })
  } catch (error) {
    console.error('🔗 创建注册表文件失败:', error)
  }
} else if (process.platform === 'darwin') {
  // macOS: 通过Info.plist注册协议
  console.log('🔗 macOS 协议注册需要通过应用包的 Info.plist 文件')
  console.log('🔗 请确保 electron-builder.json 中的 protocols 配置正确')
  console.log('✅ 协议处理器应该已经通过应用包注册')
} else {
  // Linux: 通过.desktop文件注册协议
  console.log('🔗 Linux 协议注册需要创建 .desktop 文件')
  console.log('🔗 请确保 electron-builder.json 中的 protocols 配置正确')
  console.log('✅ 协议处理器应该已经通过应用包注册')
} 
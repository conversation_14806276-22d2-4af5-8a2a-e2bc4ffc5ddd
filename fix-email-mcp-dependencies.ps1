# Email MCP 依赖修复脚本
# 解决 pydantic_core 版本冲突导致的 MCP 服务启动失败问题

Write-Host "🔧 开始修复 Email MCP Python 依赖问题..." -ForegroundColor Green
Write-Host ""

# 检查当前问题
Write-Host "📋 步骤1: 检查当前 MCP 导入状态..." -ForegroundColor Yellow
$testResult = & "python/py/python/python.exe" -c "try:`n    import mcp.server.fastmcp`n    print('SUCCESS')`nexcept Exception as e:`n    print(f'ERROR: {e}')" 2>$null

if ($testResult -like "*SUCCESS*") {
    Write-Host "✅ MCP 依赖已正常，无需修复" -ForegroundColor Green
    exit 0
}

Write-Host "❌ 检测到 MCP 导入问题: $testResult" -ForegroundColor Red
Write-Host ""

# 显示当前pydantic版本
Write-Host "📋 步骤2: 检查当前 pydantic 版本..." -ForegroundColor Yellow
& "python/py/python/python.exe" -m pip list | Select-String "pydantic"
Write-Host ""

# 卸载有问题的包
Write-Host "📋 步骤3: 卸载有冲突的 pydantic 包..." -ForegroundColor Yellow
& "python/py/python/python.exe" -m pip uninstall -y pydantic pydantic-core pydantic-settings
if ($LASTEXITCODE -ne 0) {
    Write-Host "⚠️ 卸载过程中出现警告，继续执行..." -ForegroundColor Yellow
}
Write-Host ""

# 重新安装兼容版本
Write-Host "📋 步骤4: 安装兼容版本的 pydantic 包..." -ForegroundColor Yellow
& "python/py/python/python.exe" -m pip install "pydantic>=2.7,<3.0" "pydantic-core>=2.33,<3.0" "pydantic-settings>=2.5"
if ($LASTEXITCODE -ne 0) {
    Write-Host "❌ 安装失败！" -ForegroundColor Red
    exit 1
}
Write-Host ""

# 验证修复结果
Write-Host "📋 步骤5: 验证修复结果..." -ForegroundColor Yellow
$verifyResult = & "python/py/python/python.exe" -c "try: import mcp.server.fastmcp; print('SUCCESS')" 2>$null

if ($verifyResult -like "*SUCCESS*") {
    Write-Host "✅ 修复成功！Email MCP 依赖问题已解决" -ForegroundColor Green
    Write-Host ""
    Write-Host "📋 当前 pydantic 版本:" -ForegroundColor Cyan
    & "python/py/python/python.exe" -m pip list | Select-String "pydantic"
} else {
    Write-Host "❌ 修复失败: $verifyResult" -ForegroundColor Red
    Write-Host ""
    Write-Host "💡 建议手动检查 Python 环境配置" -ForegroundColor Yellow
    exit 1
}

Write-Host ""
Write-Host "🎉 修复完成！现在可以重启应用测试 Email MCP 功能" -ForegroundColor Green 
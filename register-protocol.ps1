#!/usr/bin/env pwsh

Write-Host "🔗 正在注册 ai-cognidesk:// 协议处理器..." -ForegroundColor Green

# 获取当前应用的路径
$appPath = Join-Path $PSScriptRoot "dist\win-unpacked\犇犇数字员工助手.exe"
$devAppPath = Join-Path $PSScriptRoot "node_modules\electron\dist\electron.exe"

# 检查应用是否存在
if (Test-Path $appPath) {
    $executablePath = $appPath
    Write-Host "🔗 使用生产版本应用: $executablePath" -ForegroundColor Cyan
} elseif (Test-Path $devAppPath) {
    $executablePath = $devAppPath
    Write-Host "🔗 使用开发版本应用: $executablePath" -ForegroundColor Cyan
} else {
    Write-Host "❌ 未找到应用程序文件" -ForegroundColor Red
    Write-Host "请先构建应用程序或确保在正确的目录中运行此脚本" -ForegroundColor Yellow
    exit 1
}

# 转义路径中的反斜杠
$escapedPath = $executablePath.Replace('\', '\\')

Write-Host "🔗 应用路径: $executablePath" -ForegroundColor Cyan

try {
    # 创建注册表项
    Write-Host "🔗 创建注册表项..." -ForegroundColor Yellow
    
    # 主协议项
    New-Item -Path "HKCU:\Software\Classes\ai-cognidesk" -Force | Out-Null
    Set-ItemProperty -Path "HKCU:\Software\Classes\ai-cognidesk" -Name "(Default)" -Value "URL:ai-cognidesk Protocol"
    Set-ItemProperty -Path "HKCU:\Software\Classes\ai-cognidesk" -Name "URL Protocol" -Value ""
    
    # 图标项
    New-Item -Path "HKCU:\Software\Classes\ai-cognidesk\DefaultIcon" -Force | Out-Null
    Set-ItemProperty -Path "HKCU:\Software\Classes\ai-cognidesk\DefaultIcon" -Name "(Default)" -Value "$executablePath,1"
    
    # Shell项
    New-Item -Path "HKCU:\Software\Classes\ai-cognidesk\shell" -Force | Out-Null
    New-Item -Path "HKCU:\Software\Classes\ai-cognidesk\shell\open" -Force | Out-Null
    
    # 命令项
    New-Item -Path "HKCU:\Software\Classes\ai-cognidesk\shell\open\command" -Force | Out-Null
    Set-ItemProperty -Path "HKCU:\Software\Classes\ai-cognidesk\shell\open\command" -Name "(Default)" -Value "`"$executablePath`" `"%1`""
    
    Write-Host "✅ 协议处理器注册成功！" -ForegroundColor Green
    Write-Host "🔗 现在可以使用 ai-cognidesk:// 协议链接启动应用" -ForegroundColor Green
    
    # 测试注册是否成功
    Write-Host "🔗 验证注册..." -ForegroundColor Yellow
    $registeredCommand = Get-ItemProperty -Path "HKCU:\Software\Classes\ai-cognidesk\shell\open\command" -Name "(Default)" -ErrorAction SilentlyContinue
    if ($registeredCommand) {
        Write-Host "✅ 验证成功: $($registeredCommand.'(Default)')" -ForegroundColor Green
    } else {
        Write-Host "❌ 验证失败" -ForegroundColor Red
    }
    
} catch {
    Write-Host "❌ 注册协议处理器失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

Write-Host "🔗 协议注册完成！" -ForegroundColor Green
Write-Host "💡 提示: 如果协议仍然无法工作，请尝试重启浏览器" -ForegroundColor Yellow 
# -*- coding: utf-8 -*-

from mcp.server.fastmcp import FastMCP
import smtplib
from email.message import EmailMessage
import imaplib
import email
import email.utils
from email.header import decode_header
from datetime import datetime
from typing import List, Optional, Union

# Initialize FastMCP server
mcp = FastMCP("email_server")


class EmailEnvironment:
    """
    邮件环境配置
    """

    USER = "<EMAIL>"
    PASS = "oqypfketbsaqecbi"
    SMTP_SERVER = "smtp.qq.com"
    SMTP_PORT = 465
    SMTP_SSL = True
    IMAP_SERVER = "imap.qq.com"
    IMAP_PORT = 993
    IMAP_SSL = True


# class EmailTools(str, Enum):
#     LIST_EMAIL = "list_email"
#     RETURN_EMAIL_JSON = "return_email_json"
#     SEND_EMAIL = "send_email"


# @mcp.list_tools()
# async def list_tools() -> list[Tool]:
#     """List all available tools."""
#     return [
#         Tool(
#             name=EmailTools.SEND_EMAIL.value,
#             title="邮件发送工具",
#             description="用于发送电子邮件",
#             inputSchema={
#                 "type": "object",
#                 "properties": {
#                     "to": {"type": "list", "description": "收件人邮箱列表"},
#                     "cc": {"type": "list", "description": "抄送人邮箱列表"},
#                     "sub": {"type": "string", "description": "邮件主题"},
#                     "message": {"type": "string", "description": "邮件主题"},
#                     "is_ok": {"type": "boolean", "description": "是否经过用户二次确认"},
#
#                 }
#             }
#         )
#     ]


@mcp.tool(name="send_email")
async def send_email(
    to: List[str], sub: str, message: str, is_ok: bool = False, cc: Optional[List[str]] = None
) -> str:
    """
    发送邮件

    参数:
        to (List[str]): 收件人列表
        cc (Optional[List[str]]): 抄送人列表
        sub (str): 邮件主题
        message (str): 邮件正文
        is_ok (bool): 是否经过用户二次确认

    返回:
        str: 操作结果描述
    """
    if not is_ok:
        return "待用户二次确认"

    msg = EmailMessage()
    msg["Subject"] = sub
    msg["From"] = EmailEnvironment.USER
    msg["To"] = ", ".join(to)
    if cc:
        msg["Cc"] = ", ".join(cc)
    msg.set_content(message)

    try:
        if EmailEnvironment.SMTP_SSL:
            with smtplib.SMTP_SSL(
                EmailEnvironment.SMTP_SERVER, EmailEnvironment.SMTP_PORT
            ) as server:
                server.login(EmailEnvironment.USER, EmailEnvironment.PASS)
                # 合并收件人和抄送人
                all_recipients = to + (cc if cc else [])
                server.send_message(msg, to_addrs=all_recipients)
                server.close()
        else:
            with smtplib.SMTP(
                EmailEnvironment.SMTP_SERVER, EmailEnvironment.SMTP_PORT
            ) as server:
                server.starttls()
                server.login(EmailEnvironment.USER, EmailEnvironment.PASS)
                all_recipients = to + (cc if cc else [])
                server.send_message(msg, to_addrs=all_recipients)
                server.close()
        return "邮件发送成功"
    except Exception as e:
        return f"邮件发送失败: {type(e).__name__}: {e}"


@mcp.tool(name="list_email")
async def list_email(start_time: Optional[str] = None, end_time: Optional[str] = None) -> List[dict]:
    """
    根据时间范围查询所有未读邮件

    参数:
        start_time (Optional[str]): 开始时间，格式: 'YYYY-MM-DD HH:MM:SS'
        end_time (Optional[str]): 结束时间，格式: 'YYYY-MM-DD HH:MM:SS'

    返回:
        List[dict]: 所有邮件详情
    """

    result = []
    try:
        with imaplib.IMAP4_SSL(
            EmailEnvironment.IMAP_SERVER, EmailEnvironment.IMAP_PORT
        ) as mail:
            mail.login(EmailEnvironment.USER, EmailEnvironment.PASS)
            mail.select("INBOX")

            # 构建搜索条件
            search_criteria = ["UNSEEN"]

            # 添加时间范围过滤
            if start_time:
                start_dt = datetime.strptime(start_time, "%Y-%m-%d %H:%M:%S")
                start_date_str = start_dt.strftime("%d-%b-%Y")
                search_criteria.append(f'SINCE "{start_date_str}"')

            if end_time:
                end_dt = datetime.strptime(end_time, "%Y-%m-%d %H:%M:%S")
                # BEFORE 是排他性的，所以使用结束时间的下一天
                from datetime import timedelta

                next_day = end_dt + timedelta(days=1)
                end_date_str = next_day.strftime("%d-%b-%Y")
                search_criteria.append(f'BEFORE "{end_date_str}"')

            # 执行搜索
            typ, data = mail.search(None, *search_criteria)
            if typ != "OK":
                return []

            for num in data[0].split():
                typ, msg_data = mail.fetch(num, "(RFC822)")
                if typ != "OK":
                    continue
                    
                if msg_data and len(msg_data) > 0 and msg_data[0] and len(msg_data[0]) > 1:
                    msg_bytes = msg_data[0][1]
                    if isinstance(msg_bytes, bytes):
                        msg = email.message_from_bytes(msg_bytes)
                    else:
                        continue
                else:
                    continue

                # 获取邮件时间并进行精确到秒的过滤
                date_str = msg.get("Date")
                if date_str and (start_time or end_time):
                    try:
                        # 解析邮件时间
                        msg_time = email.utils.parsedate_to_datetime(date_str)
                        msg_timestamp = msg_time.timestamp()

                        # 精确到秒的时间过滤
                        if start_time:
                            start_timestamp = datetime.strptime(
                                start_time, "%Y-%m-%d %H:%M:%S"
                            ).timestamp()
                            if msg_timestamp < start_timestamp:
                                continue

                        if end_time:
                            end_timestamp = datetime.strptime(
                                end_time, "%Y-%m-%d %H:%M:%S"
                            ).timestamp()
                            if msg_timestamp > end_timestamp:
                                continue
                    except:
                        # 如果时间解析失败，跳过此邮件
                        continue

                subject_header = msg.get("Subject")
                subject = ""
                if subject_header:
                    subject, encoding = decode_header(subject_header)[0]
                    if isinstance(subject, bytes):
                        subject = subject.decode(encoding or "utf-8", errors="ignore")
                from_ = msg.get("From")
                date_ = msg.get("Date")

                # 获取正文
                body = ""
                if msg.is_multipart():
                    for part in msg.walk():
                        ctype = part.get_content_type()
                        cdispo = str(part.get("Content-Disposition"))
                        if ctype == "text/plain" and "attachment" not in cdispo:
                            charset = part.get_content_charset() or "utf-8"
                            payload = part.get_payload(decode=True)
                            if isinstance(payload, bytes):
                                body = payload.decode(charset, errors="ignore")
                            break
                else:
                    charset = msg.get_content_charset() or "utf-8"
                    payload = msg.get_payload(decode=True)
                    if isinstance(payload, bytes):
                        body = payload.decode(charset, errors="ignore")

                result.append(
                    {
                        "uid": num.decode("utf-8"),
                        "subject": subject,
                        "from": from_,
                        "date": date_,
                        "body": body,
                    }
                )
        return result
    except Exception as e:
        return [{"error": f"{type(e).__name__}: {e}"}]


@mcp.tool(name="mark_email_as_read")
async def mark_email_as_read(uid_list: List[str]) -> str:
    """
    批量标记邮件为已读

    参数:
        uid_list (List[str]): 邮件的唯一标识符列表

    返回:
        str: 操作结果描述
    """
    import ssl

    if not uid_list:
        return "没有需要标记的邮件"

    success_count = 0
    failed_count = 0
    failed_uids = []
    mail = None

    try:

        # 创建SSL上下文
        context = ssl.create_default_context()

        # 连接到IMAP服务器
        mail = imaplib.IMAP4_SSL(
            EmailEnvironment.IMAP_SERVER,
            EmailEnvironment.IMAP_PORT,
            ssl_context=context,
        )

        # 登录
        mail.login(EmailEnvironment.USER, EmailEnvironment.PASS)

        # 选择邮箱（默认选择收件箱）
        mail.select("INBOX")

        # 循环处理每个邮件UID
        for uid in uid_list:
            try:
                # 使用 imaplib 标记邮件为已读
                # '+FLAGS' 表示添加标志，'\\Seen' 表示已读标志
                result = mail.store(str(uid), "+FLAGS", "\\Seen")

                # 检查操作是否成功
                if result[0] == "OK":
                    success_count += 1
                    print(f"成功标记邮件 {uid} 为已读")
                else:
                    failed_count += 1
                    failed_uids.append(uid)
                    print(f"标记邮件 {uid} 失败: {result[1]}")

            except Exception as e:
                failed_count += 1
                failed_uids.append(uid)
                print(f"标记邮件 {uid} 时发生异常: {str(e)}")

        # 可选：立即同步更改到服务器
        try:
            mail.expunge()
        except Exception as e:
            print(f"同步更改时发生异常: {str(e)}")

    except imaplib.IMAP4.error as e:
        return f"IMAP操作失败: {str(e)}"
    except Exception as e:
        return f"连接或登录失败: {str(e)}"

    finally:
        # 确保关闭连接
        if mail:
            try:
                mail.close()
                mail.logout()
            except Exception as e:
                print(f"关闭连接时发生异常: {str(e)}")

    # 构建结果描述
    result = f"批量标记邮件操作完成：成功 {success_count} 封，失败 {failed_count} 封"

    if failed_uids:
        result += f"，失败的邮件UID: {', '.join(map(str, failed_uids))}"

    return result


if __name__ == "__main__":
    # Initialize and run the server
    mcp.run(transport="stdio")

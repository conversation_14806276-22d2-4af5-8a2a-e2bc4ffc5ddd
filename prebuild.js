const { extractAgreementText } = require('./extract-agreement')

async function prebuild() {
  console.log('开始预构建流程...')
  
  try {
    // 提取隐私协议文本
    console.log('正在提取隐私协议文本...')
    await extractAgreementText()
    console.log('隐私协议文本提取完成')
    
    console.log('预构建流程完成')
  } catch (error) {
    console.error('预构建流程失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  prebuild()
}

module.exports = { prebuild } 
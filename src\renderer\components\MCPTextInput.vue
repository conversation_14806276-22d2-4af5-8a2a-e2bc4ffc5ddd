<template>
  <div class="mcp-text-input-container">
    <!-- 输入区域 -->
    <div class="input-area">
      <div class="input-wrapper">
        <textarea
          ref="textInput"
          v-model="inputText"
          @keydown="handleKeydown"
          :placeholder="placeholder"
          :disabled="isProcessing"
          class="text-input"
          rows="3"
        ></textarea>
        
        <!-- 按钮组 -->
        <div class="button-group">
          <button 
            @click="sendMessage" 
            :disabled="!inputText.trim() || isProcessing"
            class="send-btn"
            title="发送消息"
          >
            <img src="/assets/send.png" alt="发送" />
          </button>
        </div>
      </div>
      
      <!-- MCP状态指示器 -->
      <div class="mcp-status" v-if="mcpStatus.initialized">
        <span class="status-indicator">🔧</span>
        <span class="status-text">MCP工具已启用</span>
        <button @click="showMCPStatus" class="status-details-btn">详情</button>
      </div>
    </div>

    <!-- 邮件发送确认弹框 -->
    <EmailConfirmModal
      :visible="showEmailModal"
      :initialData="emailModalData"
      @close="closeEmailModal"
      @send="handleEmailSend"
      @error="handleEmailError"
    />

    <!-- 文件选择对话框 -->
    <div v-if="showFileSelection" class="file-selection-modal" @click="handleModalClick">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>{{ fileSelectionMessage }}</h3>
          <button @click="cancelFileSelection" class="close-btn">×</button>
        </div>
        
        <div class="file-list">
          <div 
            v-for="(file, index) in fileList" 
            :key="index"
            @click="selectFile(file)"
            class="file-item"
          >
            <div class="file-icon">
              {{ getFileIcon(file.type) }}
            </div>
            <div class="file-info">
              <div class="file-name">{{ file.name }}</div>
              <div class="file-details">
                {{ formatFileSize(file.size) }} | {{ formatDate(file.lastModified) }}
              </div>
              <div class="file-path">{{ file.path }}</div>
            </div>
          </div>
        </div>
        
        <div class="modal-actions">
          <button @click="cancelFileSelection" class="cancel-btn">取消</button>
        </div>
      </div>
    </div>

    <!-- MCP状态详情对话框 -->
    <div v-if="showMCPDetails" class="mcp-details-modal" @click="hideMCPDetails">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>MCP工具状态</h3>
          <button @click="hideMCPDetails" class="close-btn">×</button>
        </div>
        
        <div class="mcp-details">
          <div class="client-section" v-for="(client, name) in mcpStatus.clients" :key="name">
            <h4>{{ name }} MCP客户端</h4>
            <p :class="['connection-status', { 'connected': client.connected }]">
              {{ client.connected ? '已连接' : '未连接' }}
            </p>
          </div>
          
          <div class="tools-section">
            <h4>可用工具</h4>
            <div v-for="(toolList, clientName) in mcpStatus.tools" :key="clientName">
              <h5>{{ clientName }}</h5>
              <ul>
                <li v-for="tool in toolList" :key="tool.name">
                  <strong>{{ tool.name }}</strong>: {{ tool.description }}
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 处理状态覆盖层 -->
    <div v-if="isProcessing" class="processing-overlay">
      <div class="processing-content">
        <div class="spinner"></div>
        <p>{{ processingMessage }}</p>
      </div>
    </div>
  </div>
</template>

<script>
import { sendMCPChatRequest, checkMCPStatus } from '../utils/mcpChatAPI.js'
import EmailConfirmModal from './EmailConfirmModal.vue'

export default {
  name: 'MCPTextInput',
  
  components: {
    EmailConfirmModal
  },
  
  data() {
    return {
      inputText: '',
      isRecording: false,
      isProcessing: false,
      processingMessage: '正在处理...',
      
      // 文件选择相关
      showFileSelection: false,
      fileList: [],
      fileSelectionMessage: '',
      selectedFileCallback: null,
      
      // MCP状态相关
      mcpStatus: {
        initialized: false,
        clients: {},
        tools: {}
      },
      showMCPDetails: false,
      
      // 邮件发送相关
      showEmailModal: false,
      emailModalData: {
        to: [],
        cc: [],
        subject: '',
        message: ''
      },
      pendingEmailCallback: null,
      
      // 图标路径
      voiceIcon: '/assets/voice.png',
      voiceOnIcon: '/assets/voice-on.png',
      
      placeholder: '输入您的消息，或说"帮我找个文件"来体验MCP功能...'
    }
  },

  mounted() {
    this.initializeMCP()
    this.setupFileSelectionListener()
  },

  methods: {
    async initializeMCP() {
      try {
        const status = await window.electronAPI.getMCPStatus()
        this.mcpStatus = status
        console.log('MCP状态:', status)
      } catch (error) {
        console.error('获取MCP状态失败:', error)
      }
    },

    setupFileSelectionListener() {
      // 监听来自主进程的文件选择请求
      window.electronAPI.onFileSelectionRequest((files, message) => {
        this.showFileSelectionDialog(files, message)
      })
    },

    async sendMessage() {
      if (!this.inputText.trim() || this.isProcessing) return

      const userMessage = this.inputText.trim()
      this.inputText = ''
      this.isProcessing = true
      this.processingMessage = '正在与AI对话...'

      try {
        // 发出用户消息事件
        this.$emit('user-message', userMessage)

        // 构建消息历史
        const messages = [
          {
            role: 'user',
            content: userMessage
          }
        ]

        // 发送MCP聊天请求
        const response = await sendMCPChatRequest(messages, {
          enableWebSearch: false, // 默认关闭联网搜索，用户可以通过其他方式开启
          enableKnowledge: true, // 默认启用知识库搜索
          knowledgeLimit: 3,
          enableDeepSearch: false,
          searchMode: 'auto'
        })
        
        console.log('🔍 [DEBUG] 收到完整响应:', response)
        console.log('🔍 [DEBUG] response.success:', response.success)
        console.log('🔍 [DEBUG] response.needConfirm:', response.needConfirm)
        console.log('🔍 [DEBUG] response.confirmTool:', response.confirmTool)
        console.log('🔍 [DEBUG] response.type:', response.type)
        
        if (response.success) {
          // 检查是否需要显示邮件确认弹框
          if (response.needConfirm && response.confirmTool) {
            console.log('📧 检测到邮件确认需求，显示邮件确认弹框')
            console.log('📧 确认工具数据:', response.confirmTool)
            console.log('📧 邮件数据:', response.confirmTool.emailData)
            this.showEmailConfirmModal(response.confirmTool.emailData, response)
            return
          }
          
          // 保持向后兼容的检查方式
          if (response.toolsUsed && response.toolsUsed.some(tool => tool.needConfirm)) {
            const emailTool = response.toolsUsed.find(tool => tool.needConfirm)
            console.log('📧 通过兼容方式检测到邮件确认需求')
            this.showEmailConfirmModal(emailTool.emailData, response)
            return
          }
          
          this.$emit('ai-response', {
            message: response.message,
            type: response.type,
            toolsUsed: response.toolsUsed
          })
        } else {
          this.$emit('error', response.message || '处理请求时出现错误')
        }

      } catch (error) {
        console.error('发送消息失败:', error)
        this.$emit('error', '发送消息失败，请重试')
      } finally {
        this.isProcessing = false
      }
    },

    async toggleVoiceInput() {
      if (this.isRecording) {
        await this.stopVoiceInput()
      } else {
        await this.startVoiceInput()
      }
    },

    async startVoiceInput() {
      try {
        this.isRecording = true
        this.processingMessage = '正在录音...'
        // TODO: 实现语音识别功能
        // 这里可以集成现有的语音识别模块
        console.log('开始录音')
      } catch (error) {
        console.error('开始录音失败:', error)
        this.isRecording = false
      }
    },

    async stopVoiceInput() {
      try {
        this.isRecording = false
        // TODO: 处理语音识别结果
        // const transcript = await 语音识别API()
        // this.inputText = transcript
        console.log('停止录音')
      } catch (error) {
        console.error('停止录音失败:', error)
      }
    },

    handleKeydown(event) {
      if (event.key === 'Enter' && !event.shiftKey) {
        event.preventDefault()
        this.sendMessage()
      }
    },

    // 文件选择相关方法
    showFileSelectionDialog(files, message) {
      this.fileList = files
      this.fileSelectionMessage = message
      this.showFileSelection = true
      
      return new Promise((resolve) => {
        this.selectedFileCallback = resolve
      })
    },

    selectFile(file) {
      this.showFileSelection = false
      if (this.selectedFileCallback) {
        const result = {
          success: true,
          selectedFile: file
        }
        this.selectedFileCallback(result)
        // 发送结果到主进程
        window.electronAPI.sendFileSelectionResult(result)
        this.selectedFileCallback = null
      }
    },

    cancelFileSelection() {
      this.showFileSelection = false
      if (this.selectedFileCallback) {
        const result = {
          success: false,
          cancelled: true
        }
        this.selectedFileCallback(result)
        window.electronAPI.sendFileSelectionResult(result)
        this.selectedFileCallback = null
      }
    },

    handleModalClick(event) {
      // 点击模态框背景关闭
      if (event.target === event.currentTarget) {
        this.cancelFileSelection()
      }
    },

    // MCP状态相关方法
    showMCPStatus() {
      this.showMCPDetails = true
    },

    hideMCPDetails() {
      this.showMCPDetails = false
    },

    // 工具方法
    getFileIcon(fileType) {
      const iconMap = {
        '.docx': '📄',
        '.doc': '📄',
        '.pdf': '📕',
        '.xlsx': '📊',
        '.xls': '📊',
        '.pptx': '📹',
        '.ppt': '📹',
        '.txt': '📝',
        '.jpg': '🖼️',
        '.jpeg': '🖼️',
        '.png': '🖼️',
        '.gif': '🖼️'
      }
      return iconMap[fileType] || '📄'
    },

    formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes'
      const k = 1024
      const sizes = ['Bytes', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
    },

    formatDate(dateString) {
      return new Date(dateString).toLocaleString('zh-CN')
    },

    async testMCPTools() {
      console.log('🧪 开始测试MCP工具')
      this.isProcessing = true
      this.processingMessage = '正在测试MCP工具...'

      try {
        // 1. 测试MCP状态
        console.log('🧪 步骤1: 测试MCP状态')
        const statusResult = await window.electronAPI.getMCPStatus()
        console.log('🧪 MCP状态结果:', statusResult)
        
        if (!statusResult.success) {
          throw new Error('MCP服务未正确初始化')
        }

        // 2. 测试文件搜索
        console.log('🧪 步骤2: 测试文件搜索')
        const searchResult = await window.electronAPI.executeMCPTool('search_files', { 
          query: '测试'
        })
        console.log('🧪 搜索结果:', searchResult)
        
        if (!searchResult.success) {
          throw new Error(`文件搜索失败: ${searchResult.error}`)
        }

        if (searchResult.files && searchResult.files.length > 0) {
          // 3. 测试文件打开
          console.log('🧪 步骤3: 测试文件打开')
          const testFile = searchResult.files[0]
          console.log('🧪 选择测试文件:', testFile)
          
          const openResult = await window.electronAPI.executeMCPTool('open_file', { 
            filePath: testFile.path 
          })
          console.log('🧪 打开结果:', openResult)
          
          // 发送测试结果消息
          this.$emit('ai-response', {
            message: `🧪 MCP工具测试完成！\n\n` +
                    `✅ MCP状态: ${statusResult.success ? '正常' : '异常'}\n` +
                    `✅ 文件搜索: 找到 ${searchResult.files.length} 个文件\n` +
                    `${openResult.success ? '✅' : '❌'} 文件打开: ${openResult.success ? '成功' : openResult.error}\n\n` +
                    `测试文件: ${testFile.name}\n` +
                    `文件路径: ${testFile.path}`,
            type: 'test_result',
            toolsUsed: ['get_mcp_status', 'search_files', 'open_file']
          })
        } else {
          this.$emit('ai-response', {
            message: `🧪 MCP工具测试完成！\n\n` +
                    `✅ MCP状态: 正常\n` +
                    `⚠️ 文件搜索: 未找到包含"测试"的文件\n` +
                    `⏭️ 文件打开: 跳过测试（无可用文件）\n\n` +
                    `建议: 在桌面创建一个包含"测试"字样的文件来完整测试功能`,
            type: 'test_result',
            toolsUsed: ['get_mcp_status', 'search_files']
          })
        }

      } catch (error) {
        console.error('🧪 MCP工具测试失败:', error)
        this.$emit('error', `MCP工具测试失败: ${error.message}`)
      } finally {
        this.isProcessing = false
      }
    },

    // 邮件相关方法
    showEmailConfirmModal(emailData, originalResponse) {
      console.log('📧 [MODAL] showEmailConfirmModal被调用')
      console.log('📧 [MODAL] 传入的emailData:', emailData)
      console.log('📧 [MODAL] 当前showEmailModal状态:', this.showEmailModal)
      
      this.emailModalData = {
        to: emailData.to || [],
        cc: emailData.cc || [],
        subject: emailData.subject || '',
        message: emailData.message || ''
      }
      
      console.log('📧 [MODAL] 设置后的emailModalData:', this.emailModalData)
      
      this.pendingEmailCallback = originalResponse
      this.showEmailModal = true
      
      console.log('📧 [MODAL] 设置后的showEmailModal状态:', this.showEmailModal)
      
      // 强制Vue更新DOM
      this.$nextTick(() => {
        console.log('📧 [MODAL] nextTick后的showEmailModal状态:', this.showEmailModal)
      })
    },

    closeEmailModal() {
      this.showEmailModal = false
      this.emailModalData = {
        to: [],
        cc: [],
        subject: '',
        message: ''
      }
      this.pendingEmailCallback = null
    },

    async handleEmailSend(emailPayload) {
      try {
        this.isProcessing = true
        this.processingMessage = '正在发送邮件...'

        // 确保设置is_ok为true，表示用户已确认
        const finalPayload = {
          ...emailPayload,
          is_ok: true
        }
        
        console.log('📧 发送邮件，最终参数:', finalPayload)

        // 调用MCP发送邮件工具
        const result = await window.electronAPI.executeMCPTool('send_email', finalPayload)
        
        this.closeEmailModal()
        
        if (result.success) {
          this.$emit('ai-response', {
            message: '邮件发送成功！',
            type: 'email_sent',
            toolsUsed: ['send_email']
          })
        } else {
          this.$emit('error', '邮件发送失败: ' + (result.error || '未知错误'))
        }

      } catch (error) {
        console.error('发送邮件时出错:', error)
        this.$emit('error', '发送邮件时出错: ' + error.message)
        this.closeEmailModal()
      } finally {
        this.isProcessing = false
      }
    },

    handleEmailError(errorMessage) {
      this.$emit('error', errorMessage)
      this.closeEmailModal()
    }
  }
}
</script>

<style scoped>
.mcp-text-input-container {
  position: relative;
  width: 100%;
}

.input-area {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 12px;
  padding: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.input-wrapper {
  display: flex;
  gap: 12px;
  align-items: flex-end;
}

.text-input {
  flex: 1;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  
  resize: vertical;
  min-height: 60px;
  max-height: 120px;
  transition: border-color 0.2s;
}

.text-input:focus {
  outline: none;
  border-color: #007AFF;
}

.text-input:disabled {
  background: #f5f5f5;
  color: #999;
}

.button-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.test-btn, .voice-btn, .send-btn {
  width: 44px;
  height: 44px;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  background: #f0f0f0;
}

.test-btn:hover, .voice-btn:hover, .send-btn:hover {
  background: #e0e0e0;
  transform: translateY(-1px);
}

.test-btn:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.5;
}

.voice-btn.recording {
  background: #ff4444;
  animation: pulse 1s infinite;
}

.send-btn:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
  opacity: 0.5;
}

.test-btn img, .voice-btn img, .send-btn img {
  width: 20px;
  height: 20px;
}

.mcp-status {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 12px;
  padding: 8px 12px;
  background: rgba(0, 122, 255, 0.1);
  border-radius: 6px;
  font-size: 12px;
}

.status-indicator {
  font-size: 14px;
}

.status-text {
  color: #007AFF;
  font-weight: 500;
}

.status-details-btn {
  background: none;
  border: 1px solid #007AFF;
  color: #007AFF;
  padding: 2px 8px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 11px;
}

/* 模态框样式 */
.file-selection-modal, .mcp-details-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.modal-content {
  background: white;
  border-radius: 12px;
  padding: 0;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 24px;
  border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
  margin: 0;
  color: #333;
  font-size: 18px;
}

.close-btn {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #666;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6px;
}

.close-btn:hover {
  background: #f0f0f0;
}

.file-list {
  flex: 1;
  overflow-y: auto;
  padding: 16px 24px;
  max-height: 400px;
}

.file-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 8px;
  cursor: pointer;
  transition: all 0.2s;
}

.file-item:hover {
  background: #f8f9fa;
  border-color: #007AFF;
  transform: translateY(-1px);
}

.file-icon {
  font-size: 24px;
  margin-right: 12px;
  min-width: 40px;
  text-align: center;
}

.file-info {
  flex: 1;
  min-width: 0;
}

.file-name {
  font-weight: 600;
  color: #333;
  margin-bottom: 4px;
  word-break: break-word;
}

.file-details {
  font-size: 12px;
  color: #666;
  margin-bottom: 2px;
}

.file-path {
  font-size: 11px;
  color: #999;
  word-break: break-all;
}

.modal-actions {
  padding: 16px 24px;
  border-top: 1px solid #e0e0e0;
  display: flex;
  justify-content: flex-end;
}

.cancel-btn {
  padding: 8px 16px;
  border: 1px solid #ddd;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  color: #666;
}

.cancel-btn:hover {
  background: #f5f5f5;
}

/* MCP详情样式 */
.mcp-details {
  padding: 16px 24px;
  max-height: 400px;
  overflow-y: auto;
}

.client-section, .tools-section {
  margin-bottom: 20px;
}

.client-section h4, .tools-section h4 {
  margin: 0 0 8px 0;
  color: #333;
}

.connection-status {
  font-size: 14px;
  padding: 4px 8px;
  border-radius: 4px;
  background: #ffebee;
  color: #c62828;
}

.connection-status.connected {
  background: #e8f5e8;
  color: #2e7d32;
}

.tools-section h5 {
  margin: 8px 0 4px 0;
  color: #555;
  font-size: 14px;
}

.tools-section ul {
  margin: 0;
  padding-left: 16px;
}

.tools-section li {
  margin-bottom: 4px;
  font-size: 13px;
}

/* 处理状态覆盖层 */
.processing-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12px;
  z-index: 1000;
}

.processing-content {
  text-align: center;
  color: #666;
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #f3f3f3;
  border-top: 3px solid #007AFF;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 12px;
}

/* 动画 */
@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 
/**
 * 智能语音功能配置
 */

// 默认配置
export const DEFAULT_SMART_VOICE_CONFIG = {
  // 唤醒词引擎配置
  wakeWordEngine: 'sherpa-onnx',    // 唤醒词检测引擎：'sherpa-onnx' 或 'aliyun-asr'
  
  // ASR提供商配置
  asrProvider: 'tencent',            // 只使用腾讯云ASR
  
  // 唤醒词配置
  wakeWords: ['你好犇犇', '小犇犇', '犇犇助手', '犇犇', '小犇同学'],
  checkInterval: 3000,        // 唤醒词检测间隔(ms) - 仅腾讯云ASR
  recordDuration: 2000,       // 单次检测录音时长(ms) - 仅腾讯云ASR
  
  // 腾讯云ASR词汇表配置
  tencentHotwordId: '',       // 腾讯云热词ID（可选）
  tencentReplaceTextId: 'a365fa85bacc4829a829cc8ed0fe437d',  // 腾讯云替换词汇表ID
  
  // Sherpa-ONNX 配置
  sherpaOnnxConfig: {
    sensitivity: 0.5,         // 关键词检测敏感度 (0-1)
    numThreads: 1,            // 线程数
    debug: false,             // 调试模式
    modelPath: 'assets/wasm'
  },
  
  // 智能录音配置
  maxRecordingTime: 15000,    // 最大录音时长(ms)
  minRecordingTime: 1000,     // 最小录音时长(ms)
  autoStopOnSilence: true,    // 自动静音停止
  
  // 静音检测配置
  silenceThreshold: 0.05,     // 静音阈值 (0-1)
  silenceDuration: 2000,      // 静音持续时间(ms)
  checkInterval: 100,         // 音量检测间隔(ms)
  volumeSmoothing: 0.8,       // 音量平滑系数
  
  // 语音合成配置
  enableTTS: true,            // 启用语音合成
  ttsRate: 2.4,              // 语音速度 (0.1-10) - 调整为1.6进一步加快语速
  ttsPitch: 1.0,             // 语音音调 (0-2)
  ttsVolume: 0.8,            // 语音音量 (0-1)
  ttsLang: 'zh-CN',          // 语音语言
  
  // 行为配置
  autoResumeListening: true,  // 对话结束后自动重新监听
  autoRestartAfterError: true, // 错误后自动重启
  enableDebugLog: true
}

// 预设配置方案
export const PRESET_CONFIGS = {
  // 高敏感度配置 - 适合安静环境
  high_sensitivity: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    checkInterval: 2000,
    silenceThreshold: 0.005,
    silenceDuration: 1500,
    minRecordingTime: 800,
    sherpaOnnxConfig: {
      ...DEFAULT_SMART_VOICE_CONFIG.sherpaOnnxConfig,
      sensitivity: 0.3,  // 更高敏感度
      modelPath: 'assets/wasm'
    }
  },
  
  // 低敏感度配置 - 适合嘈杂环境
  low_sensitivity: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    checkInterval: 4000,
    silenceThreshold: 0.02,
    silenceDuration: 3000,
    minRecordingTime: 1500,
    sherpaOnnxConfig: {
      ...DEFAULT_SMART_VOICE_CONFIG.sherpaOnnxConfig,
      sensitivity: 0.7,  // 更低敏感度
      modelPath: 'assets/wasm'
    }
  },
  
  // 快速响应配置 - 优化响应速度
  fast_response: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    checkInterval: 2000,
    recordDuration: 1500,
    silenceDuration: 1500,
    ttsRate: 2.4,
    sherpaOnnxConfig: {
      ...DEFAULT_SMART_VOICE_CONFIG.sherpaOnnxConfig,
      sensitivity: 0.4,  // 平衡敏感度
      modelPath: 'assets/wasm'
    }
  },
  
  // 节能配置 - 降低资源消耗
  power_save: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    wakeWordEngine: 'tencent-asr',  // 使用腾讯云ASR以降低CPU使用
    checkInterval: 5000,
    recordDuration: 2500,
    enableTTS: false,
    checkInterval: 200,
  }
}

// 环境自适应配置
export const ENVIRONMENT_CONFIGS = {
  // 办公室环境
  office: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    silenceThreshold: 0.015,
    silenceDuration: 2500,
    ttsVolume: 0.6,
  },
  
  // 家庭环境
  home: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    silenceThreshold: 0.01,
    silenceDuration: 2000,
    ttsVolume: 0.8,
  },
  
  // 嘈杂环境
  noisy: {
    ...DEFAULT_SMART_VOICE_CONFIG,
    silenceThreshold: 0.03,
    silenceDuration: 3500,
    minRecordingTime: 2000,
    ttsVolume: 1.0,
  }
}

/**
 * 配置管理器类
 */
export class SmartVoiceConfigManager {
  constructor() {
    this.currentConfig = { ...DEFAULT_SMART_VOICE_CONFIG }
    this.configKey = 'smart_voice_config'
    
    // 从本地存储加载配置
    this.loadConfig()
  }

  /**
   * 加载配置
   */
  loadConfig() {
    try {
      const saved = localStorage.getItem(this.configKey)
      if (saved) {
        const savedConfig = JSON.parse(saved)
        this.currentConfig = { ...DEFAULT_SMART_VOICE_CONFIG, ...savedConfig }
        console.log('加载保存的配置:', this.currentConfig)
      }
    } catch (error) {
      console.error('加载配置失败:', error)
      this.currentConfig = { ...DEFAULT_SMART_VOICE_CONFIG }
    }
  }

  /**
   * 保存配置
   */
  saveConfig() {
    try {
      localStorage.setItem(this.configKey, JSON.stringify(this.currentConfig))
      console.log('配置已保存')
    } catch (error) {
      console.error('保存配置失败:', error)
    }
  }

  /**
   * 获取当前配置
   */
  getConfig() {
    return { ...this.currentConfig }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    this.currentConfig = { ...this.currentConfig, ...newConfig }
    this.saveConfig()
    console.log('配置已更新:', this.currentConfig)
    return this.getConfig()
  }

  /**
   * 重置配置
   */
  resetConfig() {
    this.currentConfig = { ...DEFAULT_SMART_VOICE_CONFIG }
    this.saveConfig()
    console.log('配置已重置为默认值')
    return this.getConfig()
  }

  /**
   * 应用预设配置
   */
  applyPreset(presetName) {
    if (PRESET_CONFIGS[presetName]) {
      this.currentConfig = { ...PRESET_CONFIGS[presetName] }
      this.saveConfig()
      console.log(`已应用预设配置: ${presetName}`)
      return this.getConfig()
    } else {
      console.error('未找到预设配置:', presetName)
      return null
    }
  }

  /**
   * 应用环境配置
   */
  applyEnvironment(envName) {
    if (ENVIRONMENT_CONFIGS[envName]) {
      this.currentConfig = { ...this.currentConfig, ...ENVIRONMENT_CONFIGS[envName] }
      this.saveConfig()
      console.log(`已应用环境配置: ${envName}`)
      return this.getConfig()
    } else {
      console.error('未找到环境配置:', envName)
      return null
    }
  }

  /**
   * 获取可用的预设配置列表
   */
  getPresets() {
    return Object.keys(PRESET_CONFIGS).map(key => ({
      key,
      name: this.getPresetDisplayName(key),
      config: PRESET_CONFIGS[key]
    }))
  }

  /**
   * 获取可用的环境配置列表
   */
  getEnvironments() {
    return Object.keys(ENVIRONMENT_CONFIGS).map(key => ({
      key,
      name: this.getEnvironmentDisplayName(key),
      config: ENVIRONMENT_CONFIGS[key]
    }))
  }

  /**
   * 获取预设配置的显示名称
   */
  getPresetDisplayName(key) {
    const names = {
      high_sensitivity: '高敏感度',
      low_sensitivity: '低敏感度', 
      fast_response: '快速响应',
      power_save: '节能模式'
    }
    return names[key] || key
  }

  /**
   * 获取环境配置的显示名称
   */
  getEnvironmentDisplayName(key) {
    const names = {
      office: '办公室',
      home: '家庭',
      noisy: '嘈杂环境'
    }
    return names[key] || key
  }

  /**
   * 验证配置值
   */
  validateConfig(config) {
    const errors = []
    
    // 检查数值范围
    if (config.silenceThreshold < 0 || config.silenceThreshold > 1) {
      errors.push('静音阈值必须在0-1之间')
    }
    
    if (config.ttsRate < 0.1 || config.ttsRate > 10) {
      errors.push('语音速度必须在0.1-10之间')
    }
    
    if (config.ttsPitch < 0 || config.ttsPitch > 2) {
      errors.push('语音音调必须在0-2之间')
    }
    
    if (config.ttsVolume < 0 || config.ttsVolume > 1) {
      errors.push('语音音量必须在0-1之间')
    }
    
    if (config.checkInterval < 100) {
      errors.push('检测间隔不能小于100ms')
    }
    
    if (config.silenceDuration < 500) {
      errors.push('静音持续时间不能小于500ms')
    }
    
    return {
      isValid: errors.length === 0,
      errors
    }
  }

  /**
   * 导出配置
   */
  exportConfig() {
    return {
      timestamp: Date.now(),
      version: '1.0.0',
      config: this.getConfig()
    }
  }

  /**
   * 导入配置
   */
  importConfig(exportedData) {
    try {
      if (exportedData.config) {
        const validation = this.validateConfig(exportedData.config)
        if (validation.isValid) {
          this.updateConfig(exportedData.config)
          return { success: true }
        } else {
          return { success: false, errors: validation.errors }
        }
      } else {
        return { success: false, errors: ['配置格式不正确'] }
      }
    } catch (error) {
      return { success: false, errors: ['导入配置失败: ' + error.message] }
    }
  }
}

// 创建全局配置管理器实例
export const smartVoiceConfigManager = new SmartVoiceConfigManager() 
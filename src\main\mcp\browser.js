// 浏览器 MCP 模块
const fs = require('fs')
const { join } = require('path')
const os = require('os')

let initialized = false
let browser = null
let browserContext = null
let page = null
let availableTools = []

async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('🌐 开始直接初始化浏览器MCP服务...')

    // 设置环境变量
    process.env.PLAYWRIGHT_BROWSERS_PATH = process.env.PLAYWRIGHT_BROWSERS_PATH ||
      join(os.homedir(), 'AppData', 'Local', 'ms-playwright')

    console.log('📂 Playwright浏览器路径:', process.env.PLAYWRIGHT_BROWSERS_PATH)

    // 新的直接集成方式：使用内置的MCP功能，不再依赖外部进程
    console.log('🔧 使用直接集成方式启动浏览器MCP')

    // 动态导入playwright库
    let playwright
    try {
      console.log('📦 动态导入playwright...')
      playwright = require('playwright')
      console.log('✅ playwright导入成功，版本:', playwright.version || '未知')
    } catch (importError) {
      console.error('❌ playwright导入失败:', importError)

      // 尝试安装playwright
      try {
        console.log('📦 尝试安装playwright...')
        const { execSync } = require('child_process')
        execSync('npm install playwright --no-save', { stdio: 'inherit' })

        console.log('📦 重新尝试导入playwright...')
        playwright = require('playwright')
      } catch (installError) {
        console.error('❌ playwright安装和导入失败:', installError)
        throw new Error('无法导入或安装playwright')
      }
    }

    // 创建浏览器实例
    try {
      console.log('🌐 正在启动Chrome浏览器...')

      // 检查是否已安装浏览器
      const browserType = playwright.chromium
      const executablePath = browserType.executablePath()

      if (!fs.existsSync(executablePath)) {
        console.log('⚠️ 浏览器可执行文件不存在，尝试安装 Playwright 浏览器...')

        // 尝试安装浏览器
        try {
          const { execSync } = require('child_process')
          console.log('🔧 执行: npx playwright install chromium')
          execSync('npx playwright install chromium', { stdio: 'inherit' })
          console.log('✅ Playwright 浏览器安装成功')
        } catch (installError) {
          console.error('❌ Playwright 浏览器安装失败:', installError)
          throw new Error('无法安装 Playwright 浏览器')
        }
      }

      // 使用系统默认浏览器作为后备方案
      browser = await playwright.chromium.launch({
        headless: true, // 改为headless模式避免UI问题
        args: ['--no-sandbox', '--disable-setuid-sandbox'], // 添加安全参数
        // 如果指定的浏览器路径不存在，使用系统默认浏览器
        executablePath: fs.existsSync(executablePath) ? executablePath : undefined
      })
      console.log('✅ 浏览器启动成功')
    } catch (browserError) {
      console.error('❌ 浏览器启动失败:', browserError)
      throw browserError
    }

    // 保存浏览器引用
    browserContext = await browser.newContext()
    page = await browserContext.newPage()

    // 定义可用工具
    availableTools = [
      {
        name: 'browser_navigate',
        description: '导航到指定URL',
        inputSchema: {
          type: 'object',
          properties: {
            url: { type: 'string', description: '要导航到的URL' }
          },
          required: ['url']
        }
      },
      {
        name: 'browser_close',
        description: '关闭浏览器',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'browser_get_title',
        description: '获取当前页面标题',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'browser_get_url',
        description: '获取当前页面URL',
        inputSchema: {
          type: 'object',
          properties: {}
        }
      },
      {
        name: 'browser_screenshot',
        description: '截取当前页面截图',
        inputSchema: {
          type: 'object',
          properties: {
            path: { type: 'string', description: '截图保存路径（可选）' }
          }
        }
      }
    ]

    // 注册到管理器
    mcpManager.clients.set('browser', {
      isConnected: true,
      isRealMCP: true,
      configSource: '内置集成',
      availableTools,
      browser,
      browserContext,
      page
    })

    initialized = true
    console.log('✅ 浏览器MCP服务器初始化完成')
    console.log('🛠️ 浏览器MCP可用工具:', availableTools.map(t => t.name))

  } catch (error) {
    console.error('❌ 浏览器MCP服务器初始化失败:', error)

    // 提供降级方案：注册一个基本的浏览器服务
    console.log('🔄 启用浏览器MCP降级方案...')

    // 定义基本工具（使用系统默认浏览器）
    availableTools = [
      {
        name: 'browser_navigate',
        description: '使用系统默认浏览器打开URL',
        inputSchema: {
          type: 'object',
          properties: {
            url: { type: 'string', description: '要打开的URL' }
          },
          required: ['url']
        }
      }
    ]

    // 注册降级服务
    mcpManager.clients.set('browser', {
      isConnected: false,  // 标记为未完全连接
      isRealMCP: false,    // 标记为降级服务
      configSource: '降级方案',
      availableTools,
      fallbackMode: true   // 标记为降级模式
    })

    initialized = true  // 标记为已初始化（降级模式）
    console.log('✅ 浏览器MCP降级方案启用成功')
  }
}

function getConnectionStatus() {
  return {
    connected: initialized && browser !== null,
    service: 'browser',
    tools: availableTools.length
  }
}

function getAvailableTools() {
  return availableTools.map(tool => ({
    name: tool.name,
    description: tool.description,
    service: 'browser'
  }))
}

async function callTool(toolName, args) {
  if (!initialized) {
    throw new Error('浏览器MCP服务未初始化')
  }

  // 检查是否为降级模式
  if (!browser) {
    console.log('🔄 使用浏览器MCP降级模式')
    return await callToolFallback(toolName, args)
  }

  try {
    console.log(`🌐 调用浏览器工具: ${toolName}`)
    console.log(`🌐 工具参数:`, args)

    let result
    switch (toolName) {
      case 'browser_navigate':
        try {
          const url = args.url
          console.log(`🌐 [内置MCP] 导航到 ${url}`)
          await page.goto(url, { waitUntil: 'domcontentloaded' })
          result = { success: true, message: `成功打开网页 ${url}` }
        } catch (error) {
          console.error('❌ [内置MCP] 导航错误:', error)
          result = { success: false, error: `导航失败: ${error.message}` }
        }
        break

      case 'browser_close':
        try {
          console.log('🌐 [内置MCP] 关闭浏览器')
          await browser.close()
          browser = null
          browserContext = null
          page = null
          initialized = false
          result = { success: true, message: '浏览器已关闭' }
        } catch (error) {
          console.error('❌ [内置MCP] 关闭浏览器错误:', error)
          result = { success: false, error: `关闭浏览器失败: ${error.message}` }
        }
        break

      case 'browser_get_title':
        try {
          const title = await page.title()
          result = { success: true, title }
        } catch (error) {
          console.error('❌ [内置MCP] 获取标题错误:', error)
          result = { success: false, error: `获取标题失败: ${error.message}` }
        }
        break

      case 'browser_get_url':
        try {
          const url = page.url()
          result = { success: true, url }
        } catch (error) {
          console.error('❌ [内置MCP] 获取URL错误:', error)
          result = { success: false, error: `获取URL失败: ${error.message}` }
        }
        break

      case 'browser_screenshot':
        try {
          const screenshotPath = args.path || join(os.tmpdir(), `screenshot-${Date.now()}.png`)
          await page.screenshot({ path: screenshotPath })
          result = { success: true, path: screenshotPath, message: `截图已保存到 ${screenshotPath}` }
        } catch (error) {
          console.error('❌ [内置MCP] 截图错误:', error)
          result = { success: false, error: `截图失败: ${error.message}` }
        }
        break

      default:
        result = { success: false, error: `未知的浏览器工具: ${toolName}` }
    }

    console.log(`🌐 工具调用结果:`, result)
    return result

  } catch (error) {
    console.error(`❌ 浏览器工具调用失败: ${toolName}`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 降级模式的工具调用
 */
async function callToolFallback(toolName, args) {
  try {
    console.log(`🔄 [降级模式] 调用浏览器工具: ${toolName}`)

    switch (toolName) {
      case 'browser_navigate':
        const url = args.url
        console.log(`🔄 [降级模式] 使用系统默认浏览器打开: ${url}`)

        // 使用系统默认浏览器打开URL
        const { shell } = require('electron')
        await shell.openExternal(url)

        return {
          success: true,
          message: `已使用系统默认浏览器打开: ${url}`,
          fallbackMode: true
        }

      default:
        throw new Error(`降级模式不支持工具: ${toolName}`)
    }
  } catch (error) {
    console.error(`❌ [降级模式] 工具调用失败: ${toolName}`, error)
    throw error
  }
}

async function cleanup() {
  console.log('🧹 清理浏览器MCP资源...')

  if (browser) {
    try {
      await browser.close()
    } catch (error) {
      console.error('清理浏览器失败:', error)
    }
    browser = null
  }

  if (browserContext) {
    browserContext = null
  }

  if (page) {
    page = null
  }

  availableTools = []
  initialized = false
  console.log('✅ 浏览器MCP资源清理完成')
}

module.exports = {
  initialize,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
}
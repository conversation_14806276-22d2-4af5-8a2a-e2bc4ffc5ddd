@echo off
REM Outlook Calendar MCP Server Startup Script
REM Auto-generated by install-outlook-calendar-mcp.js

echo 📅 启动Outlook日历MCP服务器...

REM 检查Node.js是否可用
where node >nul 2>nul
if %ERRORLEVEL% NEQ 0 (
    echo ❌ 未找到Node.js，请确保Node.js已安装并添加到PATH
    exit /b 1
)

REM 检查outlook-calendar-mcp是否存在
if not exist "C:\Akazam\Code\2025\ai-cognidesk-client\node_modules\outlook-calendar-mcp\src\index.js" (
    echo ❌ 未找到outlook-calendar-mcp，请运行 npm install outlook-calendar-mcp
    exit /b 1
)

REM 启动MCP服务器
echo 🚀 正在启动Outlook日历MCP服务器...
node "C:\Akazam\Code\2025\ai-cognidesk-client\node_modules\outlook-calendar-mcp\src\index.js" %*

if %ERRORLEVEL% NEQ 0 (
    echo ❌ Outlook日历MCP服务器启动失败
    exit /b %ERRORLEVEL%
)

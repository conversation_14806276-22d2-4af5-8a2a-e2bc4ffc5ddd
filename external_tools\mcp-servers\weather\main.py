# -*- coding: utf-8 -*-
"""
Author: Mr.Car
Date: 2025-03-20 20:18:33
"""
from mcp.server.fastmcp import FastMCP
import httpx
import os
from dotenv import load_dotenv

import json
import base64
import time
from cryptography.hazmat.primitives import serialization

# 加载环境变量
load_dotenv()

# 初始化 FastMCP 服务器
server = FastMCP("weather")


def generate_jwt(kid, project_id, exp_seconds=3600):
    """
    生成和风天气API的JWT token

    Args:
        kid: 凭据ID
        project_id: 项目ID
        exp_seconds: 过期时间（秒），默认1小时

    Returns:
        JWT token字符串
    """
    private_key_pem = """-----BEGIN PRIVATE KEY-----
MC4CAQAwBQYDK2VwBCIEINpTbKxi3qikJ/tmrFpmOPYtNbvMkPAcvFT/s4cTmLZ2
-----END PRIVATE KEY-----"""
    # 当前时间戳，减去30秒防止时间误差
    current_time = int(time.time())
    iat = current_time - 30
    exp = current_time + exp_seconds

    # Header
    header = {
        "alg": "EdDSA",
        "kid": kid
    }

    # Payload
    payload = {
        "sub": project_id,
        "iat": iat,
        "exp": exp
    }

    # Base64URL编码
    def base64url_encode(data):
        json_str = json.dumps(data, separators=(',', ':'))
        return base64.urlsafe_b64encode(json_str.encode()).decode().rstrip('=')

    # 编码Header和Payload
    encoded_header = base64url_encode(header)
    encoded_payload = base64url_encode(payload)

    # 要签名的消息
    message = f"{encoded_header}.{encoded_payload}"

    # 加载私钥
    private_key = serialization.load_pem_private_key(
        private_key_pem.encode(),
        password=None
    )

    # 签名
    signature = private_key.sign(message.encode())

    # Base64URL编码签名
    encoded_signature = base64.urlsafe_b64encode(signature).decode().rstrip('=')

    # 组合JWT
    jwt_token = f"{encoded_header}.{encoded_payload}.{encoded_signature}"

    return jwt_token


@server.tool()
async def get_weather_forecast(
        adm: str,
        city: str,
        days: str
) -> dict:
    """
    获取指定城市的天气预报信息

    Args:
        adm (str): 城市的上级行政区划，例如省份或直辖市名称。
        city (str): 城市或地区名。
        days (str): 预报天数，支持最多30天预报，可选值：
            - "3d"：3天预报
            - "7d"：7天预报
            - "10d"：10天预报
            - "15d"：15天预报
            - "30d"：30天预报

    Returns:
        dict: 包含天气预报信息的字典，通常包括日期、天气状况、气温、风速等信息。
    """
    # 参数验证
    valid_days = {"3d", "7d", "10d", "15d", "30d"}
    if days not in valid_days:
        return {"error": f"无效的天数参数，支持的值: {', '.join(valid_days)}"}

    if not adm or not city:
        return {"error": "城市和行政区划参数不能为空"}

    try:
        # 生成 JWT token
        jwt = generate_jwt("CEGW84RGPF", "37KW6Q57EC", 60)

        # 创建 HTTP 客户端配置
        headers = {"Authorization": f"Bearer {jwt}"}
        base_url = "https://mm5u9vbdd9.re.qweatherapi.com"

        # 异步获取地理位置信息
        async with httpx.AsyncClient(timeout=10.0) as client:
            geo_res = await client.get(
                f"{base_url}/geo/v2/city/lookup",
                headers=headers,
                params={"adm": adm, "location": city}
            )
            geo_res.raise_for_status()
            geo_data = geo_res.json()

            # 检查地理位置查询结果
            if geo_data.get("code") != "200":
                return {"error": f"地理位置查询失败: {geo_data.get('message', '未知错误')}"}

            locations = geo_data.get("location", [])
            if not locations:
                return {"error": f"未找到城市: {adm} {city}"}

            geo_id = locations[0]["id"]

            # 异步获取天气信息
            weather_res = await client.get(
                f"{base_url}/v7/weather/{days}",
                headers=headers,
                params={"location": geo_id}
            )
            weather_res.raise_for_status()
            weather_data = weather_res.json()

            # 检查天气查询结果
            if weather_data.get("code") != "200":
                return {"error": f"天气信息查询失败: {weather_data.get('message', '未知错误')}"}

            return weather_data

    except httpx.TimeoutException:
        return {"error": "请求超时，请稍后重试"}
    except httpx.HTTPStatusError as e:
        return {"error": f"HTTP请求失败: {e.response.status_code}"}
    except httpx.RequestError as e:
        return {"error": f"网络请求错误: {str(e)}"}
    except KeyError as e:
        return {"error": f"响应数据格式错误，缺少字段: {str(e)}"}
    except Exception as e:
        return {"error": f"未知错误: {str(e)}"}


# 运行服务器
if __name__ == "__main__":
    server.run(transport="stdio")

/**
 * 阿里云ASR WebSocket语音识别器
 * 基于阿里云智能语音交互实时语音识别WebSocket API
 * 
 * 🎯 已优化配置用于检测较大声音并过滤环境杂音
 */

import { ALIYUN_ASR_CONFIG, validateAliyunConfig, ALIYUN_DEBUG_CONFIG } from './aliyun-asr-config.js'
import CryptoJS from 'crypto-js'

// 阿里云ASR状态常量
export const ALIYUN_ASR_STATES = {
  IDLE: 'idle',
  CONNECTING: 'connecting',
  CONNECTED: 'connected',
  AUTHENTICATING: 'authenticating',
  AUTHENTICATED: 'authenticated',
  STARTING: 'starting',
  LISTENING: 'listening',
  RECOGNIZING: 'recognizing',
  STOPPING: 'stopping',
  STOPPED: 'stopped',
  ERROR: 'error',
  DISCONNECTED: 'disconnected'
}

// 阿里云ASR消息类型
export const ALIYUN_MESSAGE_TYPES = {
  START_TRANSCRIPTION: 'StartTranscription',
  STOP_TRANSCRIPTION: 'StopTranscription',
  SENTENCE_BEGIN: 'SentenceBegin',
  SENTENCE_END: 'SentenceEnd',
  TRANSCRIPTION_RESULT_CHANGED: 'TranscriptionResultChanged',
  TRANSCRIPTION_COMPLETED: 'TranscriptionCompleted',
  TASK_FAILED: 'TaskFailed'
}

// 生成UUID
// 生成32位十六进制字符串（阿里云ASR要求）
const generateMessageId = () => {
  const chars = '0123456789abcdef'
  let result = ''
  for (let i = 0; i < 32; i++) {
    result += chars[Math.floor(Math.random() * chars.length)]
  }
  return result
}

const generateUUID = () => {
  return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function(c) {
    var r = Math.random() * 16 | 0, v = c == 'x' ? r : (r & 0x3 | 0x8)
    return v.toString(16)
  })
}

// 生成时间戳
const generateTimestamp = () => {
  return new Date().toISOString()
}

// 生成签名
const generateSignature = (stringToSign, secretKey) => {
  return CryptoJS.HmacSHA1(stringToSign, secretKey).toString(CryptoJS.enc.Base64)
}

// 生成Token
const generateToken = (accessKeyId, accessKeySecret, appkey) => {
  // 使用ISO 8601格式的时间戳，阿里云API要求这种格式
  const timestamp = new Date().toISOString().replace(/\.\d{3}Z$/, 'Z')
  const nonce = generateUUID()
  
  // 按照阿里云API规范构造参数
  const params = {
    AccessKeyId: accessKeyId,
    Action: 'CreateToken',
    Format: 'JSON',
    RegionId: 'cn-shanghai',
    SignatureMethod: 'HMAC-SHA1',
    SignatureNonce: nonce,
    SignatureVersion: '1.0',
    Timestamp: timestamp,
    Version: '2019-02-28'
  }
  
  // 按字母顺序排序参数
  const sortedKeys = Object.keys(params).sort()
  const sortedParams = {}
  sortedKeys.forEach(key => {
    sortedParams[key] = params[key]
  })
  
  // 构造规范化的查询字符串
  const canonicalizedQueryString = Object.keys(sortedParams)
    .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(sortedParams[key])}`)
    .join('&')
  
  // 构造待签名字符串
  const stringToSign = `GET&${encodeURIComponent('/')}&${encodeURIComponent(canonicalizedQueryString)}`
  
  // 生成签名
  const signature = generateSignature(stringToSign, accessKeySecret + '&')
  
  // 构造Token请求URL
  const tokenUrl = `https://nls-meta.cn-shanghai.aliyuncs.com/?${canonicalizedQueryString}&Signature=${encodeURIComponent(signature)}`
  
  return fetch(tokenUrl)
    .then(response => response.json())
    .then(data => {
      if (data.Token) {
        return data.Token.Id
      } else {
        throw new Error(`Token获取失败: ${data.Message || '未知错误'}`)
      }
    })
}

// 主要的阿里云WebSocket语音识别器类
export class AliyunWebSocketSpeechRecognizer {
  constructor(params = {}, isLog = false) {
    // 验证配置
    const configValidation = validateAliyunConfig()
    if (!configValidation.valid) {
      console.error('❌ 阿里云ASR配置验证失败:', configValidation.errors)
      throw new Error('阿里云ASR配置验证失败')
    }
    
    // 阿里云ASR配置参数
    this.params = {
      accessKeyId: ALIYUN_ASR_CONFIG.accessKeyId,
      accessKeySecret: ALIYUN_ASR_CONFIG.accessKeySecret,
      appkey: ALIYUN_ASR_CONFIG.appkey,
      websocketUrl: ALIYUN_ASR_CONFIG.websocketUrl,
      region: ALIYUN_ASR_CONFIG.region,
      hotwordId: ALIYUN_ASR_CONFIG.hotwordId,
      ...ALIYUN_ASR_CONFIG.audioConfig,
      ...ALIYUN_ASR_CONFIG.recognitionConfig,
      ...params
    }
    
    console.log('阿里云ASR配置 - 强化杂音过滤:', {
      accessKeyId: this.params.accessKeyId ? this.params.accessKeyId.substring(0, 10) + '...' : '未设置',
      appkey: this.params.appkey,
      websocketUrl: this.params.websocketUrl,
      region: this.params.region,
      sampleRate: this.params.sampleRate,
      format: this.params.format,
      channels: this.params.channels,
      bitsPerSample: this.params.bitsPerSample,
      enablePunctuationPrediction: this.params.enablePunctuationPrediction,
      enableIntermediateResult: this.params.enableIntermediateResult,
      enableInverseTextNormalization: this.params.enableInverseTextNormalization,
      enableDisfluency: this.params.enableDisfluency,
      enableVoiceDetection: this.params.enableVoiceDetection,
      maxEndSilence: this.params.maxEndSilence,
      maxSingleSegmentTime: this.params.maxSingleSegmentTime,
      noiseThreshold: this.params.noiseThreshold,
      note: '已优化为检测较大声音，过滤环境杂音'
    })
    
    // 状态管理
    this.currentState = ALIYUN_ASR_STATES.IDLE
    this.isLog = isLog
    this.taskId = null
    this.token = null
    this.websocket = null
    this.audioData = []
    this.isCanSendData = false
    this.isNormalEndStop = false
    this.messageSequence = 0
    
    // 重连配置
    this.reconnectAttempts = 0
    this.maxReconnectAttempts = 5
    this.reconnectDelay = 1000
    this.isReconnecting = false
    
    // 心跳配置
    this.heartbeatInterval = null
    this.heartbeatIntervalMs = 10000 // 🚀 优化：10秒心跳间隔，防止IDLE_TIMEOUT
    
    // 回调函数
    this.OnRecognitionStart = null
    this.OnSentenceBegin = null
    this.OnRecognitionResultChange = null
    this.OnSentenceEnd = null
    this.OnRecognitionComplete = null
    this.OnError = null
    this.OnRecorderStop = null
    this.OnStateChange = null
    this.OnConnectionStatusChange = null
    
    // 绑定方法
    this.handleWebSocketMessage = this.handleWebSocketMessage.bind(this)
    this.handleWebSocketOpen = this.handleWebSocketOpen.bind(this)
    this.handleWebSocketClose = this.handleWebSocketClose.bind(this)
    this.handleWebSocketError = this.handleWebSocketError.bind(this)
  }
  
  // 更新状态
  updateState(newState) {
    const oldState = this.currentState
    this.currentState = newState
    
    this.isLog && console.log(`🔄 阿里云ASR状态变更: ${oldState} -> ${newState}`)
    
    if (this.OnStateChange) {
      this.OnStateChange(newState, oldState)
    }
  }
  
  // 获取Token
  async getToken() {
    try {
      this.isLog && console.log('🔑 获取阿里云ASR Token...')
      
      if (!this.token) {
        this.token = await generateToken(
          this.params.accessKeyId,
          this.params.accessKeySecret,
          this.params.appkey
        )
        this.isLog && console.log('✅ Token获取成功:', this.token.substring(0, 20) + '...')
      }
      
      return this.token
    } catch (error) {
      console.error('❌ Token获取失败:', error)
      throw error
    }
  }
  
  // 连接WebSocket
  async connectWebSocket() {
    try {
      this.updateState(ALIYUN_ASR_STATES.CONNECTING)
      
      // 获取Token
      const token = await this.getToken()
      
      // 构造WebSocket URL
      const wsUrl = `${this.params.websocketUrl}?token=${token}`
      
      this.isLog && console.log('🔗 连接阿里云ASR WebSocket:', wsUrl)
      
      // 创建WebSocket连接
      this.websocket = new WebSocket(wsUrl)
      
      // 设置事件监听器
      this.websocket.onopen = this.handleWebSocketOpen
      this.websocket.onmessage = this.handleWebSocketMessage
      this.websocket.onclose = this.handleWebSocketClose
      this.websocket.onerror = this.handleWebSocketError
      
      // 等待连接
      return new Promise((resolve, reject) => {
        const timeout = setTimeout(() => {
          reject(new Error('WebSocket连接超时'))
        }, 10000)
        
        this.websocket.onopen = (event) => {
          clearTimeout(timeout)
          this.handleWebSocketOpen(event)
          resolve()
        }
        
        this.websocket.onerror = (error) => {
          clearTimeout(timeout)
          this.handleWebSocketError(error)
          reject(error)
        }
      })
    } catch (error) {
      console.error('❌ WebSocket连接失败:', error)
      this.updateState(ALIYUN_ASR_STATES.ERROR)
      throw error
    }
  }
  
  // 处理WebSocket打开
  handleWebSocketOpen(event) {
    this.isLog && console.log('✅ 阿里云ASR WebSocket连接成功')
    this.updateState(ALIYUN_ASR_STATES.CONNECTED)
    
    // 重置重连计数
    this.reconnectAttempts = 0
    this.isReconnecting = false
    
    // 🚀 优化：启用心跳机制，防止IDLE_TIMEOUT
    this.startHeartbeat()
    
    if (this.OnConnectionStatusChange) {
      this.OnConnectionStatusChange('connected')
    }
  }
  
  // 处理WebSocket消息
  handleWebSocketMessage(event) {
    try {
      const message = JSON.parse(event.data)
      this.isLog && console.log('📨 收到阿里云ASR消息:', message)
      
      switch (message.header?.name) {
        case ALIYUN_MESSAGE_TYPES.SENTENCE_BEGIN:
          this.handleSentenceBegin(message)
          break
        case ALIYUN_MESSAGE_TYPES.SENTENCE_END:
          this.handleSentenceEnd(message)
          break
        case ALIYUN_MESSAGE_TYPES.TRANSCRIPTION_RESULT_CHANGED:
          this.handleTranscriptionResultChanged(message)
          break
        case ALIYUN_MESSAGE_TYPES.TRANSCRIPTION_COMPLETED:
          this.handleTranscriptionCompleted(message)
          break
        case ALIYUN_MESSAGE_TYPES.TASK_FAILED:
          this.handleTaskFailed(message)
          break
        default:
          this.isLog && console.log('🔄 未知消息类型:', message.header?.name)
      }
    } catch (error) {
      console.error('❌ 处理WebSocket消息失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 处理WebSocket关闭
  handleWebSocketClose(event) {
    this.isLog && console.log('🔌 阿里云ASR WebSocket连接关闭:', event.code, event.reason)
    this.updateState(ALIYUN_ASR_STATES.DISCONNECTED)
    
    // 停止心跳
    this.stopHeartbeat()
    
    if (this.OnConnectionStatusChange) {
      this.OnConnectionStatusChange('disconnected')
    }
    
    // 如果不是正常关闭，尝试重连
    if (!this.isNormalEndStop && !this.isReconnecting && this.reconnectAttempts < this.maxReconnectAttempts) {
      this.attemptReconnect()
    }
  }
  
  // 处理WebSocket错误
  handleWebSocketError(error) {
    console.error('❌ 阿里云ASR WebSocket错误:', error)
    this.updateState(ALIYUN_ASR_STATES.ERROR)
    
    if (this.OnError) {
      this.OnError(error)
    }
  }
  
  // 尝试重连
  async attemptReconnect() {
    if (this.isReconnecting) return
    
    this.isReconnecting = true
    this.reconnectAttempts++
    
    this.isLog && console.log(`🔄 尝试重连阿里云ASR (${this.reconnectAttempts}/${this.maxReconnectAttempts})...`)
    
    // 等待重连延迟
    await new Promise(resolve => setTimeout(resolve, this.reconnectDelay * this.reconnectAttempts))
    
    try {
      await this.connectWebSocket()
      this.isLog && console.log('✅ 重连成功')
    } catch (error) {
      console.error('❌ 重连失败:', error)
      
      if (this.reconnectAttempts >= this.maxReconnectAttempts) {
        this.isLog && console.log('❌ 达到最大重连次数，停止重连')
        this.isReconnecting = false
        if (this.OnError) {
          this.OnError(new Error('达到最大重连次数'))
        }
      } else {
        this.attemptReconnect()
      }
    }
  }
  
  // 开始心跳
  startHeartbeat() {
    this.stopHeartbeat()
    this.heartbeatInterval = setInterval(() => {
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        this.sendHeartbeat()
      }
    }, this.heartbeatIntervalMs)
  }
  
  // 停止心跳
  stopHeartbeat() {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
    }
  }
  
  // 发送心跳（改为静音数据保活）
  sendHeartbeat() {
    try {
      // 🚀 优化：阿里云ASR不支持KeepAlive指令，改用静音数据保持连接
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN && this.isCanSendData) {
        // 发送静音音频数据保持连接活跃
        const silentData = new ArrayBuffer(1600) // PCM 16位单声道数据
        const silentArray = new Uint8Array(silentData)
        silentArray.fill(0) // 填充0（静音）
        
        this.websocket.send(silentData)
        this.isLog && console.log('💓 发送静音数据保持连接活跃')
      }
    } catch (error) {
      console.error('❌ 发送心跳静音数据失败:', error)
    }
  }
  
  // 发送消息
  sendMessage(message) {
    if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
      this.websocket.send(JSON.stringify(message))
      this.isLog && console.log('📤 发送消息:', message)
    } else {
      console.error('❌ WebSocket连接不可用')
      throw new Error('WebSocket连接不可用')
    }
  }
  
  // 处理句子开始
  handleSentenceBegin(message) {
    const timestamp = new Date().toISOString()
    this.isLog && console.log(`📝 [${timestamp}] 句子开始:`, message)
    
    if (this.OnSentenceBegin) {
      this.OnSentenceBegin({
        result: message.payload
      })
    }
  }
  
  // 处理句子结束
  handleSentenceEnd(message) {
    const timestamp = new Date().toISOString()
    this.isLog && console.log(`📝 [${timestamp}] 句子结束:`, message)
    
    if (this.OnSentenceEnd) {
      this.OnSentenceEnd({
        result: {
          voice_text_str: message.payload?.result || '',
          begin_time: message.payload?.begin_time || 0,
          end_time: message.payload?.end_time || 0,
          confidence: message.payload?.confidence || 0
        }
      })
    }
  }
  
  // 处理识别结果变化
  handleTranscriptionResultChanged(message) {
    const timestamp = new Date().toISOString()
    this.isLog && console.log(`🔄 [${timestamp}] 识别结果变化:`, {
      result: message.payload?.result || '',
      confidence: message.payload?.confidence || 0
    })
    
    if (this.OnRecognitionResultChange) {
      this.OnRecognitionResultChange({
        result: {
          voice_text_str: message.payload?.result || '',
          begin_time: message.payload?.begin_time || 0,
          end_time: message.payload?.end_time || 0,
          confidence: message.payload?.confidence || 0
        }
      })
    }
  }
  
  // 处理识别完成
  handleTranscriptionCompleted(message) {
    this.isLog && console.log('🏁 识别完成:', message)
    this.updateState(ALIYUN_ASR_STATES.STOPPED)
    
    if (this.OnRecognitionComplete) {
      this.OnRecognitionComplete({
        result: {
          voice_text_str: message.payload?.result || '',
          begin_time: message.payload?.begin_time || 0,
          end_time: message.payload?.end_time || 0,
          confidence: message.payload?.confidence || 0
        }
      })
    }
  }
  
  // 处理任务失败
  handleTaskFailed(message) {
    console.error('❌ 识别任务失败:', message)
    
    // 打印详细的错误信息
    if (message.header) {
      console.error('阿里云ASR失败详情 - Header:', JSON.stringify(message.header, null, 2))
    }
    if (message.payload) {
      console.error('阿里云ASR失败详情 - Payload:', JSON.stringify(message.payload, null, 2))
    }
    
    // 🚀 优化：针对IDLE_TIMEOUT等特定错误进行处理
    const statusText = message.header?.status_text || ''
    if (statusText.includes('IDLE_TIMEOUT')) {
      console.warn('⚠️ 连接空闲超时，尝试重新建立连接')
      // 清理当前连接
      this.stopHeartbeat()
      this.isCanSendData = false
      
      // 如果有错误回调，通知上层进行重连
      if (this.OnError) {
        this.OnError(new Error('连接空闲超时，需要重新启动识别'))
      }
    } else {
      this.updateState(ALIYUN_ASR_STATES.ERROR)
      
      if (this.OnError) {
        this.OnError(new Error(message.payload?.error_message || '识别任务失败'))
      }
    }
  }
  
  // 开始识别
  async start() {
    try {
      const startTime = Date.now()
      this.isLog && console.log('🚀 开始阿里云语音识别...', new Date().toISOString())
      
      // 连接WebSocket
      const connectStartTime = Date.now()
      await this.connectWebSocket()
      const connectEndTime = Date.now()
      this.isLog && console.log(`⏱️ [时间] WebSocket连接耗时: ${connectEndTime - connectStartTime}ms`)
      
      // 生成任务ID（32位十六进制字符串）
      this.taskId = generateMessageId()
      
      // 发送开始识别消息
      const messageStartTime = Date.now()
      const startMessage = {
        header: {
          message_id: generateMessageId(),
          task_id: this.taskId,
          namespace: 'SpeechTranscriber',
          name: ALIYUN_MESSAGE_TYPES.START_TRANSCRIPTION,
          appkey: this.params.appkey,
          status: 20000000,
          status_text: 'success'
        },
        payload: {
          format: this.params.format,
          sample_rate: this.params.sampleRate,
          enable_punctuation_prediction: this.params.enablePunctuationPrediction,
          enable_intermediate_result: this.params.enableIntermediateResult,
          enable_inverse_text_normalization: this.params.enableInverseTextNormalization,
          enable_disfluency: this.params.enableDisfluency,
          enable_voice_detection: this.params.enableVoiceDetection,
          max_end_silence: this.params.maxEndSilence,
          max_single_segment_time: this.params.maxSingleSegmentTime,
          enable_timestamp_alignment: this.params.enableTimestampAlignment,
          enable_first_channel_only: this.params.enableFirstChannelOnly,
          enable_semantic_sentence_detection: this.params.enableSemanticSentenceDetection,
          enable_auto_split: this.params.enableAutoSplit,
          enable_speaker_diarization: this.params.enableSpeakerDiarization,
          vocabulary_id: this.params.hotwordId // 热词ID
        }
      }
      
      this.sendMessage(startMessage)
      this.updateState(ALIYUN_ASR_STATES.STARTING)
      this.isLog && console.log(`⏱️ [时间] 发送StartTranscription消息耗时: ${Date.now() - messageStartTime}ms`)
      
      // 标记可以发送数据
      this.isCanSendData = true
      this.isLog && console.log(`⏱️ [时间] 总启动耗时: ${Date.now() - startTime}ms`)
      
      if (this.OnRecognitionStart) {
        this.OnRecognitionStart({
          task_id: this.taskId
        })
      }
      
      this.isLog && console.log('✅ 阿里云语音识别已启动')
      
    } catch (error) {
      console.error('❌ 启动阿里云语音识别失败:', error)
      this.updateState(ALIYUN_ASR_STATES.ERROR)
      
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 发送音频数据
  sendAudioData(audioData) {
    if (!this.isCanSendData) {
      console.warn('⚠️ 无法发送音频数据：识别未启动')
      return
    }
    
    if (!this.websocket || this.websocket.readyState !== WebSocket.OPEN) {
      console.warn('⚠️ 无法发送音频数据：WebSocket连接不可用')
      return
    }
    
    try {
      // 发送二进制音频数据
      this.websocket.send(audioData)
      this.isLog && console.log('🎵 发送音频数据:', audioData.byteLength, 'bytes')
    } catch (error) {
      console.error('❌ 发送音频数据失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 停止识别
  stop() {
    try {
      this.isLog && console.log('🛑 停止阿里云语音识别')
      
      this.isCanSendData = false
      this.isNormalEndStop = true
      this.updateState(ALIYUN_ASR_STATES.STOPPING)
      
      // 发送停止识别消息
      if (this.websocket && this.websocket.readyState === WebSocket.OPEN) {
        const stopMessage = {
          header: {
            message_id: generateMessageId(),
            task_id: this.taskId,
            namespace: 'SpeechTranscriber',
            name: ALIYUN_MESSAGE_TYPES.STOP_TRANSCRIPTION,
            appkey: this.params.appkey,
            status: 20000000,
            status_text: 'success'
          },
          payload: {}
        }
        
        this.sendMessage(stopMessage)
      }
      
      // 心跳已禁用
      // this.stopHeartbeat()
      
      // 关闭WebSocket连接
      if (this.websocket) {
        this.websocket.close()
        this.websocket = null
      }
      
      this.updateState(ALIYUN_ASR_STATES.STOPPED)
      
      if (this.OnRecorderStop) {
        this.OnRecorderStop()
      }
      
      this.isLog && console.log('✅ 阿里云语音识别已停止')
      
    } catch (error) {
      console.error('❌ 停止阿里云语音识别失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 销毁实例
  destroy() {
    this.stop()
    
    // 清理资源
    this.token = null
    this.taskId = null
    this.audioData = []
    this.messageSequence = 0
    this.reconnectAttempts = 0
    this.isReconnecting = false
    
    // 清理回调
    this.OnRecognitionStart = null
    this.OnSentenceBegin = null
    this.OnRecognitionResultChange = null
    this.OnSentenceEnd = null
    this.OnRecognitionComplete = null
    this.OnError = null
    this.OnRecorderStop = null
    this.OnStateChange = null
    this.OnConnectionStatusChange = null
    
    this.isLog && console.log('🗑️ 阿里云语音识别器已销毁')
  }
}

console.log('✅ 阿里云ASR WebSocket语音识别器已加载') 
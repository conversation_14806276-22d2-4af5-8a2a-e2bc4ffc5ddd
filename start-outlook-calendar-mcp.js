#!/usr/bin/env node

// 启动Outlook日历MCP服务的脚本
const path = require('path');
const os = require('os');
const fs = require('fs');
const { spawn, exec } = require('child_process');

console.log('🚀 启动Outlook日历MCP服务...');

// 检查Windows环境
function checkWindows() {
  if (process.platform !== 'win32') {
    console.error('❌ Outlook日历MCP仅支持Windows系统');
    return false;
  }
  return true;
}

// 检查outlook-calendar-mcp包是否已安装
function checkOutlookMCP() {
  try {
    require.resolve('outlook-calendar-mcp');
    console.log('✅ outlook-calendar-mcp包已安装');
    return true;
  } catch (err) {
    console.error('❌ 未找到outlook-calendar-mcp包');
    console.error('💡 请运行: npm install outlook-calendar-mcp');
    return false;
  }
}

// 检查Outlook是否正在运行
async function checkOutlookRunning() {
  return new Promise((resolve) => {
    exec('tasklist | findstr OUTLOOK', (error, stdout, stderr) => {
      if (stdout && stdout.includes('OUTLOOK.EXE')) {
        console.log('✅ Microsoft Outlook正在运行');
        return resolve(true);
      } else {
        console.warn('⚠️ Microsoft Outlook未运行');
        console.warn('💡 请先启动Outlook并登录您的账户');
        return resolve(false);
      }
    });
  });
}

// 启动MCP服务器
async function startMCPServer() {
  try {
    // 环境检查
    if (!checkWindows()) {
      process.exit(1);
    }

    if (!checkOutlookMCP()) {
      console.log('📦 尝试自动安装outlook-calendar-mcp...');
      try {
        const { execSync } = require('child_process');
        execSync('npm install outlook-calendar-mcp@latest --save', { stdio: 'inherit' });
        console.log('✅ outlook-calendar-mcp安装完成');
      } catch (installError) {
        console.error('❌ 自动安装失败:', installError.message);
        process.exit(1);
      }
    }

    // 检查Outlook状态
    const outlookRunning = await checkOutlookRunning();
    if (!outlookRunning) {
      console.warn('⚠️ 建议先启动Outlook，但将继续启动MCP服务');
    }

    // 获取outlook-calendar-mcp的路径
    const outlookMcpPath = require.resolve('outlook-calendar-mcp/src/index.js');
    console.log('📂 Outlook Calendar MCP路径:', outlookMcpPath);

    console.log('🚀 启动Outlook日历MCP服务器...');
    console.log('🔧 配置: Windows Outlook集成');
    
    // 启动子进程
    const childProcess = spawn('node', [outlookMcpPath], {
      stdio: 'inherit',
      shell: true,
      env: {
        ...process.env,
        // 可以添加特定的环境变量
      }
    });

    // 处理进程事件
    childProcess.on('spawn', () => {
      console.log('✅ Outlook日历MCP服务器已启动');
      console.log('--SERVER-STATUS: Outlook Calendar MCP Server started');
      console.log('💡 提示: 首次使用时，Outlook可能会显示安全提示，请选择"允许"');
    });

    childProcess.on('error', (error) => {
      console.error('❌ 启动失败:', error.message);
      console.error('💡 请确保：');
      console.error('   1. Microsoft Outlook已安装');
      console.error('   2. outlook-calendar-mcp包已正确安装');
      console.error('   3. Node.js版本兼容 (需要14.x或更高)');
      process.exit(1);
    });

    childProcess.on('exit', (code, signal) => {
      if (signal) {
        console.log(`👋 服务器进程被信号 ${signal} 终止`);
      } else {
        console.log(`👋 服务器进程退出，退出码: ${code}`);
      }
      
      if (code !== 0 && code !== null) {
        console.error('❌ 服务器异常退出');
        console.error('💡 可能的原因：');
        console.error('   1. Outlook未运行或未登录');
        console.error('   2. Outlook安全设置阻止了脚本访问');
        console.error('   3. Office版本不兼容');
      }
      
      process.exit(code || 0);
    });

    // 优雅关闭处理
    process.on('SIGINT', () => {
      console.log('🛑 收到终止信号，关闭Outlook日历MCP服务...');
      childProcess.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
      console.log('🛑 收到终止信号，关闭Outlook日历MCP服务...');
      childProcess.kill('SIGTERM');
    });

    // 处理Windows特有的关闭事件
    if (process.platform === 'win32') {
      process.on('SIGBREAK', () => {
        console.log('🛑 收到中断信号，关闭Outlook日历MCP服务...');
        childProcess.kill('SIGTERM');
      });
    }

    // 保持进程运行
    await new Promise(() => {});

  } catch (error) {
    console.error('❌ Outlook日历MCP服务启动失败:', error.message);
    console.error('📋 完整错误信息:', error);
    process.exit(1);
  }
}

// 错误处理
process.on('uncaughtException', (error) => {
  console.error('💥 未捕获的异常:', error.message);
  console.error('📋 堆栈跟踪:', error.stack);
  process.exit(1);
});

process.on('unhandledRejection', (reason, promise) => {
  console.error('💥 未处理的Promise拒绝:', reason);
  process.exit(1);
});

// 启动服务器
startMCPServer().catch(err => {
  console.error('💥 致命错误:', err.message);
  process.exit(1);
}); 
import {createClient} from '@libsql/client';

const client = createClient({
    url: 'file:local.db',
});

// 搜索函数
async function queryList(fileType, fileName, filePreview, pageSize = 10, pageNum = 1) {
    // 强制类型转换和验证
    pageSize = parseInt(pageSize, 10) || 10;
    pageNum = parseInt(pageNum, 10) || 1;
    if (pageSize <= 0) pageSize = 10;
    if (pageNum <= 0) pageNum = 1;

    // 基础查询和参数
    let baseSql = `SELECT
                       id,
                       file_type AS fileType,
                       file_name AS fileName,
                       file_path AS filePath,
                       source_file_path AS sourceFilePath,
                       file_preview AS filePreview,
                       remark,
                       create_time AS createTime
                   FROM user_file
                   WHERE 1 = 1`;
    let countSql = `SELECT COUNT(*) AS total
                    FROM user_file
                    WHERE 1 = 1`;
    let args = [];

    // 动态添加过滤条件
    if (fileType) {
        baseSql += ` AND file_type LIKE ?`;
        countSql += ` AND file_type LIKE ?`;
        args.push(`%${fileType}%`);
    }
    if (fileName) {
        baseSql += ` AND file_name LIKE ?`;
        countSql += ` AND file_name LIKE ?`;
        args.push(`%${fileName}%`);
    }

    if (filePreview) {
        baseSql += ` AND file_preview LIKE ?`;
        countSql += ` AND file_preview LIKE ?`;
        args.push(`%${filePreview}%`);
    }

    // 分页参数计算
    const offset = (pageNum - 1) * pageSize;
    args.push(pageSize, offset);

    // 执行查询
    try {
        // 并行执行查询
        const [countResult, dataResult] = await Promise.all([
            client.execute(countSql, args.slice(0, -2)), // 排除分页参数
            client.execute(baseSql + ` ORDER BY id DESC LIMIT ? OFFSET ?`, args)
        ]);

        return {
            total: countResult.rows[0]?.total || 0,
            rows: dataResult.rows,
            pagination: {
                pageSize,
                pageNum,
                totalPages: Math.ceil((countResult.rows[0]?.total || 0) / pageSize)
            }
        };
    } catch (error) {
        console.error('查询异常:', error);
        throw error;
    }
}


// 使用示例
// 修改后的调用方式示例
(async () => {
    try {
        const result = await queryList(
            "测试",      // 文件名模糊搜索
            "",      // 预览内容过滤
        );
        console.log(`result: ${JSON.stringify(result)}`);
    } catch (error) {
        console.error('查询失败:', error);
    }
})();

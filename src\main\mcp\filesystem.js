const fs = require('fs')
const path = require('path')
const { spawn } = require('child_process')
const { app } = require('electron')
const os = require('os')

let mcpClient = null
let mcpProcess = null
let initialized = false
let realMCPAvailable = false

/**
 * 初始化文件系统 MCP 服务
 */
async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('📁 初始化文件系统 MCP 服务...')

    // 尝试启动真实的MCP服务器
    await initializeRealMCPServer(mcpManager)

    initialized = true
    console.log('✅ 文件系统 MCP 服务初始化完成')
  } catch (error) {
    console.error('❌ 文件系统 MCP 服务初始化失败:', error)
    // 不抛出错误，使用降级方案
    initialized = true
    realMCPAvailable = false
    console.log('⚠️ 使用文件系统MCP降级方案')
  }
}

/**
 * 初始化真实的MCP服务器
 */
async function initializeRealMCPServer(mcpManager) {
  try {
    console.log('🚀 启动真实的文件系统MCP服务器...')

    // 获取用户配置的路径
    const userConfig = mcpManager.getUserConfig()
    let allowedDirs = []

    // 检查用户是否配置了自定义路径
    if (userConfig && userConfig.filePaths && userConfig.filePaths.customPaths &&
      Array.isArray(userConfig.filePaths.customPaths) &&
      userConfig.filePaths.customPaths.length > 0) {
      console.log('🔧 发现用户配置的自定义路径:', userConfig.filePaths.customPaths)
      allowedDirs = [...userConfig.filePaths.customPaths]
    }

    // 如果没有自定义路径，则使用默认的常用目录
    if (allowedDirs.length === 0) {
      const homeDir = os.homedir()
      allowedDirs = [
        path.join(homeDir, 'Downloads'),
        path.join(homeDir, 'Documents'),
        path.join(homeDir, 'Desktop'),
        // 添加一些常用的工作目录
        'C:\\',  // Windows根目录（谨慎使用）
        'D:\\',  // 常见的D盘
      ].filter(dir => {
        try {
          return fs.existsSync(dir)
        } catch {
          return false
        }
      })

      console.log('🔒 MCP文件系统使用默认设置：允许访问常用目录')
    } else {
      console.log('🔓 MCP文件系统使用自定义设置：允许访问指定目录')
    }

    console.log('📁 允许访问的目录:', allowedDirs)

    // 动态导入MCP SDK
    let StdioClientTransport, Client
    try {
      console.log('📦 加载MCP SDK...')
      // 使用动态导入方式加载MCP SDK
      const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
      const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
      StdioClientTransport = stdioModule.StdioClientTransport
      Client = clientModule.Client

      console.log('✅ MCP SDK加载成功')
    } catch (importError) {
      console.error('❌ MCP SDK加载失败:', importError)
      throw new Error('MCP SDK不可用')
    }

    // 使用 NPX 启动 filesystem MCP 服务器
    const npxCommand = process.platform === 'win32' ? 'npx.cmd' : 'npx'
    const args = [
      '-y',
      '@modelcontextprotocol/server-filesystem',
      ...allowedDirs
    ]

    console.log('🚀 启动Filesystem MCP服务器:')
    console.log('  - 命令:', npxCommand)
    console.log('  - 参数:', args)

    // 创建MCP客户端连接
    const transport = new StdioClientTransport({
      command: npxCommand,
      args: args,
      env: process.env
    })

    const client = new Client({
      name: 'nezha-filesystem-client',
      version: '1.0.0'
    }, {
      capabilities: {}
    })

    await client.connect(transport)
    console.log('✅ 文件系统MCP客户端连接成功')

    // 获取可用工具
    const tools = await client.listTools()
    console.log('📋 文件系统MCP可用工具:', tools.tools?.map(t => t.name) || [])

    mcpClient = client
    mcpProcess = transport
    realMCPAvailable = true

    console.log('✅ 真实文件系统MCP服务器初始化完成')
  } catch (error) {
    console.error('❌ 真实MCP服务器初始化失败:', error)
    throw error
  }
}

/**
 * 获取用户配置的搜索路径
 */
function getUserSearchPaths() {
  const { app } = require('electron')
  const os = require('os')

  try {
    // 尝试从存储中读取配置
    const configPath = path.join(app.getPath('userData'), 'config.json')
    if (fs.existsSync(configPath)) {
      const configData = fs.readFileSync(configPath, 'utf8')
      const userConfig = JSON.parse(configData)

      // 检查用户是否配置了自定义路径
      if (userConfig && userConfig.filePaths && userConfig.filePaths.customPaths &&
        Array.isArray(userConfig.filePaths.customPaths) &&
        userConfig.filePaths.customPaths.length > 0) {
        console.log('🔧 使用用户配置的自定义路径:', userConfig.filePaths.customPaths)
        return userConfig.filePaths.customPaths
      }
    }
  } catch (error) {
    console.warn('⚠️ 读取用户配置失败，使用默认路径:', error.message)
  }

  // 使用默认路径 - 扩展搜索范围
  const homedir = os.homedir()
  const defaultPaths = [
    path.join(homedir, 'Downloads'),
    path.join(homedir, 'Desktop'),
    path.join(homedir, 'Documents'),
    path.join(homedir, 'Pictures'),
    path.join(homedir, 'Videos'),
    // 添加一些常用的工作目录
    'C:\\Users\\<USER>\\temp',
    'C:\\tmp',
    'D:\\',
    'E:\\',
    // 用户目录的子目录
    homedir
  ].filter(dir => {
    try {
      return fs.existsSync(dir) && fs.statSync(dir).isDirectory()
    } catch {
      return false
    }
  })

  console.log('🔒 使用扩展的默认搜索路径:', defaultPaths)
  return defaultPaths
}

/**
 * 执行文件搜索
 */
async function executeFileSearch(query, directory = null) {
  try {
    console.log(`🔍 执行文件搜索: "${query}"${directory ? ` 在目录: ${directory}` : ''}`)

    // 🔧 强制使用本地搜索，因为MCP搜索存在问题
    console.log(`🔧 强制使用本地搜索（MCP搜索回退机制有问题）`)
    return await executeLocalSearch(query, directory)
  } catch (error) {
    console.error('❌ 文件搜索失败:', error)
    return {
      success: false,
      error: error.message,
      files: [],
      query: query
    }
  }
}

/**
 * 使用真实MCP执行搜索
 */
async function executeRealMCPSearch(query, directory = null) {
  try {
    console.log('🔍 使用真实MCP客户端进行文件搜索')

    // 获取搜索路径
    const searchPaths = getUserSearchPaths()
    let searchDirectories = []

    if (directory) {
      // 验证目录是否在允许范围内
      const isAllowed = searchPaths.some(allowedPath => {
        const resolvedDirectory = path.resolve(directory)
        const resolvedAllowed = path.resolve(allowedPath)
        return resolvedDirectory.startsWith(resolvedAllowed)
      })

      if (isAllowed) {
        searchDirectories = [directory]
      } else {
        console.warn(`⚠️ 指定目录不在允许范围内: ${directory}，使用所有允许路径`)
        searchDirectories = searchPaths
      }
    } else {
      // 如果没有指定目录，搜索所有允许的路径
      searchDirectories = searchPaths
    }

    console.log(`🔍 MCP搜索目录列表: [${searchDirectories.join(', ')}]`)

    // 增强查询处理：智能文件类型猜测
    const originalQuery = query
    let enhancedQueries = [query] // 原始查询始终是第一优先级

    // 如果查询不包含文件扩展名，添加智能后缀猜测
    if (!query.includes('.')) {
      console.log('🔍 检测到查询没有文件扩展名，添加智能后缀猜测')

      // 根据查询内容智能推断可能的文件类型
      const fileTypeGuesses = []

      // 图片相关猜测
      if (query.includes('图片') || query.includes('照片') || query.includes('壁纸') ||
        query.includes('相片') || query.includes('截图') || query.includes('图像')) {
        fileTypeGuesses.push(...['.jpg', '.jpeg', '.png', '.gif', '.bmp'])
      }

      // 文档相关猜测
      if (query.includes('文档') || query.includes('报告') || query.includes('文件')) {
        fileTypeGuesses.push(...['.doc', '.docx', '.pdf', '.txt'])
      }

      // 表格相关猜测
      if (query.includes('表格') || query.includes('工作表') || query.includes('数据')) {
        fileTypeGuesses.push(...['.xls', '.xlsx', '.csv'])
      }

      // 演示文稿相关猜测
      if (query.includes('演示') || query.includes('幻灯片') || query.includes('课件')) {
        fileTypeGuesses.push(...['.ppt', '.pptx'])
      }

      // 如果没有特定类型暗示，添加所有常见扩展名
      if (fileTypeGuesses.length === 0) {
        fileTypeGuesses.push(...[
          '.jpg', '.jpeg', '.png', '.pdf', '.doc', '.docx',
          '.xls', '.xlsx', '.txt', '.ppt', '.pptx'
        ])
      }

      // 为每个推测的扩展名创建增强查询
      fileTypeGuesses.forEach(ext => {
        enhancedQueries.push(query + ext)
      })

      // 还要添加不区分大小写的查询
      enhancedQueries.push(query.toLowerCase())
      if (query !== query.toUpperCase()) {
        enhancedQueries.push(query.toUpperCase())
      }

      console.log(`🔍 增强后的查询列表: [${enhancedQueries.join(', ')}]`)
    }

    console.log(`🔍 增强查询列表:`, enhancedQueries)

    let foundResults = false
    let allResults = []

    // 在所有搜索目录中搜索
    for (const searchDir of searchDirectories) {
      console.log(`🔍 在目录 "${searchDir}" 中搜索...`)

      for (const enhancedQuery of enhancedQueries) {
        console.log(`🔍 尝试查询: "${enhancedQuery}" 在 "${searchDir}"`)

        try {
          // 调用MCP的search_files工具
          const searchResult = await mcpClient.callTool({
            name: 'search_files',
            arguments: {
              query: enhancedQuery,
              path: searchDir,
              fuzzy_match: true // 启用模糊匹配
            }
          })

          console.log(`🔍 MCP搜索结果 (${searchDir}):`, searchResult)

          if (searchResult && searchResult.content && searchResult.content.length > 0) {
            try {
              const resultText = searchResult.content[0].text
              const resultData = JSON.parse(resultText)

              if (resultData.files && Array.isArray(resultData.files) && resultData.files.length > 0) {
                console.log(`✅ 在 "${searchDir}" 找到 ${resultData.files.length} 个文件 (查询: "${enhancedQuery}")`)

                const processedFiles = resultData.files.map(file => {
                  const processedFile = {
                    name: path.basename(file.path || file.name || ''),
                    path: file.path || file.name || '',
                    size: file.size || 0,
                    modified: file.modified || file.mtime || new Date(),
                    directory: path.dirname(file.path || file.name || ''),
                    query: enhancedQuery,
                    searchDirectory: searchDir
                  }

                  // 如果是增强查询且找到结果，标记这些文件
                  if (enhancedQuery !== originalQuery) {
                    processedFile.foundByEnhancedQuery = true
                    processedFile.originalQuery = originalQuery
                    processedFile.matchedQuery = enhancedQuery
                  }

                  return processedFile
                })

                allResults = allResults.concat(processedFiles)
                foundResults = true

                // 如果是原始查询找到结果，优先使用
                if (enhancedQuery === originalQuery) {
                  console.log('🔍 原始查询已找到结果，不再尝试增强查询')
                  break
                }
              }
            } catch (parseError) {
              console.warn(`⚠️ 解析MCP搜索结果失败 (${searchDir}):`, parseError)
            }
          }
        } catch (mcpError) {
          console.warn(`⚠️ MCP搜索失败 (查询: "${enhancedQuery}", 目录: "${searchDir}"):`, mcpError.message)
        }
      }

      // 如果在当前目录找到结果且是原始查询，可以提前结束
      if (foundResults && allResults.some(file => file.query === originalQuery)) {
        console.log(`🔍 在 "${searchDir}" 找到原始查询结果，停止搜索其他目录`)
        break
      }
    }

    // 去重并排序
    const uniqueResults = []
    const seenPaths = new Set()

    for (const result of allResults) {
      if (!seenPaths.has(result.path)) {
        seenPaths.add(result.path)
        uniqueResults.push(result)
      }
    }

    // 按文件名相关性排序
    uniqueResults.sort((a, b) => {
      const aScore = a.name.toLowerCase().indexOf(originalQuery.toLowerCase())
      const bScore = b.name.toLowerCase().indexOf(originalQuery.toLowerCase())
      if (aScore !== bScore) {
        return aScore - bScore
      }
      return a.name.localeCompare(b.name)
    })

    console.log(`✅ 真实MCP搜索完成，找到 ${uniqueResults.length} 个文件`)

    if (uniqueResults.length > 0) {
      console.log(`📁 找到的文件:`)
      uniqueResults.slice(0, 5).forEach((file, index) => {
        console.log(`  ${index + 1}. "${file.name}" -> "${file.path}"`)
      })
      if (uniqueResults.length > 5) {
        console.log(`  ... 还有 ${uniqueResults.length - 5} 个文件`)
      }
    }

    return {
      success: true,
      files: uniqueResults,
      searchPaths: searchDirectories,
      query: originalQuery,
      usedRealMCP: true
    }
  } catch (error) {
    console.error('❌ 真实MCP搜索失败:', error)
    // 降级到本地搜索
    console.log('🔄 降级到本地搜索...')
    return await executeLocalSearch(query, directory)
  }
}

/**
 * 执行本地搜索（降级方案）
 */
async function executeLocalSearch(query, directory = null) {
  try {
    console.log('🔍 使用本地搜索（降级方案）')

    // 获取搜索路径
    let searchDirs = []
    if (directory) {
      // 如果指定了目录，验证是否在允许的路径内
      const allowedPaths = getUserSearchPaths()
      const isAllowed = allowedPaths.some(allowedPath => {
        const resolvedDirectory = path.resolve(directory)
        const resolvedAllowed = path.resolve(allowedPath)
        return resolvedDirectory.startsWith(resolvedAllowed)
      })

      if (isAllowed) {
        searchDirs = [directory]
      } else {
        console.warn(`⚠️ 指定目录不在允许范围内: ${directory}`)
        searchDirs = getUserSearchPaths()
      }
    } else {
      searchDirs = getUserSearchPaths()
    }

    console.log(`🔍 本地搜索目录列表:`, searchDirs)

    const results = []

    // 使用 Node.js 的 fs 模块进行文件搜索
    function searchRecursive(dir, query, maxDepth = 3, currentDepth = 0) {
      try {
        if (currentDepth > maxDepth) return

        const items = fs.readdirSync(dir)

        for (const item of items) {
          const fullPath = path.join(dir, item)

          try {
            const stat = fs.statSync(fullPath)

            if (stat.isDirectory() && currentDepth < maxDepth) {
              // 递归搜索子目录，但限制深度
              searchRecursive(fullPath, query, maxDepth, currentDepth + 1)
            } else if (stat.isFile()) {
              // 检查文件名是否匹配查询
              if (item.toLowerCase().includes(query.toLowerCase())) {
                results.push({
                  name: item,
                  path: fullPath,
                  size: stat.size,
                  modified: stat.mtime,
                  directory: path.dirname(fullPath)
                })
              }
            }
          } catch (statError) {
            // 忽略无法访问的文件/目录
            console.debug(`跳过无法访问的项目: ${fullPath}`)
          }
        }
      } catch (error) {
        console.warn(`⚠️ 搜索目录失败: ${dir}`, error.message)
      }
    }

    // 在所有允许的目录中搜索
    for (const searchDir of searchDirs) {
      if (fs.existsSync(searchDir)) {
        console.log(`🔍 本地搜索目录: ${searchDir}`)
        searchRecursive(searchDir, query)
      } else {
        console.warn(`⚠️ 搜索目录不存在: ${searchDir}`)
      }
    }

    // 按文件名相关性排序
    results.sort((a, b) => {
      const aScore = a.name.toLowerCase().indexOf(query.toLowerCase())
      const bScore = b.name.toLowerCase().indexOf(query.toLowerCase())
      if (aScore !== bScore) {
        return aScore - bScore
      }
      return a.name.localeCompare(b.name)
    })

    console.log(`✅ 本地搜索完成，找到 ${results.length} 个文件`)
    if (results.length > 0) {
      console.log(`📁 找到的文件:`)
      results.slice(0, 5).forEach((file, index) => {
        console.log(`  ${index + 1}. "${file.name}" -> "${file.path}"`)
      })
      if (results.length > 5) {
        console.log(`  ... 还有 ${results.length - 5} 个文件`)
      }
    }

    return {
      success: true,
      files: results,
      searchPaths: searchDirs,
      query: query,
      usedRealMCP: false
    }
  } catch (error) {
    console.error('❌ 本地搜索失败:', error)
    return {
      success: false,
      error: error.message,
      files: [],
      query: query,
      usedRealMCP: false
    }
  }
}

/**
 * 列出目录内容
 */
async function listDirectory(dirPath) {
  try {
    console.log(`📂 列出目录: ${dirPath}`)
    
    if (!fs.existsSync(dirPath)) {
      throw new Error(`目录不存在: ${dirPath}`)
    }
    
    const items = fs.readdirSync(dirPath)
    const results = []
    
    for (const item of items) {
      const fullPath = path.join(dirPath, item)
      const stat = fs.statSync(fullPath)
      
      results.push({
        name: item,
        path: fullPath,
        type: stat.isDirectory() ? 'directory' : 'file',
        size: stat.size,
        modified: stat.mtime
      })
    }
    
    console.log(`✅ 目录列表完成，共 ${results.length} 个项目`)
    return {
      success: true,
      items: results
    }
  } catch (error) {
    console.error('❌ 列出目录失败:', error)
    return {
      success: false,
      error: error.message,
      items: []
    }
  }
}

/**
 * 读取文件内容
 */
async function readFile(filePath) {
  try {
    console.log(`📖 读取文件: ${filePath}`)
    
    if (!fs.existsSync(filePath)) {
      throw new Error(`文件不存在: ${filePath}`)
    }
    
    const stat = fs.statSync(filePath)
    if (stat.size > 10 * 1024 * 1024) { // 10MB
      throw new Error('文件过大，无法读取')
    }
    
    const content = fs.readFileSync(filePath, 'utf8')
    
    console.log(`✅ 文件读取完成，大小: ${content.length} 字符`)
    return {
      success: true,
      content,
      size: content.length,
      modified: stat.mtime
    }
  } catch (error) {
    console.error('❌ 读取文件失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 打开文件 - 增强版本，包含完整的路径处理逻辑
 */
async function openFile(filePath, options = {}) {
  try {
    console.log(`� MCP打开文件请求:`)
    console.log(`  - 原始路径: "${filePath}"`)
    console.log(`  - 路径类型: ${typeof filePath}`)
    console.log(`  - 路径长度: ${filePath ? filePath.length : 'null'}`)

    // 验证路径参数
    if (!filePath || typeof filePath !== 'string') {
      console.error(`❌ 无效的文件路径参数:`, { filePath, type: typeof filePath })
      return {
        success: false,
        error: `无效的文件路径参数: ${filePath}`,
        filePath: filePath
      }
    }

    // 清理路径中的多余转义字符和Unicode编码
    let cleanPath = filePath.trim()
    console.log(`🔧 清理前路径: "${cleanPath}"`)

    // 🔧 处理路径中的username占位符，替换为实际用户名
    const actualUsername = os.userInfo().username
    const currentHomedir = os.homedir()

    // 替换路径中的username占位符
    if (cleanPath.includes('/username/') || cleanPath.includes('\\username\\')) {
      console.log(`🔧 检测到username占位符，将替换为实际用户名: ${actualUsername}`)
      cleanPath = cleanPath
        .replace(/\/username\//g, `/${actualUsername}/`)
        .replace(/\\username\\/g, `\\${actualUsername}\\`)
      console.log(`🔧 username替换后路径: "${cleanPath}"`)
    }

    // 替换路径开头的username占位符（如果是相对于用户目录的路径）
    if (cleanPath.startsWith('username/') || cleanPath.startsWith('username\\')) {
      console.log(`🔧 检测到开头username占位符，将替换为用户主目录`)
      cleanPath = cleanPath
        .replace(/^username[/\\]/, currentHomedir + path.sep)
      console.log(`🔧 用户目录替换后路径: "${cleanPath}"`)
    }

    // 处理C:/Users/<USER>/格式的路径
    if (cleanPath.includes('C:/Users/<USER>/') || cleanPath.includes('C:\\Users\\<USER>\\')) {
      console.log(`🔧 检测到Windows用户目录占位符，将替换为实际路径`)
      cleanPath = cleanPath
        .replace(/C:\/Users\/<USER>\//g, currentHomedir.replace(/\\/g, '/') + '/')
        .replace(/C:\\Users\\<USER>\\/g, currentHomedir + '\\')
      console.log(`🔧 Windows用户目录替换后路径: "${cleanPath}"`)
    }

    // 尝试解码Unicode转义序列（如 \u81ea -> 自）
    try {
      if (cleanPath.includes('\\u')) {
        console.log(`🔧 检测到Unicode转义序列，尝试解码...`)
        // 替换Unicode转义序列
        cleanPath = cleanPath.replace(/\\u([0-9a-fA-F]{4})/g, (match, hex) => {
          return String.fromCharCode(parseInt(hex, 16))
        })
        console.log(`🔧 Unicode解码后: "${cleanPath}"`)
      }
    } catch (decodeError) {
      console.warn(`🔧 Unicode解码失败: ${decodeError.message}`)
    }

    // 移除路径中的过度转义
    cleanPath = cleanPath
      .replace(/\\{8,}/g, '\\')    // 8+个反斜杠变成1个
      .replace(/\\{4}/g, '\\')     // 4个反斜杠变成1个
      .replace(/\\{2}/g, '\\')     // 2个反斜杠变成1个

    console.log(`🔧 清理后路径: "${cleanPath}"`)
    console.log(`🔧 文件打开选项:`, options)

    // 🔧 知识库参考文件特殊处理
    const isKnowledgeReference = options.isKnowledgeReference === true

    if (isKnowledgeReference) {
      console.log(`📚 检测到知识库参考文件，跳过目录权限检查: "${cleanPath}"`)

      // 验证文件存在性
      if (!fs.existsSync(cleanPath)) {
        console.error(`❌ 知识库参考文件不存在: ${cleanPath}`)
        return {
          success: false,
          error: `知识库参考文件不存在: ${cleanPath}`,
          filePath: cleanPath,
          exists: false
        }
      }

      // 直接打开文件，不检查目录权限
      const { shell } = require('electron')
      const result = await shell.openPath(cleanPath)

      if (result === '') {
        console.log(`✅ [知识库参考] 文件打开成功: ${path.basename(cleanPath)}`)
        return {
          success: true,
          message: `已成功打开知识库参考文件: ${path.basename(cleanPath)}`,
          filePath: cleanPath,
          originalPath: filePath,
          exists: true,
          isKnowledgeReference: true
        }
      } else {
        console.error(`❌ [知识库参考] 文件打开失败，shell.openPath返回:`, result)
        return {
          success: false,
          error: `知识库参考文件打开失败: ${result}`,
          filePath: cleanPath,
          originalPath: filePath,
          exists: true,
          shellResult: result,
          isKnowledgeReference: true
        }
      }
    }

    // 🔧 MCP使用文件，执行目录权限检查
    console.log(`🔧 MCP使用文件，执行目录权限检查`)

    // 获取允许访问的目录
    const allowedDirs = getUserSearchPaths()
    console.log(`🔧 允许访问的目录:`, allowedDirs)

    // 检查文件路径是否在允许的目录中
    let isPathAllowed = false
    let targetPath = cleanPath

    console.log('🔍 检查文件路径权限...')
    for (const allowedDir of allowedDirs) {
      console.log(`  检查路径: "${cleanPath}" 是否在 "${allowedDir}" 中`)

      // 检查文件是否在允许的目录中
      if (cleanPath.startsWith(allowedDir + path.sep) || cleanPath === allowedDir) {
        isPathAllowed = true
        targetPath = cleanPath
        console.log(`  ✅ 路径已授权: "${targetPath}"`)
        break
      }
    }

    // 如果路径不在允许的目录中，尝试在允许的目录中查找同名文件
    if (!isPathAllowed) {
      console.log('🔍 路径未授权，尝试在允许目录中查找同名文件...')
      const fileName = path.basename(cleanPath)

      for (const allowedDir of allowedDirs) {
        const tryPath = path.join(allowedDir, fileName)
        console.log(`  尝试路径: "${tryPath}"`)

        try {
          if (fs.existsSync(tryPath)) {
            isPathAllowed = true
            targetPath = tryPath
            console.log(`  ✅ 找到同名文件: "${targetPath}"`)
            break
          }
        } catch (checkError) {
          console.warn(`  ⚠️ 检查路径错误: ${checkError.message}`)
        }
      }
    }

    // 如果仍然找不到有效路径，返回错误
    if (!isPathAllowed) {
      console.error(`❌ 文件路径不在允许的目录中: ${cleanPath}`)
      return {
        success: false,
        error: `文件路径不在允许的目录中: ${cleanPath}`,
        filePath: cleanPath,
        exists: false,
        allowedDirs: allowedDirs,
        restriction: '仅限配置路径访问'
      }
    }

    // 使用系统默认程序打开文件
    const { shell } = require('electron')
    console.log(`📂 正在使用shell.openPath打开: "${targetPath}"`)

    const result = await shell.openPath(targetPath)
    console.log(`📂 shell.openPath结果: "${result}"`)

    // shell.openPath在成功时返回空字符串，失败时返回错误信息
    if (result === '') {
      console.log(`✅ 文件打开成功: ${path.basename(targetPath)}`)
      return {
        success: true,
        message: `已成功打开文件: ${path.basename(targetPath)}`,
        filePath: targetPath,
        originalPath: filePath,
        exists: true
      }
    } else {
      console.error(`❌ 文件打开失败，shell.openPath返回:`, result)
      return {
        success: false,
        error: `文件打开失败: ${result}`,
        filePath: targetPath,
        originalPath: filePath,
        exists: true,
        shellResult: result
      }
    }

  } catch (error) {
    console.error('❌ 文件打开异常:', error)
    return {
      success: false,
      error: `文件打开异常: ${error.message}`,
      filePath: filePath,
      exception: error.name
    }
  }
}

/**
 * 检查是否为知识库引用文件
 */
function isKnowledgeReferenceFile(filePath) {
  const knowledgeExtensions = ['.txt', '.md', '.docx', '.doc', '.pdf']
  const ext = path.extname(filePath).toLowerCase()
  return knowledgeExtensions.includes(ext)
}

/**
 * 获取连接状态
 */
function getConnectionStatus() {
  return {
    connected: initialized,
    service: 'filesystem'
  }
}

/**
 * 获取可用工具
 */
function getAvailableTools() {
  return [
    {
      name: 'filesystem_search',
      description: '搜索文件',
      parameters: {
        query: { type: 'string', description: '搜索关键词' },
        directory: { type: 'string', description: '搜索目录（可选）' }
      }
    },
    {
      name: 'filesystem_list',
      description: '列出目录内容',
      parameters: {
        path: { type: 'string', description: '目录路径' }
      }
    },
    {
      name: 'filesystem_read',
      description: '读取文件内容',
      parameters: {
        path: { type: 'string', description: '文件路径' }
      }
    },
    {
      name: 'filesystem_open',
      description: '打开文件',
      parameters: {
        path: { type: 'string', description: '文件路径' }
      }
    }
  ]
}

/**
 * 调用工具
 */
async function callTool(toolName, args) {
  console.log(`🔧 文件系统工具调用: ${toolName}`, args)

  switch (toolName) {
    case 'filesystem_search':
    case 'search_files':  // 别名支持
      // 支持多种参数格式
      const query = args.query || args.search || ''
      const directory = args.directory || args.path || args.dir || null
      console.log(`🔍 搜索参数: query="${query}", directory="${directory || '默认'}"`)
      return await executeFileSearch(query, directory)

    case 'filesystem_list':
    case 'list_directory':  // 别名支持
      return await listDirectory(args.path || args.directory)

    case 'filesystem_read':
    case 'read_file':  // 别名支持
      return await readFile(args.path || args.file)

    case 'filesystem_open':
    case 'open_file':  // 别名支持
      return await openFile(args.path || args.file, args.options)

    default:
      throw new Error(`未知的文件系统工具: ${toolName}`)
  }
}

/**
 * 清理资源
 */
async function cleanup() {
  console.log('🧹 清理文件系统 MCP 资源...')
  initialized = false
}

module.exports = {
  initialize,
  executeFileSearch,
  listDirectory,
  readFile,
  openFile,
  isKnowledgeReferenceFile,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
} 
@echo off
chcp 65001 > nul
echo 正在杀掉所有Electron相关进程...
echo.

REM 杀掉Electron相关进程
echo 正在结束Electron进程...
taskkill /f /im electron.exe 2>nul
if %errorlevel% == 0 (
    echo ✓ 已结束 electron.exe 进程
) else (
    echo - 未找到 electron.exe 进程
)

echo 正在结束应用程序进程...
taskkill /f /im nezha-ai-desktop.exe 2>nul
if %errorlevel% == 0 (
    echo ✓ 已结束 nezha-ai-desktop.exe 进程
) else (
    echo - 未找到 nezha-ai-desktop.exe 进程
)

REM 强制杀掉所有包含electron的进程
echo.
echo 正在强制结束所有包含"electron"的进程...
for /f "tokens=2 delims=," %%i in ('tasklist /fo csv ^| findstr /i "electron"') do (
    echo 找到进程: %%i
    taskkill /f /pid %%i 2>nul
)

echo.
echo 清理完成！所有Electron相关进程已结束。
echo.
pause 
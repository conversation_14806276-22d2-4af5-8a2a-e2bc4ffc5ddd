<template>
  <div class="login-container">
    <div class="login-box">
      <div class="logo-section">
        <img :src="logoUrl" alt="Nezha" class="logo" />
        <h1 class="title">犇犇数字员工助手</h1>
        <p class="subtitle">您的智能桌面伙伴</p>
      </div>

      <!-- 登录模式选择和登录表单 -->
      <div class="login-section">
        <!-- 登录模式切换 - 暂时注释掉天翼认证 -->
        <!-- <div class="login-mode-switcher">
          <button :class="['mode-btn', { active: loginMode === 'sso' }]" @click="loginMode = 'sso'">
            天翼认证
          </button>
          <button :class="['mode-btn', { active: loginMode === 'password' }]" @click="loginMode = 'password'">
            账密登录
          </button>
        </div> -->

        <!-- 天翼认证 - 暂时注释掉 -->
        <!-- <div v-if="loginMode === 'sso'" class="sso-login">
          <p class="sso-description">使用天翼认证系统登录</p>
        </div> -->

        <!-- 账密登录 -->
        <div class="password-login">
          <form @submit.prevent="handlePasswordLogin" class="login-form">
            <div class="input-group">
              <label for="username">用户名</label>
              <input id="username" v-model="form.username" type="text" placeholder="请输入用户名" :disabled="isLogging"
                required />
            </div>

            <div class="input-group">
              <label for="password">密码</label>
              <div class="password-input-wrapper">
                <input 
                  id="password" 
                  v-model="form.password" 
                  :type="showPassword ? 'text' : 'password'" 
                  placeholder="请输入密码" 
                  :disabled="isLogging"
                  required 
                />
                <div class="password-input-actions">
                  <button 
                    v-if="form.password" 
                    type="button" 
                    class="clear-btn" 
                    @click="clearPassword"
                    title="清除密码"
                  >
                    ✕
                  </button>
                  <button 
                    type="button" 
                    class="toggle-password-btn" 
                    @click="togglePasswordVisibility"
                    :title="showPassword ? '隐藏密码' : '显示密码'"
                  >
                    {{ showPassword ? '👁️' : '👁️‍🗨️' }}
                  </button>
                </div>
              </div>
            </div>

          </form>
        </div>

        <!-- 协议同意部分 -->
        <div class="agreement-section">
          <div class="agreement-links">

          </div>
          <div class="agreement-checkbox">
            <input id="agreement" v-model="agreementChecked" type="checkbox" />
            <label for="agreement"> <span>使用前请阅读并同意</span>
              <a href="#" @click="openPrivacyPolicy" class="agreement-link">隐私政策</a>
              <span>和</span>
              <a href="#" @click="openTermsOfService"
                class="agreement-link">服务协议</a>，我已知晓当前为内测版本，产品提供方有权根据内测实际情况，终止内测及产品/服务的提供。产品提供方不对产品功能和体验做保证或承担任何责任。</label>
          </div>
        </div>

        <!-- 登录按钮 -->
        <div class="login-actions">
          <!-- 天翼认证登录按钮 - 暂时注释掉 -->
          <!-- <button v-if="loginMode === 'sso'" @click="handleSSOLogin" :disabled="isLogging || !agreementChecked"
            class="login-btn sso-btn">
            <span v-if="!isLogging">天翼认证登录</span>
            <span v-else>跳转中...</span>
          </button> -->

          <!-- 账密登录按钮 -->
          <button @click="handlePasswordLogin"
            :disabled="isLogging || !agreementChecked" class="login-btn password-btn">
            <span v-if="!isLogging">登录</span>
            <span v-else>登录中...</span>
          </button>
        </div>

        <div v-if="authStore.loginError" class="error-message">
          {{ authStore.loginError }}
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted, watch } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import logoUrl from '/assets/logo.png?url'

export default {
  name: 'Login',
  setup() {
    const authStore = useAuthStore()
    const isLogging = ref(false)
    const agreementChecked = ref(false)
    const showPassword = ref(false)
    // const loginMode = ref('sso') // 默认天翼认证 - 暂时注释掉
    const loginMode = ref('password') // 改为默认账密登录

    const form = reactive({
      username: '',
      password: '',
      remember: false
    })

    // 清除密码
    const clearPassword = () => {
      form.password = ''
    }

    // 切换密码显示/隐藏
    const togglePasswordVisibility = () => {
      showPassword.value = !showPassword.value
    }

    // 检查协议同意状态
    const checkAgreementStatus = () => {
      const agreed = localStorage.getItem('agreementAccepted')
      agreementChecked.value = agreed === 'true'
    }

    // 打开隐私政策
    const openPrivacyPolicy = () => {
      if (window.electronAPI && window.electronAPI.openUrl) {
        window.electronAPI.openUrl('http://114.67.112.88:2345/privacy-policy')
      } else {
        window.open('http://114.67.112.88:2345/privacy-policy', '_blank')
      }
    }

    // 打开服务协议
    const openTermsOfService = () => {
      if (window.electronAPI && window.electronAPI.openUrl) {
        window.electronAPI.openUrl('http://114.67.112.88:2345/terms-of-service')
      } else {
        window.open('http://114.67.112.88:2345/terms-of-service', '_blank')
      }
    }

    // 天翼认证登录 - 暂时注释掉
    // const handleSSOLogin = async () => {
    //   // 检查是否已同意协议
    //   if (!agreementChecked.value) {
    //     authStore.loginError = '请先同意隐私政策和服务协议'
    //     return
    //   }

    //   isLogging.value = true
    //   authStore.clearError()

    //   try {

    //     // 跳转到第三方认证页面
    //     const clientId = 'gpushop'
    //     const redirectUri = encodeURIComponent('ai-cognidesk://auth/sso/callback')
    //     const responseType = 'code'
    //     const ssoUrl = `http://114.80.40.197:31080/sso?client_id=${clientId}&redirect_uri=${redirectUri}&response_type=${responseType}`

    //     console.log('🔗 跳转到天翼认证页面:', ssoUrl)

    //     if (window.electronAPI && window.electronAPI.openUrl) {
    //       window.electronAPI.openUrl(ssoUrl)
    //     } else {
    //       window.open(ssoUrl, '_blank')
    //     }
    //   } catch (error) {
    //     console.error('第三方登录失败:', error)
    //     authStore.loginError = '第三方登录失败，请重试'
    //   } finally {
    //     isLogging.value = false
    //   }
    // }



    // 账密登录
    const handlePasswordLogin = async () => {
      // 检查是否已同意协议
      if (!agreementChecked.value) {
        authStore.loginError = '请先同意隐私政策和服务协议'
        return
      }

      if (!form.username || !form.password) {
        authStore.loginError = '请输入用户名和密码'
        return
      }

      isLogging.value = true
      authStore.clearError()

      try {
        const loginData = {
          username: form.username,
          password: form.password
        }

        const result = await authStore.passwordLogin(loginData)

        if (result.success) {
          console.log('登录成功')
        }
      } catch (error) {
        console.error('登录失败:', error)
      } finally {
        isLogging.value = false
      }
    }

    // 监听登录模式变化 - 暂时注释掉
    // watch(loginMode, (newMode) => {
    //   // 切换登录方式时清空错误信息
    //   authStore.clearError()
    // })

    onMounted(() => {
      // 检查协议同意状态
      // checkAgreementStatus()

      // 🔄 移除SSO回调监听，避免重复调用
      // 主进程的SSO回调已经在App.vue中统一处理
      // if (window.electronAPI && window.electronAPI.onSSOCallback) {
      //   window.electronAPI.onSSOCallback((data) => {
      //     console.log('🔗 登录页面收到SSO回调:', data)
      //     if (data.code) {
      //       handleSSOCallback(data.code)
      //     }
      //   })
      // }
    })

    // 处理SSO回调 - 暂时注释掉
    // const handleSSOCallback = async (code) => {
    //   console.log('🔗 处理SSO回调，授权码:', code)
    //   isLogging.value = true
    //   try {
    //     const result = await authStore.ssoLogin(code)
    //     if (result.success) {
    //       console.log('🔗 SSO登录成功')
    //     } else {
    //       console.error('🔗 SSO登录失败:', result.message)
    //     }
    //   } catch (error) {
    //     console.error('🔗 SSO登录异常:', error)
    //   } finally {
    //     isLogging.value = false
    //   }
    // }

    return {
      authStore,
      form,
      isLogging,
      agreementChecked,
      loginMode,
      showPassword,
      openPrivacyPolicy,
      openTermsOfService,
      clearPassword,
      togglePasswordVisibility,
      // handleSSOLogin, // 暂时注释掉
      handlePasswordLogin,
      // handleSSOCallback, // 暂时注释掉
      logoUrl
    }
  }
}
</script>

<style lang="scss" scoped>
.login-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  padding: 20px;
}

.login-box {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 20px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
}

.logo-section {
  text-align: center;
  margin-bottom: 30px;

  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    border-radius: 50%;
  }

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
  }

  .subtitle {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.agreement-section {
  margin-top: 20px;
  // padding: 20px;
  // background: rgba(255, 255, 255, 0.8);
  // border-radius: 10px;
  // border: 1px solid #e1e5e9;

  .agreement-links {
    margin-bottom: 15px;
    text-align: center;
    font-size: 14px;

    .agreement-link {
      color: #667eea;
      text-decoration: none;
      font-weight: bold;
      padding: 0 10px;
      &:hover {
        text-decoration: underline;
      }
    }

    span {
      margin: 0 5px;
      color: #666;
    }
  }

  .agreement-checkbox {
    display: flex;
    align-items: center;
    justify-content: center;

    input[type="checkbox"] {
      width: auto;
      margin-right: 8px;
    }

    label {
      margin: 0;
      color: #666;
      font-size: 14px;
      cursor: pointer;
      a{
        font-weight: bold;
        color: #667eea;
        margin: 0 5px;
      }
    }
  }
}

.login-actions {
  margin-top: 20px;

  .login-btn {
    width: 100%;
    padding: 12px;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }

    &.sso-btn {
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: white;

      &:hover:not(:disabled) {
        box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
      }
    }

    &.password-btn {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      color: white;

      &:hover:not(:disabled) {
        box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
      }
    }
  }
}

.login-section {
  .login-mode-switcher {
    display: flex;
    margin-bottom: 10px;
    border-radius: 10px;
    overflow: hidden;
    border: 1px solid #e1e5e9;

    .mode-btn {
      flex: 1;
      padding: 12px 20px;
      background: #f8f9fa;
      color: #666;
      border: none;
      cursor: pointer;
      transition: all 0.3s ease;

      &.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
      }

      &:hover:not(.active) {
        background: #e9ecef;
      }
    }
  }

  .sso-login {
    text-align: center;

    .sso-description {
      color: #666;
      margin-bottom: 20px;
    }

    .sso-btn {
      width: 100%;
      padding: 12px;
      background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
      color: white;
      border: none;
      border-radius: 10px;
      font-size: 16px;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.3s ease;

      &:hover:not(:disabled) {
        transform: translateY(-2px);
        box-shadow: 0 10px 20px rgba(40, 167, 69, 0.3);
      }

      &:disabled {
        opacity: 0.6;
        cursor: not-allowed;
        transform: none;
      }
    }
  }

  .password-login {
    // 验证码相关样式已移除
  }
}

.login-form {
  .input-group {
    margin-bottom: 10px;

    label {
      display: block;
      margin-bottom: 8px;
      color: #333;
      font-weight: 500;
    }

    .password-input-wrapper {
      position: relative;
      display: flex;
      align-items: center;

      input {
        width: 100%;
        padding: 12px 16px;
        padding-right: 80px; // 为按钮留出空间
        border: 2px solid #e1e5e9;
        border-radius: 10px;
        font-size: 16px;
        transition: all 0.3s ease;

        &:focus {
          outline: none;
          border-color: #667eea;
          box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }

        &:disabled {
          background-color: #f5f5f5;
          cursor: not-allowed;
        }
      }

      .password-input-actions {
        position: absolute;
        right: 8px;
        display: flex;
        align-items: center;
        gap: 4px;

        .clear-btn,
        .toggle-password-btn {
          background: none;
          border: none;
          padding: 4px 6px;
          border-radius: 4px;
          cursor: pointer;
          font-size: 14px;
          color: #666;
          transition: all 0.2s ease;
          display: flex;
          align-items: center;
          justify-content: center;
          min-width: 24px;
          height: 24px;

          &:hover {
            background-color: rgba(0, 0, 0, 0.1);
            color: #333;
          }

          &:active {
            transform: scale(0.95);
          }
        }

        .clear-btn {
          font-weight: bold;
          font-size: 12px;
        }

        .toggle-password-btn {
          font-size: 16px;
        }
      }
    }

    input {
      width: 100%;
      padding: 12px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 10px;
      font-size: 16px;
      transition: all 0.3s ease;

      &:focus {
        outline: none;
        border-color: #667eea;
        box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
      }

      &:disabled {
        background-color: #f5f5f5;
        cursor: not-allowed;
      }
    }
  }

  .checkbox-group {
    display: flex;
    align-items: center;
    margin-bottom: 25px;

    input[type="checkbox"] {
      width: auto;
      margin-right: 8px;
    }

    label {
      margin: 0;
      color: #666;
      font-size: 14px;
      cursor: pointer;
    }
  }

  .login-btn {
    width: 100%;
    padding: 12px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none;
    }
  }
}

.error-message {
  margin-top: 15px;
  color: #e74c3c;
  text-align: center;
  font-size: 14px;
  background: rgba(231, 76, 60, 0.1);
  padding: 10px;
  border-radius: 8px;
}
</style>
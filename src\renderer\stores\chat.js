import { defineStore } from 'pinia'
import { 
  sendChatRequest, 
  createSystemMessage, 
  createUserMessage, 
  createAssistantMessage,
  getDefaultSystemPrompt 
} from '../utils/chatAPI.js'

export const useChatStore = defineStore('chat', {
  state: () => ({
    // 聊天历史
    messages: [],
    // 是否正在处理中
    isProcessing: false,
    // 错误信息
    error: null,
    // 系统提示
    systemPrompt: getDefaultSystemPrompt(),
    // 聊天设置
    settings: {
      maxTokens: 1000,
      temperature: 0.7,
      maxHistoryLength: 20 // 最大历史消息数量
    }
  }),

  getters: {
    // 获取最近的聊天历史（排除系统消息）
    recentMessages: (state) => {
      return state.messages.filter(msg => msg.role !== 'system').slice(-10)
    },
    
    // 获取用于 API 调用的消息格式
    apiMessages: (state) => {
      const systemMessage = createSystemMessage(state.systemPrompt)
      const userMessages = state.messages.slice(-state.settings.maxHistoryLength)
      return [systemMessage, ...userMessages]
    }
  },

  actions: {
    /**
     * 发送用户消息并获取回复
     * @param {string} userInput - 用户输入
     * @returns {Promise<Object>} 回复结果
     */
    async sendMessage(userInput) {
      if (this.isProcessing) {
        return { success: false, message: '正在处理中，请稍等...' }
      }

      if (!userInput || !userInput.trim()) {
        return { success: false, message: '请输入有效的内容' }
      }

      this.isProcessing = true
      this.error = null

      try {
        // 添加用户消息到历史
        const userMessage = createUserMessage(userInput.trim())
        this.messages.push(userMessage)

        // 构建 API 调用消息
        const apiMessages = this.apiMessages

        // 调用大模型 API
        const response = await sendChatRequest(apiMessages, this.settings)

        if (response.success) {
          // 添加助手回复到历史
          const assistantMessage = createAssistantMessage(response.message)
          this.messages.push(assistantMessage)

          // 限制历史消息长度
          this.trimHistory()

          return { 
            success: true, 
            message: response.message,
            usage: response.usage 
          }
        } else {
          this.error = response.error
          return { 
            success: false, 
            message: response.message 
          }
        }
      } catch (error) {
        console.error('发送消息错误:', error)
        this.error = error.message
        return { 
          success: false, 
          message: '发送消息时发生错误，请稍后再试。' 
        }
      } finally {
        this.isProcessing = false
      }
    },

    /**
     * 清空聊天历史
     */
    clearHistory() {
      this.messages = []
      this.error = null
    },

    /**
     * 限制历史消息长度
     */
    trimHistory() {
      if (this.messages.length > this.settings.maxHistoryLength) {
        this.messages = this.messages.slice(-this.settings.maxHistoryLength)
      }
    },

    /**
     * 更新系统提示
     * @param {string} prompt - 新的系统提示
     */
    updateSystemPrompt(prompt) {
      this.systemPrompt = prompt || getDefaultSystemPrompt()
    },

    /**
     * 更新聊天设置
     * @param {Object} newSettings - 新的设置
     */
    updateSettings(newSettings) {
      this.settings = { ...this.settings, ...newSettings }
    },

    /**
     * 清除错误信息
     */
    clearError() {
      this.error = null
    },

    /**
     * 重新发送最后一条消息
     */
    async resendLastMessage() {
      if (this.messages.length === 0) {
        return { success: false, message: '没有可重发的消息' }
      }

      // 找到最后一条用户消息
      for (let i = this.messages.length - 1; i >= 0; i--) {
        if (this.messages[i].role === 'user') {
          const lastUserMessage = this.messages[i].content
          
          // 移除最后一条用户消息之后的所有消息
          this.messages = this.messages.slice(0, i)
          
          // 重新发送
          return await this.sendMessage(lastUserMessage)
        }
      }

      return { success: false, message: '没有找到可重发的用户消息' }
    },

    /**
     * 获取聊天统计信息
     */
    getChatStats() {
      const totalMessages = this.messages.length
      const userMessages = this.messages.filter(msg => msg.role === 'user').length
      const assistantMessages = this.messages.filter(msg => msg.role === 'assistant').length

      return {
        totalMessages,
        userMessages,
        assistantMessages,
        isProcessing: this.isProcessing,
        hasError: !!this.error
      }
    }
  }
}) 
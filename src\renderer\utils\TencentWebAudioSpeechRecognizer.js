/**
 * 腾讯云实时语音识别适配器
 * 基于代理服务器调用腾讯云ASR服务
 * 
 * 🎯 已优化配置用于检测较大声音并过滤环境杂音
 */

// 🔧 音频检测配置 - 可根据需要调整
const AUDIO_DETECTION_CONFIG = {
    // 本地音频检测阈值
    silenceThreshold: 0.02,     // 静音阈值：0.001(敏感) ~ 0.05(严格)
    peakThreshold: 0.15,         // 峰值阈值：0.05(敏感) ~ 0.3(严格)

    // 腾讯云API噪音控制
    noiseThreshold: 0.4,        // 云端噪音阈值：0.3(严格) ~ 0.9(宽松)

    // 增益控制
    smallVolumeGain: 1.2,       // 小音量放大倍数：1.0 ~ 3.0
    mediumVolumeGain: 1.5,      // 中音量放大倍数：1.0 ~ 3.0
    largeVolumeGain: 1.0,       // 大音量放大倍数：1.0 ~ 1.5

    // 压缩设置
    compressionRatio: 0.7,      // 压缩比例：0.5(强压缩) ~ 1.0(无压缩)
    compressionThreshold: 0.25   // 压缩触发阈值：0.2 ~ 0.5
};

// 导入配置
import { TENCENT_CONFIG, validateConfig, DEBUG_CONFIG, VOICE_CONFIG } from './config.js'

// 验证配置
const configValidation = validateConfig()
if (!configValidation.valid) {
    console.error('❌ 腾讯云配置验证失败:', configValidation.errors)
}

// 从腾讯云SDK复制的核心代码
const needFiltrationParams = ['appid', 'secretkey', 'signCallback', 'echoCancellation'];

// 格式化签名字符串
function formatSignString(query, params) {
    let paramStr = '';
    const keys = Object.keys(params).sort();
    for (let i = 0; i < keys.length; i++) {
        const key = keys[i];
        const value = params[key];
        if (value !== undefined && value !== null && value !== '') {
            paramStr += key + '=' + value + '&';
        }
    }
    return paramStr.slice(0, -1);
}

// 创建查询参数 - 修改为使用代理方式
async function createQuery(query) {
    let params = {};
    const time = new Date().getTime();

    // 使用代理配置
    const proxyParams = TENCENT_CONFIG.proxyParams;

    // 合并代理参数和用户参数
    params = {
        ...proxyParams,
        ...query
    };

    // 如果启用token认证，将用户认证信息添加到请求参数中
    if (TENCENT_CONFIG.enableTokenAuth) {
        const userToken = getUserToken();
        if (userToken) {
            // 将用户认证信息直接添加到请求参数中
            params['Authorization'] = `Bearer ${userToken}`;
            console.log('🔑 用户认证信息已添加到请求参数中');
        } else {
            console.warn('⚠️ 用户token未获取到，请求参数中不包含认证信息');
        }
    }

    // 添加时间戳和随机ID
    // params['timestamp'] = Math.round(time / 1000);
    // params['voice_id'] = guid();

    console.log('生成的代理查询参数:', params);
    delete params.hotword_id;

    return params;
}

export const guid = () => {
    return 'xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx'.replace(/[xy]/g, function (c) {
        const r = Math.random() * 16 | 0,
            v = c === 'x' ? r : (r & 0x3 | 0x8);
        return v.toString(16);
    });
};

// 获取用户token
function getUserToken() {
    try {
        return localStorage.getItem('userAuthToken') || ''
    } catch (error) {
        console.error('获取用户token失败:', error)
        return ''
    }
}

// 获取代理URL - 修改为使用代理服务器
async function getUrl(self, params) {
    try {
        console.log('开始生成代理WebSocket URL...');
        console.log('传入参数:', params);

        // 生成查询参数
        const urlQuery = await createQuery(params);

        // 构建查询字符串
        const queryString = Object.keys(urlQuery)
            .map(key => `${key}=${encodeURIComponent(urlQuery[key])}`)
            .join('&');

        // 构建最终代理URL
        const finalUrl = `${TENCENT_CONFIG.proxyUrl}?${queryString}`;

        console.log('最终代理WebSocket URL:', finalUrl);

        return finalUrl;

    } catch (error) {
        console.error('生成代理URL失败:', error);
        if (self.OnError) {
            self.OnError(error);
        }
        return false;
    }
}

// 获取CryptoJS对象
function getCryptoJS() {
    console.log('🔍 检查CryptoJS可用性:', {
        hasCryptoJS: !!window.CryptoJS,
        hasCryptoJSTest: !!window.CryptoJSTest,
        hasHmacSHA1: !!(window.CryptoJS && window.CryptoJS.HmacSHA1),
        cryptoJSReady: window.cryptoJSReady,
        location: window.location.href
    });

    // 首先检查window.CryptoJS
    if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
        console.log('✅ 使用window.CryptoJS');
        return window.CryptoJS;
    }

    // 检查window.CryptoJSTest（向后兼容）
    if (window.CryptoJSTest && window.CryptoJSTest.HmacSHA1) {
        console.log('✅ 使用window.CryptoJSTest');
        return window.CryptoJSTest;
    }

    console.error('❌ CryptoJS未加载或不可用，可能的原因:');
    console.error('  1. CryptoJS脚本加载失败');
    console.error('  2. 网络连接问题');
    console.error('  3. 文件路径错误');
    console.error('  4. Content Security Policy阻止');
    return null;
}

function toUint8Array(wordArray) {
    const words = wordArray.words;
    const sigBytes = wordArray.sigBytes;

    const u8 = new Uint8Array(sigBytes);
    for (let i = 0; i < sigBytes; i++) {
        u8[i] = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
    }
    return u8;
}

function Uint8ArrayToString(fileData) {
    let dataString = '';
    for (let i = 0; i < fileData.length; i++) {
        dataString += String.fromCharCode(fileData[i]);
    }
    return dataString;
}

// 签名回调函数 - 代理模式下不需要
async function signCallback(secretKey, signStr) {
    console.log('代理模式下不需要签名');
    return '';
}

// WebRecorder录音类
class WebRecorder {
    constructor(requestId, params, isLog) {
        this.requestId = requestId;
        this.params = params;
        this.isLog = isLog;
        this.audioContext = null;
        this.stream = null;
        this.processor = null;
        this.source = null;
        this.isRecording = false;

        // 回调函数
        this.OnReceivedData = null;
        this.OnError = null;
        this.OnStop = null;
    }

    async start() {
        try {
            // 检查浏览器支持
            if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
                throw new Error('浏览器不支持麦克风录音功能');
            }

            this.isLog && console.log('请求麦克风权限...');

            // 优化音频配置以提高识别准确性和杂音过滤
            const constraints = {
                audio: {
                    channelCount: 1,
                    sampleRate: 16000,  // 明确设置为16kHz，匹配腾讯云16k_zh_large模型
                    echoCancellation: true,  // 回声消除
                    noiseSuppression: true,  // 降噪 - 重要！
                    autoGainControl: false,   // 自动增益控制
                    // 🔥 强化降噪配置 - 针对环境杂音过滤
                    googEchoCancellation: true,      // Google回声消除
                    googNoiseSuppression: true,      // Google降噪 - 核心功能
                    googAutoGainControl: false,       // Google自动增益
                    googHighpassFilter: true,        // 高通滤波器 - 过滤低频噪音
                    googTypingNoiseDetection: true,  // 键盘噪音检测
                    googNoiseSuppression2: true,     // Google降噪2.0（如果支持）
                    // 新增配置
                    googAudioMirroring: false,   // 关闭音频镜像
                    googAutoGainControl2: false, // 关闭Google自动增益2.0
                    // 新增配置以优化声音检测
                    volume: 1.0,                     // 音量级别
                    sampleSize: 16,                  // 采样深度
                    latency: 0                       // 延迟设置
                }
            };

            try {
                this.stream = await navigator.mediaDevices.getUserMedia(constraints);
                this.isLog && console.log('麦克风权限获取成功，使用优化配置');
            } catch (error) {
                // 如果优化配置失败，降级使用基本配置
                this.isLog && console.warn('优化配置失败，使用基本配置:', error);
                const basicConstraints = {
                    audio: {
                        channelCount: 1,
                        echoCancellation: true,
                        noiseSuppression: true,
                        autoGainControl: true
                    }
                };
                this.stream = await navigator.mediaDevices.getUserMedia(basicConstraints);
                this.isLog && console.log('麦克风权限获取成功，使用基本配置');
            }

            // 创建音频上下文，尝试使用16kHz采样率
            try {
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)({ sampleRate: 16000 });
            } catch (error) {
                // 如果不支持指定采样率，使用默认采样率
                this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
                this.isLog && console.warn('无法设置16kHz采样率，使用默认采样率:', this.audioContext.sampleRate);
            }

            // 恢复音频上下文（处理浏览器的自动暂停策略）
            if (this.audioContext.state === 'suspended') {
                await this.audioContext.resume();
                this.isLog && console.log('音频上下文已恢复');
            }

            this.isLog && console.log('音频上下文采样率:', this.audioContext.sampleRate);

            // 创建音频源
            this.source = this.audioContext.createMediaStreamSource(this.stream);

            // 尝试使用现代的AudioWorkletNode，如果不支持则降级到ScriptProcessorNode
            if (this.audioContext.audioWorklet && typeof this.audioContext.audioWorklet.addModule === 'function') {
                try {
                    // 使用AudioWorkletNode的现代方案
                    this.isLog && console.log('尝试使用AudioWorkletNode...');
                    await this.setupAudioWorklet();
                } catch (error) {
                    this.isLog && console.log('AudioWorkletNode不可用，降级到ScriptProcessorNode:', error);
                    this.setupScriptProcessor();
                }
            } else {
                this.isLog && console.log('浏览器不支持AudioWorkletNode，使用ScriptProcessorNode');
                this.setupScriptProcessor();
            }

            this.isRecording = true;
            this.isLog && console.log('录音开始');

        } catch (error) {
            console.error('录音启动失败:', error);

            let errorMessage = error.message;
            if (error.name === 'NotAllowedError') {
                errorMessage = '麦克风权限被拒绝，请允许浏览器访问麦克风';
            } else if (error.name === 'NotFoundError') {
                errorMessage = '未找到麦克风设备，请检查设备连接';
            } else if (error.name === 'NotReadableError') {
                errorMessage = '麦克风设备被其他应用占用，请关闭其他音频应用';
            } else if (error.name === 'OverconstrainedError') {
                errorMessage = '麦克风设备不支持当前配置，尝试降低要求';
            }

            if (this.OnError) {
                this.OnError(new Error(errorMessage));
            }
        }
    }

    stop() {
        this.isRecording = false;

        if (this.processor) {
            this.processor.disconnect();
            this.processor = null;
        }

        if (this.source) {
            this.source.disconnect();
            this.source = null;
        }

        if (this.audioContext && this.audioContext.state !== 'closed') {
            this.audioContext.close();
            this.audioContext = null;
        }

        if (this.stream) {
            this.stream.getTracks().forEach(track => track.stop());
            this.stream = null;
        }

        if (this.OnStop) {
            this.OnStop();
        }

        this.isLog && console.log('录音停止');
    }

    destroyStream() {
        this.stop();
    }

    setupScriptProcessor() {
        try {
            // 使用ScriptProcessorNode (已废弃但兼容性好)
            // 优化缓冲区大小：1024对于16kHz采样率更合适
            const bufferSize = 1024;

            // 检查audioContext是否存在
            if (!this.audioContext) {
                throw new Error('AudioContext未初始化');
            }

            // 检查createScriptProcessor方法是否存在
            if (typeof this.audioContext.createScriptProcessor !== 'function') {
                throw new Error('浏览器不支持createScriptProcessor方法');
            }

            this.processor = this.audioContext.createScriptProcessor(bufferSize, 1, 1);

            if (!this.processor) {
                throw new Error('无法创建ScriptProcessor');
            }

            this.processor.onaudioprocess = (event) => {
                if (this.isRecording && event && event.inputBuffer) {
                    try {
                        const audioData = event.inputBuffer.getChannelData(0);

                        if (!audioData || audioData.length === 0) {
                            return;
                        }

                        // 检查音频数据是否有效
                        const hasValidAudio = this.checkAudioValidity(audioData);
                        if (!hasValidAudio) {
                            return; // 跳过静音或无效音频
                        }

                        const pcmData = this.convertToPCM16(audioData);
                        if (this.OnReceivedData && pcmData && pcmData.length > 0) {
                            this.OnReceivedData(pcmData.buffer);
                        }
                    } catch (error) {
                        console.error('音频处理错误:', error);
                    }
                }
            };

            // 检查source是否存在再连接
            if (!this.source) {
                throw new Error('音频源未初始化');
            }

            this.source.connect(this.processor);
            this.processor.connect(this.audioContext.destination);
            this.isLog && console.log('ScriptProcessorNode设置完成，缓冲区大小:', bufferSize);

        } catch (error) {
            console.error('设置ScriptProcessor失败:', error);
            if (this.OnError) {
                this.OnError(error);
            }
            throw error;
        }
    }

    /**
     * 检查音频数据有效性 - 优化为检测较大声音，过滤环境杂音
     */
    checkAudioValidity(audioData) {
        // 计算音频能量级别
        let energy = 0;
        let peakValue = 0;
        for (let i = 0; i < audioData.length; i++) {
            const absValue = Math.abs(audioData[i]);
            energy += audioData[i] * audioData[i];
            peakValue = Math.max(peakValue, absValue);
        }
        const avgEnergy = energy / audioData.length;

        // ⚡ 使用可配置的阈值，检测较大声音，过滤环境杂音
        const silenceThreshold = AUDIO_DETECTION_CONFIG.silenceThreshold;
        const peakThreshold = AUDIO_DETECTION_CONFIG.peakThreshold;

        // 双重检测：既要平均能量足够，也要有足够的峰值
        const hasEnoughEnergy = avgEnergy > silenceThreshold;
        const hasEnoughPeak = peakValue > peakThreshold;

        // 记录音频检测状态（调试用）
        if (this.isLog && (hasEnoughEnergy || hasEnoughPeak)) {
            console.log(`🎤 音频检测: 平均能量=${avgEnergy.toFixed(4)}, 峰值=${peakValue.toFixed(4)}, 通过=${hasEnoughEnergy && hasEnoughPeak}`);
        }

        return hasEnoughEnergy && hasEnoughPeak;
    }

    convertToPCM16(audioData) {
        // 改进的PCM16转换算法 - 优化音量检测
        const samples = new Int16Array(audioData.length);

        // 分析音频特征
        let maxValue = 0;
        let avgValue = 0;
        for (let i = 0; i < audioData.length; i++) {
            const absValue = Math.abs(audioData[i]);
            maxValue = Math.max(maxValue, absValue);
            avgValue += absValue;
        }
        avgValue /= audioData.length;

        // ⚡ 优化增益控制 - 针对较大声音优化
        let gain = 1.0;

        // 如果音频太小，适当放大（但设置更严格的条件）
        if (maxValue < 0.05) {
            // 对于很小的声音，不放大太多，避免放大杂音
            gain = Math.min(AUDIO_DETECTION_CONFIG.smallVolumeGain, 0.3 / Math.max(maxValue, 0.001));
        } else if (maxValue < 0.2) {
            // 对于中等音量，适度放大
            gain = Math.min(AUDIO_DETECTION_CONFIG.mediumVolumeGain, 0.5 / Math.max(maxValue, 0.01));
        } else {
            // 对于较大音量，保持或略微增强
            gain = Math.min(AUDIO_DETECTION_CONFIG.largeVolumeGain, 1.0 / maxValue);
        }

        // 动态范围压缩 - 让较大声音更突出
        const compressionRatio = AUDIO_DETECTION_CONFIG.compressionRatio;

        for (let i = 0; i < audioData.length; i++) {
            // 应用增益
            let sample = audioData[i] * gain;

            // 应用轻度压缩
            if (Math.abs(sample) > AUDIO_DETECTION_CONFIG.compressionThreshold) {
                const sign = sample >= 0 ? 1 : -1;
                const compressed = Math.pow(Math.abs(sample), compressionRatio);
                sample = sign * compressed;
            }

            // 限制范围
            sample = Math.max(-1, Math.min(1, sample));

            // 转换为16位PCM
            samples[i] = sample < 0 ? sample * 0x8000 : sample * 0x7FFF;
        }

        return samples;
    }

    async setupAudioWorklet() {
        // 创建AudioWorklet处理器的内联代码
        const workletCode = `
            class AudioProcessor extends AudioWorkletProcessor {
                process(inputs, outputs, parameters) {
                    const input = inputs[0];
                    if (input.length > 0) {
                        const audioData = input[0];
                        // 发送音频数据到主线程
                        this.port.postMessage({
                            type: 'audioData',
                            data: audioData
                        });
                    }
                    return true;
                }
            }
            registerProcessor('audio-processor', AudioProcessor);
        `;

        // 创建Blob URL
        const blob = new Blob([workletCode], { type: 'application/javascript' });
        const workletUrl = URL.createObjectURL(blob);

        try {
            await this.audioContext.audioWorklet.addModule(workletUrl);

            this.processor = new AudioWorkletNode(this.audioContext, 'audio-processor');

            this.processor.port.onmessage = (event) => {
                if (event.data.type === 'audioData' && this.isRecording) {
                    try {
                        const pcmData = this.convertToPCM16(event.data.data);
                        if (this.OnReceivedData) {
                            this.OnReceivedData(pcmData.buffer);
                        }
                    } catch (error) {
                        console.error('AudioWorklet音频处理错误:', error);
                    }
                }
            };

            this.source.connect(this.processor);
            this.processor.connect(this.audioContext.destination);

            this.isLog && console.log('AudioWorkletNode设置完成');

        } finally {
            // 清理Blob URL
            URL.revokeObjectURL(workletUrl);
        }
    }
}

// SpeechRecognizer语音识别类
class SpeechRecognizer {
    constructor(params, requestId, isLog) {
        this.socket = null;
        this.isSignSuccess = false;
        this.isSentenceBegin = false;
        this.query = { ...params };
        this.isRecognizeComplete = false;
        this.requestId = requestId;
        this.isLog = isLog;
        this.sendCount = 0;
        this.getMessageList = [];

        // 回调函数
        this.OnRecognitionStart = null;
        this.OnSentenceBegin = null;
        this.OnRecognitionResultChange = null;
        this.OnSentenceEnd = null;
        this.OnRecognitionComplete = null;
        this.OnError = null;
    }

    stop() {
        if (this.socket && this.socket.readyState === 1) {
            this.socket.send(JSON.stringify({ type: 'end' }));
            this.isRecognizeComplete = true;
        } else {
            if (this.socket && this.socket.readyState === 1) {
                this.socket.close();
            }
        }
    }

    async start() {
        try {
            this.socket = null;
            this.getMessageList = [];

            this.isLog && console.log(this.requestId, '开始建立WebSocket连接...');

            const url = await getUrl(this, this.query);
            if (!url) {
                throw new Error('URL生成失败');
            }

            this.isLog && console.log(this.requestId, 'WebSocket URL生成成功');

            if (!('WebSocket' in window)) {
                throw new Error('浏览器不支持WebSocket');
            }

            // 直接使用生成的URL建立WebSocket连接
            // 用户认证信息已经通过createQuery函数添加到请求参数中
            this.socket = new WebSocket(url);

            this.isLog && console.log(this.requestId, 'WebSocket实例创建成功');

            this.socket.onopen = (e) => {
                this.isLog && console.log(this.requestId, 'WebSocket连接建立', e);

                // 记录认证信息
                if (TENCENT_CONFIG.enableTokenAuth) {
                    const userToken = getUserToken();
                    if (userToken) {
                        console.log('🔑 WebSocket连接已建立，用户认证信息已通过请求参数传递');
                    } else {
                        console.warn('⚠️ WebSocket连接已建立，但请求参数中未包含用户认证信息');
                    }
                }
            };

            this.socket.onmessage = async (e) => {
                try {
                    this.getMessageList.push(JSON.stringify(e));
                    const response = JSON.parse(e.data);
                    if (response.code !== 0) {
                        if (this.socket.readyState === 1) {
                            this.socket.close();
                        }
                        this.isLog && console.log(this.requestId, JSON.stringify(response));
                        this.OnError && this.OnError(response);
                    } else {
                        if (!this.isSignSuccess) {
                            this.OnRecognitionStart && this.OnRecognitionStart(response);
                            this.isSignSuccess = true;
                        }
                        if (response.final === 1) {
                            this.OnRecognitionComplete && this.OnRecognitionComplete(response);
                            return;
                        }
                        if (response.result) {
                            if (response.result.slice_type === 0) {
                                this.OnSentenceBegin && this.OnSentenceBegin(response);
                                this.isSentenceBegin = true;
                            } else if (response.result.slice_type === 2) {
                                if (!this.isSentenceBegin) {
                                    this.OnSentenceBegin && this.OnSentenceBegin(response);
                                }
                                this.OnSentenceEnd && this.OnSentenceEnd(response);
                            } else {
                                this.OnRecognitionResultChange && this.OnRecognitionResultChange(response);
                            }
                        }
                        this.isLog && console.log(this.requestId, response);
                    }
                } catch (e) {
                    this.isLog && console.log(this.requestId, 'socket.onmessage catch error', JSON.stringify(e));
                }
            };

            this.socket.onerror = (e) => {
                this.isLog && console.log(this.requestId, 'socket error callback', e);
                this.socket.close();
                this.OnError && this.OnError(e);
            }

            this.socket.onclose = (event) => {
                try {
                    if (!this.isRecognizeComplete) {
                        this.isLog && console.log(this.requestId, 'socket is close and error', JSON.stringify(event));

                        // 检查是否是认证相关错误
                        if (event.code === 1006 || event.code === 1011) {
                            console.error('❌ WebSocket连接异常关闭，可能是认证失败:', {
                                code: event.code,
                                reason: event.reason,
                                hasToken: !!getUserToken(),
                                enableTokenAuth: TENCENT_CONFIG.enableTokenAuth
                            });
                        }

                        this.OnError && this.OnError(event);
                    }
                } catch (e) {
                    this.isLog && console.log(this.requestId, 'socket is onclose catch' + this.sendCount, JSON.stringify(e));
                }
            }

        } catch (error) {
            this.isLog && console.log(this.requestId, 'WebSocket连接失败:', error);
            this.OnError && this.OnError(error);
        }
    }

    close() {
        this.socket && this.socket.readyState === 1 && this.socket.close(1000);
    }

    write(data) {
        try {
            if (!this.socket || String(this.socket.readyState) !== '1') {
                setTimeout(() => {
                    if (this.socket && this.socket.readyState === 1) {
                        this.socket.send(data);
                    }
                }, 100);
                return false;
            }
            this.sendCount += 1;
            this.socket.send(data);
        } catch (e) {
            this.isLog && console.log(this.requestId, '发送数据 error catch', e);
        }
    };
}

// 主要的WebAudioSpeechRecognizer类
export class TencentWebAudioSpeechRecognizer {
    constructor(params = {}, isLog = false) {
        // 使用代理配置，移除腾讯云API相关参数
        this.params = {
            // 代理模式下的基础参数
            engine_model_type: '16k_zh',  // 16kHz中文模型
            voice_format: 1,              // PCM格式
            needvad: 1,                   // 启用语音活动检测 (重要：帮助过滤杂音)
            filter_dirty: 1,              // 过滤脏词
            filter_modal: 1,              // 过滤语气词：0-不过滤，1-部分过滤，2-严格过滤
            filter_punc: 0,               // 过滤标点符号：0-不过滤，1-过滤句末标点，2-过滤所有标点
            convert_num_mode: 1,          // 数字转换模式：0-数字，1-中文数字，2-全角数字
            word_info: 2,                 // 词信息：0-不输出，1-输出对应时间戳，2-输出详细词信息
            noise_threshold: 0.4,         // 噪音阈值
            max_speak_time: 30000,        // 最大单次识别时长(ms)
            debug: false,                 // 调试模式

            // 词汇表配置
            hotword_id: '',               // 热词ID（可选）
            replace_text_id: '',          // 替换词汇表ID（可选）

            // 合并用户传入的参数
            ...params
        };

        console.log('腾讯云ASR代理配置 - 强化杂音过滤:', {
            proxyUrl: TENCENT_CONFIG.proxyUrl,
            engine_model_type: this.params.engine_model_type,
            voice_format: this.params.voice_format,
            needvad: this.params.needvad,
            filter_modal: this.params.filter_modal,
            noise_threshold: this.params.noise_threshold,
            hotword_id: this.params.hotword_id || '未设置',
            replace_text_id: this.params.replace_text_id || '未设置',
            note: '使用代理服务器，已优化为检测较大声音，过滤环境杂音'
        });

        this.recorder = null;
        this.speechRecognizer = null;
        this.isCanSendData = false;
        this.isNormalEndStop = false;
        this.audioData = [];
        this.isLog = isLog;
        this.requestId = null;

        // 回调函数
        this.OnRecognitionStart = null;
        this.OnSentenceBegin = null;
        this.OnRecognitionResultChange = null;
        this.OnSentenceEnd = null;
        this.OnRecognitionComplete = null;
        this.OnError = null;
        this.OnRecorderStop = null;
    }

    start() {
        try {
            console.log('🚀 开始腾讯云代理语音识别...');
            console.log('🌍 环境信息:', {
                isDev: window.location.protocol === 'http:',
                location: window.location.href,
                userAgent: navigator.userAgent,
                hasWebSocket: 'WebSocket' in window,
                hasMediaDevices: 'mediaDevices' in navigator,
                hasGetUserMedia: !!(navigator.mediaDevices && navigator.mediaDevices.getUserMedia)
            });

            // 检查代理配置
            console.log('📋 腾讯云ASR代理配置:', {
                hasProxyUrl: !!TENCENT_CONFIG.proxyUrl,
                hasProxyParams: !!TENCENT_CONFIG.proxyParams,
                proxyUrl: TENCENT_CONFIG.proxyUrl,
                engine_model_type: this.params.engine_model_type
            });

            if (!TENCENT_CONFIG.proxyUrl) {
                throw new Error('腾讯云代理URL未配置');
            }

            this.isLog && console.log('🔑 开始腾讯云代理语音识别 - 配置验证通过');
            this.requestId = guid();

            // 初始化录音器
            this.recorder = new WebRecorder(this.requestId, this.params, this.isLog);

            this.recorder.OnReceivedData = (data) => {
                if (this.isCanSendData) {
                    this.speechRecognizer && this.speechRecognizer.write(data);
                }
            };

            this.recorder.OnError = (err) => {
                this.speechRecognizer && this.speechRecognizer.close();
                this.stop();
                this.OnError && this.OnError(err);
            };

            this.recorder.OnRecorderStop = () => {
                this.OnRecorderStop && this.OnRecorderStop();
            };

            // 启动录音器
            this.recorder.start();

            // 创建语音识别器
            this.speechRecognizer = new SpeechRecognizer(this.params, this.requestId, this.isLog);

            // 设置识别器回调
            this.speechRecognizer.OnRecognitionStart = (res) => {
                console.log('🎙️ 腾讯云代理语音识别开始');
                this.isCanSendData = true;
                this.OnRecognitionStart && this.OnRecognitionStart(res);
            };

            this.speechRecognizer.OnSentenceBegin = (res) => {
                this.OnSentenceBegin && this.OnSentenceBegin(res);
            };

            this.speechRecognizer.OnRecognitionResultChange = (res) => {
                this.OnRecognitionResultChange && this.OnRecognitionResultChange(res);
            };

            this.speechRecognizer.OnSentenceEnd = (res) => {
                this.OnSentenceEnd && this.OnSentenceEnd(res);
            };

            this.speechRecognizer.OnRecognitionComplete = (res) => {
                this.OnRecognitionComplete && this.OnRecognitionComplete(res);
                this.isCanSendData = false;
                this.isNormalEndStop = true;
            };

            this.speechRecognizer.OnError = (res) => {
                if (this.speechRecognizer && !this.isNormalEndStop) {
                    this.OnError && this.OnError(res);
                }
                this.speechRecognizer = null;
                this.recorder && this.recorder.stop();
                this.isCanSendData = false;
            };

            // 建立WebSocket连接
            this.speechRecognizer.start();

        } catch (e) {
            console.error('启动腾讯云代理语音识别失败:', e);
            this.OnError && this.OnError(e);
        }
    }

    stop() {
        try {
            this.isLog && console.log('停止腾讯云语音识别');

            // 停止识别器
            if (this.speechRecognizer) {
                try {
                    this.speechRecognizer.stop();
                    this.speechRecognizer.close();
                } catch (e) {
                    console.warn('停止speechRecognizer时出错:', e);
                }
                this.speechRecognizer = null;
            }

            // 停止录音器
            if (this.recorder) {
                try {
                    this.recorder.stop();
                } catch (e) {
                    console.warn('停止recorder时出错:', e);
                }
            }

            // 重置状态
            this.isCanSendData = false;
            this.isNormalEndStop = true;

        } catch (error) {
            console.error('停止ASR时发生错误:', error);
        }
    }

    destroyStream() {
        try {
            this.isLog && console.log('销毁音频流');

            // 先停止所有活动
            this.stop();

            // 销毁录音器流
            if (this.recorder) {
                try {
                    this.recorder.destroyStream();
                } catch (e) {
                    console.warn('销毁录音器流时出错:', e);
                }
                this.recorder = null;
            }

            // 清理音频数据
            this.audioData = [];

        } catch (error) {
            console.error('销毁ASR流时发生错误:', error);
        }
    }
}

// 识别状态常量
export const TENCENT_ASR_STATES = {
    IDLE: 'idle',
    CONNECTING: 'connecting',
    CONNECTED: 'connected',
    RECOGNIZING: 'recognizing',
    PROCESSING: 'processing',
    COMPLETED: 'completed',
    ERROR: 'error'
} 
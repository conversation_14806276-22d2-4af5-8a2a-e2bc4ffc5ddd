const { Tray, Menu, nativeImage, app } = require('electron')
const { join } = require('path')
const fs = require('fs')

const isDev = process.env.NODE_ENV === 'development'

/**
 * 系统托盘管理类
 */
class TrayManager {
  constructor(appManager) {
    this.appManager = appManager
    this.tray = null
  }

  /**
   * 创建系统托盘
   */
  createTray() {
    console.log('Creating system tray...')

    // 多个可能的托盘图标路径
    const possibleTrayPaths = isDev
      ? [
        join(process.cwd(), 'public/assets/logo.png'),
        join(process.cwd(), 'public/assets/logo.ico'),
        join(__dirname, '../../../public/assets/logo.png')
      ]
      : [
        join(process.resourcesPath, 'assets/logo.png'),
        join(process.resourcesPath, 'assets/logo.ico'),
        join(app.getAppPath(), 'assets/logo.ico'),
        join(app.getAppPath(), 'assets/logo.png')
      ]

    console.log('=== TRAY ICON PATHS DEBUG ===')
    console.log('isDev:', isDev)
    console.log('process.resourcesPath:', process.resourcesPath)
    console.log('app.getAppPath():', app.getAppPath())
    console.log('__dirname:', __dirname)

    let trayIconPath = null

    // 查找存在的图标文件
    for (const path of possibleTrayPaths) {
      console.log('Checking tray icon path:', path)
      console.log('File exists:', fs.existsSync(path))
      if (fs.existsSync(path)) {
        trayIconPath = path
        console.log('Found valid tray icon at:', trayIconPath)
        break
      }
    }

    // 如果没有找到图标文件，创建一个简单的图标
    if (!trayIconPath) {
      console.log('No tray icon found, creating temporary icon')
      try {
        // 尝试使用 nativeImage 创建一个简单的图标

        // 创建一个16x16的简单图标
        const iconData = nativeImage.createFromBuffer(Buffer.from([
          137, 80, 78, 71, 13, 10, 26, 10, 0, 0, 0, 13, 73, 72, 68, 82, 0, 0, 0, 16, 0, 0, 0, 16, 8, 6, 0, 0, 0, 31, 243, 255, 97, 0, 0, 0, 68, 73, 68, 65, 84, 56, 17, 99, 96, 24, 5, 163, 96, 20, 140, 130, 81, 48, 10, 70, 193, 40, 24, 5, 163, 96, 20, 140, 130, 81, 48, 10, 70, 193, 40, 24, 5, 163, 96, 20, 140, 130, 81, 48, 10, 70, 193, 40, 24, 5, 163, 96, 20, 140, 130, 81, 48, 10, 70, 193, 40, 24, 5, 163, 96, 20, 140, 130, 81, 48, 10, 70, 193, 0, 0, 0, 0, 73, 69, 78, 68, 174, 66, 96, 130
        ]))

        this.tray = new Tray(iconData)
        console.log('Tray created with generated icon')
      } catch (iconError) {
        console.error('Failed to create generated icon:', iconError)
        // 最后的备用方案：尝试不指定图标创建托盘
        try {
          this.tray = new Tray(nativeImage.createEmpty())
          console.log('Tray created with empty icon')
        } catch (emptyError) {
          console.error('Failed to create tray with empty icon:', emptyError)
          this.tray = null
          return
        }
      }
    } else {
      // 使用找到的图标文件创建托盘
      try {
        console.log('Creating tray with icon:', trayIconPath)
        this.tray = new Tray(trayIconPath)
        console.log('Tray created successfully with file icon')
      } catch (error) {
        console.error('Failed to create tray with file icon:', error)
        // 备用方案：使用生成的图标
        try {
          this.tray = new Tray(nativeImage.createEmpty())
          console.log('Tray created with empty icon as fallback')
        } catch (fallbackError) {
          console.error('All tray creation methods failed:', fallbackError)
          this.tray = null
          return
        }
      }
    }

    // 检查托盘是否创建成功
    if (!this.tray) {
      console.error('Tray creation failed completely')
      return
    }

    console.log('Tray instance created successfully')
    console.log('Tray methods available:', Object.getOwnPropertyNames(Object.getPrototypeOf(this.tray)))

    // 设置托盘提示文字
    try {
      this.tray.setToolTip('犇犇数字员工助手')
      console.log('Tray tooltip set successfully')
    } catch (tooltipError) {
      console.error('Failed to set tray tooltip:', tooltipError)
    }

    // 创建托盘右键菜单
    this.updateTrayMenu()

    // 托盘图标左键点击事件
    try {
      this.tray.on('click', () => {
        console.log('Tray clicked!')
        if (this.appManager.windowManager?.isMainWindowValid()) {
          const mainWindow = this.appManager.windowManager.getMainWindow()
          if (mainWindow.isVisible()) {
            console.log('Hiding main window')
            mainWindow.hide()
          } else {
            console.log('Showing main window')
            mainWindow.show()
            mainWindow.focus()
          }
        } else {
          console.log('Main window not valid, creating new window')
          this.appManager.windowManager?.showMainWindow()
        }
      })
      console.log('Tray click event bound successfully')
    } catch (clickError) {
      console.error('Failed to bind tray click event:', clickError)
    }

    // 托盘图标双击事件
    try {
      this.tray.on('double-click', () => {
        console.log('Tray double-clicked!')
        if (this.appManager.windowManager?.isMainWindowValid()) {
          const mainWindow = this.appManager.windowManager.getMainWindow()
          mainWindow.show()
          mainWindow.focus()
        } else {
          console.log('Main window not valid, showing main window')
          this.appManager.windowManager?.showMainWindow()
        }
      })
      console.log('Tray double-click event bound successfully')
    } catch (doubleClickError) {
      console.error('Failed to bind tray double-click event:', doubleClickError)
    }

    console.log('=== TRAY SETUP COMPLETED ===')
    console.log('Tray should now be visible in system tray')
  }

  /**
   * 更新托盘菜单状态（根据登录状态）
   */
  updateTrayMenu() {
    if (!this.tray) return

    const contextMenu = Menu.buildFromTemplate([
      {
        label: '显示主窗口',
        click: () => {
          if (this.appManager.windowManager?.isMainWindowValid()) {
            this.appManager.windowManager.showMainWindow()
          } else {
            this.appManager.windowManager?.showMainWindow()
          }
        }
      },
      {
        label: this.appManager.isLoggedIn ? '显示悬浮窗' : '悬浮窗（需登录）',
        enabled: this.appManager.isLoggedIn,
        click: () => {
          if (this.appManager.windowManager?.isFloatingWindowValid()) {
            this.appManager.windowManager.showFloatingWindow()
          } else if (this.appManager.isLoggedIn) {
            // 如果已登录但悬浮窗不存在，创建它
            this.appManager.windowManager?.createFloatingWindow()
          }
        }
      },
      { type: 'separator' },
      {
        label: '退出',
        click: () => {
          console.log('🔄 用户点击托盘退出按钮')
          // 标记应用正在退出
          this.appManager.app.isQuitting = true
          // 强制退出应用
          this.appManager.app.quit()
          // 如果quit()没有立即生效，强制退出进程
          setTimeout(() => {
            console.log('🔄 强制退出进程')
            process.exit(0)
          }, 500)
        }
      }
    ])

    this.tray.setContextMenu(contextMenu)
    console.log('🔄 托盘菜单已更新，登录状态:', this.appManager.isLoggedIn)
  }

  /**
   * 显示气球提示
   */
  displayBalloon(options) {
    if (this.tray && this.tray.displayBalloon) {
      this.tray.displayBalloon(options)
    }
  }

  /**
   * 获取托盘实例
   */
  getTray() {
    return this.tray
  }

  /**
   * 销毁托盘
   */
  destroy() {
    if (this.tray) {
      this.tray.destroy()
      this.tray = null
    }
  }
}

module.exports = TrayManager

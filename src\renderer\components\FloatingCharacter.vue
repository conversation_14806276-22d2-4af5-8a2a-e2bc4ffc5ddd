<template>
  <div class="floating-character" :class="{ collapsed: !showChatArea }">
    <!-- 聊天记录区域 -->
    <div v-if="isLoggedIn" class="chat-area" :class="{
      'chat-area-hidden': !showChatArea
    }">
      <!-- 🔄 【需求3】聊天区域标题栏和关闭按钮 -->
      <div class="chat-header">
        <button @click="newConversation" class="chat-new-btn" title="新建会话">
          🔄
        </button>
        <button @click="closeChatArea" class="chat-close-btn" title="关闭对话框">
          ×
        </button>
      </div>

      <!-- 聊天消息列表 -->
      <div class="chat-messages" ref="chatMessagesRef">
        <div v-if="recentMessages.length === 0" class="no-messages">
          暂无聊天记录
          <div class="tip">开始与犇犇对话吧！</div>
        </div>

        <div v-for="(message, index) in recentMessages" :key="`chat-${index}`" class="chat-message"
          :class="{ 'user-message': message.role === 'user', 'assistant-message': message.role === 'assistant' }">

          <!-- AI消息头像 -->
          <div v-if="message.role === 'assistant'" class="message-avatar">
            <img :src="logoImg" alt="AI" class="avatar-img" />
          </div>

          <!-- 消息内容 -->
          <div class="message-content">
            <div class="message-bubble" :class="{
              'user-bubble': message.role === 'user',
              'assistant-bubble': message.role === 'assistant',
              'thinking-bubble': message.isThinking,
              'error-bubble': message.isError,
              'email-preview-bubble': message.isEmailPreview
            }">
              <!-- 思考中动画 -->
              <div v-if="message.isThinking" class="thinking-dots">
                <span class="dot dot1"></span>
                <span class="dot dot2"></span>
                <span class="dot dot3"></span>
              </div>
              <!-- 错误图标 -->
              <div v-else-if="message.isError" class="error-content">
                <span class="error-icon">❌</span> {{ message.content }}
              </div>
              <!-- 邮件预览内容 -->
              <div v-else-if="message.isEmailPreview" class="email-preview-content"
                :key="message.timestamp + '-' + (message.isSent ? 1 : 0)">
                <!-- 邮件预览显示逻辑保持不变 -->
                <div v-if="!message.isEditing" class="email-preview-text">
                  <div class="email-preview-form">
                    <div class="form-group">
                      <label>收件人:</label>
                      <div class="preview-field">
                        <span
                          v-for="(email, idx) in (Array.isArray(message.emailData.to) ? message.emailData.to : []).filter(e => e && e.trim())"
                          :key="'to-' + idx">{{ email }}</span>
                      </div>
                    </div>
                    <div class="form-group">
                      <label>抄送:</label>
                      <div class="preview-field">
                        <span
                          v-for="(email, idx) in (Array.isArray(message.emailData.cc) ? message.emailData.cc : []).filter(e => e && e.trim())"
                          :key="'cc-' + idx">{{ email }}</span>
                      </div>
                    </div>
                    <div class="form-group">
                      <label>主题:</label>
                      <div class="preview-field">{{ message.emailData.subject }}</div>
                    </div>
                    <div class="form-group">
                      <label>正文:</label>
                      <div class="preview-field email-body">{{ message.emailData.message }}</div>
                    </div>
                  </div>
                  <div v-if="!message.isSent && !message.isSending" class="email-preview-actions">
                    <button @click="startMessageEdit(message)" class="edit-btn">✏️ 修改</button>
                    <button @click="sendEmailFromMessagePreview(message)" class="send-btn">📤 发送</button>
                  </div>
                </div>
                <!-- 邮件编辑状态 -->
                <div v-else-if="message.isEditing && message.editingData" class="email-edit-form">
                  <!-- 邮件编辑表单逻辑保持不变 -->
                  <div class="email-preview-form">
                    <div class="form-group">
                      <label>收件人:</label>
                      <div class="email-list">
                        <div v-for="(email, index) in (message.editingData.to || [])" :key="'to-edit-' + index"
                          class="email-item">
                          <input :value="message.editingData.to[index]"
                            @input="updateEditingField(message, 'to', $event.target.value, index)" type="email"
                            placeholder="请输入收件人邮箱" class="email-input" />
                          <button @click="removeEditingRecipient(message, index)" class="remove-btn"
                            v-if="message.editingData.to.length > 1">×</button>
                        </div>
                        <button @click="addEditingRecipient(message)" class="add-btn">+ 添加收件人</button>
                      </div>
                    </div>
                    <div class="form-group">
                      <label>抄送:</label>
                      <div class="email-list">
                        <div v-for="(email, index) in (message.editingData.cc || [])" :key="'cc-edit-' + index"
                          class="email-item">
                          <input :value="message.editingData.cc[index]"
                            @input="updateEditingField(message, 'cc', $event.target.value, index)" type="email"
                            placeholder="请输入抄送人邮箱" class="email-input" />
                          <button @click="removeEditingCc(message, index)" class="remove-btn"
                            v-if="message.editingData.cc.length > 1">×</button>
                        </div>
                        <button @click="addEditingCc(message)" class="add-btn">+ 添加抄送</button>
                      </div>
                    </div>
                    <div class="form-group">
                      <label>主题:</label>
                      <input :value="message.editingData.subject"
                        @input="updateEditingField(message, 'subject', $event.target.value)" type="text"
                        placeholder="请输入邮件主题" class="subject-input" />
                    </div>
                    <div class="form-group">
                      <label>正文:</label>
                      <textarea :value="message.editingData.message"
                        @input="updateEditingField(message, 'message', $event.target.value)" placeholder="请输入邮件内容"
                        class="message-textarea" rows="6"></textarea>
                    </div>
                  </div>
                  <div class="email-preview-actions">
                    <button @click="saveMessageEdit(message)" class="save-btn">💾 保存</button>
                    <button @click="cancelMessageEdit(message)" class="cancel-btn">❌ 取消</button>
                  </div>
                </div>
              </div>
              <!-- 普通消息内容 -->
              <template v-else>
                <div v-html="formatMessageContent(message.content)"></div>
              </template>

              <!-- 文件引用链接和联网搜索引用 - 仅对AI消息显示 -->
              <div
                v-if="message.role === 'assistant' && !message.isThinking && !message.isEmailPreview && (shouldShowFileReferences(message.content, message.fileReferences) || shouldShowWebReferences(message.content, message.webReferences))"
                class="search-references">
                <!-- 知识库文件引用 -->
                <div v-if="shouldShowFileReferences(message.content, message.fileReferences)" class="file-references">
                  <div class="file-references-label">📁 参考文件：</div>
                  <div v-for="(fileRef, refIndex) in message.fileReferences" :key="refIndex" class="file-reference-link"
                    @click="openFile(fileRef.filePath, { isKnowledgeReference: true })">
                    {{ fileRef.fileName }}
                  </div>
                </div>

                <!-- 联网搜索引用 -->
                <div v-if="shouldShowWebReferences(message.content, message.webReferences)" class="web-references">
                  <div class="web-references-label">🌐 搜索来源：</div>
                  <div v-for="(webRef, refIndex) in message.webReferences" :key="refIndex" class="web-reference-link"
                    @click="openWebReference(webRef.url)" :title="webRef.content">
                    {{ webRef.title }}
                  </div>
                </div>
              </div>
            </div>

            <!-- 消息操作按钮 -->
            <!-- <div class="message-actions">
              <button v-if="message.role === 'user'" @click="editUserMessage(message)"
                class="action-btn edit-message-btn" title="编辑">
                <img :src="menuImgEditImage" />
              </button>

              <button v-if="message.role === 'assistant' && !message.isThinking" @click="copyMessage(message.content)"
                class="action-btn copy-btn" title="复制">
                <img :src="menuImgCopyImage" />
              </button>
            </div> -->
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area" ref="inputAreaRef" :style="{ height: inputAreaHeight + 'px' }">
        <!-- 添加顶部拖拽句柄 -->
        <div class="input-area-resize-handle" @mousedown="startInputAreaResize"></div>

        <!-- 输入模式切换 -->
        <div class="input-mode-switch">
          <button @click="toggleInputMode" class="mode-btn" :class="{ active: currentInputMode === 'voice' }">
            <img v-if="currentInputMode === 'voice'" :src="menuImgVoiceOnImage" />
            <img v-else :src="menuImgVoiceOffImage" />
          </button>
        </div>

        <!-- 语音模式 -->
        <div v-if="currentInputMode === 'voice'" class="voice-input-area"
          :class="{ 'voice-active': smartVoiceState !== 'idle' }">
          <!-- 🔄 【新增】SVG音波动画组件，替换静态图片 -->
          <VoiceWaveAnimation :state="smartVoiceState" :is-tts-playing="isTTSPlaying" :is-enabled="smartVoiceEnabled"
            @click="handleVoiceWaveClick" class="voice-wave-component"
            :class="{ 'smaller-wave': smartVoiceState !== 'idle' }" />
          <!-- 🔄 【新增】语音模式下的停止按钮 - 仅在AI思考或回复时显示 -->
          <button v-if="isAIThinking || isTTSPlaying" @click="stopAIProcessing" class="stop-btn voice-stop-btn"
            title="停止AI思考和回复">
            <img :src="menuImgStopImage" />
          </button>
        </div>

        <!-- 文本模式 -->
        <div v-if="currentInputMode === 'text'" class="text-input-area">
          <div class="text-input-wrapper">
            <textarea v-model="textInputValue" @keydown="handleTextareaKeydown" placeholder="跟犇犇说点什么..."
              class="text-input" rows="3" ref="textareaRef"></textarea>
            <button @click="sendTextMessage" :disabled="!textInputValue.trim()" class="send-btn">
              <img :src="menuImgSendImage" />
            </button>
            <!-- 🔄 【新增】停止按钮 - 仅在AI思考或回复时显示 -->
            <button v-if="isAIThinking || isTTSPlaying" @click="stopAIProcessing" class="stop-btn" title="停止AI思考和回复">
              <img :src="menuImgStopImage" />
            </button>
          </div>
        </div>

        <!-- 联网搜索开关 -->
        <div class="web-search-area">
          <button @click="toggleWebSearch" class="web-search-toggle" :class="{ active: webSearchEnabled }"
            :title="webSearchEnabled ? '点击关闭智能联网搜索' : '点击开启智能联网搜索'">
            <span class="search-icon">🌐</span>
          </button>
        </div>
      </div>

      <!-- 状态显示 -->
      <div class="status-display">
        <!-- 🔄 【修改】根据实际智能语音状态显示对应文字 -->
        <span>{{ getStatusDisplayText() }}</span>
      </div>
    </div>
    <!-- 聊天气泡箭头 - 指向character -->
    <div class="chat-bubble-arrow" v-if="showChatArea"></div>

    <!-- 动漫人物 -->
    <div class="flex-box-center">
      <div class="character" @click="handleCharacterClick" @mousedown="handleCharacterMouseDown" @contextmenu.prevent>
        <img :src="characterImage" :alt="characterName" class="character-image" />

        <!-- 圆圈菜单 - CSS hover显示 -->
        <div class="character-menu">
          <div class="menu-item p1" @click.stop="openXiaoZhuRen">
            <img :src="menuImgXzrImage" />
            <span>晓主任</span>
          </div>
          <div class="menu-item p2" @click.stop="openXiaoLawyer">
            <img :src="menuImgXlsImage" />
            <span>晓律师</span>
          </div>
          <div class="menu-item p3" @click.stop="openAIStore">
            <img :src="menuImgRzygImage" />
            <span>数字员工<br>AI Store</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 邮件确认弹框 -->
    <EmailConfirmModal v-if="showEmailModal" :visible="showEmailModal" :initial-data="emailModalData"
      @send="handleEmailSend" @close="closeEmailModal" @error="handleEmailError" />

  </div>
</template>

<script>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { useChatStore } from '../stores/chat.js'
import { AliyunConversationManager, ALIYUN_CONVERSATION_STATES } from '../utils/AliyunConversationManager.js'
import { TencentConversationManager } from '../utils/TencentConversationManager.js'
import { smartVoiceConfigManager } from '../utils/smart-voice-config.js'
import asrProviderManager from '../utils/asrProviderManager.js'
import { sendMCPChatRequest, cancelCurrentAIRequest } from '../utils/mcpChatAPI.js'
import { MCP_SYSTEM_PROMPT } from '../utils/mcpToolDefinitions.js'
import { showAlert, showSuccess, showError, showWarning } from '../utils/dialog.js'
import CircularMenu from './CircularMenu.vue'
import TextInput from './TextInput.vue'
import EmailConfirmModal from './EmailConfirmModal.vue'
import VoiceWaveAnimation from './VoiceWaveAnimation.vue'

// 导入图片资源 - 让Vite正确处理路径
import benbenIdleImg from '/assets/characters/benben_idle.png'
import benbenListeningImg from '/assets/characters/benben_listening.png'
import benbenSpeakingImg from '/assets/characters/benben_speaking.png'
import benbenThinkingImg from '/assets/characters/benben_thinking.png'
import voiceOnImg from '/assets/voice-on.png'
import voiceImg from '/assets/voice.png'
import keywordsOnImg from '/assets/keywords-on.png'
import keywordsImg from '/assets/keywords.png'
import historyImg from '/assets/history.png'
import historyOnImg from '/assets/history-on.png'
import logoImg from '/assets/logo.png'
import menuImgBg from '/assets/menu/icon-menu-bg.png'
import menuImgXls from '/assets/menu/icon-xls.png'
import menuImgXzr from '/assets/menu/icon-xzr.png'
import menuImgRzyg from '/assets/menu/icon-szyg.png'
import menuImgVoiceOn from '/assets/menu/icon-voice.png'
import menuImgVoiceOff from '/assets/menu/icon-keybrod.png'
import menuImgSend from '/assets/menu/icon-send.png'
import menuImgStop from '/assets/menu/icon-stop.png'
import menuImgVoiceBg from '/assets/menu/icon-voice-bg.png'
import menuImgEdit from '/assets/menu/icon-edit.png'
import menuImgCopy from '/assets/menu/icon-copy.png'

export default {
  name: 'FloatingCharacter',
  components: {
    CircularMenu,
    TextInput,
    EmailConfirmModal,
    VoiceWaveAnimation
  },
  setup() {


    const authStore = useAuthStore()
    const chatStore = useChatStore()
    const showTextInput = ref(false)
    const showStatus = ref(true)
    const showChatArea = ref(false) // 新增：控制聊天区域显示，默认关闭

    // 新增状态
    const currentInputMode = ref('text') // 'voice' 或 'text'
    const textInputValue = ref('')
    const textareaRef = ref(null)
    const chatMessagesRef = ref(null)
    const webSearchEnabled = ref(false) // 联网搜索开关状态，默认关闭
    const characterState = computed(() => {
      // 使用智能语音状态
      return smartVoiceState.value !== 'idle' ? smartVoiceState.value : 'idle'
    })
    const isDragging = ref(false)
    const dragStart = ref({ x: 0, y: 0 })

    // 拖拽相关常量和状态
    const DRAG_DISTANCE_LIMIT = 5  // 拖拽阈值
    let globalMouseMoveHandler = null
    let globalMouseUpHandler = null
    let globalRightClickHandler = null

    // 智能语音相关 - 腾讯云ASR + 唤醒词
    const conversationManager = ref(null)
    const smartVoiceEnabled = ref(false)
    const smartVoiceState = ref('idle')
    const volumeLevel = ref(0)
    const showVolumeIndicator = ref(true)
    const voiceResultText = ref('')
    const recordingStatus = ref('')
    const recordingError = ref('')

    // 🔄 【新增】TTS播放状态检测
    const isTTSPlaying = ref(false)

    // 🔄 【新增】AI思考状态检测
    const isAIThinking = ref(false)

    // 邮件相关状态
    const showEmailModal = ref(false)
    const emailModalData = ref({
      to: [],
      cc: [],
      subject: '',
      message: ''
    })
    const pendingEmailCallback = ref(null)

    // 窗口高度动态调整相关状态
    const FULL_HEIGHT = 800  // 完整窗口高度
    const COLLAPSED_HEIGHT = 290  // 折叠高度：角色(210px) + 菜单偏移(60px) + 边距(20px)
    const currentWindowHeight = ref(COLLAPSED_HEIGHT) // 🔄 【修改】初始高度为折叠高度
    const isWindowResizing = ref(false)

    // 动态调整窗口高度
    const adjustWindowHeight = async (showChat) => {
      if (isWindowResizing.value) return

      // 🔄 【修复】如果正在拖拽，不调整窗口大小
      if (isDragging.value) {
        console.log('🖱️ 正在拖拽中，跳过窗口大小调整')
        return
      }

      const targetHeight = showChat ? FULL_HEIGHT : COLLAPSED_HEIGHT
      if (currentWindowHeight.value === targetHeight) {
        console.log('🔄 窗口高度已经是目标高度，无需调整:', targetHeight)
        return
      }

      console.log('🔄 开始调整窗口高度:', {
        from: currentWindowHeight.value,
        to: targetHeight,
        showChat: showChat,
        isDragging: isDragging.value
      })

      isWindowResizing.value = true

      try {
        if (window.electronAPI && window.electronAPI.resizeFloatingWindow) {
          window.electronAPI.resizeFloatingWindow({ height: targetHeight })
          currentWindowHeight.value = targetHeight
          console.log('✅ 窗口高度调整成功:', targetHeight)
        } else {
          console.error('❌ electronAPI.resizeFloatingWindow 不可用')
        }
      } catch (error) {
        console.error('❌ 窗口高度调整失败:', error)
      } finally {
        isWindowResizing.value = false
      }
    }

    // 监听聊天区域显示状态变化，动态调整窗口高度
    watch(showChatArea, (newValue) => {
      console.log('💬 聊天区域状态变化:', newValue, '调整窗口高度')

      // 🔄 【修复】检查全局拖拽标志
      if (window._showChatAreaWatcher === false) {
        console.log('🖱️ 拖拽中，忽略showChatArea状态变化')
        return
      }

      // 🔄 【修复】如果正在拖拽，延迟调整窗口大小
      if (isDragging.value) {
        console.log('🖱️ 正在拖拽中，延迟窗口大小调整')
        setTimeout(() => {
          if (!isDragging.value) {
            adjustWindowHeight(newValue)
          }
        }, 200)
      } else {
        adjustWindowHeight(newValue)
      }
    }, { immediate: false })

    // 邮件预览相关状态
    const showEmailPreview = ref(false)
    const isEmailEditing = ref(false)
    const emailPreviewData = ref({
      to: [''],
      cc: [''],
      subject: '',
      message: ''
    })
    const originalEmailData = ref(null) // 保存原始数据用于取消编辑

    // 🔄 【重要修改】分离MCP消息管理：
    // mcpConversationHistory: 发送给大模型的消息上下文（可以被清空重置）
    // displayMessageHistory: 页面显示的历史记录（永远保留，用于历史消息弹框）
    const mcpConversationHistory = ref([])  // 大模型上下文消息
    const displayMessageHistory = ref([])   // 页面显示历史记录

    // 追踪是否需要重置上下文的标志
    const shouldResetContext = ref(false)

    // 存储上一个调用的MCP工具（用于browser_navigate特殊逻辑）
    const lastMCPTool = ref(null)

    // 输入区域高度控制
    const inputAreaHeight = ref(90) // 初始默认高度
    const isInputAreaResizing = ref(false)
    const resizeStartY = ref(0)
    const resizeStartHeight = ref(0)
    const inputAreaRef = ref(null)

    // 登录状态
    const isLoggedIn = computed(() => authStore.isLoggedIn)

    // 图片资源引用 - 使用导入的图片
    const voiceOnImage = voiceOnImg
    const voiceImage = voiceImg
    const keywordsOnImage = keywordsOnImg
    const keywordsImage = keywordsImg
    const historyImage = historyImg
    const historyOnImage = historyOnImg


    const menuImgBgImage = menuImgBg
    const menuImgXlsImage = menuImgXls
    const menuImgXzrImage = menuImgXzr
    const menuImgRzygImage = menuImgRzyg
    const menuImgEditImage = menuImgEdit
    const menuImgCopyImage = menuImgCopy
    const menuImgVoiceOnImage = menuImgVoiceOn
    const menuImgVoiceOffImage = menuImgVoiceOff
    const menuImgSendImage = menuImgSend
    const menuImgVoiceBgImage = menuImgVoiceBg
    const menuImgStopImage = menuImgStop

    // 安全的历史消息访问 - 合并本地MCP历史和chatStore历史
    const recentMessages = computed(() => {
      const chatMessages = chatStore?.recentMessages || []
      // 🔄 【重要修改】使用页面显示历史记录而不是MCP上下文消息
      const mcpMessages = displayMessageHistory.value || []

      console.log('🔍 历史消息调试:', {
        chatStoreExists: !!chatStore,
        chatMessagesLength: chatMessages.length,
        displayMessagesLength: mcpMessages.length,
        chatMessages: chatMessages.map(msg => ({
          role: msg.role,
          content: msg.content?.substring(0, 50) + '...',
          hasTimestamp: !!msg.timestamp,
          timestamp: msg.timestamp
        })),
        displayMessages: mcpMessages.map(msg => ({
          role: msg.role,
          content: msg.content?.substring(0, 50) + '...',
          hasTimestamp: !!msg.timestamp,
          timestamp: msg.timestamp
        }))
      })

      // 为没有timestamp的消息添加默认timestamp
      const normalizedChatMessages = chatMessages.map(msg => ({
        ...msg,
        timestamp: msg.timestamp || Date.now() - 60000 // 默认设置为1分钟前
      }))

      const normalizedMcpMessages = mcpMessages.map(msg => ({
        ...msg,
        timestamp: msg.timestamp || Date.now()
      }))

      // 合并两个历史消息，按时间戳排序
      const allMessages = [...normalizedChatMessages, ...normalizedMcpMessages]
      const filteredMessages = allMessages
        .filter(msg => {
          // 🔄 【修复思考中气泡】保留思考中状态的消息，即使content为空
          if (msg && msg.isThinking) {
            return true
          }
          // 其他消息需要有内容才显示
          return msg && msg.content && msg.content.trim()
        })
        .sort((a, b) => {
          // 🔄 【修复位置】更细致的排序逻辑，确保思考中气泡在用户消息之后
          const timeA = a.timestamp || 0
          const timeB = b.timestamp || 0

          // 如果时间戳相同，让思考中气泡排在后面
          if (timeA === timeB) {
            if (a.isThinking && !b.isThinking) return 1  // 思考中气泡排后
            if (!a.isThinking && b.isThinking) return -1 // 普通消息排前
            if (a.role === 'user' && b.role === 'assistant') return -1 // 用户消息排前
            if (a.role === 'assistant' && b.role === 'user') return 1  // AI消息排后
          }

          return timeA - timeB
        })
        .slice(-20) // 只显示最近20条

      console.log('📊 最终历史消息:', {
        totalMessages: allMessages.length,
        filteredMessages: filteredMessages.length,
        finalMessages: filteredMessages.map((msg, index) => ({
          index: index, // 🔄 【调试】显示在列表中的位置
          role: msg.role,
          content: msg.content?.substring(0, 30) + '...',
          timestamp: msg.timestamp,
          timeFormatted: new Date(msg.timestamp).toLocaleTimeString(),
          isThinking: msg.isThinking || false, // 🔄 【调试】显示思考状态
          isError: msg.isError || false,
          isEmailPreview: msg.isEmailPreview || false
        }))
      })

      return filteredMessages
    })

    // CryptoJS加载辅助函数
    const ensureCryptoJSLoaded = () => {
      return new Promise((resolve, reject) => {
        // 如果CryptoJS已经可用，直接返回
        if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
          console.log('✅ CryptoJS已加载并可用')
          return resolve(true)
        }

        console.log('🔄 CryptoJS不可用，尝试手动加载...')

        // 创建script标签加载CryptoJS
        const script = document.createElement('script')
        script.src = '/utils/cryptojs.js'
        script.async = false

        script.onload = () => {
          console.log('✅ CryptoJS手动加载成功')
          if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
            resolve(true)
          } else {
            console.error('❌ CryptoJS加载后仍然不可用')
            reject(new Error('CryptoJS加载失败'))
          }
        }

        script.onerror = (e) => {
          console.error('❌ CryptoJS加载失败:', e)
          // 尝试CDN加载
          const cdnScript = document.createElement('script')
          cdnScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js'
          cdnScript.async = false

          cdnScript.onload = () => {
            console.log('✅ 从CDN加载CryptoJS成功')
            if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
              resolve(true)
            } else {
              reject(new Error('从CDN加载CryptoJS后仍不可用'))
            }
          }

          cdnScript.onerror = () => {
            reject(new Error('CryptoJS本地和CDN加载均失败'))
          }

          document.head.appendChild(cdnScript)
        }

        document.head.appendChild(script)
      })
    }

    // 等待CryptoJS加载辅助函数 (原有函数修改)
    const waitForCryptoJS = async (retryCount = 3) => {
      for (let i = 0; i < retryCount; i++) {
        try {
          // 尝试确保CryptoJS已加载
          await ensureCryptoJSLoaded()
          return true
        } catch (e) {
          console.error(`❌ 等待CryptoJS加载失败(尝试${i + 1}/${retryCount}):`, e)
          if (i === retryCount - 1) throw e
          // 等待一段时间后重试
          await new Promise(resolve => setTimeout(resolve, 500))
        }
      }
    }

    // ===== 多气泡系统 =====
    const bubbles = ref([])
    const bubbleIdCounter = ref(0)
    const maxBubbles = 3

    // 可见气泡列表（显示最新的3个）
    const visibleBubbles = computed(() => {
      return bubbles.value.slice(-maxBubbles)
    })

    // 气泡容器引用（用于自动滚动）
    const bubbleContainerRef = ref(null)

    // 自动滚动到最新气泡
    const scrollToLatestBubble = () => {
      nextTick(() => {
        if (bubbleContainerRef.value) {
          bubbleContainerRef.value.scrollTop = bubbleContainerRef.value.scrollHeight
        }
      })
    }

    // 添加新气泡
    const addBubble = (text, options = {}) => {
      if (!text || !text.trim()) return

      // 🔄 【新增】判断是否为简单回复，如果不是则自动显示历史消息
      if (!isSimpleReply(text) && text.length > 20) {
        console.log('📝 检测到复杂回复，自动显示历史消息:', text.substring(0, 30) + '...')
        showHistoryModalFunc()
        // 如果是复杂回复，不显示气泡，直接返回
        return
      }

      const newBubble = {
        id: bubbleIdCounter.value++,
        text: text,
        timestamp: Date.now(),
        fadeOut: false,
        hasFileReferences: options.hasFileReferences || false,
        fileReferences: options.fileReferences || []
      }

      bubbles.value.push(newBubble)

      // 调试信息
      console.log('🎈 添加气泡:', {
        text,
        bubbleId: newBubble.id,
        totalBubbles: bubbles.value.length,
        visibleBubblesCount: visibleBubbles.value.length,
        hasFileReferences: newBubble.hasFileReferences,
        fileReferencesCount: newBubble.fileReferences.length
      })

      // 如果气泡数量超过最大值，移除最旧的气泡
      if (bubbles.value.length > maxBubbles) {
        const removedBubbles = bubbles.value.splice(0, bubbles.value.length - maxBubbles)
        removedBubbles.forEach(bubble => {
          clearBubbleTimer(bubble.id)
        })
      }

      // 自动滚动到最新气泡
      scrollToLatestBubble()

      // 不再设置自动隐藏定时器，只有超过最大数量时才移除
    }

    // 移除指定气泡
    const removeBubble = (bubbleId) => {
      const bubbleIndex = bubbles.value.findIndex(b => b.id === bubbleId)
      if (bubbleIndex !== -1) {
        const bubble = bubbles.value[bubbleIndex]

        // 清理定时器
        clearBubbleTimer(bubbleId)

        // 设置淡出动画
        bubble.fadeOut = true

        // 500ms后从数组中移除
        setTimeout(() => {
          const currentIndex = bubbles.value.findIndex(b => b.id === bubbleId)
          if (currentIndex !== -1) {
            bubbles.value.splice(currentIndex, 1)
          }
        }, 500)
      }
    }

    // 清理气泡定时器
    const clearBubbleTimer = (bubbleId) => {
      const bubble = bubbles.value.find(b => b.id === bubbleId)
      if (bubble && bubble.timerId) {
        clearTimeout(bubble.timerId)
        delete bubble.timerId
      }
    }

    // 清空所有气泡
    const clearAllBubbles = () => {
      bubbles.value.forEach(bubble => {
        clearBubbleTimer(bubble.id)
      })
      bubbles.value = []
    }

    // ===== 历史消息系统 =====
    const showHistoryModal = ref(false)
    const historyListRef = ref(null)

    // 🔄 【需求3】滚动到最新消息 - 修复为使用正确的ref
    const scrollToLatestMessage = () => {
      nextTick(() => {
        if (chatMessagesRef.value) {
          chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
          console.log('📜 已滚动到最新消息', {
            scrollTop: chatMessagesRef.value.scrollTop,
            scrollHeight: chatMessagesRef.value.scrollHeight,
            messagesCount: recentMessages.value.length
          })
        } else {
          console.warn('📜 无法滚动：chatMessagesRef.value 为空')
        }
      })
    }

    // 🔄 【需求4】监控历史消息变化，自动滚动到底部 - 支持聊天区域和历史弹框
    watch(recentMessages, (newMessages, oldMessages) => {
      if (newMessages.length > (oldMessages?.length || 0)) {
        console.log('📜 【需求4】检测到新消息，自动滚动到底部')
        // 使用双重nextTick确保DOM完全更新
        nextTick(() => {
          nextTick(() => {
            if (showHistoryModal.value) {
              scrollToLatestMessage()
            } else if (showChatArea.value) {
              scrollChatToBottom()
            }
          })
        })
      }
    }, { deep: true })

    // 🔄 【需求4】监控聊天区域状态变化，打开时自动滚动到底部
    watch(showChatArea, (newValue) => {
      if (newValue && recentMessages.value.length > 0) {
        console.log('📜 【需求4】聊天区域打开，自动滚动到底部')
        nextTick(() => {
          nextTick(() => {
            scrollChatToBottom()
          })
        })
      }
    })

    // 切换历史消息弹框
    const toggleHistoryModal = () => {
      showHistoryModal.value = !showHistoryModal.value
      console.log('📜 切换历史消息弹框:', {
        showHistoryModal: showHistoryModal.value,
        messagesCount: recentMessages.value.length,
        messages: recentMessages.value
      })
      if (showHistoryModal.value) {
        // 强制触发计算属性更新，使用双重nextTick确保DOM完全更新
        nextTick(() => {
          nextTick(() => {
            scrollToLatestMessage()
          })
        })
      }
    }

    // 关闭历史消息弹框
    const closeHistoryModal = () => {
      showHistoryModal.value = false
    }

    // 🔄 【新增】显示历史消息弹框
    const showHistoryModalFunc = () => {
      if (!showHistoryModal.value) {
        showHistoryModal.value = true
        // 滚动到最新消息
        nextTick(() => {
          scrollToLatestMessage()
        })
      } else {
        // 如果已经显示，也要滚动到最新消息
        nextTick(() => {
          scrollToLatestMessage()
        })
      }
    }

    // 处理历史消息弹框点击 - 已禁用，只能通过按钮关闭
    // const handleHistoryModalClick = (event) => {
    //   // 点击遮罩层关闭弹框
    //   closeHistoryModal()
    // }

    // 格式化消息时间
    const formatMessageTime = (timestamp) => {
      if (!timestamp) return ''
      const date = new Date(timestamp)
      return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
    }

    // 🔄 【新增】判断是否为简单助手回复（用于气泡显示）
    const isSimpleReply = (message) => {
      if (!message || typeof message !== 'string') return false

      const simpleReplyPatterns = [
        /^你好[，,]?主人[！!。.]?$/,
        /^Yes[，,]?\s*Sir[！!。.]?$/i,
        /^好的[，,]?主人[！!。.]?$/,
        /^收到[，,]?主人[！!。.]?$/,
        /^明白了[，,]?主人[！!。.]?$/,
        /^没问题[，,]?主人[！!。.]?$/,
        /^好的[！!。.]?$/,
        /^收到[！!。.]?$/,
        /^明白了[！!。.]?$/,
        /^没问题[！!。.]?$/,
        /^[好OK][的啦]?[！!。.]?$/,
        /^(嗯|嗯嗯|是的|对的)[！!。.]?$/,
        /^正在思考中[.。…]*$/,
        /^请稍等[.。…]*$/,
        /^我在思考[.。…]*$/
      ]

      const trimmedMessage = message.trim()

      // 长度超过20个字符的一般不是简单回复
      if (trimmedMessage.length > 20) return false

      // 检查是否匹配简单回复模式
      return simpleReplyPatterns.some(pattern => pattern.test(trimmedMessage))
    }

    // 🔄 【优化】根据实际状态获取状态显示文字 - 统一处理所有模式
    const getStatusDisplayText = () => {
      // 🔄 【优化】第一优先级：AI思考状态（适用于所有模式）
      if (isAIThinking.value) {
        return 'AI思考中'
      }

      // 🔄 【优化】第二优先级：TTS播放状态（适用于所有模式）
      if (isTTSPlaying.value) {
        return 'AI回复中'
      }

      // 🔄 【优化】第三优先级：根据输入模式显示相应状态
      if (currentInputMode.value === 'text') {
        // 文本模式：显示基本状态
        return '文本对话中'
      }

      // 语音模式：根据语音状态显示
      if (!smartVoiceEnabled.value) {
        return '语音未启用'
      }

      // 根据实际的智能语音状态显示文字
      switch (smartVoiceState.value) {
        case 'idle':
          return '监听唤醒词'  // 🔄 【优化】移除"语音待机中"状态
        case 'listening_wake_word':
          return '监听唤醒词'
        case 'wake_word_detected':
          return '唤醒成功'
        case 'listening_command':
          return '请说话'
        case 'processing':
          return 'AI思考中'  // 实际上会被第一优先级处理
        case 'responding':
          return 'AI回复中'  // 实际上会被第二优先级处理
        case 'error':
          return '语音出错'
        default:
          return recordingStatus.value || '监听唤醒词'
      }
    }

    // 🔄 【新增】处理音波点击事件（TTS打断功能）
    const handleVoiceWaveClick = () => {
      console.log('🎵 音波被点击，当前TTS状态:', {
        isTTSPlaying: isTTSPlaying.value,
        smartVoiceEnabled: smartVoiceEnabled.value,
        conversationManager: !!conversationManager.value
      })

      // 只有在TTS播放时才允许点击打断
      if (isTTSPlaying.value && conversationManager.value) {
        console.log('🛑 用户点击音波打断TTS播放')

        try {
          // 强制停止TTS播放
          const stopped = conversationManager.value.stopTTS(true)

          if (stopped) {
            console.log('✅ TTS播放已通过音波点击成功停止')
            // 更新TTS状态
            isTTSPlaying.value = false

            // 如果智能语音已启用，回到唤醒词监听状态
            if (smartVoiceEnabled.value) {
              setTimeout(() => {
                conversationManager.value.backToWakeWordListening()
              }, 200)
            }
          } else {
            console.warn('⚠️ TTS停止失败，可能不在可停止状态')
          }
        } catch (error) {
          console.error('❌ 点击音波停止TTS时出错:', error)
        }
      } else {
        console.log('💡 当前不在TTS播放状态，音波点击无效')
      }
    }

    // 获取当前日期时间信息
    const getCurrentDateTimeInfo = () => {
      const now = new Date()
      const year = now.getFullYear()
      const month = now.getMonth() + 1
      const day = now.getDate()
      const weekday = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'][now.getDay()]
      const hour = now.getHours()
      const minute = now.getMinutes()

      return {
        dateString: `${year}年${month}月${day}日`,
        weekdayString: weekday,
        timeString: `${hour}:${minute.toString().padStart(2, '0')}`,
        fullDateTimeString: `${year}年${month}月${day}日 ${weekday} ${hour}:${minute.toString().padStart(2, '0')}`
      }
    }

    // 处理文本结果（语音或文本输入） - 支持语音输入标识
    const handleTextResult = async (text, options = {}) => {
      console.log('📝 [FLOATING] 收到文本结果:', text, options)

      // 🔄 【统一处理】检查输入来源
      const isVoiceInput = options.isVoiceInput || false
      if (isVoiceInput) {
        console.log('🎤 [VOICE_UNIFIED] 处理语音输入，使用统一MCP流程')
      }

      // 🔄 【修复】发送问题给模型后停止ASR监听，启动唤醒词监听
      if (smartVoiceEnabled.value && conversationManager.value) {
        console.log('🛑 发送消息后停止当前ASR监听，准备启动唤醒词监听')
        // 停止当前的指令监听
        await conversationManager.value.stopCommandListening()

        console.log('🎤 发送消息后启动唤醒词监听')
        // 启动唤醒词监听，等待下次交互
        setTimeout(async () => {
          if (smartVoiceEnabled.value && conversationManager.value) {
            await conversationManager.value.startWakeWordListening()
          }
        }, 500) // 稍微延迟确保状态正确切换
      }

      // 检查是否是邮件相关指令
      const emailKeywords = {
        send: ['发送', '发送邮件', '确认发送', '发出去'],
        edit: ['修改', '修改邮件', '编辑', '编辑邮件', '改一下'],
        preview: ['预览邮件', '我要预览邮件', '查看邮件预览']
      }

      // 检查是否是发送邮件指令
      if (emailKeywords.send.some(keyword => text.includes(keyword))) {
        console.log('📧 [FLOATING] 检测到发送邮件指令')

        // 如果当前有邮件预览数据，直接发送
        if (emailPreviewData.value && emailPreviewData.value.subject && emailPreviewData.value.message) {
          console.log('📧 [FLOATING] 发现邮件数据，开始发送邮件')

          // 添加发送中提示
          const sendingMessage = {
            role: 'assistant',
            content: '正在发送邮件...',
            timestamp: Date.now(),
            isThinking: true
          }
          displayMessageHistory.value.push(sendingMessage)

          if (!showHistoryModal.value) {
            showHistoryModal.value = true
          }

          nextTick(() => {
            scrollToLatestMessage()
          })

          // 调用发送邮件函数
          await sendEmailFromTextPreview()
          return
        }
      }

      // 检查是否是修改邮件指令
      if (emailKeywords.edit.some(keyword => text.includes(keyword))) {
        console.log('📧 [FLOATING] 检测到修改邮件指令')

        // 如果当前有邮件预览数据，提供修改指导
        if (emailPreviewData.value && (emailPreviewData.value.subject || emailPreviewData.value.message)) {
          console.log('📧 [FLOATING] 发现邮件数据，提供修改指导')

          const editGuideMessage = {
            role: 'assistant',
            content: '请告诉我您要修改邮件的哪个部分：\n\n1. 收件人\n2. 抄送人\n3. 主题\n4. 正文内容\n\n或者直接说出具体的修改内容，例如：\n"修改主题为xxx"\n"修改正文为xxx"\n"添加收件人**********"',
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(editGuideMessage)

          if (!showHistoryModal.value) {
            showHistoryModal.value = true
          }

          nextTick(() => {
            scrollToLatestMessage()
          })
          return
        }
      }

      // 检查是否是邮件预览相关指令
      if (emailKeywords.preview.some(keyword => text.includes(keyword))) {
        console.log('📧 [FLOATING] 检测到邮件预览指令')

        // 如果当前有邮件预览数据，重新显示预览
        if (emailPreviewData.value && (emailPreviewData.value.subject || emailPreviewData.value.message)) {
          console.log('📧 [FLOATING] 发现现有邮件数据，重新显示预览')

          // 重新生成邮件预览文本 - 确保to和cc是数组
          const toList = Array.isArray(emailPreviewData.value.to) ? emailPreviewData.value.to.filter(e => e && e.trim()) : []
          const ccList = Array.isArray(emailPreviewData.value.cc) ? emailPreviewData.value.cc.filter(e => e && e.trim()) : []

          let emailPreviewText = '📧 邮件预览\n\n'
          emailPreviewText += `收件人：${toList.join('、')}\n`
          if (ccList.length > 0) {
            emailPreviewText += `抄送：${ccList.join('、')}\n`
          }
          emailPreviewText += `主题：${emailPreviewData.value.subject}\n\n`
          emailPreviewText += `正文：\n${emailPreviewData.value.message}\n\n`
          emailPreviewText += '请确认邮件内容，您可以点击下方按钮操作，或回复"发送"、"修改"等指令。'

          // 确保emailData的格式正确
          const validatedEmailData = {
            to: Array.isArray(emailPreviewData.value.to) ? emailPreviewData.value.to : (emailPreviewData.value.to ? [emailPreviewData.value.to] : ['']),
            cc: Array.isArray(emailPreviewData.value.cc) ? emailPreviewData.value.cc : (emailPreviewData.value.cc ? [emailPreviewData.value.cc] : ['']),
            subject: emailPreviewData.value.subject || '',
            message: emailPreviewData.value.message || ''
          }

          const previewMessage = {
            role: 'assistant',
            content: emailPreviewText,
            timestamp: Date.now(),
            isSimpleReply: false,
            isEmailPreview: true,
            isEmailPreviewIng: true,
            emailData: validatedEmailData
          }
          displayMessageHistory.value.push(previewMessage)

          if (!showHistoryModal.value) {
            showHistoryModal.value = true
          }

          nextTick(() => {
            scrollToLatestMessage()
          })
          return
        }
      }

      // 🔄 【需求2】显示"思考中"提示 - 确保在用户消息之后
      // 等待一小段时间确保用户消息先显示
      await nextTick()

      // 🔄 【新增】设置AI思考状态
      isAIThinking.value = true

      const thinkingMessage = {
        role: 'assistant',
        content: '',
        timestamp: Date.now() + 10, // 🔄 【修复位置】确保时间戳晚于用户消息
        isThinking: true
      }
      displayMessageHistory.value.push(thinkingMessage)
      console.log('💭 【需求2】思考中气泡已添加到聊天历史，当前消息数:', displayMessageHistory.value.length, '位置应该在用户消息之后')

      // 🔄 【需求2】如果聊天区域没有显示，自动打开它
      if (!showChatArea.value && !window._isDraggingStarted) {
        showChatArea.value = true
        console.log('💬 自动打开聊天区域以显示加载状态')
      }

      // 🔄 【需求2】确保滚动到最新消息 - 思考中气泡添加后滚动  
      nextTick(() => {
        // 🔄 【强制重新排序】确保消息顺序正确
        displayMessageHistory.value = displayMessageHistory.value.slice().sort((a, b) => {
          const timeA = a.timestamp || 0
          const timeB = b.timestamp || 0
          if (timeA === timeB) {
            if (a.role === 'user' && b.isThinking) return -1 // 用户消息在思考中气泡前
            if (a.isThinking && b.role === 'user') return 1  // 思考中气泡在用户消息后
          }
          return timeA - timeB
        })

        console.log('💭 【强制排序】重新排序后的消息顺序:', displayMessageHistory.value.map((msg, idx) => ({
          index: idx,
          role: msg.role,
          isThinking: msg.isThinking || false,
          content: msg.content?.substring(0, 20) + '...',
          timestamp: msg.timestamp
        })))

        scrollChatToBottom()
      })

      // 使用sendMCPChatRequest处理消息
      try {
        const userMessageTimestamp = Date.now()
        const userMessage = {
          role: 'user',
          content: text,
          timestamp: userMessageTimestamp
        }

        // 🔄 【重要修改】添加到页面显示历史
        displayMessageHistory.value.push(userMessage)

        // 🔄 【重要修改】添加到MCP上下文历史
        mcpConversationHistory.value.push(userMessage)

        // 🔄 【确保顺序】立即更新思考中气泡的时间戳，确保在用户消息之后
        const lastThinkingIndex = displayMessageHistory.value.findIndex(msg => msg.isThinking)
        if (lastThinkingIndex !== -1) {
          displayMessageHistory.value[lastThinkingIndex].timestamp = userMessageTimestamp + 10
          console.log('💭 【修复位置】更新了思考中气泡的时间戳，确保在用户消息之后')
        }

        console.log('🔍 [FLOATING] 当前MCP对话历史长度:', mcpConversationHistory.value.length)



        // 准备发送给大模型的消息
        const currentTime = new Date()
        const dateTimePrompt = `当前时间：${currentTime.getFullYear()}年${(currentTime.getMonth() + 1).toString().padStart(2, '0')}月${currentTime.getDate().toString().padStart(2, '0')}日 ${currentTime.getHours().toString().padStart(2, '0')}:${currentTime.getMinutes().toString().padStart(2, '0')}:${currentTime.getSeconds().toString().padStart(2, '0')}`

        const enhancedSystemPrompt = dateTimePrompt + '\n\n' + MCP_SYSTEM_PROMPT

        // 🔄 【混合智能清空策略】MCP调用前检测
        const predictedTools = predictMCPTools(text)

        // 🔄 【智能清空策略】检查是否需要清空上下文
        const clearBeforeDecision = shouldClearBeforeMCP(predictedTools, mcpConversationHistory.value)

        if (clearBeforeDecision.clear) {
          console.log('🔄 [SMART_CLEAR_BEFORE] 清空上下文:', clearBeforeDecision)
          mcpConversationHistory.value = []
          // 重新添加当前用户消息
          mcpConversationHistory.value.push(userMessage)
        }

        // 🔄 【重要修改】构建消息列表：系统消息 + MCP对话历史
        const messagesToSend = [
          { role: 'system', content: enhancedSystemPrompt }
        ].concat(mcpConversationHistory.value.map(msg => ({
          role: msg.role,
          content: msg.content,
          timestamp: msg.timestamp
        })))

        // 发送MCP聊天请求，包含联网搜索选项
        const response = await sendMCPChatRequest(messagesToSend, {
          enableWebSearch: webSearchEnabled.value,
          enableKnowledge: true, // 默认启用知识库搜索
          knowledgeLimit: 3,
          enableDeepSearch: false, // 可以根据需要调整
          searchMode: 'auto'
        })

        console.log('🔍 [FLOATING_DEBUG] 收到MCP响应:', response)
        console.log('🔍 [FLOATING_DEBUG] response.needConfirm:', response.needConfirm)
        console.log('🔍 [FLOATING_DEBUG] response.confirmTool:', response.confirmTool)
        console.log('🔍 [FLOATING_DEBUG] 完整响应结构:', JSON.stringify(response, null, 2))

        // 🛑 检查请求是否被取消
        if (response.cancelled || response.type === 'cancelled') {
          console.log('🛑 AI请求被取消，不显示结果')
          // 移除思考中提示
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)
          // 重置AI思考状态
          isAIThinking.value = false
          return
        }

        // 检查是否需要显示邮件预览
        if (response.success && response.needEmailPreview && response.emailData) {
          console.log('📧 [FLOATING] 检测到邮件预览需求，生成长文本邮件预览')
          console.log('📧 [FLOATING] 邮件数据:', response.emailData)

          // 构建长文本格式的邮件预览
          const emailData = response.emailData
          const toList = Array.isArray(emailData.to) ? emailData.to.filter(e => e.trim()) : (emailData.to ? [emailData.to] : [])
          const ccList = Array.isArray(emailData.cc) ? emailData.cc.filter(e => e.trim()) : (emailData.cc ? [emailData.cc] : [])

          let emailPreviewText = '📧 邮件预览\n\n'
          emailPreviewText += `收件人：${toList.join('、')}\n`
          if (ccList.length > 0) {
            emailPreviewText += `抄送：${ccList.join('、')}\n`
          }
          emailPreviewText += `主题：${emailData.subject || emailData.sub || ''}\n\n`
          emailPreviewText += `正文：\n${emailData.message || emailData.content || ''}\n\n`
          emailPreviewText += '请确认邮件内容，您可以点击下方按钮操作，或回复"发送"、"修改"等指令。'

          // 保存邮件数据供后续发送使用 - 确保to和cc是数组
          emailPreviewData.value = {
            to: Array.isArray(toList) && toList.length > 0 ? toList : [''],
            cc: Array.isArray(ccList) && ccList.length > 0 ? ccList : [''],
            subject: emailData.subject || emailData.sub || '',
            message: emailData.message || emailData.content || ''
          }

          // 确保历史记录弹框是打开的
          if (!showHistoryModal.value) {
            console.log('📧 [FLOATING] 历史记录弹框未打开，先打开它')
            showHistoryModal.value = true
          }

          // 🔄 【立即移除加载状态】确保在处理响应时立即移除思考中提示
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

          // 添加邮件预览消息到历史记录 - 确保emailData格式正确
          const validatedEmailData = {
            to: Array.isArray(emailPreviewData.value.to) ? emailPreviewData.value.to : (emailPreviewData.value.to ? [emailPreviewData.value.to] : ['']),
            cc: Array.isArray(emailPreviewData.value.cc) ? emailPreviewData.value.cc : (emailPreviewData.value.cc ? [emailPreviewData.value.cc] : ['']),
            subject: emailPreviewData.value.subject || '',
            message: emailPreviewData.value.message || ''
          }

          const previewMessage = {
            role: 'assistant',
            content: emailPreviewText,
            timestamp: Date.now(),
            isSimpleReply: false,
            isEmailPreview: true, // 标记为邮件预览消息
            isEmailPreviewIng: true,
            emailData: validatedEmailData // 保存邮件数据
          }
          displayMessageHistory.value.push(previewMessage)

          // 确保滚动到最新消息
          nextTick(() => {
            scrollToLatestMessage()
          })

          // 不再显示邮件预览弹框，而是以文本形式显示
          // showEmailPreviewModal(response.emailData)
          return
        }

        // 兼容旧的邮件确认弹框
        if (response.success && response.needConfirm && response.confirmTool) {
          console.log('📧 [FLOATING] 检测到旧的邮件确认需求，显示邮件确认弹框')
          console.log('📧 [FLOATING] 确认工具数据:', response.confirmTool)
          console.log('📧 [FLOATING] 确认工具数据类型:', typeof response.confirmTool)
          console.log('📧 [FLOATING] 邮件数据:', response.confirmTool.emailData)
          console.log('📧 [FLOATING] 邮件数据类型:', typeof response.confirmTool.emailData)
          console.log('📧 [FLOATING] 邮件数据结构:', JSON.stringify(response.confirmTool.emailData, null, 2))

          // 检查可能的字段位置
          if (response.confirmTool.emailData) {
            console.log('📧 [FLOATING] 邮件数据字段详情:', {
              to: response.confirmTool.emailData.to,
              cc: response.confirmTool.emailData.cc,
              subject: response.confirmTool.emailData.subject,
              sub: response.confirmTool.emailData.sub,
              message: response.confirmTool.emailData.message,
              content: response.confirmTool.emailData.content
            })
          }

          showEmailConfirmModal(response.confirmTool.emailData, response)
          return
        }

        if (response.success) {
          // 🔄 【重要修改】处理AI回复 - 分别处理显示和上下文

          // 判断是否为简单助手回复（用于气泡显示）
          const isSimpleAssistantReply = isSimpleReply(response.message)

          // 📝 总是添加完整AI回复到页面显示历史（用于历史消息弹框）
          const assistantMessageForDisplay = {
            role: 'assistant',
            content: response.message,
            timestamp: Date.now(),
            isSimpleReply: isSimpleAssistantReply
          }

          // 添加搜索引用信息到显示历史
          if (response.fileReferences && response.fileReferences.length > 0) {
            assistantMessageForDisplay.fileReferences = deduplicateFileReferences(response.fileReferences)
            assistantMessageForDisplay.hasKnowledgeBase = true
            console.log('💾 保存AI回复到显示历史，包含文件引用:', assistantMessageForDisplay.fileReferences)
          }

          if (response.webReferences && response.webReferences.length > 0) {
            assistantMessageForDisplay.webReferences = response.webReferences
            assistantMessageForDisplay.hasWebSearch = true
            console.log('🌐 保存AI回复到显示历史，包含联网搜索引用:', assistantMessageForDisplay.webReferences)
          }

          // 🔄 【立即移除加载状态】确保在处理响应时立即移除思考中提示
          const beforeCount = displayMessageHistory.value.length
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)
          const afterCount = displayMessageHistory.value.length
          console.log('💭 【需求2】成功响应时移除思考中气泡，移除前:', beforeCount, '移除后:', afterCount)

          // 🔄 【新增】关闭AI思考状态
          isAIThinking.value = false

          // 添加实际回复
          displayMessageHistory.value.push(assistantMessageForDisplay)

          // 🔄 【新增】确保滚动到最新消息
          if (showHistoryModal.value) {
            nextTick(() => {
              scrollToLatestMessage()
            })
          }

          // 📝 直接添加AI回复到MCP上下文历史
          const assistantMessageForContext = {
            role: 'assistant',
            content: response.message,
            timestamp: Date.now()
          }

          // 添加搜索引用信息到上下文历史
          if (response.fileReferences && response.fileReferences.length > 0) {
            assistantMessageForContext.fileReferences = deduplicateFileReferences(response.fileReferences)
            assistantMessageForContext.hasKnowledgeBase = true
          }

          if (response.webReferences && response.webReferences.length > 0) {
            assistantMessageForContext.webReferences = response.webReferences
            assistantMessageForContext.hasWebSearch = true
          }

          mcpConversationHistory.value.push(assistantMessageForContext)

          // 🔄 【更新lastMCPTool】记录本次使用的工具
          if (response.toolsUsed && response.toolsUsed.length > 0) {
            lastMCPTool.value = response.toolsUsed[response.toolsUsed.length - 1] // 记录最后一个工具
            console.log('🔄 [LAST_MCP_TOOL] 更新lastMCPTool为:', lastMCPTool.value)
          } else if (response.hasKnowledgeBase || response.knowledgeSources) {
            // 知识库操作识别
            lastMCPTool.value = 'knowledge_search'
            console.log('🔄 [LAST_MCP_TOOL] 检测到知识库操作，更新lastMCPTool为: knowledge_search')
          }

          // 🔄 【混合智能清空策略】MCP调用后检测
          let toolsToCheck = response.toolsUsed || []

          // 🔧 调试：打印原始工具列表
          console.log('🔧 [DEBUG] 原始工具列表:', {
            toolsUsed: response.toolsUsed,
            hasKnowledgeBase: response.hasKnowledgeBase,
            knowledgeSources: response.knowledgeSources,
            toolsToCheck: toolsToCheck
          })

          // 如果是知识库操作，添加虚拟工具标识
          const hasKnowledgeBase = response.hasKnowledgeBase === true
          const hasKnowledgeSources = response.knowledgeSources && response.knowledgeSources.length > 0

          console.log('🔧 [DEBUG] 知识库检查:', {
            hasKnowledgeBase: response.hasKnowledgeBase,
            hasKnowledgeBaseBoolean: hasKnowledgeBase,
            knowledgeSources: response.knowledgeSources,
            knowledgeSourcesLength: response.knowledgeSources?.length,
            hasKnowledgeSourcesBoolean: hasKnowledgeSources,
            shouldSetKnowledge: hasKnowledgeBase || hasKnowledgeSources
          })

          if (hasKnowledgeBase || hasKnowledgeSources) {
            toolsToCheck = ['knowledge_search']
            console.log('🔧 [DEBUG] 设置为知识库操作:', toolsToCheck)
          }

          console.log('🔧 [DEBUG] 最终检查的工具列表:', toolsToCheck)

          if (toolsToCheck.length > 0) {
            const clearAfterDecision = shouldClearAfterMCP(toolsToCheck, mcpConversationHistory.value)

            if (clearAfterDecision.clear) {
              console.log('🔄 [SMART_CLEAR_AFTER] 清空上下文:', {
                决策: clearAfterDecision,
                使用的工具: toolsToCheck,
                清空前上下文长度: mcpConversationHistory.value.length
              })

              // 清空MCP上下文，为下次对话做准备
              mcpConversationHistory.value = []

              console.log('🔄 [SMART_CLEAR_AFTER] MCP上下文已清空，下次对话将重新开始')
            } else {
              console.log('🔄 [SMART_CLEAR_AFTER] 保持上下文:', {
                决策: clearAfterDecision,
                使用的工具: toolsToCheck,
                当前上下文长度: mcpConversationHistory.value.length
              })
            }
          }

          // 保持历史长度合理（最多20条消息）
          if (mcpConversationHistory.value.length > 20) {
            mcpConversationHistory.value = mcpConversationHistory.value.slice(-20)
          }

          // 🔄 【重要修改】所有回复都显示在聊天记录中，不再使用气泡
          console.log('📝 AI回复已添加到聊天记录中')

          // 自动滚动到最新消息
          scrollChatToBottom()

          // 🔄 【语音输入TTS处理】如果是语音输入且智能语音已启用，通过conversationManager播放TTS
          // 🔄 【文本输入TTS处理】如果是文本输入且智能语音已启用，播放简单回复的语音
          if (smartVoiceEnabled.value && conversationManager.value) {
            if (isVoiceInput) {
              // 语音输入：通过conversationManager播放TTS（带唤醒词检测）
              console.log('🎤 [VOICE_TTS] 语音输入回复，使用conversationManager播放TTS')
              await conversationManager.value.handleAIResponse(response.message, response)
            } else if (isSimpleAssistantReply) {
              // 文本输入：只有简单回复才播放TTS
              console.log('📝 [TEXT_TTS] 文本输入简单回复，播放TTS')
              // 🔄 【新增】立即设置TTS播放状态，让用户看到状态变化
              isTTSPlaying.value = true
              conversationManager.value.playTTSWithWakeWordDetection(response.message)
            }
          }
        } else {
          // 🔄 【错误时也移除加载状态】确保错误情况下也移除思考中提示
          const beforeCount = displayMessageHistory.value.length
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)
          const afterCount = displayMessageHistory.value.length
          console.log('💭 【需求2】错误响应时移除思考中气泡，移除前:', beforeCount, '移除后:', afterCount)

          // 🔄 【新增】关闭AI思考状态
          isAIThinking.value = false

          // 显示错误消息
          console.error(response.message || '抱歉，我现在无法处理您的请求呢~')
        }
      } catch (error) {
        console.error('MCP处理文本消息时发生错误:', error)

        // 🔄 【新增】移除"思考中"提示
        const beforeCount = displayMessageHistory.value.length
        displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)
        const afterCount = displayMessageHistory.value.length
        console.log('💭 【需求2】异常时移除思考中气泡，移除前:', beforeCount, '移除后:', afterCount)

        // 🔄 【新增】关闭AI思考状态
        isAIThinking.value = false

        // 🛑 【修复】如果是取消错误，不显示错误信息给用户
        if (error.name === 'CanceledError' || error.code === 'ERR_CANCELED' || error.message?.includes('canceled')) {
          console.log('🛑 检测到取消错误，不显示错误信息给用户')
          return
        }

        // 构建错误消息
        const errorMessage = error.message || '抱歉，我现在无法处理您的请求'
        const errorResponse = `抱歉，出现了错误: ${errorMessage}`

        // 添加错误消息到显示历史
        const errorMessageForDisplay = {
          role: 'assistant',
          content: errorResponse,
          timestamp: Date.now(),
          isError: true,
          isVoiceInput: isVoiceInput
        }
        displayMessageHistory.value.push(errorMessageForDisplay)

        // 🔄 【新增】确保滚动到最新消息
        if (showHistoryModal.value) {
          nextTick(() => {
            scrollToLatestMessage()
          })
        }

        // 🔄 【语音输入错误TTS】语音输入的错误也通过conversationManager播放
        if (smartVoiceEnabled.value && conversationManager.value) {
          if (isVoiceInput) {
            await conversationManager.value.handleAIResponse('哎呀，出了点小问题呢，请稍后再试吧')
          } else {
            // 🔧 修复：使用正确的TTS方法
            try {
              if (typeof conversationManager.value.playTTSWithWakeWordDetection === 'function') {
                conversationManager.value.playTTSWithWakeWordDetection('哎呀，出了点小问题呢，请稍后再试吧')
              } else {
                console.warn('⚠️ playTTSWithWakeWordDetection 方法不存在，跳过TTS播放')
              }
            } catch (ttsError) {
              console.warn('⚠️ TTS播放失败:', ttsError)
            }
          }
        }

        // 自动滚动到最新消息
        scrollChatToBottom()
      }
    }

    // 角色图像映射
    const characterImages = {
      idle: benbenIdleImg,
      listening_wake_word: benbenListeningImg,
      wake_word_detected: benbenListeningImg,
      listening_command: benbenListeningImg,
      processing: benbenThinkingImg,
      responding: benbenSpeakingImg,
      error: benbenIdleImg
    }

    // 角色图像
    const characterImage = computed(() => {
      const state = characterState.value
      return characterImages[state] || characterImages.idle
    })

    const characterName = computed(() => '犇犇')

    // 状态指示器
    const statusClass = computed(() => {
      switch (characterState.value) {
        case 'idle': return 'status-idle'
        case 'listening_wake_word': return 'status-listening'
        case 'wake_word_detected': return 'status-woke'
        case 'listening_command': return 'status-recording'
        case 'processing': return 'status-thinking'
        case 'responding': return 'status-speaking'
        case 'error': return 'status-error'
        default: return 'status-idle'
      }
    })

    // 录音状态样式
    const recordingStatusClass = computed(() => {
      if (recordingStatus.value.includes('监听唤醒词')) return 'status-listening'
      if (recordingStatus.value.includes('检测到唤醒词')) return 'status-woke'
      if (recordingStatus.value.includes('监听指令') || recordingStatus.value.includes('识别中')) return 'status-recording'
      if (recordingStatus.value.includes('思考中') || recordingStatus.value.includes('处理中')) return 'status-processing'
      if (recordingStatus.value.includes('回复中')) return 'status-speaking'
      if (recordingStatus.value.includes('错误') || recordingStatus.value.includes('失败')) return 'status-error'
      return ''
    })

    const recordingStatusIcon = computed(() => {
      if (recordingStatus.value.includes('监听唤醒词')) return '👂'
      if (recordingStatus.value.includes('检测到唤醒词')) return '🎯'
      if (recordingStatus.value.includes('监听指令') || recordingStatus.value.includes('识别中')) return '🎙️'
      if (recordingStatus.value.includes('思考中') || recordingStatus.value.includes('处理中')) return '🧠'
      if (recordingStatus.value.includes('回复中')) return '💬'
      if (recordingStatus.value.includes('完成')) return '✅'
      if (recordingStatus.value.includes('错误') || recordingStatus.value.includes('失败')) return '❌'
      return '🎤'
    })

    const recordingStatusText = computed(() => {
      return recordingStatus.value || ''
    })

    // 初始化智能对话管理器
    const initConversationManager = async () => {
      try {
        console.log('🚀 初始化智能对话管理器')

        // 等待CryptoJS加载
        try {
          await waitForCryptoJS()
        } catch (error) {
          console.error('❌ CryptoJS加载失败:', error)
          throw new Error(`智能对话初始化失败: ${error.message}`)
        }

        // 检查麦克风权限
        console.log('🎤 检查麦克风权限...')
        try {
          const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
          console.log('✅ 麦克风权限获取成功')
          stream.getTracks().forEach(track => track.stop()) // 释放权限
        } catch (micError) {
          console.error('❌ 麦克风权限获取失败:', micError)
          throw new Error(`麦克风权限被拒绝: ${micError.message}`)
        }

        // 获取配置
        console.log('📋 获取智能语音配置...')
        let config
        try {
          config = smartVoiceConfigManager.getConfig()
          console.log('✅ 配置获取完成:', {
            wakeWords: config.wakeWords,
            checkInterval: config.checkInterval,
            debug: config.debug
          })
        } catch (configError) {
          console.error('❌ 配置获取失败:', configError)
          throw new Error(`配置获取失败: ${configError.message}`)
        }

        // 获取ASR提供商配置
        const asrProvider = asrProviderManager.getProvider()
        console.log('🔄 当前ASR提供商:', asrProvider)

        // 创建对应的智能对话管理器实例
        try {
          const commonConfig = {
            // 唤醒词引擎配置 - 统一使用现有小模型
            wakeWordEngine: config.wakeWordEngine || 'sherpa-onnx',

            // Sherpa-ONNX 配置
            sherpaOnnxConfig: config.sherpaOnnxConfig || {
              sensitivity: 0.5,
              numThreads: 1,
              debug: config.debug || false,
              modelPath: 'assets/wasm'
            },

            // 语音合成配置
            enableTTS: true,
            ttsRate: 2.4,
            ttsPitch: 1.0,
            ttsVolume: 0.8,
            ttsLang: 'zh-CN',

            // 行为配置
            autoResumeListening: true,
            autoRestartAfterError: true,
            debug: config.debug || false,

            // 使用配置中的唤醒词
            wakeWords: config.wakeWords,
            checkInterval: config.checkInterval,
            recordDuration: config.recordDuration
          }

          // 只使用腾讯云ASR
          console.log('🔄 创建腾讯云智能对话管理器实例...')
          conversationManager.value = new TencentConversationManager({
            // 腾讯云ASR代理配置
            engine_model_type: '16k_zh',
            voice_format: 1,
            needvad: 1,
            filter_dirty: 1,
            filter_modal: 1,
            filter_punc: 0,
            convert_num_mode: 1,
            word_info: 2,
            noise_threshold: 0.4,
            max_speak_time: 30000,
            debug: false,

            // 词汇表配置
            hotwordId: '',  // 热词ID（可选）
            replaceTextId: 'a365fa85bacc4829a829cc8ed0fe437d',  // 替换词汇表ID

            // 通用配置
            ...commonConfig
          })

          console.log(`✅ 腾讯云智能对话管理器实例创建成功`)
        } catch (managerError) {
          console.error('❌ 智能对话管理器实例创建失败:', managerError)
          throw new Error(`智能对话管理器实例创建失败: ${managerError.message}`)
        }

        // 设置状态变化回调
        conversationManager.value.onStateChange = (state) => {
          console.log('🔄 对话状态变化:', state)

          // 映射对话状态到UI状态
          smartVoiceState.value = state

          // 🔄 【修复】优化TTS播放状态变化检测
          if (state === ALIYUN_CONVERSATION_STATES.RESPONDING) {
            // 当进入responding状态时，标记TTS开始播放
            isTTSPlaying.value = true
            console.log('🔊 TTS播放状态：开始播放')
          } else if (state !== ALIYUN_CONVERSATION_STATES.RESPONDING) {
            // 🔧 【修复】当离开responding状态时，立即检查并更新TTS状态
            const managerTTSStatus = conversationManager.value?.isTTSPlaying || false
            const browserTTSStatus = window.speechSynthesis?.speaking || false

            console.log('🔄 离开responding状态，检查TTS状态:', {
              新状态: state,
              ui状态: isTTSPlaying.value,
              管理器状态: managerTTSStatus,
              浏览器状态: browserTTSStatus
            })

            // 如果管理器和浏览器都确认TTS已停止，立即更新UI状态
            if (!managerTTSStatus && !browserTTSStatus && isTTSPlaying.value) {
              isTTSPlaying.value = false
              console.log('🔇 TTS播放状态：确认停止播放')
            }
          }

          // 🔧 【新增】特殊处理：当状态变为监听唤醒词时，确保TTS状态正确
          if (state === ALIYUN_CONVERSATION_STATES.LISTENING_WAKE_WORD && isTTSPlaying.value) {
            console.log('🔄 状态已变为监听唤醒词，确保TTS状态同步')
            isTTSPlaying.value = false
          }

          switch (state) {
            case ALIYUN_CONVERSATION_STATES.LISTENING_WAKE_WORD:
              recordingStatus.value = `正在监听唤醒词 ${conversationManager.value.config.wakeWords.join('、')}`
              // 🔄 【新增需求4】确保AI思考状态正确重置
              isAIThinking.value = false
              break
            case ALIYUN_CONVERSATION_STATES.WAKE_WORD_DETECTED:
              recordingStatus.value = '检测到唤醒词！准备监听指令...'
              break
            case ALIYUN_CONVERSATION_STATES.LISTENING_COMMAND:
              recordingStatus.value = '请说出您的问题...'
              break
            case ALIYUN_CONVERSATION_STATES.PROCESSING:
              recordingStatus.value = 'AI思考中，请稍等...'
              // 🔄 【新增】在处理状态时设置AI思考状态
              isAIThinking.value = true
              break
            case ALIYUN_CONVERSATION_STATES.RESPONDING:
              recordingStatus.value = 'AI回复中...'
              // 🔄 【新增】在回复状态时关闭AI思考状态
              isAIThinking.value = false
              break
            case ALIYUN_CONVERSATION_STATES.ERROR:
              recordingStatus.value = '出现错误，正在重新开始监听...'
              // 🔄 【新增】错误状态时重置AI思考状态
              isAIThinking.value = false
              break
            default:
              recordingStatus.value = ''
              // 🔄 【新增】其他状态时重置AI思考状态
              isAIThinking.value = false
              break
          }
        }

        // 新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程处理

        // 设置唤醒词检测回调
        conversationManager.value.onWakeWordDetected = (wakeWord) => {
          console.log('🎯 UI收到唤醒词检测:', wakeWord)

          // 🔄 【新增需求4】监听听到唤醒词默认打开聊天框切换到语音互动状态
          if (!showChatArea.value && !window._isDraggingStarted) {
            showChatArea.value = true
            console.log('📱 唤醒词检测：自动打开聊天区域')
          }

          // 🔄 【新增需求4】切换到语音输入模式
          if (currentInputMode.value !== 'voice') {
            currentInputMode.value = 'voice'
            console.log('🎤 唤醒词检测：切换到语音输入模式')
          }

          // 只显示唤醒成功气泡，不重复播放"Yes Sir"（管理器已经播放了）
          console.log('唤醒词检测成功，开始监听指令')

          // 不在这里播放语音回复，避免重复
          // conversationManager已经在handleWakeWordDetected中播放了
        }

        // 设置部分识别结果回调
        conversationManager.value.onPartialResult = (text) => {
          console.log('🎤 部分识别结果:', text)
          // 🔄 【需求5】去掉本地强制转换ASR处理后的文字 - 不再将部分识别结果显示在输入框中
          // voiceResultText.value = text
        }

        // 设置音量回调
        conversationManager.value.onVolume = (level) => {
          volumeLevel.value = level
        }

        // 设置结果回调
        conversationManager.value.onResult = (result) => {
          console.log('🤖 语音结果:', result)

          if (result.type === 'user_command') {
            // 🔄 【统一流程】处理来自语音交互的用户指令
            console.log('🎤 收到用户语音指令，使用统一MCP处理流程:', result.text)

            // 🔄 【统一处理】使用与文本输入相同的handleTextResult方法
            handleTextResult(result.text, { isVoiceInput: result.isVoiceInput })

          } else if (result.type === 'ai_response') {
            // 🔄 【简化处理】处理AI回复，主要用于错误情况或直接TTS回复
            console.log('🤖 收到AI回复（通常为错误信息）:', result.text)

            // 🔄 【修复】移除思考中状态
            displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

            const assistantMessage = {
              role: 'assistant',
              content: result.text,
              timestamp: result.timestamp || Date.now(),
              isSimpleReply: isSimpleReply(result.text),
              isError: result.isError || false,
              isVoiceInput: result.isVoiceInput || false
            }

            if (result.fileReferences && result.fileReferences.length > 0) {
              assistantMessage.fileReferences = deduplicateFileReferences(result.fileReferences)
              assistantMessage.hasKnowledgeBase = true
            }

            displayMessageHistory.value.push(assistantMessage)

            // 🔄 【新增】确保滚动到最新消息
            if (showHistoryModal.value) {
              nextTick(() => {
                scrollToLatestMessage()
              })
            }

            // 自动滚动到最新消息
            scrollChatToBottom()
          }
        }

        // 设置错误回调
        conversationManager.value.onError = (error) => {
          console.error('❌ 智能对话管理器错误:', error)
          recordingError.value = error
          recordingStatus.value = '错误: ' + error
          smartVoiceState.value = ALIYUN_CONVERSATION_STATES.ERROR
          volumeLevel.value = 0

          // 🔄 【新增】错误时也停止TTS播放状态
          if (isTTSPlaying.value) {
            isTTSPlaying.value = false
            console.log('🔇 错误状态：停止TTS播放')
          }

          // 3秒后清除错误状态
          setTimeout(() => {
            recordingError.value = ''
            if (smartVoiceEnabled.value) {
              recordingStatus.value = '正在重新开始监听唤醒词...'
            } else {
              recordingStatus.value = ''
              smartVoiceState.value = ALIYUN_CONVERSATION_STATES.IDLE
            }
          }, 3000)
        }

        // 🔄 【新增】设置额外的TTS状态监听机制
        // 监听浏览器原生speechSynthesis事件，确保状态同步
        if (window.speechSynthesis) {
          // 🔄 【优化】创建定时器定期检查TTS状态 - 适用于所有模式
          const ttsStatusChecker = setInterval(() => {
            const isSpeaking = window.speechSynthesis.speaking
            const managerTTSStatus = conversationManager.value?.isTTSPlaying || false

            // 🔄 【优化】TTS状态同步逻辑 - 适用于所有模式
            if (isTTSPlaying.value && !isSpeaking && !managerTTSStatus) {
              // TTS播放完毕，更新UI状态
              console.log('🔇 TTS播放完毕，更新UI状态:', {
                输入模式: currentInputMode.value,
                ui状态: isTTSPlaying.value,
                浏览器状态: isSpeaking,
                管理器状态: managerTTSStatus,
                语音状态: smartVoiceState.value
              })
              isTTSPlaying.value = false

              // 🔄 【修复】语音模式下TTS播放完成时，确保状态正确转换
              if (currentInputMode.value === 'voice' && smartVoiceEnabled.value && smartVoiceState.value === 'responding') {
                console.log('🔄 语音模式TTS播放完成，回到唤醒词监听状态')
                // 通知对话管理器回到唤醒词监听状态
                if (conversationManager.value && conversationManager.value.isEnabled) {
                  setTimeout(() => {
                    // 小延迟确保TTS完全停止
                    if (conversationManager.value && !conversationManager.value.isTTSPlaying) {
                      conversationManager.value.backToWakeWordListening()
                    }
                  }, 200)
                }
              }
            } else if (!isTTSPlaying.value && isSpeaking && managerTTSStatus) {
              // TTS开始播放，更新UI状态
              console.log('🔊 TTS开始播放，更新UI状态:', {
                输入模式: currentInputMode.value,
                ui状态: isTTSPlaying.value,
                浏览器状态: isSpeaking,
                管理器状态: managerTTSStatus
              })
              isTTSPlaying.value = true
            }

            // 🔄 【新增】文本模式下的TTS状态管理
            if (currentInputMode.value === 'text') {
              // 文本模式下，主要通过浏览器状态判断
              if (isTTSPlaying.value !== isSpeaking) {
                console.log('🔄 文本模式TTS状态同步:', {
                  ui状态: isTTSPlaying.value,
                  浏览器状态: isSpeaking
                })
                isTTSPlaying.value = isSpeaking
              }
            }

            // 🔄 【新增】语音模式下的状态一致性检查
            if (currentInputMode.value === 'voice' && smartVoiceEnabled.value) {
              // 如果TTS已停止但语音状态仍是responding，需要手动触发状态转换
              if (!isTTSPlaying.value && !isSpeaking && !managerTTSStatus && smartVoiceState.value === 'responding') {
                console.log('🔧 检测到语音状态不一致，触发回到唤醒词监听')
                if (conversationManager.value && conversationManager.value.isEnabled && !conversationManager.value.isCommandListening) {
                  setTimeout(() => {
                    conversationManager.value.backToWakeWordListening()
                  }, 100)
                }
              }
            }
          }, 300) // 🔄 【优化】减少检查间隔，提高响应性

          // 保存定时器引用供组件卸载时清理
          window.ttsStatusChecker = ttsStatusChecker
        }

        // 初始化对话管理器
        try {
          console.log('🔄 初始化智能对话管理器...')
          await conversationManager.value.init()
          console.log(`✅ 腾讯云智能对话管理器初始化成功`)
          return true
        } catch (initError) {
          console.error('❌ 智能对话管理器初始化失败:', initError)
          throw new Error(`智能对话管理器初始化失败: ${initError.message}`)
        }

      } catch (error) {
        console.error('❌ 初始化智能对话管理器失败:', error)
        recordingError.value = '智能对话管理器初始化失败: ' + error.message
        console.error(`语音功能初始化失败: ${error.message}`)
        return false
      }
    }

    // 仅在语音唤醒时停止TTS（移除强制停止功能）
    const stopTTS = () => {
      try {
        // 只在智能语音模式下，通过唤醒词检测来停止TTS
        if (smartVoiceEnabled.value && conversationManager.value) {
          // 让对话管理器自然处理语音停止
          console.log('TTS将通过唤醒词检测或自动播放完成来停止')
          return
        }

        // 非智能语音模式下才允许手动停止
        if (!smartVoiceEnabled.value && window.speechSynthesis && window.speechSynthesis.speaking) {
          window.speechSynthesis.cancel()
          console.log('TTS已停止（非智能语音模式）')
        }
      } catch (error) {
        console.error('停止TTS失败:', error)
      }
    }

    // 切换智能语音模式
    const toggleSmartVoice = async () => {
      console.log('🎤 语音按钮被点击，当前状态:', {
        smartVoiceEnabled: smartVoiceEnabled.value,
        conversationManager: !!conversationManager.value,
        state: smartVoiceState.value
      })

      try {
        if (smartVoiceEnabled.value) {
          // 关闭智能语音
          console.log('🔄 正在关闭智能语音...')
          await stopSmartVoice()
        } else {
          // 开启智能语音
          console.log('🔄 正在开启智能语音...')
          await startSmartVoice()
        }
      } catch (error) {
        console.error('❌ 切换智能语音模式失败:', error)
        // 在控制台输出错误信息
        console.error(`语音功能出错: ${error.message}`)
      }
    }

    // 🔧 【修复启动时点击问题】防重复播放的标志
    let hasPlayedInitialGreeting = false

    // 开启智能语音 - 唤醒词模式
    const startSmartVoice = async () => {
      try {
        console.log('🎤 启动智能语音...')
        if (smartVoiceEnabled.value) {
          console.log('⚠️ 智能语音已启动')
          return true
        }

        // 🔧 【增强】添加更多安全检查
        if (!window.CryptoJS) {
          console.warn('⚠️ CryptoJS未加载，可能影响智能语音功能')
        }

        // 重置错误状态
        recordingError.value = ''

        // 获取最新的ASR提供商配置
        const currentProvider = asrProviderManager.getProvider()
        console.log('🔄 当前ASR提供商:', currentProvider)

        // 初始化对话管理器（如果尚未初始化或需要重新初始化）
        console.log('🚀 初始化智能对话管理器...')

        // 强制重新初始化对话管理器，确保ASR提供商切换立即生效
        console.log('🔄 强制重新初始化对话管理器，确保使用最新的ASR提供商')
        // 先清理现有的对话管理器
        if (conversationManager.value) {
          try {
            conversationManager.value.cleanup()
            console.log('✅ 已清理现有对话管理器')
          } catch (cleanupError) {
            console.warn('⚠️ 清理对话管理器时出错:', cleanupError)
          }
          conversationManager.value = null
        }

        // 🔧 【增强】添加超时保护
        const initPromise = initConversationManager()
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('对话管理器初始化超时')), 30000)
        })

        await Promise.race([initPromise, timeoutPromise])

        // 启动对话管理器
        if (conversationManager.value) {
          // 启动对话管理器
          await conversationManager.value.start()
          smartVoiceEnabled.value = true
          // 获取配置中的唤醒词
          const config = smartVoiceConfigManager.getConfig()
          recordingStatus.value = `正在监听唤醒词 ${config.wakeWords.join('、')}`
          smartVoiceState.value = 'listening_wake_word' // 修改数据源而非计算属性
          console.log('✅ 智能语音启动成功')

          // 使用TTS播放欢迎信息
          try {
            console.log('🎤 播放欢迎信息')
            // 🔧 【修复】改进播放欢迎信息的方式，添加更多错误处理
            if (conversationManager.value && conversationManager.value.isEnabled) {
              // ✅ 【恢复TTS播放】现在应用稳定，恢复欢迎语音播放
              console.log('🔊 播放欢迎语音: 你好,主人')
              console.log('🔄 开始调用playUninterruptibleTTS...')
              try {
                await conversationManager.value.playUninterruptibleTTS('你好,主人');
                console.log('✅ 欢迎语音播放完成')

                // 🔄 移除重复发送，只在finally块中发送一次

              } catch (playError) {
                console.warn('⚠️ TTS播放出错，跳过欢迎信息:', playError)
                // 即使TTS播放失败，也不影响应用启动
                // 移除重复发送，只在finally块中发送一次
              }
            } else {
              console.log('⚠️ 对话管理器未启用，跳过欢迎语音播放')
              // 移除重复发送，只在finally块中发送一次
            }
          } catch (ttsError) {
            console.warn('⚠️ 播放欢迎信息失败:', ttsError)
          } finally {
            // 🔧 【修复】确保状态正确设置，添加延迟确保TTS完全结束
            console.log('✅ 欢迎信息播放流程完成，等待TTS完全结束...')
            await new Promise(resolve => setTimeout(resolve, 800));
            console.log('✅ 欢迎信息播放流程完全结束')

            // 🔄 【修复】只在finally块中发送一次播放完成通知，避免重复
            if (window.electronAPI && window.electronAPI.invoke) {
              window.electronAPI.invoke('send-status-message', '智能语音启动完成').catch(error => {
                console.warn('⚠️ 通知主进程智能语音启动完成失败:', error)
              })
            }
          }

          console.log('🔧 startSmartVoice方法即将返回true')
          return true
        } else {
          throw new Error('对话管理器初始化失败')
        }
      } catch (error) {
        console.error('❌ 启动智能语音失败:', error)
        recordingError.value = error.message || '启动失败'
        smartVoiceEnabled.value = false
        recordingStatus.value = '智能语音启动失败'
        smartVoiceState.value = 'idle' // 修改数据源而非计算属性

        // 🔧 【增强】添加错误恢复机制
        try {
          if (conversationManager.value) {
            conversationManager.value.cleanup()
            conversationManager.value = null
          }
        } catch (cleanupError) {
          console.warn('⚠️ 清理对话管理器失败:', cleanupError)
        }

        throw error
      }
    }

    // 关闭智能语音
    const stopSmartVoice = async () => {
      try {
        console.log('⏹️ 关闭智能语音...')

        // 让语音播放自然结束，不强制停止
        console.log('让当前语音播放自然结束...')

        // 停止智能对话管理器
        if (conversationManager.value) {
          conversationManager.value.stop()
        }

        smartVoiceEnabled.value = false
        smartVoiceState.value = ALIYUN_CONVERSATION_STATES.IDLE
        recordingStatus.value = ''
        volumeLevel.value = 0

        // 🔧 【修复启动时点击问题】重置防重复播放标志
        hasPlayedInitialGreeting = false
        console.log('🔄 重置防重复播放标志')

        console.log('✅ 智能语音已关闭')

      } catch (error) {
        console.error('❌ 关闭智能语音失败:', error)
      }
    }

    // 切换菜单
    const toggleMenu = () => {
      return
    }

    // 处理菜单点击
    const handleMenuClick = (menuItem) => {
      switch (menuItem) {
        case 'config':
          openMainWindow('Config')
          break
        case 'help':
          openMainWindow('Help')
          break
        case 'about':
          openMainWindow('About')
          break
        case 'exit':
          closeApp()
          break
      }
    }

    // 切换文本输入
    const toggleTextInput = () => {
      showTextInput.value = !showTextInput.value
      // 清空语音结果，因为这是手动打开的文本输入
      if (showTextInput.value) {
        voiceResultText.value = ''
      }
    }

    // 🔄 【需求2】切换输入模式（语音/文本）- 优化状态切换逻辑
    const toggleInputMode = () => {
      if (currentInputMode.value === 'voice') {
        // 🔄 【需求2】从语音模式切换到文本模式 - 不需要关闭语音监听
        currentInputMode.value = 'text'
        console.log('🔄 从语音模式切换到文本模式，保持语音监听')
        // 不关闭智能语音，让它继续在后台监听
      } else {
        // 🔄 【需求2】从文本模式切换到语音模式 - 需要检测语音功能是否开启
        currentInputMode.value = 'voice'
        console.log('🔄 从文本模式切换到语音模式')

        // 检测语音功能是否开启，没有开启的话自动开启
        if (!smartVoiceEnabled.value) {
          console.log('🎤 语音功能未开启，自动开启')
          startSmartVoice().catch(error => {
            console.error('❌ 自动开启语音功能失败:', error)
            // 如果开启失败，切换回文本模式
            currentInputMode.value = 'text'
          })
        }
      }
      console.log('🔄 输入模式切换为:', currentInputMode.value)
    }

    // 编辑用户消息
    const editUserMessage = (message) => {
      console.log('✏️ 编辑用户消息:', message.content)

      // 如果当前是语音模式且正在监听，停止监听
      if (currentInputMode.value === 'voice' && smartVoiceEnabled.value) {
        stopSmartVoice()
      }

      // 切换到文本模式
      currentInputMode.value = 'text'

      // 将消息内容复制到输入框
      textInputValue.value = message.content

      // 聚焦到文本框
      nextTick(() => {
        if (textareaRef.value) {
          textareaRef.value.focus()
        }
      })
    }

    // 复制消息到剪切板
    const copyMessage = async (content) => {
      try {
        console.log('📋 开始复制消息:', content)

        // 清理HTML标签，只保留纯文本
        const textContent = content.replace(/<[^>]*>/g, '').replace(/&nbsp;/g, ' ').trim()

        if (!textContent) {
          console.warn('⚠️ 复制内容为空')
          showWarning('没有可复制的内容')
          return
        }

        console.log('📋 处理后的文本内容:', textContent)

        // 优先使用现代剪切板API
        if (navigator.clipboard && navigator.clipboard.writeText) {
          try {
            await navigator.clipboard.writeText(textContent)
            console.log('✅ 使用Clipboard API复制成功')
            showSuccess('消息已复制到剪切板')
            return
          } catch (clipboardError) {
            console.warn('⚠️ Clipboard API失败，尝试降级方案:', clipboardError)
          }
        }

        // 降级方案：使用execCommand
        try {
          const textArea = document.createElement('textarea')
          textArea.value = textContent
          textArea.style.position = 'fixed'
          textArea.style.left = '-999999px'
          textArea.style.top = '-999999px'
          document.body.appendChild(textArea)
          textArea.focus()
          textArea.select()

          const successful = document.execCommand('copy')
          document.body.removeChild(textArea)

          if (successful) {
            console.log('✅ 使用execCommand复制成功')
          } else {
            throw new Error('execCommand返回false')
          }
        } catch (execError) {
          console.error('❌ execCommand也失败了:', execError)
          throw execError
        }

      } catch (error) {
        console.error('❌ 复制完全失败:', error)

        // 提供更详细的错误信息
        let errorMessage = '复制失败'
        if (error.name === 'NotAllowedError') {
          errorMessage = '复制失败：浏览器阻止了剪切板访问，请手动选择文本复制'
        } else if (error.name === 'SecurityError') {
          errorMessage = '复制失败：安全限制，请手动选择文本复制'
        } else {
          errorMessage = `复制失败：${error.message || '未知错误'}，请手动选择文本复制`
        }

        showError(errorMessage)
      }
    }

    // 发送文本消息
    const sendTextMessage = () => {
      const message = textInputValue.value.trim()
      if (!message) return

      console.log('📤 发送文本消息:', message)

      // 清空输入框
      textInputValue.value = ''

      // 🔄 【需求4】确保聊天区域打开，以便显示消息
      if (!showChatArea.value && !window._isDraggingStarted) {
        showChatArea.value = true
      }

      // 调用现有的文本处理逻辑
      handleTextResult(message)

      // 🔄 【需求4】发送消息后立即滚动到底部
      nextTick(() => {
        scrollChatToBottom()
      })
    }

    // 处理文本框按键事件
    const handleTextareaKeydown = (event) => {
      // 🔄 【需求1】Enter 发送消息，Ctrl+Enter 换行
      if (event.key === 'Enter') {
        if (event.ctrlKey || event.metaKey) {
          // Ctrl+Enter 或 Cmd+Enter 换行（默认行为，不阻止）
          return
        } else {
          // 单独的 Enter 发送消息
          event.preventDefault()
          sendTextMessage()
        }
      }
    }

    // 切换输入区域大小
    const toggleInputAreaSize = () => {
      // 循环切换三种预设高度
      currentSizeIndex.value = (currentSizeIndex.value + 1) % inputAreaSizes.length
      inputAreaHeight.value = inputAreaSizes[currentSizeIndex.value]

      // 确保文本区域获得焦点并适应新高度
      nextTick(() => {
        if (textareaRef.value) {
          textareaRef.value.focus()
          textareaRef.value.style.height = `${inputAreaHeight.value - 20}px` // 留出一些内边距
        }
      })
    }

    // 监听文本输入区域模式变化，正确设置初始高度
    watch(currentInputMode, (newMode) => {
      if (newMode === 'text') {
        nextTick(() => {
          // 确保textarea高度匹配
          if (textareaRef.value) {
            textareaRef.value.style.height = `${inputAreaHeight.value - 30}px`
          }
        })
      }
    })

    // 切换联网搜索开关
    const toggleWebSearch = () => {
      webSearchEnabled.value = !webSearchEnabled.value
      console.log('🌐 联网搜索开关切换:', webSearchEnabled.value ? '开启' : '关闭')

      // 可选：显示切换提示
      const statusText = webSearchEnabled.value ? '联网搜索已开启' : '联网搜索已关闭'
      console.log('🌐', statusText)
    }

    // 🔄 【需求3】滚动聊天消息到底部 - 修复historyListRef为空的问题
    const scrollChatToBottom = () => {
      nextTick(() => {
        if (chatMessagesRef.value) {
          chatMessagesRef.value.scrollTop = chatMessagesRef.value.scrollHeight
          console.log('📜 聊天消息已滚动到底部')
        } else {
          console.warn('📜 无法滚动：chatMessagesRef.value 为空')
        }
      })
    }



    // 关闭文本输入
    const closeTextInput = () => {
      showTextInput.value = false
      voiceResultText.value = ''

      // 可选：清理对话历史（用户关闭对话框时）
      // mcpConversationHistory.value = []
    }

    // 清理MCP对话历史
    const clearMCPHistory = () => {
      mcpConversationHistory.value = []
      console.log('🗑️ MCP对话历史已清理')
    }

    // 测试大模型调用
    const testChatAPI = async () => {
      console.log('🧪 测试大模型调用按钮被点击')

      try {
        // 确保智能对话管理器已初始化
        if (!conversationManager.value) {
          const success = await initConversationManager()
          if (!success) {
            throw new Error('智能对话管理器初始化失败')
          }
        }

        // 显示测试开始信息
        console.log('正在测试大模型调用...')

        // 调用测试方法
        const result = await conversationManager.value.testChatAPI('你好，这是一个测试消息')

        // 显示测试结果
        console.log(result.success ?
          `测试成功！AI回复: ${result.message}` :
          `测试失败: ${result.message}`)

      } catch (error) {
        console.error('❌ 测试大模型调用失败:', error)

        // 显示错误信息
        console.error(`测试失败: ${error.message}`)
      }
    }

    // 打开主窗口
    const openMainWindow = (page) => {
      if (window.electronAPI) {
        window.electronAPI.showMainWindow(page)
      }
    }

    // 关闭应用
    const closeApp = () => {
      if (window.electronAPI) {
        window.electronAPI.closeApp()
      }
    }

    // 打开晓主任网站
    const openXiaoZhuRen = async () => {
      console.log('🌐 使用浏览器MCP打开晓主任网站')

      try {
        // 使用MCP浏览器工具打开网站
        const result = await window.electronAPI.executeMCPTool('browser_navigate', {
          url: 'https://dx.mltai.cn/app-web/passport/login'
        })

        if (result.success) {
          console.log('✅ 晓主任网站已在浏览器中打开')

          // 添加成功提示到聊天历史
          const successMessage = {
            role: 'assistant',
            content: '已为您打开晓主任网站',
            timestamp: Date.now(),
            isSimpleReply: true
          }
          displayMessageHistory.value.push(successMessage)
        } else {
          console.error('❌ 打开晓主任网站失败:', result.error)
          throw new Error(result.error || '打开网站失败')
        }
      } catch (error) {
        console.error('❌ 使用MCP打开晓主任网站失败:', error)

        // 添加错误消息到聊天历史
        const errorMessage = {
          role: 'assistant',
          content: '打开晓主任网站失败: ' + error.message,
          timestamp: Date.now(),
          isError: true
        }
        displayMessageHistory.value.push(errorMessage)
      }
    }

    // 打开晓律师网站
    const openXiaoLawyer = async () => {
      console.log('🌐 使用浏览器MCP打开晓律师网站')

      try {
        // 使用MCP浏览器工具打开网站
        const result = await window.electronAPI.executeMCPTool('browser_navigate', {
          url: 'https://dx.jurisai.cn'
        })

        if (result.success) {
          console.log('✅ 晓律师网站已在浏览器中打开')

          // 添加成功提示到聊天历史
          const successMessage = {
            role: 'assistant',
            content: '已为您打开晓律师网站',
            timestamp: Date.now(),
            isSimpleReply: true
          }
          displayMessageHistory.value.push(successMessage)
        } else {
          console.error('❌ 打开晓律师网站失败:', result.error)
          throw new Error(result.error || '打开网站失败')
        }
      } catch (error) {
        console.error('❌ 使用MCP打开晓律师网站失败:', error)

        // 添加错误消息到聊天历史
        const errorMessage = {
          role: 'assistant',
          content: '打开晓律师网站失败: ' + error.message,
          timestamp: Date.now(),
          isError: true
        }
        displayMessageHistory.value.push(errorMessage)
      }
    }

    // 打开AI Store网站
    const openAIStore = async () => {
      console.log('🌐 使用浏览器MCP打开AI Store网站')

      try {
        // 使用MCP浏览器工具打开网站
        const result = await window.electronAPI.executeMCPTool('browser_navigate', {
          url: 'https://www.ctyun.cn/partners/topic/10000134'
        })

        if (result.success) {
          console.log('✅ AI Store网站已在浏览器中打开')

          // 添加成功提示到聊天历史
          const successMessage = {
            role: 'assistant',
            content: '已为您打开天翼云AI Store网站',
            timestamp: Date.now(),
            isSimpleReply: true
          }
          displayMessageHistory.value.push(successMessage)
        } else {
          console.error('❌ 打开AI Store网站失败:', result.error)
          throw new Error(result.error || '打开网站失败')
        }
      } catch (error) {
        console.error('❌ 使用MCP打开AI Store网站失败:', error)

        // 添加错误消息到聊天历史
        const errorMessage = {
          role: 'assistant',
          content: '打开天翼云AI Store网站失败: ' + error.message,
          timestamp: Date.now(),
          isError: true
        }
        displayMessageHistory.value.push(errorMessage)
      }
    }



    // 打开文件
    const openFile = async (filePath, options = {}) => {
      try {
        console.log('📂 尝试打开文件:', filePath, '选项:', options)

        if (window.electronAPI && window.electronAPI.openFile) {
          const result = await window.electronAPI.openFile(filePath, options)
          if (result.success) {
            console.log(`已打开文件: ${result.fileName || result.message}`)
          } else {
            console.error(`打开文件失败: ${result.error}`)
            if (options.isKnowledgeReference) {
              showError(`打开参考文件失败: ${result.error}`)
            } else {
              showError(`打开文件失败: ${result.error}`)
            }
          }
        } else {
          console.warn('❌ 文件打开功能不可用')
          showWarning('文件打开功能暂时不可用')
        }
      } catch (error) {
        console.error('❌ 打开文件失败:', error)
        if (options.isKnowledgeReference) {
          showError(`打开参考文件失败: ${error.message}`)
        } else {
          showError(`打开文件失败: ${error.message}`)
        }
      }
    }

    // 去重文件引用
    const deduplicateFileReferences = (fileReferences) => {
      if (!fileReferences || !Array.isArray(fileReferences)) {
        return []
      }

      const uniqueFiles = new Map()

      fileReferences.forEach(ref => {
        if (ref.filePath) {
          // 使用文件路径作为唯一标识符
          const key = ref.filePath.toLowerCase()

          // 如果路径已存在，保留相似度更高的
          if (!uniqueFiles.has(key) || uniqueFiles.get(key).similarity < ref.similarity) {
            uniqueFiles.set(key, ref)
          }
        }
      })

      const result = Array.from(uniqueFiles.values())
      console.log('📁 文件引用去重:', {
        原始数量: fileReferences.length,
        去重后数量: result.length,
        去重前: fileReferences.map(r => r.fileName),
        去重后: result.map(r => r.fileName)
      })

      return result
    }

    // 检查是否应该显示文件引用
    const shouldShowFileReferences = (message, fileReferences) => {
      if (!fileReferences || fileReferences.length === 0) return false
      if (!message || typeof message !== 'string') return false

      // 检查消息是否包含"根据知识库数据"或"根据知识库和联网搜索"
      const hasKnowledgeKeyword = message.includes('根据知识库数据') || message.includes('根据知识库和联网搜索')

      console.log('🔍 文件引用显示判断:', {
        消息长度: message.length,
        文件引用数量: fileReferences.length,
        包含知识库关键词: hasKnowledgeKeyword,
        消息预览: message.substring(0, 50) + '...'
      })

      return hasKnowledgeKeyword
    }

    // 检查是否应该显示联网搜索引用
    const shouldShowWebReferences = (message, webReferences) => {
      if (!webReferences || webReferences.length === 0) return false
      if (!message || typeof message !== 'string') return false

      // 检查消息是否包含"根据联网搜索结果"或"根据知识库和联网搜索"
      const hasWebSearchKeyword = message.includes('根据联网搜索结果') || message.includes('根据知识库和联网搜索')

      console.log('🌐 联网搜索引用显示判断:', {
        消息长度: message.length,
        搜索引用数量: webReferences.length,
        包含联网搜索关键词: hasWebSearchKeyword,
        消息预览: message.substring(0, 50) + '...'
      })

      return hasWebSearchKeyword
    }

    // 打开联网搜索引用链接
    const openWebReference = (url) => {
      if (url) {
        try {
          console.log('🌐 打开联网搜索引用链接:', url)
          // 使用浏览器打开链接
          if (window.electronAPI && window.electronAPI.executeMCPTool) {
            window.electronAPI.executeMCPTool('browser_navigate', { url })
              .then(result => {
                if (result.success) {
                  console.log('✅ 联网搜索引用链接已在浏览器中打开')
                } else {
                  console.error('❌ 打开联网搜索引用链接失败:', result.error)
                }
              })
              .catch(error => {
                console.error('❌ 打开联网搜索引用链接时出错:', error)
              })
          } else {
            // 后备方案：使用默认浏览器
            window.open(url, '_blank')
          }
        } catch (error) {
          console.error('❌ 打开联网搜索引用链接失败:', error)
        }
      }
    }

    // 🔄 【需求3】切换聊天区域显示状态 - 开启聊天框默认切换到语音输入状态
    const toggleChatArea = () => {
      showChatArea.value = !showChatArea.value
      console.log('💬 聊天区域状态切换:', showChatArea.value ? '打开' : '关闭')

      // 🔄 【需求3】如果打开聊天区域，默认切换到语音输入状态
      if (showChatArea.value) {
        currentInputMode.value = 'voice'
        console.log('🎤 聊天区域打开：默认切换到语音输入模式')

        // 【修复启动时点击问题】防止重复启动智能语音
        if (!smartVoiceEnabled.value) {
          console.log('🎤 聊天区域打开：智能语音未启用，自动启用')
          startSmartVoice().catch(error => {
            console.error('❌ 自动启用智能语音失败:', error)
          })
        } else {
          console.log('✅ 智能语音已启用，无需重复启动')
        }

        // 滚动到最新消息
        nextTick(() => {
          scrollChatToBottom()
        })
      }
    }

    // 🔄 【需求3】关闭聊天区域
    const closeChatArea = () => {
      showChatArea.value = false
      console.log('❌ 关闭聊天区域')
    }

    // 🔄 【新增】新建会话 - 清空所有上下文和聊天记录
    const newConversation = async () => {
      console.log('🔄 新建会话，清空所有上下文和聊天记录')

      try {
        // 1. 🛑 取消当前正在进行的AI请求
        console.log('🛑 1. 取消当前正在进行的AI请求')
        const requestCancelled = cancelCurrentAIRequest()
        if (requestCancelled) {
          console.log('✅ AI请求已取消')
        } else {
          console.log('ℹ️ 没有正在进行的AI请求需要取消')
        }

        // 2. 停止所有AI处理
        if (isTTSPlaying.value) {
          if (window.speechSynthesis && window.speechSynthesis.speaking) {
            window.speechSynthesis.cancel()
          }
          isTTSPlaying.value = false
        }

        // 3. 停止AI思考状态
        isAIThinking.value = false

        // 4. 清空所有对话上下文
        mcpConversationHistory.value = []
        displayMessageHistory.value = []

        // 5. 清空文本输入框
        textInputValue.value = ''

        // 6. 重置相关状态
        shouldResetContext.value = false
        lastMCPTool.value = null

        // 7. 停止对话管理器的当前处理
        if (conversationManager.value) {
          conversationManager.value.stopTTS()
          try {
            await conversationManager.value.stopCommandListening()
          } catch (error) {
            console.warn('停止指令监听失败:', error)
          }
          conversationManager.value.isProcessingCommand = false
          conversationManager.value.isProcessingWakeWord = false
          conversationManager.value.isCommandListening = false
          conversationManager.value.isWakeWordListening = false
        }

        // 8. 🔄 重置智能语音状态
        if (smartVoiceEnabled.value) {
          console.log('🔄 8. 重置智能语音状态')
          smartVoiceState.value = 'listening_wake_word'
        } else {
          smartVoiceState.value = 'idle'
        }

        // 8. 重新启动唤醒词监听（如果在语音模式）
        if (smartVoiceEnabled.value && conversationManager.value) {
          console.log('🎤 8. 重新启动唤醒词监听')
          conversationManager.value.isEnabled = true
          await conversationManager.value.startWakeWordListening()
        }

        // 9. 添加新会话开始提示
        const newSessionMessage = {
          role: 'assistant',
          content: '新会话已开始，有什么我可以帮助您的吗？',
          timestamp: Date.now(),
          isSimpleReply: true
        }
        displayMessageHistory.value.push(newSessionMessage)

        // 10. 滚动到最新消息
        nextTick(() => {
          scrollChatToBottom()
        })

        console.log('✅ 新会话创建成功')

      } catch (error) {
        console.error('❌ 新建会话时出错:', error)

        // 添加错误提示
        const errorMessage = {
          role: 'assistant',
          content: '新建会话时出现错误，请重试',
          timestamp: Date.now(),
          isError: true
        }
        displayMessageHistory.value.push(errorMessage)
      }
    }



    // 移除鼠标进入/离开事件处理 - 改用CSS hover

    // 清理全局事件监听器
    const cleanupGlobalListeners = () => {
      console.log('🧹 清理全局事件监听器')

      if (globalMouseMoveHandler) {
        window.removeEventListener('mousemove', globalMouseMoveHandler, { capture: true, passive: false })
        globalMouseMoveHandler = null
      }
      if (globalMouseUpHandler) {
        window.removeEventListener('mouseup', globalMouseUpHandler, { capture: true, passive: false })
        globalMouseUpHandler = null
      }
      if (globalRightClickHandler) {
        window.removeEventListener('contextmenu', globalRightClickHandler, { passive: false })
        globalRightClickHandler = null
      }

      // 重置拖拽状态
      isDragging.value = false
      dragStart.value = { x: 0, y: 0 }

      // 🔄 【修复】清除窗口尺寸缓存，下次拖拽时重新获取
      windowBounds = null
      console.log('🖱️ 拖拽结束，已清除窗口尺寸缓存')

      // 🔄 【修复】拖拽结束时，恢复所有事件
      document.body.style.pointerEvents = ''
      document.body.style.userSelect = ''

      // 🔄 【修复】拖拽结束时，恢复showChatArea的监听器
      if (window._showChatAreaWatcher === false) {
        window._showChatAreaWatcher = true
        console.log('🖱️ 拖拽结束：恢复showChatArea监听器')
      }

      // 🔄 【修复】清除拖拽开始标志
      window._isDraggingStarted = false

      // 🔄 【修复】通知主进程拖拽结束
      if (window.electronAPI && window.electronAPI.floatingWindowDragEnd) {
        window.electronAPI.floatingWindowDragEnd()
      }

      // 🔄 【修复】拖拽结束后，重新检查并调整窗口高度
      setTimeout(() => {
        if (showChatArea.value) {
          console.log('🖱️ 拖拽结束后，重新调整窗口高度到完整高度')
          adjustWindowHeight(true)
        } else {
          console.log('🖱️ 拖拽结束后，重新调整窗口高度到折叠高度')
          adjustWindowHeight(false)
        }
      }, 150) // 延迟150ms确保主进程的拖拽结束处理完成

      console.log('✅ 全局事件监听器清理完成')
    }

    // 单击处理 - 使用Vue的@click事件
    const handleCharacterClick = (event) => {
      console.log('🖱️ 单击事件：切换聊天区域')

      // 如果正在拖拽，不处理点击
      if (isDragging.value) {
        console.log('🖱️ 正在拖拽，忽略点击事件')
        return
      }

      // 🔄 【修复】添加延迟检查，防止拖拽过程中意外触发
      setTimeout(() => {
        if (!isDragging.value) {
          // 切换聊天区域
          toggleChatArea()
        } else {
          console.log('🖱️ 延迟检查：仍在拖拽中，忽略点击事件')
        }
      }, 100)
    }

    // 双击和拖拽检测变量
    let lastMouseDownTime = 0
    let lastMouseDownPos = { x: 0, y: 0 }
    let isDoubleClickCandidate = false

    // 鼠标按下处理 - 专门处理双击和拖拽
    const handleCharacterMouseDown = (event) => {
      // 只处理左键
      if (event.button !== 0) return

      console.log('🖱️ 鼠标按下，检测双击和拖拽')

      // 🔄 【修复】立即阻止事件冒泡，防止触发其他事件
      event.preventDefault()
      event.stopPropagation()
      event.stopImmediatePropagation()

      // 🔄 【修复】设置拖拽开始标志，防止其他事件触发
      window._isDraggingStarted = true

      const currentTime = Date.now()
      const currentPos = { x: event.clientX, y: event.clientY }

      // 检测双击
      const timeDiff = currentTime - lastMouseDownTime
      const posDiff = Math.sqrt(
        Math.pow(currentPos.x - lastMouseDownPos.x, 2) +
        Math.pow(currentPos.y - lastMouseDownPos.y, 2)
      )

      if (timeDiff < 400 && posDiff < 10 && isDoubleClickCandidate) {
        // 双击检测成功
        console.log('🖱️ 检测到双击事件')
        handleDoubleClick()
        isDoubleClickCandidate = false
        return
      }

      // 记录当前点击信息
      lastMouseDownTime = currentTime
      lastMouseDownPos = currentPos
      isDoubleClickCandidate = true

      // 开始拖拽监听
      startDragListening(event)
    }

    // 双击处理 - 弹出主窗口
    const handleDoubleClick = () => {
      console.log('🖱️ 双击处理：弹出主窗口知识库页面')
      // 清理任何拖拽状态
      cleanupGlobalListeners()
      // 打开主窗口
      openMainWindow('knowledge')
    }

    // 拖拽监听开始
    const startDragListening = (event) => {
      // 确保只处理左键拖拽
      if (event.button !== 0) return

      console.log('🖱️ 开始拖拽监听')

      // 添加阻止默认行为
      event.preventDefault()
      event.stopPropagation()

      const startX = event.clientX
      const startY = event.clientY
      let hasDragStarted = false

      // 全局鼠标移动处理
      globalMouseMoveHandler = (moveEvent) => {
        // 阻止默认行为，防止选择文本等
        moveEvent.preventDefault()
        moveEvent.stopPropagation()

        const deltaX = Math.abs(moveEvent.clientX - startX)
        const deltaY = Math.abs(moveEvent.clientY - startY)
        const distance = Math.sqrt(deltaX * deltaX + deltaY * deltaY)

        // 判断是否开始拖拽
        if (!hasDragStarted && distance > DRAG_DISTANCE_LIMIT) {
          hasDragStarted = true
          isDragging.value = true
          dragStart.value = { x: startX, y: startY }
          isDoubleClickCandidate = false // 开始拖拽后取消双击候选
          console.log('🔥 开始拖拽，移动距离:', distance)

          // 🔄 【修复】拖拽开始时，禁用所有可能导致状态变化的事件
          document.body.style.pointerEvents = 'none'
          document.body.style.userSelect = 'none'

          // 🔄 【修复】拖拽开始时，立即阻止所有其他事件
          event.stopImmediatePropagation()

          // 🔄 【修复】拖拽开始时，临时禁用showChatArea的监听器
          if (window._showChatAreaWatcher) {
            window._showChatAreaWatcher = false
            console.log('🖱️ 拖拽开始：临时禁用showChatArea监听器')
          }

          // 🔄 【修复】通知主进程拖拽开始
          if (window.electronAPI && window.electronAPI.floatingWindowDragStart) {
            window.electronAPI.floatingWindowDragStart()
          }

          // 🔄 【修复】拖拽开始时获取窗口尺寸，避免拖拽过程中频繁调用
          if (!windowBounds && window.electronAPI && window.electronAPI.getFloatingWindowBounds) {
            // 使用Promise方式获取窗口尺寸，避免在非异步函数中使用await
            window.electronAPI.getFloatingWindowBounds()
              .then(result => {
                if (result.success) {
                  windowBounds = result
                  console.log('🖱️ 拖拽开始时获取到窗口尺寸:', windowBounds)
                }
              })
              .catch(error => {
                console.error('❌ 拖拽开始时获取窗口尺寸失败:', error)
              })
          }
        }

        // 执行拖拽
        if (hasDragStarted) {
          performDrag(moveEvent)
        }
      }

      // 全局鼠标释放处理
      globalMouseUpHandler = (upEvent) => {
        console.log('🖱️ 鼠标释放，结束拖拽监听')
        cleanupGlobalListeners()
      }

      // 添加全局事件监听器，使用capture确保事件在捕获阶段就处理
      window.addEventListener('mousemove', globalMouseMoveHandler, { capture: true, passive: false })
      window.addEventListener('mouseup', globalMouseUpHandler, { capture: true, passive: false })

      // 🔄 【修复】添加右键菜单阻止
      globalRightClickHandler = (rightEvent) => {
        rightEvent.preventDefault()
        rightEvent.stopPropagation()
        return false
      }
      window.addEventListener('contextmenu', globalRightClickHandler, { passive: false })
    }

    // 优化的拖拽执行方法
    let lastDragTime = 0
    let windowBounds = null // 缓存窗口尺寸信息

    const performDrag = async (moveEvent) => {
      // 拖拽节流：避免过于频繁的API调用
      const now = Date.now()
      if (now - lastDragTime < 16) return // ~60fps
      lastDragTime = now

      try {
        // 🔄 【修复】拖拽过程中使用缓存的窗口尺寸，不再获取
        if (!windowBounds) {
          // 如果拖拽过程中没有窗口尺寸信息，使用默认值
          windowBounds = {
            bounds: { width: 400, height: 290 },
            screen: { width: window.screen.availWidth, height: window.screen.availHeight }
          }
        }

        const deltaX = moveEvent.clientX - dragStart.value.x
        const deltaY = moveEvent.clientY - dragStart.value.y

        // 🔄 【修复】使用更简单的拖拽逻辑，避免缩放问题
        // 直接使用鼠标移动的像素距离，不进行缩放补偿
        let newX = window.screenX + deltaX
        let newY = window.screenY + deltaY

        // 使用实际的窗口和屏幕尺寸
        const screenWidth = windowBounds.screen.width
        const screenHeight = windowBounds.screen.height
        const windowWidth = windowBounds.bounds.width
        const windowHeight = windowBounds.bounds.height

        console.log('🖱️ 拖拽边界检查:', {
          screenSize: `${screenWidth}x${screenHeight}`,
          windowSize: `${windowWidth}x${windowHeight}`,
          newPosition: `${newX},${newY}`
        })

        // 边界检查
        const margin = 20
        const minX = -windowWidth * 0.8  // 允许部分超出屏幕左边
        const maxX = screenWidth - windowWidth * 0.2  // 至少保留20%可见
        const minY = 0  // 不允许超出屏幕上边
        const maxY = screenHeight - windowHeight * 0.3  // 至少保留30%可见

        // 限制坐标范围
        newX = Math.max(minX, Math.min(newX, maxX))
        newY = Math.max(minY, Math.min(newY, maxY))

        if (window.electronAPI && window.electronAPI.dragFloatingWindow) {
          window.electronAPI.dragFloatingWindow({
            x: Math.round(newX),
            y: Math.round(newY)
          })
        }
      } catch (error) {
        console.error('拖拽处理错误:', error)
        cleanupGlobalListeners()  // 出错时清理
      }
    }



    // 添加键盘快捷键取消拖拽
    const handleKeyDown = (event) => {
      if (event.key === 'Escape' && isDragging.value) {
        console.log('⌨️ ESC键取消拖拽')
        cleanupGlobalListeners()
      }
    }

    // 生命周期
    onMounted(async () => {
      try {
        console.log('🚀 FloatingCharacter组件已挂载')

        // 🔄 【修复】初始化全局拖拽标志
        window._showChatAreaWatcher = true
        window._isDraggingStarted = false

        // 添加键盘事件监听
        window.addEventListener('keydown', handleKeyDown)

        // 添加用户交互事件监听，用于解锁音频上下文
        const unlockAudioContext = () => {
          console.log('👆 检测到用户交互，尝试解锁音频上下文')

          // 尝试播放静音音频来解锁音频上下文
          if (conversationManager.value) {
            conversationManager.value.unlockAudio()
          } else {
            // 如果会话管理器尚未创建，创建一个临时的音频上下文
            try {
              const audioContext = new (window.AudioContext || window.webkitAudioContext)()
              const oscillator = audioContext.createOscillator()
              oscillator.frequency.setValueAtTime(0, audioContext.currentTime) // 0Hz = 静音
              oscillator.connect(audioContext.destination)
              oscillator.start(0)
              oscillator.stop(audioContext.currentTime + 0.001) // 播放1ms
              console.log('✅ 通过用户交互成功解锁音频')
            } catch (error) {
              console.warn('⚠️ 通过用户交互解锁音频失败:', error)
            }
          }

          // 移除事件监听器，因为只需要解锁一次
          document.removeEventListener('click', unlockAudioContext)
          document.removeEventListener('touchstart', unlockAudioContext)
          document.removeEventListener('keydown', unlockAudioContext)
        }

        // 添加各种用户交互事件监听
        document.addEventListener('click', unlockAudioContext, { once: true })
        document.addEventListener('touchstart', unlockAudioContext, { once: true })
        document.addEventListener('keydown', unlockAudioContext, { once: true })

        // 检查登录状态
        await authStore.checkLoginStatus()

        // 监听登录状态变化
        if (window.electronAPI) {
          window.electronAPI.onLoginStatusChanged((status) => {
            authStore.isLoggedIn = status
          })

          // 添加对ASR提供商变更的直接监听
          window.electronAPI.onASRProviderChanged(async (data) => {
            console.log('🔄 收到ASR提供商变更事件(主进程):', data)

            // 显示需要重启应用的提示
            const providerName = asrProviderManager.getProviderName()
            displayStatus(`已切换到${providerName}，唤醒词检测需要重启应用才能完全生效`, 'warning', 10000)

            // 如果智能语音已启动，则重新启动以应用新提供商
            if (smartVoiceEnabled.value) {
              console.log('🔄 ASR提供商已变更，立即重新初始化智能语音...')

              try {
                // 先停止当前服务
                await stopSmartVoice()
                console.log('✅ 已停止当前智能语音服务')

                // 短暂延迟后重新启动
                setTimeout(async () => {
                  try {
                    await startSmartVoice()
                    console.log('✅ 智能语音服务已使用新ASR提供商重新启动')
                    // 不要覆盖重启提示
                    // displayStatus(`语音服务已切换到${asrProviderManager.getProviderName()}`)
                  } catch (restartError) {
                    console.error('❌ 重新启动智能语音失败:', restartError)
                    displayStatus('重新启动语音服务失败，请重启应用', 'error')
                  }
                }, 500) // 减少延迟时间，加快响应速度
              } catch (stopError) {
                console.error('❌ 停止当前智能语音服务失败:', stopError)
              }
            } else {
              console.log('ℹ️ 智能语音未启用，无需重新初始化，但已更新ASR提供商设置')
            }
          })

          // 添加对强制重新加载ASR的监听
          window.electronAPI.onForceReloadASR(async (data) => {
            console.log('🔄 收到强制重新加载ASR事件:', data)

            // 强制重新初始化对话管理器
            if (smartVoiceEnabled.value) {
              console.log('🔄 强制重新初始化对话管理器，使用最新ASR提供商')

              try {
                // 先停止当前服务
                await stopSmartVoice()
                console.log('✅ 已停止当前智能语音服务')

                // 立即重新启动，不延迟
                try {
                  await startSmartVoice()
                  console.log('✅ 智能语音服务已强制重新初始化')
                } catch (restartError) {
                  console.error('❌ 强制重新初始化智能语音失败:', restartError)
                }
              } catch (stopError) {
                console.error('❌ 停止当前智能语音服务失败:', stopError)
              }
            }
          })

          // 监听状态消息
          if (window.electronAPI.onStatusMessage) {
            window.electronAPI.onStatusMessage((message) => {
              console.log('💬 收到状态消息:', message)
            })
          }

          // 新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程处理
          console.log('✅ 新的 Sherpa-ONNX 集成方式：使用 Web Audio API 和 WASM，无需主进程处理')
        }

        // 检查CryptoJS状态
        console.log('🔍 检查CryptoJS状态:', {
          hasCryptoJS: !!window.CryptoJS,
          hasCryptoJSTest: !!window.CryptoJSTest,
          hasHmacSHA1: !!(window.CryptoJS && window.CryptoJS.HmacSHA1)
        })

        // 🔄 【需求1】软件启动并且加载完成后主动开启智能语音，开始监听唤醒词
        console.log('🎤 启动后主动开启智能语音监听唤醒词')
        // 🔧 【恢复智能语音启动，但跳过欢迎语音】
        console.log('🔧 恢复智能语音启动，跳过欢迎语音播放')
        // 🔄 【修复】移除延时启动，直接启动智能语音
        try {
          console.log('🔧 开始启动智能语音...')
          await startSmartVoice()
          console.log('✅ 启动后智能语音自动启动成功')

          // 🔄 【修复】不在这里发送消息，让 startSmartVoice 方法在欢迎语音播放完成后发送
        } catch (error) {
          console.error('❌ 启动后自动启动智能语音失败:', error)
          recordingError.value = '启动后自动启动智能语音失败: ' + error.message
          setTimeout(() => {
            recordingError.value = ''
            recordingStatus.value = ''
          }, 5000)

          // 🔄 【修复】即使智能语音启动失败，也通知主进程关闭loading
          if (window.electronAPI && window.electronAPI.invoke) {
            window.electronAPI.invoke('send-status-message', '智能语音启动完成').catch(error => {
              console.warn('⚠️ 通知主进程智能语音启动完成失败:', error)
            })
          }
        }


        // 🔧 【新增】添加全局错误处理，防止应用闪退
        window.addEventListener('error', (event) => {
          console.error('🔧 全局错误捕获:', {
            message: event.message,
            filename: event.filename,
            lineno: event.lineno,
            colno: event.colno,
            error: event.error
          })
          // 防止错误导致应用闪退
          event.preventDefault()
          return false
        })

        window.addEventListener('unhandledrejection', (event) => {
          console.error('🔧 未处理的Promise拒绝:', event.reason)
          // 防止Promise拒绝导致应用闪退
          event.preventDefault()
        })

        // 监听ASR提供商变更事件
        window.addEventListener('message', async (event) => {
          if (event.data && event.data.type === 'asr-provider-changed') {
            console.log('🔄 收到ASR提供商变更事件:', event.data)

            // 如果智能语音已启动，则重新启动以应用新提供商
            if (smartVoiceEnabled.value && conversationManager.value) {
              console.log('🔄 ASR提供商已变更，重新初始化智能语音...')

              // 先停止当前服务
              try {
                await stopSmartVoice()
                console.log('✅ 已停止当前智能语音服务')

                // 延迟一秒后重新启动
                setTimeout(async () => {
                  try {
                    await startSmartVoice()
                    console.log('✅ 智能语音服务已使用新ASR提供商重新启动')
                    displayStatus('语音服务已重新启动，使用' + asrProviderManager.getProviderName())
                  } catch (restartError) {
                    console.error('❌ 重新启动智能语音失败:', restartError)
                    displayStatus('重新启动语音服务失败，请手动尝试', 'error')
                  }
                }, 1000)
              } catch (stopError) {
                console.error('❌ 停止当前智能语音服务失败:', stopError)
              }
            } else {
              console.log('ℹ️ 智能语音未启用，无需重新初始化')
            }
          }

          // 监听模型变更事件
          if (event.data && event.data.type === 'model-changed') {
            console.log('🔄 收到模型变更事件:', event.data)
            // 在UI中显示模型变更通知
            displayStatus(`已切换到模型: ${event.data.modelName}`)
          }
        })

      } catch (error) {
        console.error('❌ FloatingCharacter组件初始化失败:', error)
      }
    })

    onUnmounted(() => {
      console.log('🧹 FloatingCharacter组件卸载，清理资源')

      // 🔧 【增强】更全面的资源清理
      try {
        // 清理腾讯云ASR资源
        if (conversationManager.value) {
          console.log('🧹 清理对话管理器资源...')
          conversationManager.value.cleanup()
          conversationManager.value = null
        }

        // 强制停止语音播放
        console.log('🧹 停止TTS播放...')
        stopTTS()

        // 🔄 【新增】清理TTS状态检查定时器
        if (window.ttsStatusChecker) {
          clearInterval(window.ttsStatusChecker)
          window.ttsStatusChecker = null
          console.log('🧹 TTS状态检查定时器已清理')
        }

        // 清理鼠标事件监听器
        console.log('🧹 清理全局事件监听器...')
        cleanupGlobalListeners()

        // 清理键盘事件监听器
        window.removeEventListener('keydown', handleKeyDown)

        // 重置拖拽状态
        isDragging.value = false

        // 清理Electron API监听器
        if (window.electronAPI) {
          window.electronAPI.removeAllListeners('login-status-changed')
        }

        // 🔧 【新增】清理Web Audio资源
        if (window.audioContext) {
          try {
            window.audioContext.close()
            window.audioContext = null
            console.log('🧹 Web Audio上下文已清理')
          } catch (audioError) {
            console.warn('⚠️ 清理Web Audio上下文失败:', audioError)
          }
        }

        // 🔧 【新增】清理WASM模块
        if (window.Module) {
          try {
            window.Module = null
            console.log('🧹 WASM模块引用已清理')
          } catch (wasmError) {
            console.warn('⚠️ 清理WASM模块失败:', wasmError)
          }
        }

        // 🔧 【新增】清理定时器
        const highestTimeoutId = setTimeout(() => { }, 0)
        for (let i = 0; i < highestTimeoutId; i++) {
          clearTimeout(i)
        }
        console.log('🧹 所有定时器已清理')

        console.log('✅ FloatingCharacter组件资源清理完成')

      } catch (error) {
        console.error('❌ FloatingCharacter组件资源清理失败:', error)
      }
    })

    // 邮件确认弹框相关方法
    const showEmailConfirmModal = (emailData, originalResponse) => {
      console.log('📧 [FLOATING_MODAL] showEmailConfirmModal被调用')
      console.log('📧 [FLOATING_MODAL] 传入的emailData:', emailData)
      console.log('📧 [FLOATING_MODAL] emailData类型:', typeof emailData)
      console.log('📧 [FLOATING_MODAL] emailData详细结构:', JSON.stringify(emailData, null, 2))
      console.log('📧 [FLOATING_MODAL] 当前showEmailModal状态:', showEmailModal.value)

      // 确保emailData存在并且有正确的结构
      if (!emailData || typeof emailData !== 'object') {
        console.error('📧 [FLOATING_MODAL] emailData无效:', emailData)
        emailData = {}
      }

      emailModalData.value = {
        to: Array.isArray(emailData.to) ? emailData.to : (emailData.to ? [emailData.to] : []),
        cc: Array.isArray(emailData.cc) ? emailData.cc : (emailData.cc ? [emailData.cc] : []),
        subject: emailData.subject || emailData.sub || '',
        message: emailData.message || emailData.content || ''
      }

      console.log('📧 [FLOATING_MODAL] 设置后的emailModalData:', emailModalData.value)
      console.log('📧 [FLOATING_MODAL] 邮件数据字段检查:', {
        'to字段': emailData.to,
        'cc字段': emailData.cc,
        'subject字段': emailData.subject,
        'sub字段': emailData.sub,
        'message字段': emailData.message,
        'content字段': emailData.content
      })

      pendingEmailCallback.value = originalResponse
      showEmailModal.value = true

      console.log('📧 [FLOATING_MODAL] 设置后的showEmailModal状态:', showEmailModal.value)
    }

    const closeEmailModal = () => {
      console.log('📧 [FLOATING_MODAL] closeEmailModal被调用')
      showEmailModal.value = false
      emailModalData.value = {
        to: [],
        cc: [],
        subject: '',
        message: ''
      }
      pendingEmailCallback.value = null
    }

    const handleEmailSend = async (emailPayload) => {
      try {
        console.log('📧 [FLOATING_MODAL] handleEmailSend被调用，参数:', emailPayload)

        // 确保设置is_ok为true，表示用户已确认
        const finalPayload = {
          ...emailPayload,
          is_ok: true
        }

        console.log('📧 [FLOATING_MODAL] 发送邮件，最终参数:', finalPayload)

        // 调用MCP发送邮件工具
        const result = await window.electronAPI.executeMCPTool('send_email', finalPayload)

        closeEmailModal()

        // 🔧 【修复邮件弹框bug】立即移除思考中状态，无论邮件发送成功还是失败
        console.log('📧 [FLOATING_MODAL] 移除思考中状态')
        displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

        if (result.success) {
          console.log('📧 [FLOATING_MODAL] 邮件发送成功')

          // 添加成功消息到历史记录
          const successMessage = {
            role: 'assistant',
            content: '邮件发送成功！',
            timestamp: Date.now(),
            isSimpleReply: true
          }
          displayMessageHistory.value.push(successMessage)

          // 确保滚动到最新消息
          if (showHistoryModal.value) {
            nextTick(() => {
              scrollToLatestMessage()
            })
          }

          console.log('邮件发送成功！')
        } else {
          console.error('📧 [FLOATING_MODAL] 邮件发送失败:', result.error)

          // 添加失败消息到历史记录
          const errorMessage = {
            role: 'assistant',
            content: '邮件发送失败: ' + (result.error || '未知错误'),
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(errorMessage)

          // 确保滚动到最新消息
          if (showHistoryModal.value) {
            nextTick(() => {
              scrollToLatestMessage()
            })
          }

          console.error('邮件发送失败: ' + (result.error || '未知错误'))
        }

      } catch (error) {
        console.error('📧 [FLOATING_MODAL] 发送邮件时出错:', error)

        // 🔧 【修复邮件弹框bug】确保异常情况下也移除思考中状态
        displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

        // 添加错误消息到历史记录
        const errorMessage = {
          role: 'assistant',
          content: '发送邮件时出错: ' + error.message,
          timestamp: Date.now(),
          isSimpleReply: false
        }
        displayMessageHistory.value.push(errorMessage)

        // 确保滚动到最新消息
        if (showHistoryModal.value) {
          nextTick(() => {
            scrollToLatestMessage()
          })
        }

        console.error('发送邮件时出错: ' + error.message)
        closeEmailModal()
      }
    }

    const handleEmailError = (errorMessage) => {
      console.error('📧 [FLOATING_MODAL] 邮件错误:', errorMessage)
      alert('邮件错误: ' + errorMessage)
      closeEmailModal()
    }

    // 邮件预览相关方法
    const showEmailPreviewModal = (emailData) => {
      console.log('📧 [EMAIL_PREVIEW] 显示邮件预览:', emailData)

      // 设置邮件预览数据
      emailPreviewData.value = {
        to: Array.isArray(emailData.to) ? emailData.to.filter(e => e) : (emailData.to ? [emailData.to] : ['']),
        cc: Array.isArray(emailData.cc) ? emailData.cc.filter(e => e) : (emailData.cc ? [emailData.cc] : ['']),
        subject: emailData.subject || emailData.sub || '',
        message: emailData.message || emailData.content || ''
      }

      // 确保至少有一个空的收件人字段
      if (emailPreviewData.value.to.length === 0) {
        emailPreviewData.value.to = ['']
      }

      // 确保至少有一个空的抄送字段
      if (emailPreviewData.value.cc.length === 0) {
        emailPreviewData.value.cc = ['']
      }

      console.log('📧 [EMAIL_PREVIEW] 设置后的emailPreviewData:', emailPreviewData.value)

      // 保存原始数据
      originalEmailData.value = JSON.parse(JSON.stringify(emailPreviewData.value))

      // 显示预览模式
      showEmailPreview.value = true
      isEmailEditing.value = false

      console.log('📧 [EMAIL_PREVIEW] showEmailPreview状态:', showEmailPreview.value)
      console.log('📧 [EMAIL_PREVIEW] isEmailEditing状态:', isEmailEditing.value)
    }

    const closeEmailPreview = () => {
      showEmailPreview.value = false
      isEmailEditing.value = false
      emailPreviewData.value = {
        to: [''],
        cc: [''],
        subject: '',
        message: ''
      }
      originalEmailData.value = null
    }

    const startEmailEdit = () => {
      isEmailEditing.value = true
      // 保存当前数据用于取消时恢复
      originalEmailData.value = JSON.parse(JSON.stringify(emailPreviewData.value))
    }

    const saveEmailEdit = () => {
      isEmailEditing.value = false
      // 更新原始数据
      originalEmailData.value = JSON.parse(JSON.stringify(emailPreviewData.value))
    }

    const cancelEmailEdit = () => {
      // 恢复原始数据
      if (originalEmailData.value) {
        emailPreviewData.value = JSON.parse(JSON.stringify(originalEmailData.value))
      }
      isEmailEditing.value = false
    }

    const addRecipient = () => {
      emailPreviewData.value.to.push('')
    }

    const removeRecipient = (index) => {
      if (emailPreviewData.value.to.length > 1) {
        emailPreviewData.value.to.splice(index, 1)
      }
    }

    const addCc = () => {
      emailPreviewData.value.cc.push('')
    }

    const removeCc = (index) => {
      if (emailPreviewData.value.cc.length > 1) {
        emailPreviewData.value.cc.splice(index, 1)
      }
    }

    const sendEmailFromPreview = async () => {
      try {
        // 验证必填字段
        const to = emailPreviewData.value.to.filter(email => email.trim())
        const cc = emailPreviewData.value.cc.filter(email => email.trim())

        if (to.length === 0) {
          alert('请至少填写一个收件人邮箱')
          return
        }

        if (!emailPreviewData.value.subject.trim()) {
          alert('请填写邮件主题')
          return
        }

        if (!emailPreviewData.value.message.trim()) {
          alert('请填写邮件内容')
          return
        }

        // 构建邮件数据
        const emailPayload = {
          to,
          cc: cc.length > 0 ? cc : undefined,
          sub: emailPreviewData.value.subject.trim(),
          message: emailPreviewData.value.message.trim(),
          is_ok: true // 用户已确认
        }

        console.log('📧 [EMAIL_PREVIEW] 发送邮件:', emailPayload)

        // 调用MCP发送邮件工具
        const result = await window.electronAPI.executeMCPTool('send_email', emailPayload)

        // 关闭预览
        closeEmailPreview()

        if (result.success) {
          console.log('📧 [EMAIL_PREVIEW] 邮件发送成功')

          // 添加成功消息到历史记录
          const successMessage = {
            role: 'assistant',
            content: '邮件发送成功！',
            timestamp: Date.now(),
            isSimpleReply: true
          }
          displayMessageHistory.value.push(successMessage)

          console.log('邮件发送成功！')
        } else {
          console.error('📧 [EMAIL_PREVIEW] 邮件发送失败:', result.error)

          // 添加失败消息到历史记录
          const errorMessage = {
            role: 'assistant',
            content: '邮件发送失败: ' + (result.error || '未知错误'),
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(errorMessage)

          addBubble('邮件发送失败: ' + (result.error || '未知错误'))
        }

      } catch (error) {
        console.error('📧 [EMAIL_PREVIEW] 发送邮件时出错:', error)

        // 添加错误消息到历史记录
        const errorMessage = {
          role: 'assistant',
          content: '发送邮件时出错: ' + error.message,
          timestamp: Date.now(),
          isSimpleReply: false
        }
        displayMessageHistory.value.push(errorMessage)

        console.error('发送邮件时出错: ' + error.message)
        closeEmailPreview()
      }
    }

    // 格式化邮件预览文本（添加换行和样式）
    const formatEmailPreviewText = (content) => {
      if (!content) return ''

      // 先处理换行
      let htmlContent = content.replace(/\n/g, '<br/>')

      // 处理邮件预览标题
      htmlContent = htmlContent.replace(/📧 邮件预览/, '<strong style="color: #007AFF; font-size: 16px;">📧 邮件预览</strong>')

      // 处理收件人字段
      htmlContent = htmlContent.replace(/收件人：([^<]+?)(?=<br|$)/g, '<div class="email-field"><span class="field-label">收件人：</span><span class="field-value">$1</span></div>')

      // 处理抄送字段（可选）
      htmlContent = htmlContent.replace(/抄送：([^<]+?)(?=<br|$)/g, '<div class="email-field"><span class="field-label">抄送：</span><span class="field-value">$1</span></div>')

      // 处理主题字段
      htmlContent = htmlContent.replace(/主题：([^<]+?)(?=<br|$)/g, '<div class="email-field"><span class="field-label">主题：</span><span class="field-value">$1</span></div>')

      // 处理正文字段（特殊处理多行内容）
      htmlContent = htmlContent.replace(/正文：<br\/>(.*?)(?=<br\/><br\/>请确认|$)/s, (match, bodyContent) => {
        return `<div class="email-field"><span class="field-label">正文：</span><div class="field-value email-body">${bodyContent}</div></div>`
      })

      // 如果上面的正则没有匹配到，尝试更宽泛的匹配
      if (!htmlContent.includes('email-body')) {
        htmlContent = htmlContent.replace(/正文：<br\/>(.*?)(?=<br\/><br\/>|$)/s, (match, bodyContent) => {
          return `<div class="email-field"><span class="field-label">正文：</span><div class="field-value email-body">${bodyContent}</div></div>`
        })
      }

      // 处理确认文本
      htmlContent = htmlContent.replace(/请确认邮件内容.*$/, '<div class="email-confirm-text">请确认邮件内容，点击下方按钮进行操作。</div>')

      return htmlContent
    }

    // 格式化普通消息内容（支持换行）
    const formatMessageContent = (content) => {
      if (!content) return ''
      return content.replace(/\n/g, '<br/>')
    }

    // 从消息预览发送邮件
    const sendEmailFromMessagePreview = async (message) => {
      console.log('📧 [MESSAGE_PREVIEW] 从消息预览发送邮件:', message)

      // 找到消息在displayMessageHistory中的索引
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) {
        console.error('📧 [MESSAGE_PREVIEW] 找不到消息')
        return
      }

      const currentMessage = displayMessageHistory.value[idx]

      if (!currentMessage.emailData) {
        console.error('📧 [MESSAGE_PREVIEW] 邮件数据不存在')
        alert('邮件数据不存在，无法发送')
        return
      }

      // 立即标记为发送中状态，隐藏按钮
      const sendingMessage = {
        ...currentMessage,
        isSending: true
      }
      displayMessageHistory.value.splice(idx, 1, sendingMessage)

      // 添加"发送中"的提示消息
      displayMessageHistory.value.push({
        role: 'assistant',
        content: '正在发送邮件...',
        timestamp: Date.now(),
        isThinking: true
      })

      try {
        // 使用消息中的邮件数据发送
        emailPreviewData.value = { ...currentMessage.emailData }
        await sendEmailFromTextPreview()

        // 发送成功，更新原始消息为"已发送"
        const successIdx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
        if (successIdx !== -1) {
          const successMessage = displayMessageHistory.value[successIdx]
          const finalMessage = {
            ...successMessage,
            isSent: true,
            isSending: false,
            content: successMessage.content.replace(/请确认邮件内容.*$/, '✅ 邮件已发送')
          }
          displayMessageHistory.value.splice(successIdx, 1, finalMessage)
        }
      } catch (error) {
        console.error('📧 [MESSAGE_PREVIEW] 发送邮件失败:', error)
        alert(`发送邮件失败: ${error.message}`)

        // 发送失败，恢复按钮显示
        const errorIdx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
        if (errorIdx !== -1) {
          const errorMessage = displayMessageHistory.value[errorIdx]
          const restoredMessage = {
            ...errorMessage,
            isSending: false
          }
          displayMessageHistory.value.splice(errorIdx, 1, restoredMessage)
        }
      } finally {
        nextTick(() => { scrollToLatestMessage() })
      }
    }

    // 从消息预览编辑邮件
    const editEmailFromMessagePreview = (message) => {
      console.log('📧 [MESSAGE_EDIT] 开始编辑邮件消息:', message)

      if (!message.emailData) {
        console.error('📧 [MESSAGE_EDIT] 邮件数据不存在')
        alert('邮件数据不存在，无法编辑')
        return
      }

      // 设置邮件预览数据
      emailPreviewData.value = {
        to: Array.isArray(message.emailData.to) ? message.emailData.to : [message.emailData.to],
        cc: Array.isArray(message.emailData.cc) ? message.emailData.cc : (message.emailData.cc ? [message.emailData.cc] : ['']),
        subject: message.emailData.subject || '',
        message: message.emailData.message || ''
      }

      // 保存原始数据用于取消时恢复
      originalEmailData.value = JSON.parse(JSON.stringify(emailPreviewData.value))

      // 显示预览模式并进入编辑状态
      showEmailPreview.value = true
      isEmailEditing.value = true

      console.log('📧 [MESSAGE_EDIT] 邮件编辑状态:', {
        showEmailPreview: showEmailPreview.value,
        isEmailEditing: isEmailEditing.value,
        emailData: emailPreviewData.value
      })

      // 确保滚动到最新消息
      nextTick(() => {
        scrollToLatestMessage()
      })
    }

    // 从文本预览发送邮件
    const sendEmailFromTextPreview = async () => {
      try {
        // 验证必填字段
        const to = emailPreviewData.value.to.filter(email => email.trim())
        const cc = emailPreviewData.value.cc.filter(email => email.trim())

        if (to.length === 0) {
          // 移除思考中状态
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

          const errorMessage = {
            role: 'assistant',
            content: '邮件发送失败：请至少填写一个收件人邮箱',
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(errorMessage)
          return
        }

        if (!emailPreviewData.value.subject.trim()) {
          // 移除思考中状态
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

          const errorMessage = {
            role: 'assistant',
            content: '邮件发送失败：请填写邮件主题',
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(errorMessage)
          return
        }

        if (!emailPreviewData.value.message.trim()) {
          // 移除思考中状态
          displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

          const errorMessage = {
            role: 'assistant',
            content: '邮件发送失败：请填写邮件内容',
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(errorMessage)
          return
        }

        // 构建邮件数据
        const emailPayload = {
          to,
          cc: cc.length > 0 ? cc : undefined,
          sub: emailPreviewData.value.subject.trim(),
          message: emailPreviewData.value.message.trim(),
          is_ok: true // 用户已确认
        }

        console.log('📧 [EMAIL_TEXT_PREVIEW] 发送邮件:', emailPayload)

        // 调用MCP发送邮件工具
        const result = await window.electronAPI.executeMCPTool('send_email', emailPayload)

        // 移除思考中状态
        displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

        if (result.success) {
          console.log('📧 [EMAIL_TEXT_PREVIEW] 邮件发送成功')

          // 添加成功消息到历史记录
          const successMessage = {
            role: 'assistant',
            content: '✅ 邮件发送成功！',
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(successMessage)

          // 清空邮件预览数据
          emailPreviewData.value = {
            to: [''],
            cc: [''],
            subject: '',
            message: ''
          }

          console.log('邮件发送成功！')
        } else {
          console.error('📧 [EMAIL_TEXT_PREVIEW] 邮件发送失败:', result.error)

          // 添加失败消息到历史记录
          const errorMessage = {
            role: 'assistant',
            content: '❌ 邮件发送失败: ' + (result.error || '未知错误'),
            timestamp: Date.now(),
            isSimpleReply: false
          }
          displayMessageHistory.value.push(errorMessage)

          console.error('邮件发送失败: ' + (result.error || '未知错误'))
        }

        // 确保滚动到最新消息
        nextTick(() => {
          scrollToLatestMessage()
        })

      } catch (error) {
        console.error('📧 [EMAIL_TEXT_PREVIEW] 发送邮件时出错:', error)

        // 移除思考中状态
        displayMessageHistory.value = displayMessageHistory.value.filter(msg => !msg.isThinking)

        // 添加错误消息到历史记录
        const errorMessage = {
          role: 'assistant',
          content: '❌ 发送邮件时出错: ' + error.message,
          timestamp: Date.now(),
          isSimpleReply: false
        }
        displayMessageHistory.value.push(errorMessage)

        console.error('发送邮件时出错: ' + error.message)

        // 确保滚动到最新消息
        nextTick(() => {
          scrollToLatestMessage()
        })
      }
    }

    // 开始编辑邮件消息
    const startMessageEdit = (message) => {
      console.log('📧 [MESSAGE_EDIT] 开始编辑邮件消息:', message)

      if (!message.emailData) {
        console.error('📧 [MESSAGE_EDIT] 邮件数据不存在')
        alert('邮件数据不存在，无法编辑')
        return
      }
      console.log('🔄:', displayMessageHistory.value)
      // 找到消息在displayMessageHistory中的索引
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp)
      if (idx === -1) {
        console.error('📧 [MESSAGE_EDIT] 找不到消息')
        return
      }

      // 创建新的消息对象，确保响应式更新
      const emailDataCopy = JSON.parse(JSON.stringify(message.emailData))

      // 确保to和cc是数组
      if (!Array.isArray(emailDataCopy.to)) {
        emailDataCopy.to = emailDataCopy.to ? [emailDataCopy.to] : ['']
      }
      if (!Array.isArray(emailDataCopy.cc)) {
        emailDataCopy.cc = emailDataCopy.cc ? [emailDataCopy.cc] : ['']
      }

      const updatedMessage = {
        ...displayMessageHistory.value[idx],
        isEditing: true,
        editingData: emailDataCopy
      }

      // 使用splice触发响应式更新
      displayMessageHistory.value.splice(idx, 1, updatedMessage)

      console.log('📧 [MESSAGE_EDIT] 进入编辑状态，已创建数据副本')

      // 确保滚动到最新消息
      nextTick(() => {
        scrollToLatestMessage()
      })
    }

    // 保存邮件编辑
    const saveMessageEdit = (message) => {
      console.log('📧 [MESSAGE_EDIT] 保存邮件编辑:', message)

      // 找到消息在displayMessageHistory中的索引
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) {
        console.error('📧 [MESSAGE_EDIT] 找不到消息')
        return
      }

      const currentMessage = displayMessageHistory.value[idx]

      // 验证编辑副本是否存在
      if (!currentMessage.editingData) {
        console.error('📧 [MESSAGE_EDIT] 找不到编辑中的数据')
        return
      }

      // 从副本中提取并处理数据
      const processedData = {
        to: Array.isArray(currentMessage.editingData.to) ? currentMessage.editingData.to.filter(e => e && e.trim()) : [],
        cc: Array.isArray(currentMessage.editingData.cc) ? currentMessage.editingData.cc.filter(e => e && e.trim()) : [],
        subject: (currentMessage.editingData.subject || '').trim(),
        message: (currentMessage.editingData.message || '').trim()
      }

      // 验证必填字段
      if (processedData.to.length === 0) {
        alert('请至少填写一个收件人邮箱')
        return
      }
      if (!processedData.subject) {
        alert('请填写邮件主题')
        return
      }
      if (!processedData.message) {
        alert('请填写邮件内容')
        return
      }

      // 创建更新后的消息对象
      const updatedMessage = {
        ...currentMessage,
        emailData: processedData,
        isEditing: false
      }

      // 删除editingData属性
      delete updatedMessage.editingData

      // 使用splice触发响应式更新
      displayMessageHistory.value.splice(idx, 1, updatedMessage)

      console.log('📧 [MESSAGE_EDIT] 保存完成，更新后的数据:', processedData)

      // 更新全局邮件预览数据
      emailPreviewData.value = { ...processedData }
    }

    // 取消邮件编辑
    const cancelMessageEdit = (message) => {
      console.log('📧 [MESSAGE_EDIT] 取消邮件编辑:', message)

      // 找到消息在displayMessageHistory中的索引
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) {
        console.error('📧 [MESSAGE_EDIT] 找不到消息')
        return
      }

      const currentMessage = displayMessageHistory.value[idx]

      // 创建更新后的消息对象，退出编辑模式
      const updatedMessage = {
        ...currentMessage,
        isEditing: false
      }

      // 删除editingData属性
      delete updatedMessage.editingData

      // 使用splice触发响应式更新
      displayMessageHistory.value.splice(idx, 1, updatedMessage)

      console.log('📧 [MESSAGE_EDIT] 取消完成')
    }

    // 添加收件人
    const addEditingRecipient = (message) => {
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) return

      const currentMessage = displayMessageHistory.value[idx]
      if (!currentMessage.editingData) return

      const updatedMessage = {
        ...currentMessage,
        editingData: {
          ...currentMessage.editingData,
          to: [...currentMessage.editingData.to, '']
        }
      }

      displayMessageHistory.value.splice(idx, 1, updatedMessage)
    }

    // 移除收件人
    const removeEditingRecipient = (message, index) => {
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) return

      const currentMessage = displayMessageHistory.value[idx]
      if (!currentMessage.editingData || currentMessage.editingData.to.length <= 1) return

      const newTo = [...currentMessage.editingData.to]
      newTo.splice(index, 1)

      const updatedMessage = {
        ...currentMessage,
        editingData: {
          ...currentMessage.editingData,
          to: newTo
        }
      }

      displayMessageHistory.value.splice(idx, 1, updatedMessage)
    }

    // 添加抄送
    const addEditingCc = (message) => {
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) return

      const currentMessage = displayMessageHistory.value[idx]
      if (!currentMessage.editingData) return

      const updatedMessage = {
        ...currentMessage,
        editingData: {
          ...currentMessage.editingData,
          cc: [...currentMessage.editingData.cc, '']
        }
      }

      displayMessageHistory.value.splice(idx, 1, updatedMessage)
    }

    // 移除抄送
    const removeEditingCc = (message, index) => {
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) return

      const currentMessage = displayMessageHistory.value[idx]
      if (!currentMessage.editingData || currentMessage.editingData.cc.length <= 1) return

      const newCc = [...currentMessage.editingData.cc]
      newCc.splice(index, 1)

      const updatedMessage = {
        ...currentMessage,
        editingData: {
          ...currentMessage.editingData,
          cc: newCc
        }
      }

      displayMessageHistory.value.splice(idx, 1, updatedMessage)
    }

    // 更新编辑中的邮件字段
    const updateEditingField = (message, field, value, index = null) => {
      const idx = displayMessageHistory.value.findIndex(m => m.timestamp === message.timestamp && m.isEmailPreview)
      if (idx === -1) return

      const currentMessage = displayMessageHistory.value[idx]
      if (!currentMessage.editingData) return

      let updatedEditingData = { ...currentMessage.editingData }

      if (index !== null) {
        // 更新数组元素（如to[0]）
        if (field === 'to' || field === 'cc') {
          updatedEditingData[field] = [...currentMessage.editingData[field]]
          updatedEditingData[field][index] = value
        }
      } else {
        // 更新普通字段（如subject）
        updatedEditingData[field] = value
      }

      const updatedMessage = {
        ...currentMessage,
        editingData: updatedEditingData
      }

      displayMessageHistory.value.splice(idx, 1, updatedMessage)
    }

    // 🔄 【问题1解答】MCP执行前会重新开启对话的工具列表
    // 当前设置的工具包括：
    // - open_file: 打开文件
    // - query_flights: 查询航班
    // - get_weather_forecast: 查询天气预报
    // 
    // 注意：browser_navigate 不再触发清空操作
    // 
    // 🔄 【混合智能清空策略】操作类型分类
    const OPERATION_TYPES = {
      FILE: 'file',
      TRAVEL: 'travel',
      EMAIL: 'email',
      WEB: 'web',
      SYSTEM: 'system',
      KNOWLEDGE: 'knowledge'  // 新增知识库操作类型
    }

    // 🔄 【混合智能清空策略】MCP工具分类映射
    const MCP_TOOL_TYPES = {
      // 文件操作
      'search_files': OPERATION_TYPES.FILE,
      'list_directory': OPERATION_TYPES.FILE,
      'read_file': OPERATION_TYPES.FILE,
      'open_file': OPERATION_TYPES.FILE,
      'directory_tree': OPERATION_TYPES.FILE,

      // 旅行规划
      'query_flights': OPERATION_TYPES.TRAVEL,
      'get_weather_forecast': OPERATION_TYPES.TRAVEL,

      // 邮件操作
      'list_email': OPERATION_TYPES.EMAIL,
      'mark_email_as_read': OPERATION_TYPES.EMAIL,
      'send_email': OPERATION_TYPES.EMAIL,

      // 网页浏览
      'browser_navigate': OPERATION_TYPES.WEB,
      'baidu_search': OPERATION_TYPES.WEB,     // 百度查询归类为网页操作

      // 系统状态
      'get_status': OPERATION_TYPES.SYSTEM,

      // 知识库操作（虚拟工具，用于识别知识库问答）
      'knowledge_search': OPERATION_TYPES.KNOWLEDGE
    }

    // 🔄 【混合智能清空策略】获取工具的操作类型
    const getOperationType = (tools) => {
      if (!tools || tools.length === 0) return null

      // 返回第一个工具的类型（大多数情况下只有一个工具）
      const firstTool = tools[0]
      return MCP_TOOL_TYPES[firstTool] || null
    }

    // 🔄 【混合智能清空策略】检查是否是操作链的终结动作
    const isOperationChainEnd = (currentTools, operationType) => {
      if (!currentTools || currentTools.length === 0) return false

      const endActions = {
        [OPERATION_TYPES.FILE]: ['open_file'],
        [OPERATION_TYPES.TRAVEL]: ['get_weather_forecast', 'query_flights'],
        [OPERATION_TYPES.EMAIL]: ['mark_email_as_read', 'send_email'],
        [OPERATION_TYPES.WEB]: ['browser_navigate', 'baidu_search'],  // 网页操作通常都是终结动作
        [OPERATION_TYPES.KNOWLEDGE]: ['knowledge_search']  // 知识库搜索是终结动作
      }

      const endActionsForType = endActions[operationType] || []
      return currentTools.some(tool => endActionsForType.includes(tool))
    }

    // 🔄 【混合智能清空策略】检查是否是独立操作
    const isIndependentOperation = (tools, conversationHistory) => {
      if (!tools || tools.length === 0) return false

      const independentTools = ['get_weather_forecast', 'query_flights', 'open_file', 'browser_navigate', 'baidu_search', 'knowledge_search']
      const hasIndependentTool = tools.some(tool => independentTools.includes(tool))

      if (!hasIndependentTool) return false

      // 检查是否是文件操作链的一部分
      if (tools.includes('open_file')) {
        // 检查最近的对话是否包含文件搜索
        const recentMessages = conversationHistory.slice(-4) // 查看最近4条消息
        const hasRecentFileSearch = recentMessages.some(msg =>
          msg.content && (
            msg.content.includes('搜索') ||
            msg.content.includes('文件') ||
            msg.content.includes('第') && (msg.content.includes('个') || msg.content.includes('开'))
          )
        )
        return !hasRecentFileSearch // 如果没有相关的文件搜索上下文，则认为是独立操作
      }

      // 检查是否是知识库操作
      if (tools.includes('knowledge_search')) {
        // 知识库搜索总是独立操作
        return true
      }

      return true
    }

    // 🔄 【混合智能清空策略】获取最近使用的工具类型
    const getLastOperationType = () => {
      if (!lastMCPTool.value) return null
      return MCP_TOOL_TYPES[lastMCPTool.value] || null
    }

    // 🔄 【混合智能清空策略】调用前清空检测
    const shouldClearBeforeMCP = (predictedTools, conversationHistory) => {
      if (!predictedTools || predictedTools.length === 0) {
        return { clear: false, reason: '无工具预测' }
      }

      // 🔧 特殊处理：search_files 永远不在调用前清空上下文
      if (predictedTools.includes('search_files')) {
        console.log('🔄 [SMART_CLEAR] search_files 调用前不清空上下文，保持多文件选择能力')
        return { clear: false, reason: 'search_files 调用前保持上下文' }
      }

      const currentType = getOperationType(predictedTools)
      const lastType = getLastOperationType()

      console.log('🔄 [SMART_CLEAR] 调用前检测:', {
        predictedTools,
        currentType,
        lastType,
        lastMCPTool: lastMCPTool.value,
        conversationLength: conversationHistory.length
      })

      // 1. 操作类型切换检测
      if (lastType && currentType && lastType !== currentType) {
        return {
          clear: true,
          reason: `操作类型切换: ${lastType} → ${currentType}`,
          timing: 'before'
        }
      }

      // 2. 同类独立操作检测
      if (currentType === lastType && isIndependentOperation(predictedTools, conversationHistory)) {
        return {
          clear: true,
          reason: `同类独立操作: ${currentType}`,
          timing: 'before'
        }
      }

      // 3. 长时间间隔检测（超过5分钟）
      if (conversationHistory.length > 0) {
        const lastMessage = conversationHistory[conversationHistory.length - 1]
        const timeDiff = Date.now() - (lastMessage.timestamp || 0)
        if (timeDiff > 5 * 60 * 1000) { // 5分钟
          return {
            clear: true,
            reason: '时间间隔过长',
            timing: 'before'
          }
        }
      }

      return { clear: false, reason: '保持上下文' }
    }

    // 🔄 【混合智能清空策略】调用后清空检测
    const shouldClearAfterMCP = (actualTools, conversationHistory) => {
      if (!actualTools || actualTools.length === 0) {
        return { clear: false, reason: '无实际工具使用' }
      }

      // 🔧 调试：详细打印工具检查过程
      console.log('🔧 [DEBUG] shouldClearAfterMCP 详细检查:', {
        actualTools: actualTools,
        includesSearchFiles: actualTools.includes('search_files'),
        toolsArray: Array.isArray(actualTools) ? actualTools : 'not array',
        toolsLength: actualTools?.length
      })

      // 🔧 特殊处理：search_files 永远不清空上下文，以支持多文件选择
      if (actualTools.includes('search_files')) {
        console.log('🔄 [SMART_CLEAR] search_files 不清空上下文，保持多文件选择能力')
        return { clear: false, reason: 'search_files 保持上下文' }
      }

      const operationType = getOperationType(actualTools)

      console.log('🔄 [SMART_CLEAR] 调用后检测:', {
        actualTools,
        operationType,
        conversationLength: conversationHistory.length
      })

      // 1. 操作链完成检测
      if (isOperationChainEnd(actualTools, operationType)) {
        return {
          clear: true,
          reason: `操作链完成: ${operationType}`,
          timing: 'after'
        }
      }

      // 2. 独立操作完成检测（不在操作链中的单独操作）
      if (isIndependentOperation(actualTools, conversationHistory)) {
        // 检查是否真的是独立操作，而不是操作链的一部分
        const hasRecentRelatedContext = conversationHistory.slice(-3).some(msg => {
          if (operationType === OPERATION_TYPES.FILE) {
            return msg.content && (msg.content.includes('搜索') || msg.content.includes('找到'))
          }
          if (operationType === OPERATION_TYPES.TRAVEL) {
            return msg.content && (msg.content.includes('机票') || msg.content.includes('航班'))
          }
          if (operationType === OPERATION_TYPES.WEB) {
            return msg.content && (msg.content.includes('网站') || msg.content.includes('浏览'))
          }
          return false
        })

        if (!hasRecentRelatedContext) {
          return {
            clear: true,
            reason: `独立操作完成: ${operationType}`,
            timing: 'after'
          }
        }
      }

      return { clear: false, reason: '保持上下文' }
    }

    // 🔄 【混合智能清空策略】智能工具预测（基于用户输入文本）
    const predictMCPTools = (userText) => {
      const predictions = []

      // 数字员工产品名称检测 - 优先级最高，直接跳过知识库搜索
      const digitalEmployeeProducts = ['晓主任', '晓文宣', '晓招聘', '晓策通', '数字员工市场', '晓律师', '晓客服', '晓助理', '晓秘书']
      const hasDigitalEmployeeProduct = digitalEmployeeProducts.some(product => userText.includes(product))

      if (hasDigitalEmployeeProduct) {
        console.log(`🎯 检测到数字员工产品，跳过知识库搜索: ${userText}`)
        predictions.push('browser_navigate')
        return predictions
      }

      // 知识库问答预测（仅在非数字员工产品时触发）
      if (/知识库|文档|资料|说明|手册|指南|如何|怎么|什么是|介绍|详细|具体/.test(userText)) {
        predictions.push('knowledge_search')
      }

      // 文件操作预测
      if (/搜索.*文件|查找.*文件|找.*文件/.test(userText)) {
        predictions.push('search_files')
      }
      if (/打开.*文件|打开第|打开.*个/.test(userText)) {
        predictions.push('open_file')
      }
      if (/列出|列表|目录/.test(userText)) {
        predictions.push('list_directory')
      }
      if (/读取|查看.*内容/.test(userText)) {
        predictions.push('read_file')
      }

      // 专门功能预测（优先级更高）
      if (/查询.*航班|机票|飞机|航班.*查询|.*到.*机票|.*飞.*/.test(userText)) {
        predictions.push('query_flights')
      }
      if (/天气|气温|下雨|预报/.test(userText)) {
        predictions.push('get_weather_forecast')
      }

      // 一般搜索预测（排除专门功能和知识库）
      if (/搜索|查询|找一下|百度.*搜索/.test(userText) &&
        !/机票|航班|飞机|天气|气温|预报|知识库|文档|资料|说明|手册|指南|如何|怎么|什么是|介绍|详细|具体/.test(userText)) {
        predictions.push('baidu_search')
      }

      // 邮件预测
      if (/查看.*邮件|邮件.*列表/.test(userText)) {
        predictions.push('list_email')
      }
      if (/标记.*已读|已读/.test(userText)) {
        predictions.push('mark_email_as_read')
      }

      // 网页预测（非搜索类）
      if (/打开.*网站|访问.*网站|浏览/.test(userText) &&
        !/搜索|查询/.test(userText)) {
        predictions.push('browser_navigate')
      }

      return predictions
    }

    // 🔄 【优化】改进检测逻辑，使其更准确（保留旧功能兼容）
    const isNewConversationCommand = (text) => {
      const newConversationPatterns = [
        // 打开文件相关
        /打开.*文件|打开.*文档|打开文档|开启.*文件/,
        // 航班查询
        /查询.*航班|查航班|机票/,
        // 天气查询
        /查询.*天气|查天气|天气.*怎么样|天气.*如何|.*天气预报/
      ]

      const willStartNew = newConversationPatterns.some(pattern => pattern.test(text))

      // 🔄 【调试工具】打印检测结果
      if (willStartNew) {
        console.log('🔄 [NEW_CONVERSATION_DEBUG] 指令将重新开启对话:', {
          指令: text,
          匹配的模式: newConversationPatterns.filter(pattern => pattern.test(text)),
          当前对话长度: mcpConversationHistory.value.length
        })
      }

      return willStartNew
    }



    // 🔄 【调试工具】测试重新开启对话检测
    const testNewConversationDetection = () => {
      const testCommands = [
        '打开文件',
        '打开文档',
        '打开百度网站',
        '查询航班信息',
        '今天天气怎么样',
        '查询天气',
        '天气预报',
        '你好',
        '访问网站',
        '查天气'
      ]

      console.log('🧪 [NEW_CONVERSATION_TEST] 测试重新开启对话检测:')
      testCommands.forEach(cmd => {
        const willStartNew = isNewConversationCommand(cmd)
        console.log(`  "${cmd}" -> ${willStartNew ? '✅ 会重新开启' : '❌ 不会重新开启'}`)
      })
    }

    // 🔄 【增强】停止AI处理（思考和回复）- 初始化对话状态
    const stopAIProcessing = async () => {
      const timestamp = new Date().toISOString()
      console.log(`⏹️ [${timestamp}] 用户点击停止按钮，停止所有AI活动并初始化对话`)

      try {
        // 1. 🛑 取消当前正在进行的AI请求
        console.log('🛑 1. 取消当前正在进行的AI请求')
        const requestCancelled = cancelCurrentAIRequest()
        if (requestCancelled) {
          console.log('✅ AI请求已取消')
        } else {
          console.log('ℹ️ 没有正在进行的AI请求需要取消')
        }

        // 2. 🛑 强制停止所有TTS播放
        console.log('🔇 2. 强制停止所有TTS播放')
        if (window.speechSynthesis && window.speechSynthesis.speaking) {
          window.speechSynthesis.cancel()
        }
        isTTSPlaying.value = false

        // 3. 🛑 停止所有对话管理器活动
        console.log('🛑 3. 停止对话管理器的所有活动')
        if (conversationManager.value) {
          // 停止TTS
          if (typeof conversationManager.value.stopTTS === 'function') {
            conversationManager.value.stopTTS()
          }

          // 停止所有ASR实例（安全地检查方法是否存在）
          try {
            if (typeof conversationManager.value.stopAllASRInstances === 'function') {
              await conversationManager.value.stopAllASRInstances()
            } else {
              // 如果没有stopAllASRInstances方法，使用其他停止方法
              if (typeof conversationManager.value.stopCommandListening === 'function') {
                await conversationManager.value.stopCommandListening()
              }
              if (typeof conversationManager.value.stopWakeWordDetection === 'function') {
                await conversationManager.value.stopWakeWordDetection()
              }
            }
          } catch (stopError) {
            console.warn('停止ASR实例时出错:', stopError)
          }

          // 停止指令监听
          try {
            if (typeof conversationManager.value.stopCommandListening === 'function') {
              await conversationManager.value.stopCommandListening()
            }
          } catch (stopError) {
            console.warn('停止指令监听时出错:', stopError)
          }

          // 停止当前状态处理
          conversationManager.value.isProcessingCommand = false
          conversationManager.value.isProcessingWakeWord = false
          conversationManager.value.isCommandListening = false
          conversationManager.value.isWakeWordListening = false

          // 清空识别结果
          if (conversationManager.value.finalResults) {
            conversationManager.value.finalResults = []
          }
          if (conversationManager.value.currentSentence) {
            conversationManager.value.currentSentence = ''
          }

          console.log('✅ 对话管理器已完全停止')
        }

        // 4. 🛑 停止所有ASR监听
        console.log('🛑 4. 停止所有ASR监听')
        // 已通过conversationManager.stopAllASRInstances()处理
        console.log('✅ ASR监听已通过对话管理器停止')

        // 5. 🧠 停止AI思考状态
        console.log('🧠 5. 停止AI思考状态')
        isAIThinking.value = false

        // 6. 💭 移除所有思考中和临时消息
        console.log('💭 6. 清理思考中和临时消息')
        const beforeCount = displayMessageHistory.value.length
        displayMessageHistory.value = displayMessageHistory.value.filter(msg =>
          !msg.isThinking && !msg.isSimpleReply
        )
        const afterCount = displayMessageHistory.value.length
        console.log(`  移除前: ${beforeCount}，移除后: ${afterCount}`)

        // 7. 🗑️ 清空所有对话上下文，完全重置
        console.log('🗑️ 7. 清空所有对话上下文')
        mcpConversationHistory.value = []
        // 清空文本输入框
        textInputValue.value = ''

        // 8. 🔄 重置智能语音状态
        console.log('🔄 8. 重置智能语音状态')
        if (smartVoiceEnabled.value) {
          smartVoiceState.value = 'listening_wake_word'
        } else {
          smartVoiceState.value = 'idle'
        }

        // 🔧 【修复启动时点击问题】重置防重复播放标志
        hasPlayedInitialGreeting = false
        console.log('🔄 重置防重复播放标志')

        // 9. 🎤 重新启动唤醒词监听（如果在语音模式）
        if (smartVoiceEnabled.value && conversationManager.value) {
          console.log('🎤 9. 重新启动唤醒词监听')
          try {
            // 确保对话管理器已启用
            conversationManager.value.isEnabled = true
            // 重新开始监听唤醒词
            if (typeof conversationManager.value.startWakeWordListening === 'function') {
              await conversationManager.value.startWakeWordListening()
            }
            console.log('✅ 唤醒词监听已重新启动')
          } catch (startError) {
            console.warn('重新启动唤醒词监听时出错:', startError)
          }
        }

        // 10. ✅ 添加停止确认消息
        console.log('✅ 10. 添加停止确认消息')
        const stopMessage = {
          role: 'assistant',
          content: '好的，我已经停止所有任务。有什么新的问题吗？',
          timestamp: Date.now(),
          isSimpleReply: true
        }
        displayMessageHistory.value.push(stopMessage)

        // 11. 📜 确保滚动到最新消息
        if (showHistoryModal.value) {
          nextTick(() => {
            scrollToLatestMessage()
          })
        }

        console.log('✅ 所有AI活动已完全停止，对话状态已初始化')

      } catch (error) {
        console.error('❌ 停止AI处理时出错:', error)
      }
    }

    // 将AI回复显示为简单气泡
    const addAIBubble = (text) => {
      addBubble(text)
    }

    // 添加状态消息显示
    const displayStatus = (message, type = 'info', duration = 3000) => {
      if (!message) return

      // 添加一个气泡提示
      addBubble(message)
    }

    // 启动智能语音

    // 开始拖拽调整input-area高度
    const startInputAreaResize = (event) => {
      if (event.target.classList.contains('input-area-resize-handle')) {
        event.preventDefault()
        isInputAreaResizing.value = true
        resizeStartY.value = event.clientY
        resizeStartHeight.value = inputAreaHeight.value

        // 添加全局鼠标事件监听
        window.addEventListener('mousemove', handleInputAreaResize)
        window.addEventListener('mouseup', stopInputAreaResize)

        console.log('🖱️ 开始调整输入区域高度')
      }
    }

    // 拖拽调整过程中
    const handleInputAreaResize = (event) => {
      if (!isInputAreaResizing.value) return

      const deltaY = resizeStartY.value - event.clientY
      let newHeight = resizeStartHeight.value + deltaY

      // 限制最小和最大高度
      newHeight = Math.max(90, Math.min(200, newHeight))
      inputAreaHeight.value = newHeight

      // 同步调整textarea高度
      if (textareaRef.value) {
        textareaRef.value.style.height = `${newHeight - 30}px`
      }
    }

    // 停止拖拽调整
    const stopInputAreaResize = () => {
      isInputAreaResizing.value = false

      // 移除全局事件监听
      window.removeEventListener('mousemove', handleInputAreaResize)
      window.removeEventListener('mouseup', stopInputAreaResize)

      console.log('🖱️ 输入区域高度调整完成:', inputAreaHeight.value)
    }

    return {
      // 状态
      showTextInput,
      showStatus,
      showChatArea,
      characterState,
      isDragging,
      smartVoiceEnabled,
      smartVoiceState,
      volumeLevel,
      showVolumeIndicator,
      voiceResultText,
      recordingStatus,
      recordingError,
      isLoggedIn,
      // 新增状态
      currentInputMode,
      textInputValue,
      textareaRef,
      chatMessagesRef,
      webSearchEnabled,
      inputAreaHeight,
      inputAreaRef,
      startInputAreaResize,
      // 窗口高度动态调整相关
      currentWindowHeight,
      isWindowResizing,
      adjustWindowHeight,
      FULL_HEIGHT,
      COLLAPSED_HEIGHT,
      // 🔄 【新增】TTS相关状态和函数
      isTTSPlaying,
      isAIThinking,
      getStatusDisplayText,
      handleVoiceWaveClick,
      // 邮件消息编辑相关方法
      startMessageEdit,
      saveMessageEdit,
      cancelMessageEdit,
      addEditingRecipient,
      removeEditingRecipient,
      addEditingCc,
      removeEditingCc,
      updateEditingField,
      // 邮件相关状态
      showEmailModal,
      emailModalData,
      pendingEmailCallback,
      // 邮件预览相关状态
      showEmailPreview,
      emailPreviewData,
      // 🔄 【重要修改】返回两个历史记录变量
      mcpConversationHistory,
      displayMessageHistory,
      shouldResetContext,
      lastMCPTool,
      // 历史消息
      recentMessages,
      // 图片资源
      characterImage,
      characterName,
      voiceOnImage,
      voiceImage,
      keywordsOnImage,
      keywordsImage,
      historyImage,
      historyOnImage,
      menuImgBgImage,
      menuImgXlsImage,
      menuImgXzrImage,
      menuImgRzygImage,
      menuImgEditImage,
      menuImgCopyImage,
      menuImgVoiceOnImage,
      menuImgVoiceOffImage,
      menuImgSendImage,
      menuImgStopImage,
      menuImgVoiceBgImage,
      logoImg,
      // 状态指示器
      statusClass,
      recordingStatusClass,
      recordingStatusIcon,
      recordingStatusText,
      // 多气泡系统（已删除）
      // bubbles,
      // bubbleContainerRef,
      // visibleBubbles,
      // addBubble,
      // removeBubble,
      // clearAllBubbles,
      // scrollToLatestBubble,
      // 历史消息系统（部分已删除，现在聊天记录固定显示）
      // showHistoryModal,
      // historyListRef,
      // toggleHistoryModal,
      // closeHistoryModal,
      formatMessageTime,
      scrollToLatestMessage,
      // addTestMessage,
      // 🔄 【新增】简单回复判断函数
      isSimpleReply,
      // 方法
      toggleMenu,
      handleMenuClick,
      toggleTextInput,
      closeTextInput,
      handleTextResult,
      testChatAPI,
      openMainWindow,

      clearMCPHistory,
      openFile,
      deduplicateFileReferences,
      shouldShowFileReferences,
      shouldShowWebReferences,
      openWebReference,
      showHistoryModalFunc,
      toggleSmartVoice,
      // 聊天区域控制方法
      toggleChatArea,
      closeChatArea,
      newConversation,
      // 角色图片事件处理方法
      handleCharacterClick,
      handleCharacterMouseDown,
      handleDoubleClick,
      performDrag,
      cleanupGlobalListeners,
      // 新增菜单点击方法
      openXiaoZhuRen,
      openXiaoLawyer,
      openAIStore,
      // 新增方法
      toggleInputMode,
      editUserMessage,
      copyMessage,
      sendTextMessage,
      handleTextareaKeydown,
      toggleWebSearch,
      scrollChatToBottom,
      // 邮件相关方法
      showEmailConfirmModal,
      closeEmailModal,
      handleEmailSend,
      handleEmailError,
      // 邮件预览相关方法
      showEmailPreviewModal,
      closeEmailPreview,
      startEmailEdit,
      saveEmailEdit,
      cancelEmailEdit,
      addRecipient,
      removeRecipient,
      addCc,
      removeCc,
      sendEmailFromPreview,
      sendEmailFromTextPreview,
      // 邮件预览消息处理
      formatEmailPreviewText,
      formatMessageContent,
      sendEmailFromMessagePreview,
      editEmailFromMessagePreview,

      // 🔄 【问题1解答】MCP执行前会重新开启对话的工具列表
      isNewConversationCommand,
      testNewConversationDetection,

      // 🔄 【混合智能清空策略】新增函数
      OPERATION_TYPES,
      MCP_TOOL_TYPES,
      getOperationType,
      isOperationChainEnd,
      isIndependentOperation,
      getLastOperationType,
      shouldClearBeforeMCP,
      shouldClearAfterMCP,
      predictMCPTools,
      stopAIProcessing
    }
  }
}
</script>

<style lang="scss" scoped>
.floating-character {
  position: relative;
  width: 400px;
  height: 800px;
  user-select: none; // 🔄 【保留】防止意外选择UI元素
  border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  transition: height 0.3s ease; // 🔄 【新增】高度变化的过渡效果

  // 🔄 【新增】动态高度调整
  &.collapsed {
    height: 290px;
  }

  // 🔄 【需求4】但允许消息内容被选择
  .message-bubble {
    user-select: text !important;
  }
}

.character {
  position: relative;
  width: 210px;
  height: 210px;
  cursor: move;
  /* 设置为移动光标，更直观地表示可拖动 */
  transition: all 0.3s ease;
  z-index: 1000;
  -webkit-user-drag: none;
  /* 防止默认拖拽行为 */

  &:hover {
    transform: scale(1.05);

    .character-menu {
      opacity: 1;
      visibility: visible;
      transform: translateX(-50%) translateY(0);
    }
  }

  .character-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s ease;
    pointer-events: none; // 让鼠标事件穿透到父容器
    position: relative;
    z-index: 99;
  }
}

.character-menu {
  position: absolute;
  top: -50px;
  left: 50%;
  transform: translateX(-50%) translateY(-10px);
  z-index: 1000;
  background: url('/assets/menu/icon-menu-bg.png') no-repeat top center;
  background-size: cover;
  width: 376px;
  height: 211px;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;

  // 🔄 【新增】确保菜单在折叠状态下也能正确显示
  .floating-character.collapsed & {
    // 菜单在折叠状态下需要更多向上偏移，以确保完全可见
    top: -60px;
    transform: translateX(-50%) translateY(-20px);
  }

  .menu-item {
    padding: 8px 12px;
    cursor: pointer;
    transition: all 0.2s ease;
    border-radius: 6px;
    font-size: 16px;
    position: absolute;
    z-index: 1001;

    img {
      display: block;
      margin: 0 auto;
    }

    span {
      font-size: 14px;
      display: block;
      text-align: center;
      color: #999999;
    }

    &.p1 {
      top: 100px;
      left: 0;
    }

    &.p2 {
      top: 0;
      left: 50%;
      transform: translateX(-50%);
    }

    &.p3 {
      top: 100px;
      right: 0;
    }

    &:hover {
      img {
        transform: scale(1.05);
      }

      span {
        color: rgba($color: #ffffff, $alpha: 1);
      }
    }
  }
}

.chat-messages {
  overflow-y: auto;
  padding: 16px;
  flex: 1;
  min-height: 0; // 允许flex子元素收缩

  .no-messages {
    text-align: center;
    color: #6c757d;
    padding: 40px 20px;
    font-size: 14px;

    .tip {
      margin-top: 8px;
      font-size: 12px;
      color: #adb5bd;
    }
  }


}

.chat-message {
  margin-bottom: 16px;
  display: flex;
  align-items: flex-start;
  animation: messageSlideIn 0.3s ease-out;

  &.user-message {
    flex-direction: row-reverse;

    .message-content {
      align-items: flex-end;
    }

    .message-bubble {
      background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
      color: white;
      border-bottom-right-radius: 4px;
    }
  }

  &.assistant-message {
    flex-direction: row;

    .message-content {
      align-items: flex-start;
    }

    .message-bubble {
      background: white;
      color: #333;
      border: 1px solid #e1e5e9;
      border-bottom-left-radius: 4px;
    }
  }

  .message-avatar {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    flex-shrink: 0;
    margin: 0 8px;
    overflow: hidden;
    border: 1px solid #e1e5e9;

    .avatar-img {
      width: 100%;
      height: 100%;
      object-fit: cover;
    }
  }

  .message-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    min-width: 0;
  }

  .message-bubble {
    padding: 12px 16px;
    border-radius: 16px;
    display: inline-block;
    max-width: 95%;
    word-wrap: break-word;
    font-size: 14px;
    line-height: 1.5;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    user-select: text !important; // 🔄 【需求1】用户和AI回复都支持文本选择
    cursor: text; // 🔄 【需求1】显示文本选择光标

    // 确保内部所有元素都可以选择文本
    * {
      user-select: text !important;
    }

    &.thinking-bubble {
      background: #f8f9fa !important;
      border: 1px solid #e9ecef !important;
      min-height: 60px; // 🔄 【修复】确保思考中气泡有足够高度

      .thinking-dots {
        display: flex !important;
        align-items: center;
        justify-content: center;
        height: 40px; // 🔄 【修复】增加高度让动画更明显
        padding: 12px;

        .dot {
          width: 8px; // 🔄 【修复】增大点的尺寸
          height: 8px;
          border-radius: 50%;
          background: #007bff !important; // 🔄 【修复】使用更明显的蓝色
          margin: 0 3px; // 🔄 【修复】增加间距
          animation: thinking 1.4s infinite ease-in-out both;

          &.dot1 {
            animation-delay: -0.32s;
          }

          &.dot2 {
            animation-delay: -0.16s;
          }

          &.dot3 {
            animation-delay: 0s; // 🔄 【修复】确保第三个点有延迟
          }
        }
      }
    }

    &.error-bubble {
      background: #f8d7da;
      border: 1px solid #f5c6cb;
      color: #721c24;

      .error-content {
        display: flex;
        align-items: center;
        gap: 8px;

        .error-icon {
          font-size: 16px;
        }
      }
    }

    // 搜索引用样式
    .search-references {
      margin-top: 12px;
      padding-top: 8px;
      border-top: 1px solid rgba(0, 0, 0, 0.1);
    }

    .file-references,
    .web-references {
      margin-bottom: 8px;

      &:last-child {
        margin-bottom: 0;
      }

      .file-references-label,
      .web-references-label {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.6);
        margin-bottom: 6px;
        font-weight: 500;
      }

      .file-reference-link,
      .web-reference-link {
        display: inline-block;
        font-size: 13px;
        text-decoration: underline;
        cursor: pointer;
        margin-right: 8px;
        margin-bottom: 4px;
        transition: all 0.2s ease;
        padding: 2px 4px;
        border-radius: 4px;
      }

      .file-reference-link {
        color: #007bff;

        &:hover {
          background: rgba(0, 123, 255, 0.1);
          color: #0056b3;
          text-decoration: none;
        }
      }

      .web-reference-link {
        color: #28a745;

        &:hover {
          background: rgba(40, 167, 69, 0.1);
          color: #1e7e34;
          text-decoration: none;
        }
      }
    }
  }

  .message-actions {
    display: flex;
    gap: 4px;
    margin-top: 4px;
    opacity: 0;
    transition: opacity 0.2s ease;

    .action-btn {
      background: none;
      border: none;
      padding: 4px 8px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 12px;
      color: #6c757d;
      transition: all 0.2s ease;

      &:hover {
        background: #f8f9fa;
        color: #495057;
        transform: translateY(-1px);
      }

      span {
        font-size: 14px;
      }
    }
  }

  &:hover .message-actions {
    opacity: 1;
  }
}

.voice-input-area {
  height: 50px; // 🔄 【调整】从原来的60px减小为50px
  // position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-top: 10px; // 从原来的15px减小到10px
  transition: height 0.3s ease; // 添加高度过渡效果

  // 添加语音波形组件样式
  .voice-wave-component {
    width: 100%;
    transition: transform 0.3s ease, width 0.3s ease;

    &.smaller-wave {
      transform: scale(0.75); // 缩小比例
      width: 75%; // 减小宽度
    }
  }

  // 🔄 【保留】原有的语音状态样式，用于其他语音相关UI
  .voice-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e5e9;

    &.status-listening {
      border-color: #f39c12;
      background: #fff3cd;
    }

    &.status-woke {
      border-color: #e67e22;
      background: #ffeaa7;
    }

    &.status-recording {
      border-color: #e74c3c;
      background: #ffebee;
    }

    &.status-processing {
      border-color: #9b59b6;
      background: #f3e5f5;
    }

    &.status-speaking {
      border-color: #3498db;
      background: #e3f2fd;
    }

    .status-icon {
      font-size: 16px;
      animation: statusPulse 1s infinite;
    }

    .status-text {
      flex: 1;
      font-size: 14px;
      color: #495057;
    }
  }

  /* 语音模式下的停止按钮样式 */
  .voice-stop-btn {
    border: none;
    cursor: pointer;
    position: absolute;
    border-radius: 12px;
    /* 圆角边框 */
    width: 33px;
    height: 33px;
    bottom: 12px;
    right: 20px;
    /* 调整右边距，避免过于靠边 */
    z-index: 99;
    font-size: 16px;
    transition: all 0.2s ease;

    img {
      width: 100%;
      height: 100%;
    }

    &:hover {
      transform: scale(1.05);
    }

    &:active {
      transform: scale(0.95);
      background: #ffe8e8;
    }
  }

  .voice-status {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: white;
    border-radius: 8px;
    border: 1px solid #e1e5e9;

    &.status-listening {
      border-color: #f39c12;
      background: #fff3cd;
    }

    &.status-woke {
      border-color: #e67e22;
      background: #ffeaa7;
    }

    &.status-recording {
      border-color: #e74c3c;
      background: #ffebee;
    }

    &.status-processing {
      border-color: #9b59b6;
      background: #f3e5f5;
    }

    &.status-speaking {
      border-color: #3498db;
      background: #e3f2fd;
    }

    .status-icon {
      font-size: 16px;
      animation: statusPulse 1s infinite;
    }

    .status-text {
      flex: 1;
      font-size: 14px;
      color: #495057;
    }
  }

  .voice-toggle-btn {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    border: none;
    border-radius: 8px;
    padding: 12px 24px;
    color: white;
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(40, 167, 69, 0.2);

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
    }

    &.active {
      background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
      box-shadow: 0 2px 8px rgba(220, 53, 69, 0.2);

      &:hover {
        box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
      }
    }
  }
}

.web-search-area {
  margin-top: 8px;
  display: flex;
  position: absolute;
  bottom: 10px;
  left: 10px;

  .web-search-toggle {
    background: rgba(255, 255, 255, 0.9);
    border: 2px solid #e1e5e9;
    border-radius: 50%;
    width: 40px;
    height: 40px;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    backdrop-filter: blur(10px);
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

    &::before {
      content: '';
      position: absolute;
      top: -2px;
      left: -2px;
      right: -2px;
      bottom: -2px;
      border-radius: 50%;
      background: linear-gradient(135deg, #667eea, #764ba2);
      opacity: 0;
      transition: opacity 0.3s ease;
      z-index: -1;
    }

    &:hover:not(:disabled) {
      border-color: #667eea;
      transform: translateY(-2px) scale(1.05);
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.3);
    }

    &.active {
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      border-color: #667eea;
      color: white;
      box-shadow: 0 4px 16px rgba(102, 126, 234, 0.4);
      transform: scale(1.1);

      &::before {
        opacity: 1;
      }

      &:hover {
        transform: translateY(-2px) scale(1.15);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.5);
      }

      .search-icon {
        animation: searchPulse 2s infinite;
      }
    }

    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      transform: none !important;
    }

    .search-icon {
      font-size: 18px;
      line-height: 1;
      filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
      transition: all 0.3s ease;
    }
  }
}

@keyframes searchPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.1);
  }
}

// 添加新的动画
@keyframes messageSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes statusPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }
}

// 滚动条样式
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;

  &:hover {
    background: #a8a8a8;
  }
}

@keyframes statusPulse {

  0%,
  100% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }
}

// 邮件预览相关样式保持不变，但优化结构
.email-preview-content {
  .email-preview-form {
    .form-group {
      margin-bottom: 12px;

      label {
        display: block;
        font-size: 13px;
        font-weight: 500;
        color: #495057;
        margin-bottom: 4px;
      }

      .preview-field {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 8px 12px;
        font-size: 13px;
        color: #495057;
        border: 1px solid #e9ecef;

        span {
          display: inline-block;
          background: #e3f2fd;
          border-radius: 4px;
          padding: 2px 8px;
          margin: 2px 4px 2px 0;
          font-size: 12px;
          color: #1976d2;
        }

        &.email-body {
          white-space: pre-wrap;
          line-height: 1.4;
        }
      }
    }
  }

  .email-preview-actions {
    display: flex;
    justify-content: center;
    gap: 12px;
    margin-top: 16px;
    padding-top: 12px;
    border-top: 1px solid #e9ecef;

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 13px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;

      &.edit-btn {
        background: #ffc107;
        color: white;

        &:hover {
          background: #e0a800;
        }
      }

      &.send-btn {
        background: #28a745;
        color: white;

        &:hover {
          background: #218838;
        }
      }

      &.save-btn {
        background: #007bff;
        color: white;

        &:hover {
          background: #0056b3;
        }
      }

      &.cancel-btn {
        background: #6c757d;
        color: white;

        &:hover {
          background: #5a6268;
        }
      }
    }
  }
}

// 历史消息弹框 - 简洁风格，定位在气泡上方
.history-modal {
  position: fixed;
  right: 80px; // 与人物位置对齐
  bottom: 230px;
  /* 确保底部在气泡最上方之上，留出适当间距 */
  z-index: 1500;
  animation: fadeIn 0.3s ease;

  .history-content {
    background: white;
    border: 2px solid #000;
    border-radius: 8px;
    padding: 0;
    width: 380px;
    height: 100%;
    /* 使用全部可用高度 */
    max-height: 370px;
    /* 设置最大高度 */
    overflow: hidden;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
    display: flex;
    flex-direction: column;
    animation: historySlideIn 0.3s ease;
  }

  // 邮件预览模式样式
  .email-preview-mode {
    display: flex;
    flex-direction: column;
    height: 100%;
    background: white;

    .email-preview-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 16px;
      border-bottom: 1px solid #eee;
      background: #f8f9fa;
      border-radius: 6px 6px 0 0;

      h3 {
        margin: 0;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }

      .close-preview-btn {
        background: none;
        border: none;
        font-size: 18px;
        color: #666;
        cursor: pointer;
        padding: 4px 8px;
        border-radius: 4px;
        transition: all 0.2s ease;

        &:hover {
          background: #e9ecef;
          color: #333;
        }
      }
    }

    .email-preview-content {
      flex: 1;
      overflow-y: auto;
      padding: 16px;
      display: flex;
      flex-direction: column;
      gap: 16px;

      .email-preview-form {
        .form-group {
          margin-bottom: 16px;

          label {
            display: block;
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 6px;
          }

          .preview-field {
            background: #f8f9fa;
            border-radius: 4px;
            padding: 8px 12px;
            font-size: 14px;
            color: #333;
            margin-bottom: 2px;
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
            min-height: 36px;
            border: 1px solid transparent;

            &.editable {
              background: #fff;
              border-color: #ddd;
              cursor: text;
            }

            span {
              background: #e8f4fd;
              border-radius: 3px;
              padding: 2px 8px;
              margin-right: 4px;
              margin-bottom: 2px;
              color: #1976d2;
              font-size: 13px;
            }
          }

          .email-list {
            display: flex;
            flex-direction: column;
            gap: 8px;

            .email-item {
              display: flex;
              gap: 8px;
              align-items: center;

              .email-input {
                flex: 1;
                padding: 8px 12px;
                border: 1px solid #ddd;
                border-radius: 4px;
                font-size: 14px;
                transition: border-color 0.2s;

                &:focus {
                  outline: none;
                  border-color: #007AFF;
                }
              }

              .remove-btn {
                background: #ff4757;
                color: white;
                border: none;
                border-radius: 4px;
                width: 24px;
                height: 24px;
                cursor: pointer;
                font-size: 12px;
                display: flex;
                align-items: center;
                justify-content: center;
                transition: background 0.2s;

                &:hover {
                  background: #ff3742;
                }
              }
            }

            .add-btn {
              background: #007AFF;
              color: white;
              border: none;
              border-radius: 4px;
              padding: 8px 12px;
              font-size: 14px;
              cursor: pointer;
              transition: background 0.2s;

              &:hover {
                background: #0056b3;
              }
            }
          }

          .subject-input {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;
            transition: border-color 0.2s;

            &:focus {
              outline: none;
              border-color: #007AFF;
            }
          }

          .message-textarea {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 14px;

            resize: vertical;
            min-height: 120px;
            transition: border-color 0.2s;

            &:focus {
              outline: none;
              border-color: #007AFF;
            }
          }
        }
      }

      .email-preview-actions {
        display: flex;
        gap: 12px;
        justify-content: center;
        padding-top: 16px;
        border-top: 1px solid #eee;

        button {
          padding: 10px 20px;
          border: none;
          border-radius: 6px;
          font-size: 14px;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s;
          min-width: 80px;

          &.edit-btn {
            background: #ffa500;
            color: white;

            &:hover {
              background: #ff8c00;
            }
          }

          &.send-btn {
            background: #28a745;
            color: white;

            &:hover {
              background: #218838;
            }
          }

          &.save-btn {
            background: #007AFF;
            color: white;

            &:hover {
              background: #0056b3;
            }
          }

          &.cancel-btn {
            background: #6c757d;
            color: white;

            &:hover {
              background: #5a6268;
            }
          }
        }
      }
    }
  }

  // 🔄 【新增】历史消息标题栏样式
  .history-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;
    border-bottom: 1px solid #eee;
    background: #f8f9fa;
    border-radius: 6px 6px 0 0;

    .history-title {
      font-size: 14px;
      font-weight: 600;
      color: #333;
    }

    .history-close-btn {
      background: none;
      border: none;
      font-size: 16px;
      color: #666;
      cursor: pointer;
      padding: 4px 8px;
      border-radius: 4px;
      transition: all 0.2s ease;

      &:hover {
        background: #e9ecef;
        color: #333;
      }
    }
  }

  .history-list {
    flex: 1;
    overflow-y: auto;
    padding: 12px;
    height: 100%;
    /* 使用弹框的全部高度 */
    background: white;

    .no-history {
      text-align: center;
      color: #999;
      padding: 40px 20px;
      font-size: 14px;
    }

    .history-item {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-end;
      animation: chatSlideIn 0.3s ease-out;

      &.user-item {
        flex-direction: row-reverse;
        justify-content: flex-start;

        .message-content-wrapper {
          display: flex;
          justify-content: flex-end;
        }

        .message-bubble {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
          color: white;
          border: none;
        }
      }

      &.assistant-item {
        flex-direction: row;
        justify-content: flex-start;

        .message-content-wrapper {
          display: flex;
          justify-content: flex-start;
        }

        .message-bubble {
          background: #e8f4fd;
          color: #333;
          border: 1px solid #b3d9f7;
        }
      }

      .message-avatar {
        width: 28px;
        height: 28px;
        border-radius: 50%;
        flex-shrink: 0;
        margin-right: 8px;
        margin-top: 2px;
        overflow: hidden;
        border: 1px solid #ddd;

        .avatar-img {
          width: 100%;
          height: 100%;
          object-fit: cover;
        }
      }

      .message-content-wrapper {
        flex: 1;
        min-width: 0;
      }

      .message-bubble {
        padding: 8px 12px;
        border-radius: 8px;
        display: inline-block;
        max-width: 85%;
        word-wrap: break-word;
        font-size: 14px;
        line-height: 1.4;
        margin-bottom: 4px;

        // 历史消息中的文件引用样式
        .history-file-references {
          margin-top: 10px;
          padding-top: 8px;
          border-top: 1px solid rgba(0, 0, 0, 0.1);

          .history-file-references-label {
            font-size: 11px;
            color: rgba(0, 0, 0, 0.6);
            margin-bottom: 4px;
            font-weight: 500;
          }

          .history-file-reference-link {
            display: inline-block;
            color: #1976d2; // 蓝色
            font-size: 12px;
            text-decoration: underline;
            cursor: pointer;
            margin-right: 8px;
            margin-bottom: 2px;
            transition: all 0.2s ease;
            padding: 1px 3px;
            border-radius: 3px;

            &:hover {
              background: rgba(25, 118, 210, 0.1);
              color: #0d47a1; // 深蓝色
              text-decoration: none;
              transform: translateX(2px);
            }
          }
        }
      }

      // Assistant消息中的文件引用样式特殊处理
      &.assistant-item .message-bubble .history-file-references {
        border-top-color: rgba(179, 217, 247, 0.5);

        .history-file-references-label {
          color: rgba(0, 0, 0, 0.5);
        }
      }
    }
  }
}

// 思考动画保留
@keyframes thinking {

  0%,
  80%,
  100% {
    transform: scale(0);
  }

  40% {
    transform: scale(1);
  }
}

// 通用组件样式
.btn-primary {
  background: linear-gradient(135deg, #007bff 0%, #0056b3 100%);
  border: none;
  border-radius: 6px;
  padding: 8px 16px;
  color: white;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
  }
}

// 重复的thinking样式已移除，统一使用message-bubble内的样式

.error-message {
  background-color: #ffebee !important;
  border: 1px solid #ffcdd2 !important;
  color: #b71c1c !important;
}

.error-icon {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon span {
  font-size: 16px;
}

// 邮件预览消息特殊样式
.email-preview-message {
  background: linear-gradient(135deg, #e8f4fd 0%, #f0f8ff 100%) !important;
  border: 1px solid #b3d9f7 !important;
  border-radius: 12px !important;
  padding: 16px !important;
  max-width: 95% !important;
}

.email-preview-content {
  .email-preview-text {
    margin-bottom: 16px;
    line-height: 1.6;

    .email-field {
      margin-bottom: 12px;
      display: flex;
      align-items: flex-start;

      .field-label {
        font-weight: 600;
        color: #333;
        min-width: 60px;
        margin-right: 8px;
        flex-shrink: 0;
      }

      .field-value {
        color: #555;
        flex: 1;

        &.email-body {
          background: #f8f9fa;
          padding: 8px;
          border-radius: 6px;
          border-left: 3px solid #007AFF;
          margin-top: 4px;
          white-space: pre-wrap;

        }
      }
    }

    .email-confirm-text {
      margin-top: 12px;
      padding: 8px 12px;
      background: rgba(0, 122, 255, 0.1);
      border-radius: 6px;
      color: #007AFF;
      font-size: 14px;
      text-align: center;
    }
  }

  .email-action-buttons {
    display: flex;
    gap: 12px;
    justify-content: center;
    padding-top: 12px;
    border-top: 1px solid rgba(179, 217, 247, 0.5);

    button {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      min-width: 80px;

      &.send-email-btn {
        background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #218838 0%, #1c9976 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
      }

      &.edit-email-btn {
        background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
        color: white;

        &:hover {
          background: linear-gradient(135deg, #e0a800 0%, #dc6502 100%);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }
      }
    }
  }
}

.email-preview-form .preview-field {
  background: #f8f9fa;
  border-radius: 4px;
  padding: 8px 12px;
  font-size: 14px;
  color: #333;
  margin-bottom: 2px;
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.email-preview-form .preview-field span {
  background: #e8f4fd;
  border-radius: 3px;
  padding: 2px 8px;
  margin-right: 4px;
  margin-bottom: 2px;
  color: #1976d2;
  font-size: 13px;
}

.email-preview-actions.sticky-actions {
  position: sticky;
  bottom: 0;
  background: #fff;
  z-index: 2;
  padding-bottom: 8px;
  border-top: 1px solid #eee;
  margin-top: 8px;
  box-shadow: 0 -2px 8px rgba(0, 0, 0, 0.03);
  display: flex;
  gap: 16px;
  justify-content: center;
}

.email-preview-actions button {
  min-width: 80px;
  font-size: 16px;
  font-weight: bold;
  padding: 10px 24px;
  border-radius: 6px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.04);
}

// 邮件预览样式优化
.email-preview-content {
  background: #ffffff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  margin: 8px 0;
  padding: 16px;
  width: 100%; // 设置合适的宽度

  .email-preview-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid #eee;

    h3 {
      margin: 0;
      font-size: 16px;
      color: #333;
    }
  }

  .email-preview-form {
    .form-group {
      margin-bottom: 12px;

      label {
        display: block;
        font-size: 13px;
        color: #666;
        margin-bottom: 4px;
      }

      .preview-field {
        background: #f8f9fa;
        border-radius: 6px;
        padding: 6px 10px;
        font-size: 13px;
        min-height: 32px;

        &.editable {
          cursor: pointer;

          &:hover {
            background: #f0f2f5;
          }
        }

        span {
          display: inline-block;
          background: #e9ecef;
          border-radius: 4px;
          padding: 2px 6px;
          margin: 2px;
          font-size: 12px;
        }
      }

      .email-input {
        width: 100%;
        padding: 6px 10px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        font-size: 13px;

        &:focus {
          outline: none;
          border-color: #007bff;
          box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.1);
        }
      }

      .subject-input {
        width: 100%;
        padding: 6px 10px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        font-size: 13px;
      }

      .message-textarea {
        width: 100%;
        padding: 8px 10px;
        border: 1px solid #dee2e6;
        border-radius: 6px;
        font-size: 13px;
        resize: vertical;
        min-height: 80px;
      }

      .email-list {
        .email-item {
          display: flex;
          align-items: center;
          margin-bottom: 4px;

          .remove-btn {
            background: none;
            border: none;
            color: #dc3545;
            padding: 2px 6px;
            cursor: pointer;
            font-size: 16px;

            &:hover {
              color: #bd2130;
            }
          }
        }

        .add-btn {
          background: none;
          border: 1px dashed #007bff;
          color: #007bff;
          padding: 4px 8px;
          border-radius: 4px;
          width: 100%;
          margin-top: 4px;
          cursor: pointer;
          font-size: 12px;

          &:hover {
            background: rgba(0, 123, 255, 0.1);
          }
        }
      }
    }
  }

  .email-preview-actions {
    display: flex;
    justify-content: flex-end;
    gap: 8px;
    margin-top: 12px;
    padding-top: 12px;
    border-top: 1px solid #eee;

    button {
      padding: 6px 12px;
      border-radius: 4px;
      border: none;
      font-size: 13px;
      cursor: pointer;
      transition: all 0.2s;

      &.edit-btn {
        background: #6c757d;
        color: white;

        &:hover {
          background: #5a6268;
        }
      }

      &.send-btn {
        background: #007bff;
        color: white;

        &:hover {
          background: #0056b3;
        }
      }

      &.save-btn {
        background: #28a745;
        color: white;

        &:hover {
          background: #218838;
        }
      }

      &.cancel-btn {
        background: #dc3545;
        color: white;

        &:hover {
          background: #c82333;
        }
      }
    }
  }
}

// 消息气泡中的邮件预览
.message-bubble {
  &.email-preview-message {
    background: #f8f9fa;
    border: 1px solid #e9ecef;

    .email-preview-text {
      font-size: 13px;

      .email-field {
        margin-bottom: 8px;

        .field-label {
          color: #666;
          font-weight: 500;
        }

        .field-value {
          color: #333;
          margin-left: 4px;

          &.email-body {
            margin-top: 4px;
            white-space: pre-line;
            padding: 8px;
            background: #fff;
            border-radius: 4px;
          }
        }
      }

      .email-confirm-text {
        margin-top: 12px;
        padding-top: 8px;
        border-top: 1px solid #dee2e6;
        color: #666;
        font-size: 12px;
        text-align: center;
      }
    }
  }
}

.chat-area {
  width: 100%;
  height: 600px; // 默认高度
  background: radial-gradient(circle at center, #EEF8FE 0%, #D7ECFE 100%);
  border-radius: 12px;
  padding: 0;
  display: flex;
  flex-direction: column;
  z-index: 100;
  overflow: hidden;
  transition: all 0.3s ease;
  position: relative;

  &.chat-area-hidden {
    height: 0;
    min-height: 0;
    background: transparent;
    padding: 0;
    margin: 0;
    border: none;
    border-radius: 0;
    opacity: 0;
    visibility: hidden;
    pointer-events: none;

    // 🔄 【新增】完全隐藏所有子元素
    >* {
      opacity: 0;
      visibility: hidden;
      pointer-events: none;
      height: 0;
      min-height: 0;
    }
  }
}

// 🔄 【需求3】聊天标题栏样式
.chat-header {
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 12px 16px;

  .chat-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: #333;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
  }

  .chat-new-btn {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    font-weight: bold;
    color: #666;
    transition: all 0.2s ease;
    line-height: 1;
    margin-right: 8px;

    &:hover {
      background: #28a745;
      border-color: #28a745;
      color: white;
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .chat-close-btn {
    background: rgba(255, 255, 255, 0.8);
    border: 1px solid rgba(102, 126, 234, 0.3);
    border-radius: 50%;
    width: 28px;
    height: 28px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 18px;
    font-weight: bold;
    color: #666;
    transition: all 0.2s ease;
    line-height: 1;

    &:hover {
      background: #ff4757;
      border-color: #ff4757;
      color: white;
      transform: scale(1.1);
      box-shadow: 0 2px 8px rgba(255, 71, 87, 0.3);
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

.chat-bubble-arrow {
  position: absolute;
  bottom: 185px;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 20px;
  z-index: 99;
  transition: all 0.3s ease;

  // 使用clip-path创建圆润的气泡箭头
  background: radial-gradient(circle at center, #EEF8FE 0%, #D7ECFE 100%);
  clip-path: polygon(50% 100%, 0% 0%, 100% 0%);
  border-radius: 10px 10px 0 0;

  // 添加柔和阴影
  filter: drop-shadow(0 2px 6px rgba(0, 0, 0, 0.08));

  // 使用伪元素增强圆润效果
  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: 50%;
    transform: translateX(-50%);
    width: 20px;
    height: 12px;
    background: radial-gradient(ellipse at center, #EEF8FE 0%, #D7ECFE 100%);
    border-radius: 50%;
    z-index: -1;
  }

  // 添加底部圆润过渡
  &::after {
    content: '';
    position: absolute;
    bottom: -3px;
    left: 50%;
    transform: translateX(-50%);
    width: 8px;
    height: 8px;
    background: #D7ECFE;
    border-radius: 50%;
    z-index: 1;
  }
}

.input-area {
  position: relative;
  background: #FFFFFF;
  box-shadow: 0px 2px 12px 2px #CCCCCC;
  border-radius: 12px;
  padding: 16px;
  flex-shrink: 0; // 防止输入区域被压缩
  margin: 0 16px;
  min-height: 60px; // 设置最小高度
  max-height: 200px; // 设置最大高度
  overflow: visible; // 允许resize句柄溢出

  .input-area-resize-handle {
    position: absolute;
    top: -5px;
    left: 0;
    right: 0;
    height: 10px;
    cursor: ns-resize;
    z-index: 99;

    &:hover::before {
      content: '';
      position: absolute;
      top: 4px;
      left: 50%;
      transform: translateX(-50%);
      width: 30px;
      height: 3px;
      background: rgba(0, 123, 255, 0.5);
      border-radius: 3px;
    }
  }
}

.input-mode-switch {
  position: absolute;
  top: 14px;
  left: 16px;
  z-index: 99;

  .mode-btn {
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.2s;

    &.active {
      color: #007bff;
    }
  }

  .mode-text {
    font-size: 14px;
    color: #666;
  }
}

.voice-status {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: #e9ecef;
  border-radius: 4px;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);

  .status-icon {
    font-size: 16px;
    color: #666;
  }

  .status-text {
    font-size: 14px;
    color: #666;
  }
}

.voice-toggle-btn {
  background: none;
  border: none;
  color: #666;
  font-size: 16px;
  cursor: pointer;
  transition: color 0.2s;

  &.active {
    color: #007bff;
  }
}

.text-input-area {
  width: 100%;
  height: 100%;
  padding-left: 12%;
  padding-right: 12%;
}

.text-input-wrapper {
  width: 100%;
  height: 100%;
  border-radius: 12px;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
  margin-bottom: 5px;
  transition: all 0.3s ease;
  display: flex;
  flex-direction: column;

  .text-input {
    border: none !important;
    outline: none;
    font-size: 14px;
    color: #333;
    width: 100%;
    height: 100%;
    outline: 0;
    zoom: none;
    resize: none; // 禁用拖拽
    min-height: 30px;
    box-sizing: border-box;
    padding: 10px;
    background: none !important;
  }

  .send-btn {
    border: none;
    cursor: pointer;
    position: absolute;
    background: none;
    right: 12px;
    bottom: 10px;
    z-index: 99;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.1);
    }

    &:active {
      transform: scale(0.95);
    }
  }

  .stop-btn {
    border: none;
    cursor: pointer;
    position: absolute;
    border-radius: 50%;
    background: #fff;
    /* 圆角边框 */
    right: 12px;
    /* 在输入框右下方 */
    width: 33px;
    height: 33px;
    bottom: 12px;
    z-index: 99;
    font-size: 16px;
    transition: all 0.2s ease;

    img {
      width: 100%;
      height: 100%;
    }

    &:active {
      transform: scale(0.95);
    }
  }


}

.web-search-area {
  border-radius: 4px;

  .web-search-btn {
    border: 1px solid #e1e5e9;
    padding: 0;
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
    transition: color 0.2s;

    &:hover {
      color: #007bff;
    }
  }

  .search-text {
    font-size: 14px;
    color: #666;
  }
}

.flex-box-end {
  display: flex;
  justify-content: flex-end;
}

.flex-box-center {
  display: flex;
  justify-content: center;
  align-items: flex-end;
  z-index: 1000;
  flex: 1;
  padding-bottom: 20px; // 底部边距
  min-height: 210px; // 🔄 【新增】确保最小高度容纳角色

  // 🔄 【新增】折叠状态下的布局调整
  .floating-character.collapsed & {
    padding-bottom: 10px; // 折叠状态下减少底部边距
    min-height: 210px; // 确保折叠状态下也有足够空间
  }
}

.status-display {
  margin: 5px 0;
  text-align: center;

  // 🔄 【新增】折叠状态下的状态显示调整
  .floating-character.collapsed & {
    position: absolute;
    bottom: 5px;
    left: 50%;
    transform: translateX(-50%);
    width: 100%;
    margin: 0;
    z-index: 999;
  }

  span {
    font-size: 12px;
    font-weight: bold;
    background: linear-gradient(90deg, #46AEF7 0%, #1DD5E6 100%);
    background-clip: text;
    -webkit-background-clip: text;
    color: transparent;
    -webkit-text-fill-color: transparent;
  }
}
</style>
/**
 * 阿里云ASR完整语音识别器
 * 整合WebSocket连接器和音频录制器
 * 基于阿里云智能语音交互实时语音识别API
 * 
 * 🎯 已优化配置用于检测较大声音并过滤环境杂音
 */

import { AliyunWebSocketSpeechRecognizer, ALIYUN_ASR_STATES } from './AliyunWebSocketSpeechRecognizer.js'
import AliyunWebAudioRecorder from './AliyunWebAudioRecorder.js'
import { ALIYUN_ASR_CONFIG, validateAliyunConfig } from './aliyun-asr-config.js'

// 生成唯一ID
const generateId = () => {
  return Date.now().toString(36) + Math.random().toString(36).substr(2, 9)
}

// 主要的阿里云WebAudio语音识别器类
export class AliyunWebAudioSpeechRecognizer {
  constructor(params = {}, isLog = false) {
    // 验证配置
    const configValidation = validateAliyunConfig()
    if (!configValidation.valid) {
      console.error('❌ 阿里云ASR配置验证失败:', configValidation.errors)
      throw new Error('阿里云ASR配置验证失败')
    }
    
    // 阿里云ASR配置参数
    this.params = {
      ...ALIYUN_ASR_CONFIG,
      ...ALIYUN_ASR_CONFIG.audioConfig,
      ...ALIYUN_ASR_CONFIG.recognitionConfig,
      ...params
    }
    
    console.log('阿里云ASR完整识别器配置 - 强化杂音过滤:', {
      accessKeyId: this.params.accessKeyId ? this.params.accessKeyId.substring(0, 10) + '...' : '未设置',
      appkey: this.params.appkey,
      websocketUrl: this.params.websocketUrl,
      region: this.params.region,
      sampleRate: this.params.sampleRate,
      format: this.params.format,
      channels: this.params.channels,
      bitsPerSample: this.params.bitsPerSample,
      enablePunctuationPrediction: this.params.enablePunctuationPrediction,
      enableIntermediateResult: this.params.enableIntermediateResult,
      enableInverseTextNormalization: this.params.enableInverseTextNormalization,
      enableDisfluency: this.params.enableDisfluency,
      enableVoiceDetection: this.params.enableVoiceDetection,
      maxEndSilence: this.params.maxEndSilence,
      maxSingleSegmentTime: this.params.maxSingleSegmentTime,
      hotwordId: this.params.hotwordId,
      noiseThreshold: this.params.noiseThreshold,
      note: '已优化为检测较大声音，过滤环境杂音'
    })
    
    // 实例管理
    this.requestId = generateId()
    this.isLog = isLog
    this.recognizer = null
    this.recorder = null
    this.audioData = []
    this.isCanSendData = false
    this.isNormalEndStop = false
    
    // 状态管理
    this.currentState = ALIYUN_ASR_STATES.IDLE
    this.isRecording = false
    this.isConnected = false
    this.lastError = null
    
    // 音频处理配置
    this.audioChunkSize = 1600 // 每次发送的音频数据大小（字节）
    this.audioSendInterval = 20 // 🚀 优化：减少发送间隔到20ms，保持连接活跃
    this.audioSendTimer = null
    this.audioQueue = []
    this.lastAudioSendTime = 0 // 记录最后发送音频数据的时间
    
    // 性能监控
    this.performanceStats = {
      startTime: 0,
      endTime: 0,
      totalAudioSent: 0,
      totalResultsReceived: 0,
      averageLatency: 0,
      errorCount: 0,
      reconnectCount: 0
    }
    
    // 回调函数
    this.OnRecognitionStart = null
    this.OnSentenceBegin = null
    this.OnRecognitionResultChange = null
    this.OnSentenceEnd = null
    this.OnRecognitionComplete = null
    this.OnError = null
    this.OnRecorderStop = null
    this.OnStateChange = null
    this.OnVolumeChange = null
    
    this.isLog && console.log('✅ 阿里云ASR完整识别器已创建:', {
      requestId: this.requestId,
      params: this.params
    })
  }
  
  // 更新状态
  updateState(newState) {
    const oldState = this.currentState
    this.currentState = newState
    
    this.isLog && console.log(`🔄 阿里云ASR状态变更: ${oldState} -> ${newState}`)
    
    if (this.OnStateChange) {
      this.OnStateChange(newState, oldState)
    }
  }
  
  // 开始识别
  async start() {
    try {
      this.isLog && console.log('🚀 开始阿里云语音识别完整流程...')
      this.performanceStats.startTime = Date.now()
      
      // 检查环境
      this.checkEnvironment()
      
      // 更新状态
      this.updateState(ALIYUN_ASR_STATES.CONNECTING)
      
      // 创建WebSocket识别器
      this.recognizer = new AliyunWebSocketSpeechRecognizer(this.params, this.isLog)
      
      // 设置识别器回调
      this.setupRecognizerCallbacks()
      
      // 启动识别器
      await this.recognizer.start()
      
      // 创建音频录制器
      this.recorder = new AliyunWebAudioRecorder(this.requestId, this.params, this.isLog)
      
      // 设置录制器回调
      this.setupRecorderCallbacks()
      
      // 启动录制器
      console.log('🎤 [STARTUP] 启动音频录制器...')
      await this.recorder.start()
      console.log('✅ [STARTUP] 音频录制器启动成功')
      
      // 开始音频发送定时器
      console.log('⏰ [STARTUP] 启动音频发送定时器...')
      this.startAudioSendTimer()
      console.log('✅ [STARTUP] 音频发送定时器启动成功')
      
      // 设置状态
      this.isCanSendData = true
      this.isRecording = true
      this.isConnected = true
      this.updateState(ALIYUN_ASR_STATES.LISTENING)
      
      // 性能统计
      this.performanceStats.totalAudioSent = 0
      this.performanceStats.totalResultsReceived = 0
      this.performanceStats.errorCount = 0
      
      if (this.OnRecognitionStart && this.recognizer) {
        this.OnRecognitionStart({
          requestId: this.requestId,
          taskId: this.recognizer.taskId || null
        })
      }
      
      this.isLog && console.log('✅ 阿里云语音识别完整流程已启动')
      
    } catch (error) {
      console.error('❌ 启动阿里云语音识别失败:', error)
      this.lastError = error
      this.updateState(ALIYUN_ASR_STATES.ERROR)
      this.performanceStats.errorCount++
      
      if (this.OnError) {
        this.OnError(error)
      }
      
      throw error
    }
  }
  
  // 检查环境
  checkEnvironment() {
    // 检查WebSocket支持
    if (!window.WebSocket) {
      throw new Error('浏览器不支持WebSocket')
    }
    
    // 检查音频API支持
    if (!window.AudioContext && !window.webkitAudioContext) {
      throw new Error('浏览器不支持Web Audio API')
    }
    
    // 检查麦克风权限
    if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
      throw new Error('浏览器不支持麦克风访问')
    }
    
    // 检查必要的配置
    if (!this.params.accessKeyId || !this.params.accessKeySecret || !this.params.appkey) {
      throw new Error('阿里云ASR配置不完整')
    }
    
    this.isLog && console.log('✅ 环境检查通过')
  }
  
  // 设置识别器回调
  setupRecognizerCallbacks() {
    this.recognizer.OnRecognitionStart = (res) => {
      this.isLog && console.log('🎙️ 识别开始:', res)
      if (this.OnRecognitionStart) {
        this.OnRecognitionStart(res)
      }
    }
    
    this.recognizer.OnSentenceBegin = (res) => {
      this.isLog && console.log('📝 句子开始:', res)
      if (this.OnSentenceBegin) {
        this.OnSentenceBegin(res)
      }
    }
    
    this.recognizer.OnRecognitionResultChange = (res) => {
      this.isLog && console.log('🔄 识别结果变化:', res)
      this.performanceStats.totalResultsReceived++
      
      if (this.OnRecognitionResultChange) {
        this.OnRecognitionResultChange(res)
      }
    }
    
    this.recognizer.OnSentenceEnd = (res) => {
      this.isLog && console.log('📝 句子结束:', res)
      if (this.OnSentenceEnd) {
        this.OnSentenceEnd(res)
      }
    }
    
    this.recognizer.OnRecognitionComplete = (res) => {
      this.isLog && console.log('🏁 识别完成:', res)
      this.performanceStats.endTime = Date.now()
      
      if (this.OnRecognitionComplete) {
        this.OnRecognitionComplete(res)
      }
      
      this.isCanSendData = false
      this.isNormalEndStop = true
    }
    
    this.recognizer.OnError = (error) => {
      console.error('❌ 识别器错误:', error)
      this.lastError = error
      this.performanceStats.errorCount++
      
      if (this.OnError) {
        this.OnError(error)
      }
    }
    
    this.recognizer.OnStateChange = (newState, oldState) => {
      this.isLog && console.log(`🔄 识别器状态变更: ${oldState} -> ${newState}`)
      this.updateState(newState)
    }
    
    this.recognizer.OnConnectionStatusChange = (status) => {
      this.isLog && console.log('🔗 连接状态变更:', status)
      this.isConnected = (status === 'connected')
      
      if (status === 'disconnected' && this.isRecording) {
        this.performanceStats.reconnectCount++
      }
    }
  }
  
  // 设置录制器回调
  setupRecorderCallbacks() {
    this.recorder.OnReceivedData = (audioData) => {
      this.handleAudioData(audioData)
    }
    
    this.recorder.OnError = (error) => {
      console.error('❌ 录制器错误:', error)
      this.lastError = error
      this.performanceStats.errorCount++
      
      if (this.OnError) {
        this.OnError(error)
      }
    }
    
    this.recorder.OnStop = () => {
      this.isLog && console.log('🛑 录制器已停止')
      this.isRecording = false
      
      if (this.OnRecorderStop) {
        this.OnRecorderStop()
      }
    }
    
    this.recorder.OnVolumeChange = (volume) => {
      if (this.OnVolumeChange) {
        this.OnVolumeChange(volume)
      }
    }
  }
  
  // 处理音频数据
  handleAudioData(audioData) {
    // 只在调试模式下输出详细日志
    if (this.isLog) {
      console.log('🎵 [AUDIO_FLOW] 收到音频数据:', {
        size: audioData ? audioData.byteLength : 0,
        isCanSendData: this.isCanSendData,
        isConnected: this.isConnected,
        queueLength: this.audioQueue.length,
        hasAudioData: !!audioData
      })
    }
    
    if (!audioData || audioData.byteLength === 0) {
      console.warn('⚠️ [AUDIO_FLOW] 音频数据无效，跳过处理')
      return
    }
    
    if (!this.isCanSendData || !this.isConnected) {
      console.log('⚠️ [AUDIO_FLOW] 音频数据被跳过:', {
        isCanSendData: this.isCanSendData,
        isConnected: this.isConnected
      })
      return
    }
    
    try {
      // 将音频数据添加到队列
      this.audioQueue.push(audioData)
      
      // 更新统计
      this.performanceStats.totalAudioSent += audioData.byteLength
      
      // 只在调试模式下输出队列日志
      if (this.isLog) {
        console.log('🎵 [AUDIO_FLOW] 音频数据已添加到队列:', {
          size: audioData.byteLength,
          queueLength: this.audioQueue.length,
          totalSent: this.performanceStats.totalAudioSent
        })
      }
      
    } catch (error) {
      console.error('❌ 处理音频数据失败:', error)
      this.performanceStats.errorCount++
      
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 开始音频发送定时器
  startAudioSendTimer() {
    this.stopAudioSendTimer()
    
    this.audioSendTimer = setInterval(() => {
      this.sendQueuedAudioData()
    }, this.audioSendInterval)
    
    this.isLog && console.log('⏰ 音频发送定时器已启动')
  }
  
  // 停止音频发送定时器
  stopAudioSendTimer() {
    if (this.audioSendTimer) {
      clearInterval(this.audioSendTimer)
      this.audioSendTimer = null
      this.isLog && console.log('⏹️ 音频发送定时器已停止')
    }
  }
  
  // 发送队列中的音频数据
  sendQueuedAudioData() {
    // 只在调试模式下输出定时器日志
    if (this.isLog) {
      console.log('⏰ [AUDIO_SEND] 定时器触发，检查音频队列:', {
        isCanSendData: this.isCanSendData,
        isConnected: this.isConnected,
        queueLength: this.audioQueue.length
      })
    }
    
    if (!this.isCanSendData || !this.isConnected) {
      return
    }
    
    // 🚀 优化：如果队列为空且长时间没有发送数据，发送静音数据保持连接
    const now = Date.now()
    if (this.audioQueue.length === 0 && now - this.lastAudioSendTime > 500) {
      try {
        // 🚀 更频繁发送静音数据（0.5秒内没有音频数据时），防止IDLE_TIMEOUT
        const silentData = new ArrayBuffer(1600) // 与audioChunkSize相同
        const silentArray = new Uint8Array(silentData)
        silentArray.fill(0) // 填充0（静音）
        
        this.recognizer.sendAudioData(silentData)
        this.lastAudioSendTime = now
        
        if (this.isLog) {
          console.log('🔇 [AUDIO_SEND] 发送静音数据保持连接活跃（0.5秒间隔）')
        }
      } catch (error) {
        console.error('❌ 发送静音数据失败:', error)
      }
      return
    }
    
    if (this.audioQueue.length === 0) {
      return
    }
    
    try {
      // 处理队列中的音频数据
      let sentCount = 0
      while (this.audioQueue.length > 0) {
        const audioData = this.audioQueue.shift()
        
        // 发送音频数据到识别器
        this.recognizer.sendAudioData(audioData)
        this.lastAudioSendTime = Date.now() // 更新最后发送时间
        sentCount++
        
        // 限制每次发送的数据量，避免过载
        if (this.audioQueue.length > 10) {
          this.isLog && console.log('⚠️ 音频队列过长，跳过部分数据')
          break
        }
      }
      
      // 只在调试模式下输出发送完成日志
      if (this.isLog) {
        console.log('✅ [AUDIO_SEND] 发送完成:', {
          sentCount,
          remainingQueue: this.audioQueue.length
        })
      }
      
    } catch (error) {
      console.error('❌ 发送音频数据失败:', error)
      this.performanceStats.errorCount++
      
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 停止识别
  stop() {
    try {
      this.isLog && console.log('🛑 停止阿里云语音识别完整流程...')
      
      this.isCanSendData = false
      this.isNormalEndStop = true
      this.updateState(ALIYUN_ASR_STATES.STOPPING)
      
      // 停止音频发送定时器
      this.stopAudioSendTimer()
      
      // 发送剩余的音频数据
      this.sendQueuedAudioData()
      
      // 停止录制器
      if (this.recorder) {
        this.recorder.stop()
        this.recorder = null
      }
      
      // 停止识别器
      if (this.recognizer) {
        this.recognizer.stop()
        this.recognizer = null
      }
      
      // 清理队列
      this.audioQueue = []
      
      // 更新状态
      this.isRecording = false
      this.isConnected = false
      this.updateState(ALIYUN_ASR_STATES.STOPPED)
      
      // 计算性能统计
      if (this.performanceStats.startTime > 0) {
        this.performanceStats.endTime = Date.now()
        this.performanceStats.averageLatency = 
          (this.performanceStats.endTime - this.performanceStats.startTime) / 
          Math.max(1, this.performanceStats.totalResultsReceived)
        
        this.isLog && console.log('📊 性能统计:', this.performanceStats)
      }
      
      this.isLog && console.log('✅ 阿里云语音识别完整流程已停止')
      
    } catch (error) {
      console.error('❌ 停止阿里云语音识别失败:', error)
      this.performanceStats.errorCount++
      
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 暂停识别
  pause() {
    try {
      this.isLog && console.log('⏸️ 暂停阿里云语音识别...')
      
      this.isCanSendData = false
      
      if (this.recorder) {
        this.recorder.pause()
      }
      
      this.stopAudioSendTimer()
      
      this.isLog && console.log('✅ 阿里云语音识别已暂停')
      
    } catch (error) {
      console.error('❌ 暂停阿里云语音识别失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 恢复识别
  resume() {
    try {
      this.isLog && console.log('▶️ 恢复阿里云语音识别...')
      
      this.isCanSendData = true
      
      if (this.recorder) {
        this.recorder.resume()
      }
      
      this.startAudioSendTimer()
      
      this.isLog && console.log('✅ 阿里云语音识别已恢复')
      
    } catch (error) {
      console.error('❌ 恢复阿里云语音识别失败:', error)
      if (this.OnError) {
        this.OnError(error)
      }
    }
  }
  
  // 获取状态
  getState() {
    return {
      currentState: this.currentState,
      isRecording: this.isRecording,
      isConnected: this.isConnected,
      isCanSendData: this.isCanSendData,
      lastError: this.lastError,
      performanceStats: this.performanceStats,
      audioQueueLength: this.audioQueue.length,
      requestId: this.requestId,
      taskId: this.recognizer?.taskId || null
    }
  }
  
  // 获取音频统计
  getAudioStats() {
    return this.recorder ? this.recorder.getAudioStats() : null
  }
  
  // 设置音频参数
  setAudioParams(params) {
    if (this.recorder) {
      // 设置增益
      if (params.gain !== undefined) {
        this.recorder.setGain(params.gain)
      }
      
      // 设置压缩器参数
      if (params.compressor) {
        this.recorder.setCompressorParams(
          params.compressor.threshold,
          params.compressor.ratio,
          params.compressor.attack,
          params.compressor.release
        )
      }
      
      // 设置滤波器参数
      if (params.filter) {
        this.recorder.setFilterParams(
          params.filter.frequency,
          params.filter.Q
        )
      }
    }
  }
  
  // 重置统计
  resetStats() {
    this.performanceStats = {
      startTime: 0,
      endTime: 0,
      totalAudioSent: 0,
      totalResultsReceived: 0,
      averageLatency: 0,
      errorCount: 0,
      reconnectCount: 0
    }
    
    this.isLog && console.log('📊 性能统计已重置')
  }
  
  // 销毁实例
  destroy() {
    this.stop()
    
    // 清理资源
    this.audioData = []
    this.audioQueue = []
    this.lastError = null
    
    // 清理回调
    this.OnRecognitionStart = null
    this.OnSentenceBegin = null
    this.OnRecognitionResultChange = null
    this.OnSentenceEnd = null
    this.OnRecognitionComplete = null
    this.OnError = null
    this.OnRecorderStop = null
    this.OnStateChange = null
    this.OnVolumeChange = null
    
    this.isLog && console.log('🗑️ 阿里云ASR完整识别器已销毁')
  }
}

console.log('✅ 阿里云ASR完整语音识别器已加载') 
const { contextBridge, ipcRenderer } = require('electron')

console.log('Preload script is loading...')

// 暴露给渲染进程的API
contextBridge.exposeInMainWorld('electronAPI', {
  // 登录相关
  login: (credentials) => ipcRenderer.invoke('login', credentials),
  logout: () => ipcRenderer.invoke('logout'),
  getLoginStatus: () => ipcRenderer.invoke('get-login-status'),
  
  // 协议相关
  onSSOCallback: (callback) => {
    ipcRenderer.on('sso-callback', (event, data) => callback(data))
  },
  
  // 打开URL
  openUrl: (url) => ipcRenderer.invoke('open-url', url),

  // 获取应用版本
  getAppVersion: () => ipcRenderer.invoke('get-app-version'),

  // 窗口控制
  showMainWindow: (page) => ipcRenderer.send('show-main-window', page),
  hideMainWindow: () => ipcRenderer.send('hide-main-window'),
  closeApp: () => ipcRenderer.send('close-app'),
  toggleDevTools: (enabled) => ipcRenderer.send('toggle-dev-tools', enabled),

  // 欢迎语音播放API
  speakWelcomeMessage: (options) => ipcRenderer.invoke('speak-welcome-message', options),

  // 悬浮窗拖拽
  dragFloatingWindow: (position) => ipcRenderer.send('floating-window-drag', position),
  
  // 悬浮窗大小调整
  resizeFloatingWindow: (size) => ipcRenderer.send('floating-window-resize', size),
  floatingWindowDragStart: () => ipcRenderer.send('floating-window-drag-start'),
  floatingWindowDragEnd: () => ipcRenderer.send('floating-window-drag-end'),
  getFloatingWindowBounds: () => ipcRenderer.invoke('get-floating-window-bounds'),
  
  // 获取Windows缩放比例
  getWindowsScaleFactor: () => ipcRenderer.invoke('get-windows-scale-factor'),
  
  // 通用IPC发送方法
  sendIPC: (channel, data) => ipcRenderer.send(channel, data),

  // 输入结果发送
  sendVoiceResult: (text) => ipcRenderer.send('voice-input-result', text),
  sendTextResult: (text) => ipcRenderer.send('text-input-result', text),

  // 通知事件
  notifyEvent: (eventType, eventData) => ipcRenderer.invoke('notifyEvent', eventType, eventData),
  
  // 监听ASR提供商变更
  onASRProviderChanged: (callback) => {
    ipcRenderer.on('asr-provider-changed', (event, data) => callback(data))
  },
  
  // 监听强制重新加载ASR
  onForceReloadASR: (callback) => {
    ipcRenderer.on('force-reload-asr', (event, data) => callback(data))
  },
  
  // 监听模型变更
  onModelChanged: (callback) => {
    ipcRenderer.on('model-changed', (event, data) => callback(data))
  },

  // === MCP相关API ===
  
  // 执行MCP工具
  executeMCPTool: (toolType, args) => ipcRenderer.invoke('execute-mcp-tool', toolType, args),
  
  // 获取MCP状态
  getMCPStatus: () => ipcRenderer.invoke('get-mcp-status'),
  
  // 重启浏览器MCP服务
  restartBrowserMCP: () => ipcRenderer.invoke('restart-browser-mcp'),
  restartOutlookCalendarMCP: () => ipcRenderer.invoke('restart-outlook-calendar-mcp'),
  
  // 文件选择对话框
  showFileSelection: (files, message) => ipcRenderer.invoke('show-file-selection', files, message),
  sendFileSelectionResult: (result) => ipcRenderer.invoke('file-selection-result', result),
  
  // 监听文件选择请求
  onFileSelectionRequest: (callback) => {
    ipcRenderer.on('file-selection-request', (event, files, message) => {
      callback(files, message)
    })
  },

  // === 邮件服务相关API ===
  
  // === 新的 Sherpa-ONNX 关键词检测相关API ===
  // 新的集成方式使用 Web Audio API 和 WASM，无需主进程处理
  


  // 获取待办列表
  getTodoList: () => ipcRenderer.invoke('get-todo-list'),
  
  // 清除提醒
  clearReminder: (reminderId) => ipcRenderer.invoke('clear-reminder', reminderId),
  
  // 通用事件监听器
  on: (channel, callback) => {
    ipcRenderer.on(channel, (event, ...args) => callback(...args))
  },
  
  // 移除事件监听器
  removeListener: (channel, callback) => {
    ipcRenderer.removeListener(channel, callback)
  },
  
  // 手动检查邮件
  checkEmailsManual: () => ipcRenderer.invoke('check-emails-manual'),
  
  // === 邮件配置相关API ===
  getEmailConfig: () => ipcRenderer.invoke('get-email-config'),
  saveEmailConfig: (config) => ipcRenderer.invoke('save-email-config', config),
  testEmailConfig: (config) => ipcRenderer.invoke('test-email-config', config),
  deleteEmailConfig: () => ipcRenderer.invoke('delete-email-config'),
  
  // === 日历同步相关API ===
  
  // 手动同步日历
  syncCalendarManual: (todoIds) => ipcRenderer.invoke('sync-calendar-manual', todoIds),
  
  // 获取同步状态
  getSyncStatus: () => ipcRenderer.invoke('get-sync-status'),
  
  // 监听待办事项同步到日历的事件
  onTodoSyncedToCalendar: (callback) => {
    ipcRenderer.on('todo-synced-to-calendar', (event, data) => callback(data))
  },

  // 监听AI请求调试信息
  onAIRequestStart: (callback) => {
    ipcRenderer.on('ai-request-start', (event, data) => callback(data))
  },
  onAIRequestComplete: (callback) => {
    ipcRenderer.on('ai-request-complete', (event, data) => callback(data))
  },
  onAIRequestError: (callback) => {
    ipcRenderer.on('ai-request-error', (event, data) => callback(data))
  },
  
  // 监听邮件处理完成事件
  onNewEmailsProcessed: (callback) => {
    ipcRenderer.on('new-emails-processed', (event, data) => callback(data))
  },
  
  // 监听邮件提醒事件
  onShowEmailReminder: (callback) => {
    ipcRenderer.on('show-email-reminder', (event, reminderData) => callback(reminderData))
  },
  
  // 监听语音播报事件
  onSpeakText: (callback) => {
    ipcRenderer.on('speak-text', (event, speechData) => callback(speechData))
  },

  // 监听主进程console日志
  onMainConsoleLog: (callback) => {
    ipcRenderer.on('main-console-log', (event, logData) => callback(logData))
  },
  
  // 监听通知事件
  onShowNotification: (callback) => {
    ipcRenderer.on('show-notification', (event, notification) => callback(notification))
  },
  
  // === 知识库相关API ===
  
  // 通用IPC调用
  invoke: (channel, ...args) => ipcRenderer.invoke(channel, ...args),
  
  // 知识库操作
  initKnowledge: () => ipcRenderer.invoke('knowledge-init'),
  searchKnowledge: (query, limit) => ipcRenderer.invoke('knowledge-search', query, limit),
  getKnowledgeStats: () => ipcRenderer.invoke('knowledge-stats'),
  indexDocument: (filePath) => ipcRenderer.invoke('knowledge-index-document', filePath),
  clearKnowledge: () => ipcRenderer.invoke('knowledge-clear'),
  rebuildKnowledge: () => ipcRenderer.invoke('knowledge-rebuild'),
  debugKnowledge: () => ipcRenderer.invoke('knowledge-debug'),
  
  // 文件系统操作
  selectDirectory: () => ipcRenderer.invoke('select-directory'),
  getDirectoryFiles: (dirPath, extensions) => ipcRenderer.invoke('get-directory-files', dirPath, extensions),
  getPath: (pathName) => ipcRenderer.invoke('get-path', pathName),
  openFile: (filePath, options = {}) => ipcRenderer.invoke('open-file', filePath, options),
  
  // 文件路径配置相关
  getFilePathsConfig: () => ipcRenderer.invoke('get-file-paths-config'),
  updateFilePathsConfig: (config) => ipcRenderer.invoke('update-file-paths-config', config),
  selectCustomPath: () => ipcRenderer.invoke('select-custom-path'),

  // 事件监听
  onLoginStatusChanged: (callback) => {
    ipcRenderer.on('login-status-changed', (event, status) => callback(status))
  },
  onNavigateTo: (callback) => {
    ipcRenderer.on('navigate-to', (event, page) => callback(page))
  },
  onStatusMessage: (callback) => {
    ipcRenderer.on('status-message', (event, message) => callback(message))
  },

  // 移除监听器
  removeAllListeners: (channel) => {
    ipcRenderer.removeAllListeners(channel)
  }
})

console.log('Preload script loaded successfully') 
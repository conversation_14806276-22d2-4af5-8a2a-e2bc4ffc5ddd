/**
 * 新的 Sherpa-ONNX 关键词检测器
 * 基于 external_tools/keyword-spotter 示例代码重新实现
 * 使用 Web Audio API 和 WASM 进行关键词检测
 */

export class NewSherpaOnnxWakeWordDetector {
  constructor(options = {}) {
    console.log('🚀 初始化新的 Sherpa-ONNX 关键词检测器')
    
    // 配置选项
    this.config = {
      // 模型文件路径 - 相对于public目录
      modelPath: 'assets/wasm',
      
      // 音频配置
      sampleRate: 16000,
      featureDim: 80,
      
      // 模型配置
      numThreads: 1,
      provider: 'cpu',
      debug: options.debug || false,
      
      // 关键词配置
      keywords: options.keywords || ['你好犇犇', '犇犇', '奔奔', '笨笨'],
      
      // 检测配置
      sensitivity: options.sensitivity || 0.1, // 检测敏感度
      
      // 🔧 【增强】调试配置
      verboseDebug: options.verboseDebug || false, // 详细调试模式
      showAllResults: options.showAllResults || true, // 显示所有识别结果
      
      ...options
    }
    
    // 状态管理
    this.isInitialized = false
    this.isListening = false
    
    // Web Audio API 相关
    this.audioCtx = null
    this.mediaStream = null
    this.recorder = null
    this.source = null
    
    // Sherpa-ONNX 相关
    this.Module = null
    this.recognizer = null
    this.recognizer_stream = null
    
    // 回调函数
    this.onKeywordDetected = null
    this.onError = null
    this.onStatusChange = null
    
    console.log('📋 关键词检测器配置:', {
      modelPath: this.config.modelPath,
      keywords: this.config.keywords,
      sampleRate: this.config.sampleRate,
      numThreads: this.config.numThreads,
      debug: this.config.debug
    })
  }

  /**
   * 初始化检测器
   */
  async initialize() {
    if (this.isInitialized) {
      console.log('⚠️ 检测器已经初始化')
      return true
    }

    try {
      console.log('🔧 开始初始化新的 Sherpa-ONNX 检测器...')
      
      // 加载 WASM 模块
      await this.loadWasmModule()
      
      // 创建关键词检测器
      await this.createRecognizer()
      
      this.isInitialized = true
      console.log('✅ 新的 Sherpa-ONNX 检测器初始化成功')
      
      return true
      
    } catch (error) {
      console.error('❌ 初始化新的 Sherpa-ONNX 检测器异常:', error)
      this.onError && this.onError(error)
      return false
    }
  }

  /**
   * 加载 WASM 模块
   */
  async loadWasmModule() {
    try {
      console.log('📦 加载 WASM 模块...')
      
      // 🔧 【增强】添加更安全的WASM模块配置
      window.Module = {
        locateFile: (path, scriptDirectory) => {
          if (path.endsWith('.data') || path.endsWith('.wasm')) {
            return `${this.config.modelPath}/${path}`
          }
          return scriptDirectory + path
        },
        onRuntimeInitialized: () => {
          console.log('✅ WASM 模块初始化完成')
          this.Module = window.Module
          // WASM模块初始化完成后，加载sherpa-onnx-kws.js
          this.loadSherpaOnnxKws()
        },
        onAbort: (what) => {
          console.error('❌ WASM 模块中止:', what)
        },
        onError: (error) => {
          console.error('❌ WASM 模块错误:', error)
        },
        print: (text) => {
          if (this.config.debug) {
            console.log('WASM:', text)
          }
        },
        printErr: (text) => {
          console.error('WASM Error:', text)
        }
      }
      
      // 动态导入 WASM 脚本
      const wasmPath = `${this.config.modelPath}/sherpa-onnx-wasm-kws-main.js`
      
      // 🔧 【增强】添加超时保护
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('WASM脚本加载超时')), 30000)
      })
      
      const loadPromise = new Promise((resolve, reject) => {
        const script = document.createElement('script')
        script.src = wasmPath
        script.onload = () => {
          console.log('✅ WASM 脚本加载完成')
          resolve()
        }
        script.onerror = (error) => {
          console.error('❌ WASM 脚本加载失败:', error)
          reject(new Error('WASM 脚本加载失败'))
        }
        document.head.appendChild(script)
      })
      
      await Promise.race([loadPromise, timeoutPromise])
      
    } catch (error) {
      console.error('❌ 加载 WASM 模块失败:', error)
      // 🔧 【增强】清理失败的模块
      if (window.Module) {
        window.Module = null
      }
      throw error
    }
  }

  /**
   * 加载 sherpa-onnx-kws.js 文件
   */
  loadSherpaOnnxKws() {
    try {
      console.log('📦 加载 sherpa-onnx-kws.js...')
      
      const kwsPath = `${this.config.modelPath}/sherpa-onnx-kws.js`
      
      // 创建script标签加载sherpa-onnx-kws.js
      const script = document.createElement('script')
      script.src = kwsPath
      script.onload = () => {
        console.log('✅ sherpa-onnx-kws.js 加载完成')
        // 检查createKws函数是否可用
        if (typeof window.createKws === 'function') {
          console.log('✅ createKws 函数可用')
          this.createKws = window.createKws
        } else {
          console.error('❌ createKws 函数不可用')
          throw new Error('createKws 函数不可用')
        }
      }
      script.onerror = (error) => {
        console.error('❌ sherpa-onnx-kws.js 加载失败:', error)
        throw new Error('sherpa-onnx-kws.js 加载失败')
      }
      document.head.appendChild(script)
      
    } catch (error) {
      console.error('❌ 加载 sherpa-onnx-kws.js 失败:', error)
      throw error
    }
  }

  /**
   * 创建识别器
   */
  async createRecognizer() {
    try {
      console.log('🔧 创建关键词检测器...')
      
      // 等待createKws函数加载完成
      let retryCount = 0
      const maxRetries = 50 // 最多等待5秒
      
      while (!this.createKws && retryCount < maxRetries) {
        await new Promise(resolve => setTimeout(resolve, 100))
        retryCount++
        if (retryCount % 10 === 0) {
          console.log(`⏳ 等待 createKws 函数加载... (${retryCount/10}s)`)
        }
      }
      
      if (!this.createKws) {
        throw new Error('createKws 函数加载超时')
      }
      
      // 检查Module是否可用
      if (!this.Module) {
        throw new Error('WASM Module 未初始化')
      }
      
      // 创建配置对象
      const transducerConfig = {
        encoder: './encoder-epoch-12-avg-2-chunk-16-left-64.onnx',
        decoder: './decoder-epoch-12-avg-2-chunk-16-left-64.onnx',
        joiner: './joiner-epoch-12-avg-2-chunk-16-left-64.onnx',
      }
      
      const modelConfig = {
        transducer: transducerConfig,
        tokens: './tokens.txt',
        provider: 'cpu',
        modelType: '',
        numThreads: 1,
        debug: 1,
        modelingUnit: 'cjkchar',
        bpeVocab: '',
      }
      
      const featConfig = {
        samplingRate: 16000,
        featureDim: 80,
      }
      
      // 将中文关键词转换为拼音格式
      const keywordLines = this.config.keywords.map(keyword => {
        const pinyinMap = {
          '你好犇犇': 'n ǐ h ǎo b ēn b ēn @你好犇犇',
          '犇犇': 'b ēn b ēn @犇犇',
          '小犇犇': 'x iǎo b ēn b ēn @小犇犇',
          '犇犇助手': 'b ēn b ēn @犇犇助手',
          '小犇同学': 'x iǎo b ēn b ēn t óng x ué @小犇同学',
        }
        
        return pinyinMap[keyword] || `${keyword} @${keyword}`
      })
      // console.log('keywordLines', keywordLines)
      // // 只使用在tokens.txt中存在的词汇，避免使用不存在的token
      // const validKeywords = keywordLines.filter(keyword => {
      //   // 过滤掉包含"小犇犇"、"犇犇助手"等可能不存在的词汇
      //   const invalidTokens = ['小犇犇', '犇犇助手', '小犇同学']
      //   return !invalidTokens.some(token => keyword.includes(token))
      // })
      
      // if (validKeywords.length === 0) {
      //   // 如果没有有效关键词，使用默认的关键词
      //   validKeywords.push('n ǐ h ǎo b ēn b ēn @你好犇犇')
      // }
      console.log('keywordLines ', keywordLines)
      const configObj = {
        featConfig: featConfig,
        modelConfig: modelConfig,
        maxActivePaths: 4,
        numTrailingBlanks: 1,
        keywordsScore: 1.0,
        keywordsThreshold: 0.1, // 使用更低的阈值
        keywords: keywordLines.join('\n')
      }
      
      console.log('📋 关键词检测器配置:', configObj)
      
      // 创建关键词检测器
      this.recognizer = this.createKws(this.Module, configObj)
      
      // 检查创建是否成功
      if (!this.recognizer || !this.recognizer.handle) {
        throw new Error('关键词检测器创建失败：handle 为空')
      }
      
      console.log('✅ 关键词检测器创建成功')
      
    } catch (error) {
      console.error('❌ 创建关键词检测器失败:', error)
      throw error
    }
  }

  /**
   * 开始监听
   */
  async startListening() {
    if (!this.isInitialized) {
      throw new Error('检测器未初始化')
    }

    if (this.isListening) {
      console.log('⚠️ 已经在监听中')
      return true  // 返回 true 表示已经成功启动
    }

    try {
      console.log('🎧 开始监听唤醒词...')
      
      // 启动 Web Audio
      await this.startWebAudio()
      
      this.isListening = true
      this.onStatusChange && this.onStatusChange('listening')
      
      console.log('✅ 唤醒词监听已启动')
      return true  // 返回 true 表示成功启动
      
    } catch (error) {
      console.error('❌ 启动监听失败:', error)
      this.onError && this.onError(error)
      throw error
    }
  }

  /**
   * 启动 Web Audio
   */
  async startWebAudio() {
    try {
      console.log('🔊 启动 Web Audio...')
      
      // 获取麦克风权限
      const stream = await navigator.mediaDevices.getUserMedia({ audio: true })
      
      // 创建音频上下文
      this.audioCtx = new AudioContext({ sampleRate: this.config.sampleRate })
      console.log('音频上下文采样率:', this.audioCtx.sampleRate)
      
      // 创建媒体流源
      this.mediaStream = this.audioCtx.createMediaStreamSource(stream)
      
      // 🔧 【修复】使用更现代的音频处理方式，避免ScriptProcessorNode弃用警告
      const bufferSize = 4096
      const numberOfInputChannels = 1
      const numberOfOutputChannels = 0 // 不需要输出通道
      
      // 尝试使用AudioWorkletNode（更现代的方式）
      if (this.audioCtx.audioWorklet && typeof AudioWorkletNode !== 'undefined') {
        try {
          // 创建简单的音频处理器
          const workletCode = `
            class AudioProcessor extends AudioWorkletProcessor {
              constructor() {
                super();
                this.buffer = [];
                this.bufferSize = ${bufferSize};
              }
              
              process(inputs, outputs, parameters) {
                const input = inputs[0];
                if (input && input.length > 0) {
                  const channel = input[0];
                  if (channel) {
                    // 将音频数据发送到主线程
                    this.port.postMessage({
                      type: 'audioData',
                      data: channel.slice()
                    });
                  }
                }
                return true;
              }
            }
            registerProcessor('audio-processor', AudioProcessor);
          `;
          
          const blob = new Blob([workletCode], { type: 'application/javascript' });
          const url = URL.createObjectURL(blob);
          
          await this.audioCtx.audioWorklet.addModule(url);
          
          this.workletNode = new AudioWorkletNode(this.audioCtx, 'audio-processor');
          this.workletNode.port.onmessage = (event) => {
            if (event.data.type === 'audioData') {
              this.processAudioData(event.data.data);
            }
          };
          
          // 连接音频节点
          this.mediaStream.connect(this.workletNode);
          this.workletNode.connect(this.audioCtx.destination);
          
          URL.revokeObjectURL(url);
          console.log('✅ 使用AudioWorkletNode启动Web Audio成功');
          
        } catch (workletError) {
          console.warn('⚠️ AudioWorkletNode失败，回退到ScriptProcessorNode:', workletError);
          this.createScriptProcessor(bufferSize, numberOfInputChannels, numberOfOutputChannels);
        }
      } else {
        // 回退到ScriptProcessorNode
        this.createScriptProcessor(bufferSize, numberOfInputChannels, numberOfOutputChannels);
      }
      
      console.log('✅ Web Audio 启动成功')
      
    } catch (error) {
      console.error('❌ 启动 Web Audio 失败:', error)
      throw error
    }
  }

  /**
   * 创建ScriptProcessorNode（回退方案）
   */
  createScriptProcessor(bufferSize, numberOfInputChannels, numberOfOutputChannels) {
    if (this.audioCtx.createScriptProcessor) {
      this.recorder = this.audioCtx.createScriptProcessor(
        bufferSize, numberOfInputChannels, numberOfOutputChannels)
    } else {
      this.recorder = this.audioCtx.createJavaScriptNode(
        bufferSize, numberOfInputChannels, numberOfOutputChannels)
    }
    
    // 设置音频处理回调
    this.recorder.onaudioprocess = (e) => {
      try {
        // 获取音频数据
        let samples = new Float32Array(e.inputBuffer.getChannelData(0))
        this.processAudioData(samples)
      } catch (error) {
        console.error('❌ ScriptProcessorNode音频处理失败:', error)
      }
    }
    
    // 连接音频节点
    this.mediaStream.connect(this.recorder)
    this.recorder.connect(this.audioCtx.destination)
    
    console.log('✅ 使用ScriptProcessorNode启动Web Audio成功')
  }

  /**
   * 处理音频数据
   */
  processAudioData(samples) {
    try {
      // 🔧 【增强】添加音频数据统计
      if (this.config.verboseDebug) {
        const audioStats = {
          sampleCount: samples.length,
          sampleRate: this.config.sampleRate,
          duration: samples.length / this.config.sampleRate,
          maxAmplitude: Math.max(...samples.map(Math.abs)),
          avgAmplitude: samples.reduce((sum, val) => sum + Math.abs(val), 0) / samples.length
        }
        // console.log('🎵 音频数据统计:', audioStats)
      }
      
      // 重采样到目标采样率
      samples = this.downsampleBuffer(samples, this.config.sampleRate)
      
      // 创建识别流（如果不存在）
      if (this.recognizer_stream == null) {
        this.recognizer_stream = this.recognizer.createStream()
        console.log('🔄 创建新的识别流')
      }
      
      // 输入音频数据
      this.recognizer_stream.acceptWaveform(this.config.sampleRate, samples)
      
      // 检查是否有结果
      let resultCount = 0
      while (this.recognizer.isReady(this.recognizer_stream)) {
        this.recognizer.decode(this.recognizer_stream)
        
        const result = this.recognizer.getResult(this.recognizer_stream)
        resultCount++
        
        // 🔧 【增强】输出所有识别结果，包括非唤醒词
        if (result && this.config.showAllResults) {
          // console.log(`🔍 识别结果 #${resultCount}:`, {
          //   keyword: result.keyword || '无关键词',
          //   score: result.score || '无分数',
          //   timestamp: new Date().toISOString(),
          //   rawResult: result
          // })
          
          // 检查是否包含关键词
          if (result.keyword && result.keyword.length > 0) {
            // 检查是否匹配配置的关键词
            const isWakeWord = this.config.keywords.some(keyword => 
              result.keyword.includes(keyword) || keyword.includes(result.keyword)
            )
            
            if (isWakeWord) {
              console.log('🎯 检测到唤醒词:', result.keyword, '分数:', result.score)
              
              // 触发回调
              this.onKeywordDetected && this.onKeywordDetected(result)
              
              // 重置流
              this.recognizer.reset(this.recognizer_stream)
              console.log('🔄 重置识别流（唤醒词触发）')
            } else {
              console.log('⚠️ 检测到非唤醒词:', result.keyword, '分数:', result.score)
              
              // 即使不是唤醒词也重置流，避免累积
              this.recognizer.reset(this.recognizer_stream)
              console.log('🔄 重置识别流（非唤醒词）')
            }
          } else {
            // console.log('🔇 无关键词识别结果')
          }
        }
      }
    } catch (error) {
      console.error('❌ 处理音频数据失败:', error)
      this.onError && this.onError(error)
    }
  }

  /**
   * 重采样音频数据
   */
  downsampleBuffer(buffer, exportSampleRate) {
    if (exportSampleRate === this.audioCtx.sampleRate) {
      return buffer
    }
    
    const sampleRateRatio = this.audioCtx.sampleRate / exportSampleRate
    const newLength = Math.round(buffer.length / sampleRateRatio)
    const result = new Float32Array(newLength)
    
    let offsetResult = 0
    let offsetBuffer = 0
    
    while (offsetResult < result.length) {
      const nextOffsetBuffer = Math.round((offsetResult + 1) * sampleRateRatio)
      let accum = 0, count = 0
      
      for (let i = offsetBuffer; i < nextOffsetBuffer && i < buffer.length; i++) {
        accum += buffer[i]
        count++
      }
      
      result[offsetResult] = accum / count
      offsetResult++
      offsetBuffer = nextOffsetBuffer
    }
    
    return result
  }

  /**
   * 停止监听
   */
  async stopListening() {
    if (!this.isListening) {
      console.log('⚠️ 未在监听中')
      return
    }

    try {
      console.log('🛑 停止监听唤醒词...')
      
      // 停止 Web Audio
      this.stopWebAudio()
      
      // 清理识别流
      if (this.recognizer_stream) {
        try {
          this.recognizer_stream.free()
        } catch (freeError) {
          console.warn('⚠️ 清理识别流失败:', freeError)
        }
        this.recognizer_stream = null
      }
      
      this.isListening = false
      this.onStatusChange && this.onStatusChange('stopped')
      
      console.log('✅ 唤醒词监听已停止')
      
    } catch (error) {
      console.error('❌ 停止监听失败:', error)
      this.onError && this.onError(error)
    }
  }

  /**
   * 停止 Web Audio
   */
  stopWebAudio() {
    try {
      // 清理AudioWorkletNode
      if (this.workletNode) {
        this.workletNode.disconnect()
        this.workletNode.port.close()
        this.workletNode = null
      }
      
      // 清理ScriptProcessorNode
      if (this.recorder) {
        this.recorder.disconnect()
        this.recorder = null
      }
      
      if (this.mediaStream) {
        this.mediaStream.disconnect()
        this.mediaStream = null
      }
      
      if (this.audioCtx) {
        this.audioCtx.close()
        this.audioCtx = null
      }
      
      console.log('✅ Web Audio 已停止')
      
    } catch (error) {
      console.error('❌ 停止 Web Audio 失败:', error)
    }
  }

  /**
   * 更新关键词
   */
  async updateKeywords(keywords) {
    try {
      console.log('🔄 更新关键词:', keywords)
      
      this.config.keywords = keywords
      
      // 如果正在监听，需要重新创建检测器
      if (this.isListening) {
        await this.stopListening()
        await this.createRecognizer()
        await this.startListening()
      } else {
        await this.createRecognizer()
      }
      
      console.log('✅ 关键词更新成功')
      
    } catch (error) {
      console.error('❌ 更新关键词失败:', error)
      this.onError && this.onError(error)
    }
  }

  /**
   * 获取状态
   */
  getStatus() {
    return {
      isInitialized: this.isInitialized,
      isListening: this.isListening,
      config: this.config
    }
  }

  /**
   * 销毁检测器
   */
  async destroy() {
    try {
      console.log('🗑️ 销毁新的 Sherpa-ONNX 检测器...')
      
      // 停止监听
      if (this.isListening) {
        await this.stopListening()
      }
      
      // 清理识别器
      if (this.recognizer) {
        try {
          this.recognizer.free()
        } catch (freeError) {
          console.warn('⚠️ 清理识别器失败:', freeError)
        }
        this.recognizer = null
      }
      
      // 清理识别流
      if (this.recognizer_stream) {
        try {
          this.recognizer_stream.free()
        } catch (streamError) {
          console.warn('⚠️ 清理识别流失败:', streamError)
        }
        this.recognizer_stream = null
      }
      
      // 清理模块
      this.Module = null
      
      // 🔧 【增强】更安全的WASM模块清理
      if (window.Module) {
        try {
          // 尝试调用WASM模块的清理函数
          if (window.Module._free && typeof window.Module._free === 'function') {
            // 这里可以添加具体的清理逻辑
          }
        } catch (moduleError) {
          console.warn('⚠️ 清理WASM模块失败:', moduleError)
        }
        window.Module = null
      }
      
      this.isInitialized = false
      
      console.log('✅ 新的 Sherpa-ONNX 检测器已销毁')
      
    } catch (error) {
      console.error('❌ 销毁检测器失败:', error)
    }
  }
}

export default NewSherpaOnnxWakeWordDetector 
/**
 * 知识库代理服务
 * 处理向量化和重排序的代理请求
 */

import { apiRequest } from './apiManager.js'
import { API_CONFIG } from './apiConfig.js'

/**
 * 知识库代理服务类
 */
class KnowledgeProxyService {
  constructor() {
    this.baseURL = API_CONFIG.PROD_API_BASE_URL
  }

  /**
   * 获取文本的向量表示（通过代理）
   * @param {string} text - 要向量化的文本
   * @param {Object} options - 可选参数
   * @returns {Promise<Float32Array>} 向量表示
   */
  async getEmbedding(text, options = {}) {
    try {
      console.log('🔗 通过代理获取文本向量表示:', {
        textLength: text.length,
        model: options.model || 'BAAI/bge-m3'
      })

      const requestData = {
        model: options.model || 'BAAI/bge-m3',
        input: text,
        encoding_format: options.encoding_format || 'float'
      }

      const response = await apiRequest(
        'POST',
        API_CONFIG.ENDPOINTS.KNOWLEDGE_EMBEDDINGS,
        requestData
      )
      
      if (!response.success) {
        throw new Error(`向量化请求失败: ${response.error}`)
      }

      // 处理响应数据
      const embeddingData = response.data.data || response.data
      if (!embeddingData || !embeddingData[0] || !embeddingData[0].embedding) {
        throw new Error('代理返回的向量数据格式不正确')
      }

      const embedding = new Float32Array(embeddingData[0].embedding)
      console.log(`✅ 代理向量化成功，维度: ${embedding.length}`)
      
      return embedding
    } catch (error) {
      console.error('❌ 代理向量化失败:', error)
      throw error
    }
  }

  /**
   * 重排序文档片段（通过代理）
   * @param {Array} documents - 文档片段数组
   * @param {string} query - 查询文本
   * @param {Object} options - 可选参数
   * @returns {Promise<Array>} 重排序后的结果
   */
  async rerank(documents, query, options = {}) {
    try {
      console.log('🔄 通过代理重排序文档片段:', {
        documentsCount: documents.length,
        queryLength: query.length,
        model: options.model || 'BAAI/bge-reranker-v2-m3'
      })

      const requestData = {
        query: query,
        documents: documents,
        model: options.model || 'BAAI/bge-reranker-v2-m3'
      }

      const response = await apiRequest(
        'POST',
        API_CONFIG.ENDPOINTS.KNOWLEDGE_RERANK,
        requestData
      )

      if (!response.success) {
        throw new Error(`重排序请求失败: ${response.error}`)
      }

      // 处理响应数据
      const rerankData = response.data.data || response.data
      if (!rerankData || !rerankData.results) {
        throw new Error('代理返回的重排序数据格式不正确')
      }

      console.log(`✅ 代理重排序成功，返回 ${rerankData.results.length} 个结果`)
      
      return rerankData.results
    } catch (error) {
      console.error('❌ 代理重排序失败:', error)
      throw error
    }
  }

  /**
   * 批量获取向量表示（通过代理）
   * @param {Array<string>} texts - 文本数组
   * @param {Object} options - 可选参数
   * @returns {Promise<Array<Float32Array>>} 向量表示数组
   */
  async getBatchEmbeddings(texts, options = {}) {
    try {
      console.log('🔗 通过代理批量获取向量表示:', {
        textsCount: texts.length,
        model: options.model || 'BAAI/bge-m3'
      })

      const requestData = {
        model: options.model || 'BAAI/bge-m3',
        input: texts,
        encoding_format: options.encoding_format || 'float'
      }

      const response = await apiRequest(
        'POST',
        API_CONFIG.ENDPOINTS.KNOWLEDGE_EMBEDDINGS,
        requestData
      )

      if (!response.success) {
        throw new Error(`批量向量化请求失败: ${response.error}`)
      }

      // 处理响应数据
      const embeddingData = response.data.data || response.data
      if (!embeddingData || !Array.isArray(embeddingData)) {
        throw new Error('代理返回的批量向量数据格式不正确')
      }

      const embeddings = embeddingData.map(item => {
        if (!item || !item.embedding) {
          throw new Error('批量向量数据中某个项目格式不正确')
        }
        return new Float32Array(item.embedding)
      })

      console.log(`✅ 代理批量向量化成功，处理 ${embeddings.length} 个文本`)
      
      return embeddings
    } catch (error) {
      console.error('❌ 代理批量向量化失败:', error)
      throw error
    }
  }

  /**
   * 检查代理服务状态
   * @returns {Promise<boolean>} 服务是否可用
   */
  async checkServiceStatus() {
    try {
      // 发送一个简单的测试请求
      const testResponse = await apiRequest(
        'POST',
        API_CONFIG.ENDPOINTS.KNOWLEDGE_EMBEDDINGS,
        {
          model: 'BAAI/bge-m3',
          input: 'test',
          encoding_format: 'float'
        }
      )

      return testResponse.success
    } catch (error) {
      console.error('❌ 知识库代理服务状态检查失败:', error)
      return false
    }
  }
}

// 创建单例实例
const knowledgeProxyService = new KnowledgeProxyService()

export default knowledgeProxyService 
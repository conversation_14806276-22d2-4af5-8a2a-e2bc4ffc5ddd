/**
 * 统一API管理器
 * 处理所有API请求的统一错误管理
 */

import axios from 'axios'
import { getApiBaseUrl, needsToken, getTimeout } from './apiConfig.js'

/**
 * 创建统一的API客户端
 * @param {string} baseURL - API基础地址
 * @param {boolean} requireToken - 是否需要token
 * @returns {Object} axios实例
 */
function createApiClient(baseURL = null, requireToken = true) {
  // 使用配置中心的API地址
  const apiBaseUrl = baseURL || getApiBaseUrl()
  
  // 获取用户token
  const userToken = localStorage.getItem('userAuthToken') || ''
  
  const headers = {
    'Content-Type': 'application/json'
  }
  
  // 只有需要token的接口才添加Authorization头
  if (requireToken && userToken) {
    headers['Authorization'] = `Bearer ${userToken}`
  }
  
  const client = axios.create({
    baseURL: apiBaseUrl,
    headers,
    timeout: getTimeout(apiBaseUrl)
  })

  // 请求拦截器
  client.interceptors.request.use(
    (config) => {
      const requiresToken = needsToken(config.url)
      const currentToken = localStorage.getItem('userAuthToken') || ''
      
      console.log('🌐 API请求:', {
        method: config.method?.toUpperCase(),
        url: config.url,
        requiresToken,
        hasToken: !!currentToken,
        tokenLength: currentToken.length
      })
      
      // 动态更新Authorization头
      if (requiresToken && currentToken) {
        config.headers['Authorization'] = `Bearer ${currentToken}`
      } else if (!requiresToken) {
        delete config.headers['Authorization']
      }
      
      return config
    },
    (error) => {
      console.error('❌ API请求错误:', error)
      return Promise.reject(error)
    }
  )

  // 响应拦截器 - 统一错误处理
  client.interceptors.response.use(
    (response) => {
      console.log('✅ API响应成功:', {
        status: response.status,
        url: response.config.url,
        dataType: typeof response.data
      })
      return response
    },
    (error) => {
      console.error('❌ API响应错误:', {
        status: error.response?.status,
        url: error.config?.url,
        message: error.response?.data?.msg || error.message
      })
      
      return handleApiError(error)
    }
  )

  return client
}

/**
 * 统一处理API错误
 * @param {Error} error - axios错误对象
 * @returns {Promise} 处理后的错误
 */
function handleApiError(error) {
  if (!error.response) {
    // 网络错误
    showErrorMessage('网络连接失败，请检查网络状态')
    return Promise.reject(error)
  }

  const { status, data } = error.response
  
  switch (status) {
    case 200:
      // 200是正常返回，不应该进入错误处理
      return Promise.resolve(error.response)
      
    case 401:
      // Token过期，需要退出登录
      console.log('🔑 Token已过期，执行退出登录操作')
      showErrorMessage('登录已过期，请重新登录')
      
      // 延迟执行退出登录，让用户看到提示
      setTimeout(() => {
        // 清除本地存储
        localStorage.removeItem('userAuthToken')
        localStorage.removeItem('userAuthInfo')
        
        // 通知主进程（如果存在）
        if (window.electronAPI && window.electronAPI.logout) {
          window.electronAPI.logout()
        }
        
        // 跳转到登录页
        if (window.location.pathname !== '/login') {
          window.location.href = '/login'
        }
      }, 1500)
      break
      
    case 403:
      showErrorMessage('权限不足，无法访问该资源')
      break
      
    case 404:
      showErrorMessage('请求的资源不存在')
      break
      
    case 500:
      showErrorMessage('服务器内部错误，请稍后重试')
      break
      
    default:
      // 其他状态码，显示服务器返回的消息
      const message = data?.msg || data?.message || `请求失败 (${status})`
      showErrorMessage(message)
      break
  }

  return Promise.reject(error)
}

/**
 * 显示错误消息
 * @param {string} message - 错误消息
 */
function showErrorMessage(message) {
  console.error('🚨 API错误:', message)
  
  // 如果存在全局的消息提示系统，使用它
  if (window.showError) {
    window.showError(message)
  } else if (window.$message) {
    window.$message.error(message)
  } else {
    // 使用浏览器原生alert作为fallback
    alert(`错误: ${message}`)
  }
}

/**
 * 统一的API请求方法
 * @param {string} method - HTTP方法
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
export async function apiRequest(method, url, data = null, options = {}) {
  const requiresToken = needsToken(url)
  const client = createApiClient(null, requiresToken)
  
  try {
    const config = {
      method: method.toLowerCase(),
      url,
      ...options
    }
    
    if (data) {
      if (method.toLowerCase() === 'get') {
        config.params = data
      } else {
        config.data = data
      }
    }
    
    const response = await client(config)
    
    // 处理嵌套的响应结构
    const responseData = response.data.data || response.data
    
    return {
      success: true,
      data: responseData,
      status: response.status
    }
    
  } catch (error) {
    // 错误已经在拦截器中处理，这里只需要返回错误信息
    return {
      success: false,
      error: error.response?.data?.msg || error.message,
      status: error.response?.status
    }
  }
}

/**
 * GET请求
 * @param {string} url - 请求URL
 * @param {Object} params - 查询参数
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
export async function apiGet(url, params = {}, options = {}) {
  return apiRequest('GET', url, params, options)
}

/**
 * POST请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
export async function apiPost(url, data = {}, options = {}) {
  return apiRequest('POST', url, data, options)
}

/**
 * PUT请求
 * @param {string} url - 请求URL
 * @param {Object} data - 请求数据
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
export async function apiPut(url, data = {}, options = {}) {
  return apiRequest('PUT', url, data, options)
}

/**
 * DELETE请求
 * @param {string} url - 请求URL
 * @param {Object} options - 其他选项
 * @returns {Promise} 请求结果
 */
export async function apiDelete(url, options = {}) {
  return apiRequest('DELETE', url, null, options)
}

/**
 * 检查API连接状态
 * @returns {Promise<boolean>} 是否连接正常
 */
export async function checkApiConnection() {
  try {
    const result = await apiGet('/common/defaultVersion')
    return result.success
  } catch (error) {
    return false
  }
}

/**
 * 获取API客户端实例
 * @param {string} baseURL - API基础地址
 * @returns {Object} axios实例
 */
export function getApiClient(baseURL) {
  return createApiClient(baseURL)
}

// 导出默认的API客户端
export const apiClient = createApiClient()

export default {
  apiRequest,
  apiGet,
  apiPost,
  apiPut,
  apiDelete,
  checkApiConnection,
  getApiClient,
  apiClient
} 
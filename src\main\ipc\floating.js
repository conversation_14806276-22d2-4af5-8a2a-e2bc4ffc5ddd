const { ipcMain, screen } = require('electron')

/**
 * 悬浮窗相关的IPC处理程序
 */
class FloatingIPCHandler {
  constructor(appManager) {
    this.appManager = appManager
  }

  /**
   * 注册所有悬浮窗相关的IPC处理程序
   */
  register() {
    // 悬浮窗拖拽开始
    ipcMain.on('floating-window-drag-start', () => {
      console.log('🖱️ 悬浮窗拖拽开始')
      this.appManager.windowManager.setFloatingWindowDragging(true)
    })

    // 悬浮窗拖拽结束
    ipcMain.on('floating-window-drag-end', () => {
      console.log('🖱️ 悬浮窗拖拽结束')
      this.appManager.windowManager.setFloatingWindowDragging(false)
    })

    // 悬浮窗拖拽处理
    ipcMain.on('floating-window-drag', (event, position) => {
      if (this.appManager.windowManager.isFloatingWindowValid()) {
        try {
          const window = this.appManager.windowManager.getFloatingWindow()
          const currentBounds = window.getBounds()
          window.setBounds({
            x: position.x,
            y: position.y,
            width: currentBounds.width,
            height: currentBounds.height
          })
        } catch (error) {
          console.error('❌ 悬浮窗拖拽失败:', error)
        }
      }
    })

    // 获取悬浮窗当前尺寸
    ipcMain.handle('get-floating-window-bounds', () => {
      if (this.appManager.windowManager.isFloatingWindowValid()) {
        try {
          const bounds = this.appManager.windowManager.getFloatingWindow().getBounds()
          const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize
          
          console.log('📏 获取悬浮窗尺寸:', bounds, '屏幕尺寸:', {screenWidth, screenHeight})
          
          return {
            success: true,
            bounds,
            screen: { width: screenWidth, height: screenHeight }
          }
        } catch (error) {
          console.error('❌ 获取悬浮窗尺寸失败:', error)
          return { success: false, error: error.message }
        }
      }
      return { success: false, error: '悬浮窗不存在' }
    })

    // 悬浮窗大小调整处理
    ipcMain.on('floating-window-resize', (event, size) => {
      this.appManager.windowManager.handleFloatingWindowHeightAdjust(size.height)
    })

    // 悬浮窗高度调整处理
    ipcMain.handle('floating-window-height-adjust', async (event, newHeight) => {
      try {
        console.log('📏 收到悬浮窗高度调整请求:', newHeight)
        
        if (!this.appManager.windowManager.isFloatingWindowValid()) {
          return { success: false, error: '悬浮窗不存在' }
        }

        this.appManager.windowManager.handleFloatingWindowHeightAdjust(newHeight)
        
        return { success: true }
      } catch (error) {
        console.error('❌ 悬浮窗高度调整失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 显示主窗口
    ipcMain.on('show-main-window', () => {
      this.appManager.windowManager.showMainWindow()
    })

    // 切换开发者工具
    ipcMain.on('toggle-dev-tools', () => {
      if (this.appManager.windowManager.isMainWindowValid()) {
        const mainWindow = this.appManager.windowManager.getMainWindow()
        if (mainWindow.webContents.isDevToolsOpened()) {
          mainWindow.webContents.closeDevTools()
        } else {
          mainWindow.webContents.openDevTools()
        }
      }
    })

    console.log('✅ 悬浮窗相关IPC处理程序已注册')
  }
}

module.exports = FloatingIPCHandler

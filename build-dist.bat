@echo off
echo.
echo ===================================
echo     犇犇数字员工助手 - 构建脚本
echo ===================================
echo.

echo [1/3] 删除dist文件夹...
if exist "dist" (
    rmdir /s /q "dist"
    echo ✅ dist文件夹已删除
) else (
    echo ℹ️  dist文件夹不存在，跳过删除
)

echo.
echo [2/3] 杀掉electron进程...
tasklist | findstr /i "electron" > nul
if %errorlevel% == 0 (
    echo 🔍 发现electron进程，正在终止...
    taskkill /f /im electron.exe > nul 2>&1
    taskkill /f /im "犇犇数字员工助手.exe" > nul 2>&1
    taskkill /f /im "nezha-ai-desktop.exe" > nul 2>&1
    echo ✅ electron进程已终止
) else (
    echo ℹ️  没有发现electron进程
)

echo.
echo [3/3] 开始构建应用...
echo 🚀 运行 npm run electron:dist...
echo.

npm run electron:dist

echo.
if %errorlevel% == 0 (
    echo ✅ 构建完成！
    echo 📦 生成的文件位于 dist 文件夹
    echo.
    echo 可以运行以下命令打开dist文件夹：
    echo explorer dist
) else (
    echo ❌ 构建失败！
    echo 请检查错误信息并修复后重试
)

echo.
echo ===================================
echo        构建脚本执行完成
echo ===================================
pause 
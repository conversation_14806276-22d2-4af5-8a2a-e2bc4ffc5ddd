/**
 * ASR提供商管理工具
 * 用于管理和切换不同的ASR服务
 */
import { DEFAULT_SMART_VOICE_CONFIG } from './smart-voice-config.js'

// 本地存储键名
const STORAGE_KEY = 'nezha-asr-provider'

// ASR提供商列表 - 禁用阿里云，只使用腾讯云
const PROVIDERS = [
  { id: 'tencent', name: '腾讯云语音识别' }
]

/**
 * ASR提供商管理器
 */
export default {
  /**
   * 获取当前ASR提供商ID
   * @returns {String} 'tencent'
   */
  getProviderId() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const config = JSON.parse(stored)
        // 强制使用腾讯云，忽略存储的阿里云配置
        return 'tencent'
      }
      return 'tencent'
    } catch (error) {
      console.error('获取ASR提供商失败:', error)
      return 'tencent'
    }
  },

  /**
   * 获取当前ASR提供商（兼容旧方法）
   * @returns {String} 'tencent'
   */
  getProvider() {
    return this.getProviderId()
  },
  
  /**
   * 获取当前ASR提供商名称
   * @returns {String} 提供商显示名称
   */
  getProviderName() {
    const id = this.getProviderId()
    const provider = PROVIDERS.find(p => p.id === id)
    return provider ? provider.name : '腾讯云语音识别'
  },
  
  /**
   * 获取所有可用的ASR提供商列表
   * @returns {Array} 提供商列表
   */
  getProviders() {
    return PROVIDERS
  },

  /**
   * 设置ASR提供商
   * @param {String} provider - 只接受'tencent'
   */
  setProvider(provider) {
    try {
      // 只允许设置腾讯云
      if (provider !== 'tencent') {
        console.warn('阿里云ASR已被禁用，强制使用腾讯云')
        provider = 'tencent'
      }
      
      // 获取现有配置
      let config = {}
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        config = JSON.parse(stored)
      }
      
      // 更新提供商
      config.asrProvider = provider
      
      // 保存配置
      localStorage.setItem(STORAGE_KEY, JSON.stringify(config))
      
      console.log('ASR提供商已更新为:', provider)
      return true
    } catch (error) {
      console.error('设置ASR提供商失败:', error)
      return false
    }
  }
} 
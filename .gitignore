# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json

# Build outputs
dist/
dist-electron/
*.tgz

# Debug and build artifacts
debug/
out/
build/

# Runtime data
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/

# nyc test coverage
.nyc_output

# Logs
logs
*.log

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# Editor history files
.history/

# OS generated files
Thumbs.db
.DS_Store

# Electron build output
out/
build/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Temporary folders
tmp/
temp/

# Windows specific
*.exe
*.msi
*.zip
*.lock

# App specific
app-update.yml
dev-app-update.yml

# Python environment (auto-generated)
python/
*.tar.gz
python-*.tar.gz 
uv.lock
.venv
__pycache__
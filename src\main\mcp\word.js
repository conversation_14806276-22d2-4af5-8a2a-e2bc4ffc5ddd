// Word MCP 模块
const fs = require('fs')
const path = require('path')
const { join } = require('path')
const os = require('os')

let initialized = false
let mcpClient = null
let mcpTransport = null
let availableTools = []

// 检查是否为开发环境
const isDev = process.env.NODE_ENV === 'development' || !require('electron').app.isPackaged

// 处理文件路径，包括用户名占位符替换
function processFilePath(filePath) {
  if (!filePath) return filePath

  const userHomeDir = os.homedir()
  const actualUsername = os.userInfo().username

  // 首先处理路径标识符
  if (filePath === 'Desktop') {
    return join(userHomeDir, 'Desktop')
  } else if (filePath === 'Documents') {
    return join(userHomeDir, 'Documents')
  } else if (filePath === 'Downloads') {
    return join(userHomeDir, 'Downloads')
  }

  // 处理路径中的username占位符
  if (filePath.includes('/username/') || filePath.includes('\\username\\')) {
    filePath = filePath
      .replace(/\/username\//g, `/${actualUsername}/`)
      .replace(/\\username\\/g, `\\${actualUsername}\\`)
  }

  // 处理C:/Users/<USER>/格式的路径
  if (filePath.includes('C:/Users/<USER>/') || filePath.includes('C:\\Users\\<USER>\\')) {
    filePath = filePath
      .replace(/C:\/Users\/<USER>\//g, userHomeDir.replace(/\\/g, '/') + '/')
      .replace(/C:\\Users\\<USER>\\/g, userHomeDir + '\\')
  }

  // 如果仍然不是绝对路径，添加默认基础路径
  if (!path.isAbsolute(filePath)) {
    // 检查是否是 Desktop/文件名.docx 这种格式
    if (filePath.startsWith('Desktop/') || filePath.startsWith('Documents/') || filePath.startsWith('Downloads/')) {
      const pathParts = filePath.split('/')
      const baseDir = pathParts[0] // Desktop, Documents, Downloads
      const fileName = pathParts.slice(1).join('/') // 文件名部分

      // 转换基础目录为绝对路径
      let absoluteBaseDir
      if (baseDir === 'Desktop') {
        absoluteBaseDir = join(userHomeDir, 'Desktop')
      } else if (baseDir === 'Documents') {
        absoluteBaseDir = join(userHomeDir, 'Documents')
      } else if (baseDir === 'Downloads') {
        absoluteBaseDir = join(userHomeDir, 'Downloads')
      } else {
        absoluteBaseDir = join(userHomeDir, 'Desktop') // 默认桌面
      }

      filePath = join(absoluteBaseDir, fileName)
    } else {
      // 默认添加到桌面
      filePath = join(userHomeDir, 'Desktop', filePath)
    }
  }

  return filePath
}

// 解析MCP返回的结果
function parseMCPResult(mcpResult, toolName) {
  let parsedResult

  if (mcpResult.content && Array.isArray(mcpResult.content)) {
    // MCP返回的是content数组格式
    const textContent = mcpResult.content
      .filter(item => item.type === 'text')
      .map(item => item.text)

    const joinedContent = textContent.join('\n')
    try {
      // 尝试解析JSON结果
      parsedResult = JSON.parse(joinedContent)
    } catch {
      // 如果不是JSON，直接使用文本
      parsedResult = {
        success: true,
        message: joinedContent,
        rawContent: joinedContent
      }
    }
  } else {
    // 其他格式的结果
    parsedResult = {
      success: true,
      message: '工具调用成功',
      result: mcpResult
    }
  }

  return parsedResult
}

async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('正在初始化Word MCP服务器...')

    // 读取MCP配置文件
    const mcpConfigPath = isDev
      ? join(process.cwd(), 'mcp-config.json')
      : join(process.resourcesPath, 'mcp-config.json')
    console.log('📋 读取Word MCP配置文件:', mcpConfigPath)

    if (!fs.existsSync(mcpConfigPath)) {
      console.error('❌ MCP配置文件不存在:', mcpConfigPath)
      throw new Error('MCP配置文件不存在')
    }

    const mcpConfig = JSON.parse(fs.readFileSync(mcpConfigPath, 'utf8'))
    console.log('📋 Word MCP配置内容:', JSON.stringify(mcpConfig, null, 2))

    // 获取word-server-mcp配置
    const wordServerConfig = mcpConfig.mcpServers['word-server-mcp']
    if (!wordServerConfig) {
      console.error('❌ 未找到word-server-mcp配置')
      throw new Error('未找到word-server-mcp配置')
    }

    console.log('🔧 word-server-mcp配置:', wordServerConfig)
    console.log('🔧 命令:', wordServerConfig.command)
    console.log('🔧 参数:', wordServerConfig.args)

    // 加载MCP SDK
    console.log('📦 加载MCP SDK...')
    try {
      // 使用动态导入方式加载MCP SDK
      const stdioModule = await import('@modelcontextprotocol/sdk/client/stdio.js')
      const clientModule = await import('@modelcontextprotocol/sdk/client/index.js')
      const { StdioClientTransport } = stdioModule
      const { Client } = clientModule

      console.log('✅ MCP SDK加载成功')

      // 解析命令和参数的绝对路径
      const resolvedCommand = isDev
        ? wordServerConfig.command
        : join(process.resourcesPath, wordServerConfig.command)

      const resolvedArgs = wordServerConfig.args.map(arg =>
        isDev ? arg : join(process.resourcesPath, arg)
      )

      console.log('🚀 启动Word MCP服务器:')
      console.log('  - 原始命令:', wordServerConfig.command)
      console.log('  - 解析后命令:', resolvedCommand)
      console.log('  - 原始参数:', wordServerConfig.args)
      console.log('  - 解析后参数:', resolvedArgs)
      console.log('  - 环境:', isDev ? '开发环境' : '生产环境')

      // 验证Python可执行文件是否存在
      if (!fs.existsSync(resolvedCommand)) {
        console.error('❌ Python可执行文件不存在:', resolvedCommand)
        throw new Error(`Python可执行文件不存在: ${resolvedCommand}`)
      }

      // 验证MCP服务器脚本是否存在
      if (resolvedArgs.length > 0 && !fs.existsSync(resolvedArgs[0])) {
        console.error('❌ Word MCP服务器脚本不存在:', resolvedArgs[0])
        throw new Error(`Word MCP服务器脚本不存在: ${resolvedArgs[0]}`)
      }

      // 处理环境变量
      const resolvedEnv = {}
      if (wordServerConfig.env) {
        for (const [key, value] of Object.entries(wordServerConfig.env)) {
          if (key === 'PYTHONPATH' && typeof value === 'string') {
            resolvedEnv[key] = isDev ? value : join(process.resourcesPath, value)
            console.log(`🔧 环境变量 ${key}: "${value}" → "${resolvedEnv[key]}"`)
          } else {
            resolvedEnv[key] = value
          }
        }
      }

      // 跳过Word Python依赖检查以避免弹框显示
      console.log('🔍 跳过Word Python依赖检查（避免弹框显示）')
      console.log('⚠️ 假设Word Python依赖已安装，直接继续初始化')

      // 创建Word MCP客户端连接
      mcpTransport = new StdioClientTransport({
        command: resolvedCommand,
        args: resolvedArgs,
        env: {
          ...process.env,
          ...resolvedEnv
        }
      })

      mcpClient = new Client({
        name: 'nezha-word-client',
        version: '1.0.0'
      }, {
        capabilities: {}
      })

      // 连接到Word MCP服务器
      await mcpClient.connect(mcpTransport)
      console.log('✅ 已连接到word-server MCP服务器')

      // 获取可用工具列表
      console.log('📋 正在获取Word工具列表...')
      const tools = await mcpClient.listTools()

      console.log('📋 Word工具列表获取成功:', tools)
      console.log('📋 可用的Word工具:', tools.tools?.map(t => t.name) || [])

      availableTools = tools.tools || []
      initialized = true

      console.log('✅ 真实Word MCP客户端已初始化')
      console.log('🔧 可用工具数量:', availableTools.length)
      console.log('🔧 配置来源: mcp-config.json')
      console.log('🔧 MCP传输协议: STDIO')

      return {
        name: 'word-server',
        isConnected: true,
        mcpClient: mcpClient,
        mcpTransport: mcpTransport,
        availableTools: availableTools,
        isRealMCP: true,
        configSource: 'mcp-config.json'
      }

    } catch (importError) {
      console.error('❌ MCP SDK导入失败:', importError)
      throw importError
    }

  } catch (error) {
    console.error('❌ Word MCP初始化失败:', error)
    // 错误回退到模拟模式
    console.log('🔄 回退到模拟模式...')

    availableTools = [
      { name: 'word_create', description: '创建Word文档（模拟）' },
      { name: 'word_insert', description: '插入内容到Word文档（模拟）' },
      { name: 'word_read', description: '读取Word文档（模拟）' },
      { name: 'word_open', description: '打开Word文档（模拟）' },
      { name: 'word_edit', description: '编辑Word文档（模拟）' }
    ]
    initialized = true

    console.log('✅ 使用模拟Word客户端（错误回退）')
    return {
      name: 'word-server',
      isConnected: true,
      mcpClient: null,
      mcpTransport: null,
      availableTools: availableTools,
      isRealMCP: false,
      configSource: '错误回退模式'
    }
  }
}

function getConnectionStatus() {
  return {
    connected: initialized,
    service: 'word',
    isRealMCP: mcpClient !== null,
    availableTools: availableTools.length
  }
}

function getAvailableTools() {
  return availableTools
}

async function callTool(toolName, args) {
  if (!initialized) {
    throw new Error('Word服务未初始化')
  }

  if (!mcpClient) {
    // 模拟模式
    console.log(`📄 模拟调用Word工具: ${toolName}`, args)
    return {
      success: true,
      message: `模拟执行 ${toolName}`,
      data: { tool: toolName, args: args }
    }
  }

  try {
    console.log(`📄 [WORD_MCP] 调用真实Word工具: ${toolName}`, args)

    // 处理路径参数
    let processedArgs = { ...args }

    if (args.file_path) {
      processedArgs.file_path = processFilePath(args.file_path)
      console.log(`🔧 [WORD_MCP] 路径转换: "${args.file_path}" → "${processedArgs.file_path}"`)
    }

    console.log(`📄 [WORD_MCP] 最终参数:`, JSON.stringify(processedArgs, null, 2))

    // 使用MCP协议调用工具
    const mcpResult = await mcpClient.callTool({
      name: toolName,
      arguments: processedArgs
    })

    console.log(`✅ [WORD_MCP] MCP调用成功:`, mcpResult)

    // 解析MCP返回的结果
    let parsedResult = parseMCPResult(mcpResult, toolName)

    // 对于word_create成功的情况，自动打开创建的文档
    if (toolName === 'word_create' && parsedResult.success) {
      try {
        const createdFilePath = join(processedArgs.file_path, processedArgs.file_name)
        console.log(`📂 [WORD_MCP] 自动打开创建的文档: ${createdFilePath}`)

        // 这里需要调用文件系统的openFile方法
        // 暂时先记录，实际实现需要引用文件系统模块
        parsedResult.autoOpened = false
        parsedResult.openMessage = '自动打开功能需要文件系统模块支持'
        parsedResult.createdFilePath = createdFilePath

        console.log(`📂 [WORD_MCP] 文档创建完成: ${createdFilePath}`)
      } catch (openError) {
        console.warn(`📂 [WORD_MCP] 自动打开失败:`, openError)
        parsedResult.autoOpened = false
        parsedResult.openMessage = `自动打开失败: ${openError.message}`
      }
    }

    console.log(`🎯 [WORD_MCP] 解析后结果:`, parsedResult)
    return parsedResult

  } catch (error) {
    console.error(`❌ Word工具调用失败: ${toolName}`, error)
    return {
      success: false,
      error: `Word工具调用失败: ${error.message}`,
      toolName: toolName,
      args: args,
      exception: error.name
    }
  }
}

async function cleanup() {
  console.log('🧹 清理Word MCP资源...')
  
  if (mcpClient) {
    try {
      await mcpClient.close()
      console.log('✅ Word MCP客户端已关闭')
    } catch (error) {
      console.error('❌ 关闭Word MCP客户端失败:', error)
    }
  }

  if (mcpTransport) {
    try {
      await mcpTransport.close()
      console.log('✅ Word MCP传输已关闭')
    } catch (error) {
      console.error('❌ 关闭Word MCP传输失败:', error)
    }
  }

  mcpClient = null
  mcpTransport = null
  availableTools = []
  initialized = false
}

module.exports = {
  initialize,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
} 
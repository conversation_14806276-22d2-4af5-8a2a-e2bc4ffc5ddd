<template>
  <div class="todo-view">
    <div class="view-header">
      <div class="header-content">
        <h1 class="view-title">待办事项</h1>
      </div>

    </div>

    <div class="view-content">
      <div class="content-header">
        <h2 class="content-title">我的待办列表</h2>
        <div class="header-actions">
          <!-- <button @click="refreshTodos" class="action-btn" :disabled="isRefreshing">
          {{ isRefreshing ? '刷新中...' : '刷新' }}
        </button> -->
          <button @click="checkEmailsManually" class="action-btn" :disabled="isChecking">
            <img :src="iconRefseImg" alt="刷新" class="action-icon">
          </button>
        </div>
      </div>
      <!-- 待办事项列表 -->
      <div class="todo-content">


        <div class="todo-list-container">
          <div v-if="todos.length === 0" class="empty-state">
            <div class="empty-icon">📭</div>
            <h3>暂无待办事项</h3>
            <p>系统会自动检查邮件并提取待办事项</p>
          </div>

          <div v-else class="todo-list">
            <div v-for="todo in todos" :key="todo.id" class="todo-item" :class="{
              'high-urgency': todo.urgency === 'high',
              'medium-urgency': todo.urgency === 'medium',
              'low-urgency': todo.urgency === 'low',
              'completed': todo.completed
            }">
              <div class="todo-main">
                <div class="todo-content">
                  <div class="todo-header">
                    <h4 class="todo-subject">{{ todo.subject }}</h4>
                    <div class="todo-badges">
                      <span class="todo-type">{{ getTodoTypeLabel(todo.todoType) }}</span>
                    </div>
                  </div>

                  <p class="todo-description">{{ todo.todoDescription }}</p>

                  <div class="todo-meta">
                    <span class="todo-from">来自: {{ todo.from }}</span>
                    <span v-if="todo.dueDate" class="todo-due">
                      截止: {{ formatDate(todo.dueDate) }}
                    </span>
                    <span v-if="todo.syncedToCalendar" class="sync-status synced">
                      📅 已同步
                    </span>
                  </div>

                  <div class="todo-actions">
                    <button
                      @click="toggleComplete(todo)"
                      class="action-btn complete-btn"
                      :class="{ active: todo.completed }"
                      :title="todo.completed ? '取消完成' : '标记完成'"
                    >
                      {{ todo.completed ? '✓' : '○' }}
                    </button>
                    <button
                      @click="deleteTodo(todo)"
                      class="action-btn delete-btn"
                      title="删除待办事项"
                    >
                      🗑️
                    </button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 状态消息 -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'

// 导入图标资源 - 确保打包后正确
import iconRefseImg from '/assets/icon-refse.png'

export default {
  name: 'TodoView',
  setup() {
    const todos = ref([])
    const isRefreshing = ref(false)
    const isChecking = ref(false)
    const statusMessage = ref('')
    const statusType = ref('info')

    // 加载待办事项
    const loadTodos = async () => {
      try {
        isRefreshing.value = true
        const result = await window.electronAPI.invoke('get-todo-list')
        if (result.success) {
          todos.value = result.todos || []
        }
      } catch (error) {
        console.error('加载待办列表失败:', error)
        showStatus('加载待办列表失败', 'error')
      } finally {
        isRefreshing.value = false
      }
    }

    // 刷新待办事项
    const refreshTodos = async () => {
      await loadTodos()
      showStatus('待办列表已刷新', 'success')
    }

    // 手动检查邮件
    const checkEmailsManually = async () => {
      try {
        isChecking.value = true
        const result = await window.electronAPI.checkEmailsManual()
        if (result.success) {
          showStatus(result.message, 'success')
          await loadTodos()
        } else {
          showStatus('检查邮件失败: ' + result.error, 'error')
        }
      } catch (error) {
        console.error('手动检查邮件失败:', error)
        showStatus('检查邮件时出错: ' + error.message, 'error')
      } finally {
        isChecking.value = false
      }
    }

    // 切换完成状态
    const toggleComplete = async (todo) => {
      try {
        const result = await window.electronAPI.invoke('update-todo', todo.uid, {
          completed: !todo.completed
        })

        if (result.success) {
          todo.completed = !todo.completed
          showStatus(todo.completed ? '任务已完成' : '任务已取消完成', 'success')
        } else {
          showStatus('更新任务状态失败: ' + result.error, 'error')
        }
      } catch (error) {
        console.error('更新任务状态失败:', error)
        showStatus('更新任务状态时出错: ' + error.message, 'error')
      }
    }

    // 删除待办事项
    const deleteTodo = async (todo) => {
      if (!confirm(`确定要删除待办事项"${todo.subject}"吗？`)) {
        return
      }

      try {
        const result = await window.electronAPI.invoke('delete-todo', todo.uid)

        if (result.success) {
          // 从本地列表中移除
          const index = todos.value.findIndex(t => t.uid === todo.uid)
          if (index !== -1) {
            todos.value.splice(index, 1)
          }

          showStatus('待办事项已删除', 'success')
        } else {
          showStatus('删除待办事项失败: ' + result.error, 'error')
        }
      } catch (error) {
        console.error('删除待办事项失败:', error)
        showStatus('删除待办事项时出错: ' + error.message, 'error')
      }
    }

    // 获取待办类型标签
    const getTodoTypeLabel = (type) => {
      const typeLabels = {
        meeting: '📅 会议',
        task: '📋 任务',
        deadline: '⏰ 截止',
        reply: '📨 回复',
        approval: '✅ 审批'
      }
      return typeLabels[type] || '📄 其他'
    }

    // 获取紧急程度标签
    const getUrgencyLabel = (urgency) => {
      const urgencyLabels = {
        high: '紧急',
        medium: '普通',
        low: '较低'
      }
      return urgencyLabels[urgency] || '普通'
    }

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return ''
      try {
        const date = new Date(dateString)
        return date.toLocaleString('zh-CN', {
          month: 'short',
          day: 'numeric',
          hour: '2-digit',
          minute: '2-digit'
        })
      } catch (error) {
        return dateString
      }
    }

    // 显示状态消息
    const showStatus = (message, type = 'info') => {
      statusMessage.value = message
      statusType.value = type
      setTimeout(() => {
        statusMessage.value = ''
      }, 3000)
    }

    // 设置事件监听器
    const setupEventListeners = () => {
      window.electronAPI.onNewEmailsProcessed?.((data) => {
        loadTodos()
        showStatus(`处理了 ${data.totalEmails} 封邮件，新增 ${data.todoEmails} 个待办事项`, 'success')
      })
      
      // 监听邮件配置删除事件，自动刷新待办列表
      window.electronAPI.on('email-config-deleted', () => {
        console.log('收到邮件配置删除通知，刷新待办列表')
        loadTodos()
        showStatus('邮件配置已删除，待办列表已清空', 'info')
      })
    }

    onMounted(() => {
      loadTodos()
      setupEventListeners()
    })

    return {
      todos,
      isRefreshing,
      isChecking,
      statusMessage,
      statusType,
      refreshTodos,
      checkEmailsManually,
      toggleComplete,
      deleteTodo,
      getTodoTypeLabel,
      getUrgencyLabel,
      formatDate,
      // 导出图标资源
      iconRefseImg
    }
  }
}
</script>

<style lang="scss" scoped>
.todo-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.view-header {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  .view-title {
    margin: 8px 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 50%;
  background: none;
  backdrop-filter: blur(10px);
  color: var(--text-dark);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.primary {
    background: var(--btn-primary-bg);
    color: var(--text-inverse);
    border-color: var(--btn-primary-bg);

    &:hover:not(:disabled) {
      background: var(--btn-primary-hover);
      border-color: var(--btn-primary-hover);
    }
  }
}

.action-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.view-content {
  flex: 1;
  padding: 24px;
  border-radius: 24px 0 0 0;
  overflow: auto;
  background: var(--view-content-bg);

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 8px;
  }

  &::-webkit-scrollbar-track {
    background: var(--border-color-light);
    border-radius: 4px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 4px;

    &:hover {
      background: var(--text-secondary);
    }
  }
}

.todo-content {
  overflow: hidden;
  transition: all 0.2s ease;
}

.content-header {
  display: flex;
  justify-content: space-between;
  align-items: center;

  h2 {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-dark);
  }
}

.todo-list-container {
  max-height: 600px;
  overflow-y: auto;

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--border-color-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;

    &:hover {
      background: var(--text-secondary);
    }
  }
}

.empty-state {
  text-align: center;
  padding: 60px 24px;
  color: var(--text-secondary);

  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  h3 {
    margin: 0 0 8px 0;
    color: var(--text-dark);
  }

  p {
    margin: 0;
    font-size: 14px;
    color: var(--text-muted);
  }
}

.todo-list {
  display: flex;
  flex-direction: column;
  margin-top: 20px;
}

.todo-item {
  transition: all 0.2s ease;
  position: relative;
  margin-bottom: 20px;
  border-radius: 10px;
  background: var(--card-bg);

  &.completed {
    opacity: 0.6;
  }

}

.todo-main {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 10px 12px;
}

.todo-checkbox {
  margin-top: 4px;
}

.todo-actions {
  display: flex;
  gap: 8px;
  margin-top: 12px;
}

.todo-actions .action-btn {
  width: 32px;
  height: 32px;
  border: 2px solid #ddd;
  border-radius: 50%;
  background: white;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
  transition: all 0.2s;
  color: #666;
}

.todo-actions .complete-btn:hover {
  border-color: #4CAF50;
  color: #4CAF50;
  background: rgba(76, 175, 80, 0.1);
}

.todo-actions .complete-btn.active {
  background: #4CAF50;
  border-color: #4CAF50;
  color: white;
}

.todo-actions .delete-btn:hover {
  border-color: #f44336;
  color: #f44336;
  background: rgba(244, 67, 54, 0.1);
}

.checkbox-btn {
  width: 20px;
  height: 20px;
  border: 2px solid var(--border-color-dark);
  border-radius: 4px;
  background: var(--card-bg);
  color: var(--primary-color);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 12px;
  transition: all 0.2s ease;

  &:hover {
    border-color: var(--primary-color);
    transform: scale(1.05);
  }

  &.active {
    background: var(--primary-color);
    border-color: var(--primary-color);
    color: var(--text-inverse);
  }
}

.todo-content {
  flex: 1;
}

.todo-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  border-bottom: 1px solid var(--view-content-bg);
  padding-bottom: 8px;
}

.todo-subject {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--text-dark);
  line-height: 1.4;
}

.todo-badges {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
}

.todo-type,
.urgency-badge {
  font-size: 12px;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
}

.todo-type {
  background: var(--badge-info-bg);
  color: var(--badge-info-text);
}

.urgency-badge {
  &.high {
    background: var(--badge-error-bg);
    color: var(--badge-error-text);
  }

  &.medium {
    background: var(--badge-warning-bg);
    color: var(--badge-warning-text);
  }

  &.low {
    background: var(--badge-success-bg);
    color: var(--badge-success-text);
  }
}

.todo-description {
  margin: 0 0 12px 0;
  color: var(--text-secondary);
  font-size: 14px;
  line-height: 1.4;
}

.todo-meta {
  display: flex;
  gap: 16px;
  font-size: 12px;
  color: var(--text-muted);
}

.sync-status.synced {
  color: var(--success-color);
  font-weight: 500;
}

.status-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: var(--shadow-modal);
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: statusSlideIn 0.3s ease-out;

  &.success {
    background: var(--badge-success-bg);
    color: var(--badge-success-text);
    border: 1px solid var(--badge-success-text);
  }

  &.error {
    background: var(--badge-error-bg);
    color: var(--badge-error-text);
    border: 1px solid var(--badge-error-text);
  }

  &.info {
    background: var(--badge-info-bg);
    color: var(--badge-info-text);
    border: 1px solid var(--badge-info-text);
  }
}

@keyframes statusSlideIn {
  from {
    opacity: 0;
    transform: translateX(100px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 暗黑主题下的特殊处理
.dark-theme .todo-view {
  .empty-icon {
    filter: brightness(0.8);
  }

  .todo-item:hover {
    border-color: var(--border-color-medium);
  }
}

// 选择文本样式
.todo-view {
  ::selection {
    background: rgba(var(--primary-rgb), 0.3);
    color: inherit;
  }

  ::-moz-selection {
    background: rgba(var(--primary-rgb), 0.3);
    color: inherit;
  }
}

// 焦点样式增强
.action-btn:focus,
.checkbox-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}
</style>
#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const os = require('os')
const https = require('https')

console.log('🐍 开始配置Python环境和MCP依赖...')

// Python可执行文件路径
const pythonExe = path.join(__dirname, 'python', 'py', 'python', 'python.exe')

function checkPython() {
  try {
    if (fs.existsSync(pythonExe)) {
      console.log('✅ 找到Python可执行文件:', pythonExe)
      
      // 检查Python版本
      const version = execSync(`"${pythonExe}" --version`, { encoding: 'utf8' }).trim()
      console.log('✅ Python版本:', version)
      return true
    } else {
      console.error('❌ Python可执行文件不存在:', pythonExe)
      return false
    }
  } catch (error) {
    console.error('❌ Python检查失败:', error.message)
    return false
  }
}

function installPythonDependencies() {
  try {
    console.log('�� 开始安装Python依赖...')
    
    // 设置PYTHONPATH环境变量
    const pythonPath = path.join(__dirname, 'python', 'py', 'python', 'Lib', 'site-packages')
    process.env.PYTHONPATH = pythonPath
    
    console.log('📂 Python包路径:', pythonPath)
    
    // 需要安装的包
    const packages = [
      'mcp[cli]>=1.10.1',
      'fastmcp>=0.1.0',
      'pywin32>=310',
      'asyncio-mqtt>=0.11.0',
      'python-dotenv>=1.0.0',
      'httpx>=0.28.1',
      'pinyin>=0.4.0'
    ]
    
    console.log('📋 将安装以下包:', packages)
    
    // 逐个安装包
    for (const pkg of packages) {
      try {
        console.log(`📦 正在安装: ${pkg}`)
        
        const installCmd = `"${pythonExe}" -m pip install "${pkg}" --target "${pythonPath}" --upgrade`
        console.log(`🔧 执行命令: ${installCmd}`)
        
        execSync(installCmd, { 
          stdio: 'inherit',
          env: {
            ...process.env,
            PYTHONPATH: pythonPath
          }
        })
        
        console.log(`✅ ${pkg} 安装成功`)
      } catch (error) {
        console.error(`❌ ${pkg} 安装失败:`, error.message)
        
        // 尝试使用不同的安装方式
        try {
          console.log(`🔄 尝试简化安装: ${pkg.split('>=')[0] || pkg.split('==')[0] || pkg}`)
          const simplePkg = pkg.split('>=')[0] || pkg.split('==')[0] || pkg
          const simpleInstallCmd = `"${pythonExe}" -m pip install "${simplePkg}" --target "${pythonPath}"`
          
          execSync(simpleInstallCmd, { 
            stdio: 'inherit',
            env: {
              ...process.env,
              PYTHONPATH: pythonPath
            }
          })
          
          console.log(`✅ ${simplePkg} 简化安装成功`)
        } catch (retryError) {
          console.error(`❌ ${pkg} 重试安装也失败:`, retryError.message)
          console.log('⚠️ 继续安装其他包...')
        }
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ Python依赖安装失败:', error.message)
    return false
  }
}

function verifyInstallation() {
  try {
    console.log('🔍 验证MCP安装...')
    
    // 测试导入MCP模块
    const testScript = `
try:
    import mcp
    print("✅ MCP模块导入成功")
    print(f"✅ MCP版本: {getattr(mcp, '__version__', '未知')}")
except ImportError as e:
    print(f"❌ MCP模块导入失败: {e}")

try:
    import fastmcp
    print("✅ FastMCP模块导入成功")
except ImportError as e:
    print(f"❌ FastMCP模块导入失败: {e}")

try:
    import win32com.client
    print("✅ pywin32模块导入成功")
except ImportError as e:
    print(f"❌ pywin32模块导入失败: {e}")
`;
    
    const pythonPath = path.join(__dirname, 'python', 'py', 'python', 'Lib', 'site-packages')
    
    execSync(`"${pythonExe}" -c "${testScript}"`, {
      stdio: 'inherit',
      env: {
        ...process.env,
        PYTHONPATH: pythonPath
      }
    })
    
    console.log('✅ 验证完成')
    return true
  } catch (error) {
    console.error('❌ 验证失败:', error.message)
    return false
  }
}

function testMCPServers() {
  try {
    console.log('🧪 测试MCP服务器...')
    
    // 测试邮件服务器
    const emailServerPath = path.join(__dirname, 'mcp-servers', 'email-server', 'main.py')
    if (fs.existsSync(emailServerPath)) {
      console.log('📧 测试邮件服务器脚本...')
      
      const testEmailScript = `
import sys
sys.path.insert(0, r'${path.join(__dirname, 'python', 'py', 'python', 'Lib', 'site-packages')}')

try:
    exec(open(r'${emailServerPath}').read())
    print("✅ 邮件服务器脚本语法正确")
except Exception as e:
    print(f"❌ 邮件服务器脚本错误: {e}")
`;
      
      try {
        execSync(`"${pythonExe}" -c "${testEmailScript}"`, {
          stdio: 'inherit',
          timeout: 5000 // 5秒超时
        })
      } catch (error) {
        console.log('⚠️ 邮件服务器测试超时或出错（这可能是正常的）')
      }
    }
    
    // 测试Office服务器
    const officeServerPath = path.join(__dirname, 'mcp-servers', 'office-bot', 'main.py')
    if (fs.existsSync(officeServerPath)) {
      console.log('📝 测试Office服务器脚本...')
      
      const testOfficeScript = `
import sys
sys.path.insert(0, r'${path.join(__dirname, 'python', 'py', 'python', 'Lib', 'site-packages')}')

try:
    exec(open(r'${officeServerPath}').read())
    print("✅ Office服务器脚本语法正确")
except Exception as e:
    print(f"❌ Office服务器脚本错误: {e}")
`;
      
      try {
        execSync(`"${pythonExe}" -c "${testOfficeScript}"`, {
          stdio: 'inherit',
          timeout: 5000 // 5秒超时
        })
      } catch (error) {
        console.log('⚠️ Office服务器测试超时或出错（这可能是正常的）')
      }
    }
    
    return true
  } catch (error) {
    console.error('❌ MCP服务器测试失败:', error.message)
    return false
  }
}

// 主执行流程
async function main() {
  try {
    console.log('🚀 === Python MCP环境配置开始 ===')
    
    // 1. 检查Python
    if (!checkPython()) {
      console.error('⛔ Python检查失败，无法继续')
      process.exit(1)
    }
    
    // 2. 安装依赖
    if (!installPythonDependencies()) {
      console.error('⛔ Python依赖安装失败')
      process.exit(1)
    }
    
    // 3. 验证安装
    if (!verifyInstallation()) {
      console.warn('⚠️ 验证失败，但继续执行')
    }
    
    // 4. 测试MCP服务器
    if (!testMCPServers()) {
      console.warn('⚠️ MCP服务器测试失败，但继续执行')
    }
    
    console.log('🎉 === Python MCP环境配置完成 ===')
    console.log('💡 现在可以启动应用并测试MCP功能')
    
  } catch (error) {
    console.error('💥 配置过程中发生致命错误:', error)
    process.exit(1)
  }
}

// 运行主程序
if (require.main === module) {
  main()
}

module.exports = {
  checkPython,
  installPythonDependencies,
  verifyInstallation,
  testMCPServers
} 
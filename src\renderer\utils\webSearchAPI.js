import { apiPost } from './apiManager.js'

/**
 * 百度千帆AI搜索配置
 */
const WEB_SEARCH_CONFIG = {
  model: 'ernie-3.5-8k',
  timeout: 120000  // 2分钟超时
}

/**
 * 创建联网搜索API客户端
 */
function createWebSearchClient() {
  console.log('🌐 联网搜索 - 使用统一API管理器')
  
  return {
    post: async (url, data) => {
      const result = await apiPost(url, data)
      if (result.success) {
        return { data: result.data }
      } else {
        throw new Error(result.error)
      }
    }
  }
}

/**
 * 执行联网搜索
 * @param {string} query - 搜索查询内容
 * @param {Object} options - 搜索选项
 * @returns {Promise<Object>} 搜索结果
 */
export async function performWebSearch(query, options = {}) {
  try {
    console.log('🌐 开始联网搜索:', query)
    
    const apiClient = createWebSearchClient()
    
    const requestData = {
      messages: [
        {
          content: query,
          role: "user"
        }
      ],
      stream: false,
      model: WEB_SEARCH_CONFIG.model,
      instruction: options.instruction || "简明扼要地回答，重点突出关键信息",
      enable_corner_markers: false,  // 关闭角标以提高速度
      enable_deep_search: false,     // 关闭深度搜索以提高速度
      resource_type_filter: options.resourceFilter || [
        { type: "web", top_k: 3 }     // 保持3个结果平衡速度和质量
      ],
      search_mode: "required",       // 强制搜索，避免判断延迟
      enable_followup_queries: false,
      temperature: 0.1,
      max_completion_tokens: 1024    // 减少生成长度以提高速度
    }

    console.log('🌐 联网搜索请求参数:', requestData)
    console.log('🌐 联网搜索请求URL:', '/api/tool/ai_search/chat/completions')

    const response = await apiClient.post('/api/tool/ai_search/chat/completions', requestData)
    
    // 处理嵌套的响应结构：response.data.data
    const responseData = response.data.data || response.data
    
    if (responseData && responseData.choices && responseData.choices.length > 0) {
      const result = {
        success: true,
        content: responseData.choices[0].message.content,
        references: responseData.references || [],
        followupQueries: responseData.followup_queries || [],
        usage: responseData.usage,
        isSafe: responseData.is_safe
      }
      
      console.log('🌐 联网搜索成功:', {
        contentLength: result.content?.length || 0,
        referencesCount: result.references.length,
        hasFollowup: result.followupQueries.length > 0
      })
      
      return result
    } else {
      console.error('🌐 联网搜索响应格式异常:', response.data)
      return {
        success: false,
        error: '搜索响应格式异常',
        content: ''
      }
    }
  } catch (error) {
    console.error('🌐 联网搜索失败:', error)
    
    // 检查具体错误类型
    if (error.response) {
      console.error('🌐 API响应错误:', {
        status: error.response.status,
        data: error.response.data
      })
      return {
        success: false,
        error: `API请求失败: ${error.response.status} - ${error.response.data?.message || '未知错误'}`,
        content: ''
      }
    } else if (error.request) {
      console.error('🌐 网络请求失败:', error.request)
      return {
        success: false,
        error: '网络连接失败，请检查网络状态',
        content: ''
      }
    } else {
      console.error('🌐 请求配置错误:', error.message)
      return {
        success: false,
        error: `请求配置错误: ${error.message}`,
        content: ''
      }
    }
  }
}

/**
 * 格式化搜索引用信息
 * @param {Array} references - 引用数组
 * @returns {string} 格式化后的引用文本
 */
export function formatSearchReferences(references) {
  if (!references || references.length === 0) {
    return ''
  }
  
  return references.map((ref, index) => {
    return `[${ref.id || index + 1}] ${ref.title}\n${ref.url}\n${ref.content ? ref.content.substring(0, 150) + '...' : ''}`
  }).join('\n\n')
}

/**
 * 检查联网搜索是否可用
 * @returns {Promise<boolean>} 是否可用
 */

export async function checkWebSearchAvailable() {
  try {
    const testResult = await performWebSearch('测试连接', {
      resourceFilter: [{ type: "web", top_k: 3 }]
    })
    return testResult.success
  } catch (error) {
    console.error('🌐 联网搜索可用性检查失败:', error)
    return false
  }
} 
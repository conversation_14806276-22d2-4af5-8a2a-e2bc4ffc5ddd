const { screen } = require('electron')
const MainWindowManager = require('./mainWindow')
const FloatingWindowManager = require('./floatingWindow')
const TrayManager = require('./tray')

/**
 * 窗口管理器 - 统一管理所有窗口和托盘
 */
class WindowManager {
  constructor(appManager) {
    this.appManager = appManager

    // 初始化各个窗口管理器
    this.mainWindowManager = new MainWindowManager(appManager)
    this.floatingWindowManager = new FloatingWindowManager(appManager)
    this.trayManager = new TrayManager(appManager)

    // 注意：不在构造函数中设置显示器监听器，因为 screen 模块需要在 app ready 后才能使用
  }

  /**
   * 设置显示器监听器
   */
  setupDisplayListeners() {
    try {
      // 监听显示器配置变化
      if (screen && typeof screen.on === 'function') {
        screen.on('display-metrics-changed', (event, display, changedMetrics) => {
          console.log('显示器配置变化:', changedMetrics)

          if (changedMetrics.includes('scaleFactor')) {
            const newScale = display.scaleFactor
            console.log(`Windows缩放比例从 ${this.appManager.displayScale * 100}% 更改为 ${newScale * 100}%`)
            this.appManager.displayScale = newScale

            // 重新应用反向缩放到已存在的窗口
            if (this.mainWindowManager.isValid()) {
              this.appManager.applyWindowScale(this.mainWindowManager.getWindow())
              // 重新调整主窗口位置
              this.mainWindowManager.reposition()
            }
            if (this.floatingWindowManager.isValid()) {
              this.appManager.applyWindowScale(this.floatingWindowManager.getWindow())
              // 重新调整悬浮窗位置
              this.floatingWindowManager.reposition()
            }
          }
        })
      } else {
        console.warn('Screen object not available, display listeners not set up')
      }
    } catch (error) {
      console.error('Failed to setup display listeners:', error)
    }
  }

  /**
   * 初始化所有窗口和托盘
   */
  initialize() {
    console.log('🪟 初始化窗口管理器...')

    // 设置显示器监听器（现在 app 已经 ready）
    this.setupDisplayListeners()

    // 创建主窗口
    this.mainWindowManager.createWindow()

    // 创建系统托盘
    this.trayManager.createTray()

    // 初始化托盘菜单状态
    this.trayManager.updateTrayMenu()

    console.log('✅ 窗口管理器初始化完成')
  }

  /**
   * 创建悬浮窗（通常在登录后调用）
   */
  createFloatingWindow() {
    if (!this.floatingWindowManager.isValid()) {
      this.floatingWindowManager.createWindow()
    } else {
      this.floatingWindowManager.show()
    }
  }

  /**
   * 设置悬浮窗拖拽状态
   */
  setFloatingWindowDragging(isDragging) {
    this.floatingWindowManager.setDragging(isDragging)
  }

  /**
   * 处理悬浮窗高度调整
   */
  handleFloatingWindowHeightAdjust(newHeight) {
    if (!this.floatingWindowManager.isValid()) return

    const window = this.floatingWindowManager.getWindow()
    const currentBounds = window.getBounds()
    const { width: screenWidth, height: screenHeight } = screen.getPrimaryDisplay().workAreaSize
    
    // 预期的高度值
    const expectedHeights = [290, 400, 500, 600, 700, 800]
    const tolerance = 10
    const isExpectedHeight = expectedHeights.some(h => Math.abs(newHeight - h) <= tolerance)
    
    // 计算高度差异
    const heightDiff = Math.abs(newHeight - currentBounds.height)
    
    // 拖拽状态检查
    if (this.floatingWindowManager._isFloatingWindowDragging) {
      // 拖拽过程中，只允许预期的高度调整
      if (!isExpectedHeight) {
        console.log('⚠️ 拖拽过程中检测到非预期的窗口大小调整，忽略:', {
          currentHeight: currentBounds.height,
          newHeight: newHeight,
          isExpectedHeight: isExpectedHeight,
          timestamp: new Date().toISOString()
        })
        return
      }
    } else {
      // 非拖拽过程中，允许正常的高度调整
      console.log('✅ 非拖拽过程中，允许窗口高度调整:', {
        currentHeight: currentBounds.height,
        newHeight: newHeight,
        heightDiff: heightDiff,
        isExpectedHeight: isExpectedHeight
      })
    }
    
    // 智能定位：优化聊天框展开时的位置计算
    if (heightDiff > 5) { // 只有当高度变化超过5像素时才调整位置
      const margin = 20
      let newY, newX = currentBounds.x

      // 判断是展开还是收缩
      const isExpanding = newHeight > currentBounds.height

      if (isExpanding) {
        // 展开时：优先保持顶部位置，如果空间不够再向上调整
        newY = currentBounds.y

        // 检查是否有足够空间向下展开
        if (newY + newHeight + margin > screenHeight) {
          // 空间不够，向上调整位置
          newY = screenHeight - newHeight - margin
          // 确保不超出屏幕顶部
          newY = Math.max(margin, newY)
        }
      } else {
        // 收缩时：保持底部位置不变
        newY = currentBounds.y + (currentBounds.height - newHeight)

        // 边界检查
        const minY = margin
        const maxY = screenHeight - newHeight - margin
        newY = Math.max(minY, Math.min(newY, maxY))
      }

      // X坐标边界检查
      const maxX = screenWidth - currentBounds.width - margin
      const minX = -currentBounds.width * 0.8 // 允许部分超出屏幕
      newX = Math.max(minX, Math.min(newX, maxX))
      
      console.log('🔄 调整悬浮窗位置:', {
        action: isExpanding ? '展开聊天框' : '收缩聊天框',
        oldPosition: { x: currentBounds.x, y: currentBounds.y },
        newPosition: { x: newX, y: newY },
        oldSize: { width: currentBounds.width, height: currentBounds.height },
        newSize: { width: currentBounds.width, height: newHeight },
        screenSize: { width: screenWidth, height: screenHeight }
      })
      
      // 设置新位置和大小
      window.setBounds({
        x: newX,
        y: newY,
        width: currentBounds.width,
        height: newHeight
      })
    }
  }

  /**
   * 更新托盘菜单状态
   */
  updateTrayMenu() {
    this.trayManager.updateTrayMenu()
  }

  /**
   * 显示主窗口
   */
  showMainWindow() {
    this.mainWindowManager.show()
  }

  /**
   * 隐藏主窗口
   */
  hideMainWindow() {
    this.mainWindowManager.hide()
  }

  /**
   * 显示悬浮窗
   */
  showFloatingWindow() {
    this.floatingWindowManager.show()
  }

  /**
   * 隐藏悬浮窗
   */
  hideFloatingWindow() {
    this.floatingWindowManager.hide()
  }

  /**
   * 获取主窗口实例
   */
  getMainWindow() {
    return this.mainWindowManager.getWindow()
  }

  /**
   * 获取悬浮窗实例
   */
  getFloatingWindow() {
    return this.floatingWindowManager.getWindow()
  }

  /**
   * 获取托盘实例
   */
  getTray() {
    return this.trayManager.getTray()
  }

  /**
   * 检查主窗口是否有效
   */
  isMainWindowValid() {
    return this.mainWindowManager.isValid()
  }

  /**
   * 检查悬浮窗是否有效
   */
  isFloatingWindowValid() {
    return this.floatingWindowManager.isValid()
  }

  /**
   * 应用窗口缩放
   */
  applyWindowScale(window) {
    return this.appManager.applyWindowScale(window)
  }

  /**
   * 重新定位所有窗口
   */
  repositionAllWindows() {
    if (this.mainWindowManager.isValid()) {
      this.mainWindowManager.reposition()
    }
    if (this.floatingWindowManager.isValid()) {
      this.floatingWindowManager.reposition()
    }
  }

  /**
   * 销毁所有窗口和托盘
   */
  destroy() {
    console.log('🪟 销毁窗口管理器...')
    
    this.mainWindowManager.destroy()
    this.floatingWindowManager.destroy()
    this.trayManager.destroy()
    
    console.log('✅ 窗口管理器已销毁')
  }
}

module.exports = WindowManager

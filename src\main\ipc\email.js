const { ipcMain } = require('electron')
const Store = require('electron-store')

/**
 * 邮件相关的IPC处理程序
 */
class EmailIPCHandler {
  constructor(appManager) {
    this.appManager = appManager
    this.store = new Store()
  }

  /**
   * 注册所有邮件相关的IPC处理程序
   */
  register() {
    // 获取邮件配置
    ipcMain.handle('get-email-config', async (event) => {
      try {
        const emailConfig = this.store.get('emailConfig', null)
        return { success: true, config: emailConfig }
      } catch (error) {
        return { success: false, error: error.message }
      }
    })

    // 保存邮件配置
    ipcMain.handle('save-email-config', async (event, config) => {
      if (this.appManager.emailService) {
        return await this.appManager.emailService.saveEmailConfig(config)
      } else {
        return { success: false, error: '电子邮件服务未初始化' }
      }
    })

    // 测试邮件配置
    ipcMain.handle('test-email-config', async (event, config) => {
      if (this.appManager.emailService) {
        return await this.appManager.emailService.testEmailConfig(config)
      } else {
        return { success: false, error: '电子邮件服务未初始化' }
      }
    })

    // 删除邮件配置
    ipcMain.handle('delete-email-config', async () => {
      if (this.appManager.emailService) {
        return await this.appManager.emailService.deleteEmailConfig()
      } else {
        return { success: false, error: '电子邮件服务未初始化' }
      }
    })

    console.log('✅ 邮件相关IPC处理程序已注册')
  }
}

module.exports = EmailIPCHandler

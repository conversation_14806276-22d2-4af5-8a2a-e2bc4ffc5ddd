<template>
  <div v-if="visible" class="dialog-overlay" @click="handleOverlayClick">
    <div class="dialog-content" @click.stop>
      <div class="dialog-header">
        <div class="dialog-icon" :class="iconClass">
          <span>{{ iconText }}</span>
        </div>
        <h3 class="dialog-title">{{ title }}</h3>
      </div>
      
      <div class="dialog-body">
        <p class="dialog-message">{{ message }}</p>
      </div>
      
      <div class="dialog-footer">
        <button 
          v-if="type === 'confirm'"
          @click="handleCancel" 
          class="dialog-btn secondary"
          :disabled="loading"
        >
          {{ cancelText }}
        </button>
        <button 
          @click="handleConfirm" 
          class="dialog-btn primary"
          :class="{ danger: isDanger }"
          :disabled="loading"
        >
          {{ confirmText }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, computed } from 'vue'

export default {
  name: 'CustomDialog',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    type: {
      type: String,
      default: 'alert', // 'alert' | 'confirm'
      validator: (value) => ['alert', 'confirm'].includes(value)
    },
    title: {
      type: String,
      default: '提示'
    },
    message: {
      type: String,
      required: true
    },
    confirmText: {
      type: String,
      default: '确定'
    },
    cancelText: {
      type: String,
      default: '取消'
    },
    variant: {
      type: String,
      default: 'info', // 'info' | 'success' | 'warning' | 'error'
      validator: (value) => ['info', 'success', 'warning', 'error'].includes(value)
    },
    closeOnOverlay: {
      type: Boolean,
      default: true
    },
    loading: {
      type: Boolean,
      default: false
    }
  },
  emits: ['confirm', 'cancel', 'close'],
  setup(props, { emit }) {
    const iconClass = computed(() => {
      return `dialog-icon-${props.variant}`
    })
    
    const iconText = computed(() => {
      const icons = {
        info: 'ℹ️',
        success: '✅',
        warning: '⚠️',
        error: '❌'
      }
      return icons[props.variant] || icons.info
    })
    
    const isDanger = computed(() => {
      return props.variant === 'error' || props.variant === 'warning'
    })

    const handleConfirm = () => {
      emit('confirm')
      emit('close')
    }

    const handleCancel = () => {
      emit('cancel')
      emit('close')
    }

    const handleOverlayClick = () => {
      if (props.closeOnOverlay) {
        emit('close')
      }
    }

    return {
      iconClass,
      iconText,
      isDanger,
      handleConfirm,
      handleCancel,
      handleOverlayClick
    }
  }
}
</script>

<style lang="scss" scoped>
.dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
  animation: fadeIn 0.2s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-content {
  background: var(--modal-bg);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color-light);
  border-radius: 16px;
  width: 100%;
  max-width: 480px;
  margin: 20px;
  overflow: hidden;
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dialog-header {
  padding: 24px 24px 16px;
  text-align: center;
  background: var(--modal-header-bg);
}

.dialog-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 16px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28px;
  
  &.dialog-icon-info {
    background: rgba(59, 130, 246, 0.1);
    border: 2px solid rgba(59, 130, 246, 0.2);
  }
  
  &.dialog-icon-success {
    background: rgba(34, 197, 94, 0.1);
    border: 2px solid rgba(34, 197, 94, 0.2);
  }
  
  &.dialog-icon-warning {
    background: rgba(245, 158, 11, 0.1);
    border: 2px solid rgba(245, 158, 11, 0.2);
  }
  
  &.dialog-icon-error {
    background: rgba(239, 68, 68, 0.1);
    border: 2px solid rgba(239, 68, 68, 0.2);
  }
}

.dialog-title {
  margin: 0;
  font-size: 20px;
  font-weight: 600;
  color: var(--text-dark);
}

.dialog-body {
  padding: 0 24px 24px;
  text-align: center;
}

.dialog-message {
  margin: 0;
  font-size: 16px;
  line-height: 1.5;
  color: var(--text-secondary);
  white-space: pre-wrap;
}

.dialog-footer {
  padding: 16px 24px 24px;
  display: flex;
  gap: 12px;
  justify-content: center;
}

.dialog-btn {
  padding: 12px 24px;
  border: 1px solid var(--border-color-dark);
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 80px;
  
  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
  
  &.secondary {
    background: var(--btn-secondary-bg);
    color: var(--text-dark);
    border-color: var(--border-color-dark);
    
    &:hover:not(:disabled) {
      background: var(--btn-secondary-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
  }
  
  &.primary {
    background: var(--btn-primary-bg);
    color: var(--text-inverse);
    border-color: var(--btn-primary-bg);
    
    &:hover:not(:disabled) {
      background: var(--btn-primary-hover);
      border-color: var(--btn-primary-hover);
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(59, 130, 246, 0.3);
    }
    
    &.danger {
      background: var(--btn-danger-bg);
      border-color: var(--btn-danger-bg);
      
      &:hover:not(:disabled) {
        background: var(--btn-danger-hover);
        border-color: var(--btn-danger-hover);
        box-shadow: 0 4px 12px rgba(239, 68, 68, 0.3);
      }
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .dialog-content {
    max-width: 90vw;
    margin: 10px;
  }
  
  .dialog-header {
    padding: 20px 20px 12px;
  }
  
  .dialog-icon {
    width: 56px;
    height: 56px;
    font-size: 24px;
    margin-bottom: 12px;
  }
  
  .dialog-title {
    font-size: 18px;
  }
  
  .dialog-body {
    padding: 0 20px 20px;
  }
  
  .dialog-message {
    font-size: 15px;
  }
  
  .dialog-footer {
    padding: 12px 20px 20px;
    flex-direction: column;
    
    .dialog-btn {
      width: 100%;
    }
  }
}

// 暗黑主题
.dark-theme .dialog-content {
  box-shadow: 
    0 20px 25px -5px rgba(0, 0, 0, 0.3),
    0 10px 10px -5px rgba(0, 0, 0, 0.2),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

// 加载状态
.dialog-btn:disabled {
  position: relative;
  
  &::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 16px;
    height: 16px;
    margin: -8px 0 0 -8px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
  }
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}
</style> 
import axios from 'axios'
import { getChatConfig, getChatSettings } from './config/modelConfig.js'
import modelManager from './modelManager.js'

// 获取用户token
function getUserToken() {
  try {
    return localStorage.getItem('userAuthToken') || ''
  } catch (error) {
    console.error('获取用户token失败:', error)
    return ''
  }
}

// 获取配置并创建 axios 实例
function createApiClient() {
  const config = getChatConfig()
  const modelConfig = modelManager.getModelConfig()
  
  // 所有模型都使用同一个地址和用户token认证
  const headers = {
    'Content-Type': 'application/json'
  }
  
  // 获取用户token进行认证
  const userToken = getUserToken()
  if (userToken) {
    headers['Authorization'] = `Bearer ${userToken}`
  }
  
  return axios.create({
    baseURL: modelConfig.baseURL,
    headers: headers,
    timeout: config.timeout
  })
}

/**
 * 解析可能包含嵌套JSON的content
 * @param {string} content - 原始content内容
 * @returns {string} 解析后的content
 */
function parseNestedContent(content) {
  try {
    // 如果content不是字符串，直接返回
    if (typeof content !== 'string') {
      return content || ''
    }

    // 尝试解析JSON
    const parsed = JSON.parse(content)
    
    // 如果解析成功且包含content字段，则返回内层的content
    if (parsed && typeof parsed === 'object' && parsed.content) {
      console.log('🔄 检测到嵌套JSON格式，提取内层content:', parsed.content)
      return parsed.content
    }
    
    // 如果不包含content字段但解析成功，返回原内容
    return content
  } catch (error) {
    // 如果不是JSON格式，直接返回原内容
    return content
  }
}

/**
 * 发送聊天请求到大模型
 * @param {Array} messages - 消息历史数组，格式：[{role: 'user'|'assistant'|'system', content: string}]
 * @param {Object} options - 可选参数
 * @returns {Promise} API 响应
 */
export async function sendChatRequest(messages, options = {}) {
  try {
    const config = getChatConfig()
    const modelConfig = modelManager.getModelConfig()
    const apiClient = createApiClient()
    
    // 构建请求数据
    const requestData = {
      messages: messages,
      max_tokens: options.maxTokens || config.maxTokens,
      temperature: options.temperature || config.temperature,
      stream: false
    }
    
    // 根据模型类型设置不同的参数
    if (modelConfig.isSystemDefault) {
      // 系统默认模型：model传空字符串
      requestData.model = ''
    } else {
      // 其他模型：传具体的model名称
      requestData.model = modelConfig.id
    }

    console.log('发送聊天请求:', {
      model: modelConfig.name,
      baseURL: modelConfig.baseURL,
      requestData: requestData
    })

    const response = await apiClient.post('/chat/completions', requestData)

    console.log('收到聊天响应:', response.data)

    // 处理嵌套的响应结构：response.data.data
    const responseData = response.data.data || response.data
    
    if (responseData && responseData.choices && responseData.choices.length > 0) {
      const rawContent = responseData.choices[0].message.content
      const parsedContent = parseNestedContent(rawContent)
      
      return {
        success: true,
        message: parsedContent.trim(),
        usage: responseData.usage
      }
    } else {
      throw new Error('API 响应格式不正确')
    }
  } catch (error) {
    console.error('聊天 API 错误:', error)

    let errorMessage = '抱歉，我现在无法回答您的问题。'

    if (error.response) {
      // 服务器响应了错误状态码
      if (error.response.status === 401) {
        errorMessage = '认证失败，请检查登录状态。'
      } else if (error.response.status === 429) {
        errorMessage = '请求过于频繁，请稍后再试。'
      } else if (error.response.status >= 500) {
        errorMessage = '服务器错误，请稍后再试。'
      }
    } else if (error.request) {
      // 请求发出但没有收到响应
      errorMessage = '网络连接失败，请检查网络设置。'
    }

    return {
      success: false,
      message: errorMessage,
      error: error.message
    }
  }
}

/**
 * 创建系统提示消息
 * @param {string} systemPrompt - 系统提示内容
 * @returns {Object} 系统消息对象
 */
export function createSystemMessage(systemPrompt) {
  return {
    role: 'system',
    content: systemPrompt
  }
}

/**
 * 创建用户消息
 * @param {string} content - 用户输入内容
 * @returns {Object} 用户消息对象
 */
export function createUserMessage(content) {
  return {
    role: 'user',
    content: content,
    timestamp: Date.now()
  }
}

/**
 * 创建助手消息
 * @param {string} content - 助手回复内容
 * @returns {Object} 助手消息对象
 */
export function createAssistantMessage(content) {
  return {
    role: 'assistant',
    content: content,
    timestamp: Date.now()
  }
}

/**
 * 获取默认的系统提示
 * @returns {string} 默认系统提示
 */
export function getDefaultSystemPrompt() {
  const settings = getChatSettings()
  return settings.systemPrompt
} 
import { defineStore } from 'pinia'
import CryptoJS from 'crypto-js'
import { ref } from 'vue'
import { apiPost, apiGet } from '../utils/apiManager.js'
import { getApiBaseUrl } from '../utils/apiConfig.js'

export const useAuthStore = defineStore('auth', {
  state: () => ({
    isLoggedIn: false,
    user: null,
    token: null,
    loginError: null,
    isCheckingLoginStatus: false // 防止重复检查登录状态
  }),

  actions: {
    // 计算签名
    calculateSign(code, redirectUri, ts) {
      const signKey = '56add80b-1c6f-4674-9754-e6f0cdda310b'
      const signData = `${code}${redirectUri}${ts}${signKey}`
      return CryptoJS.SHA256(signData).toString()
    },

    // 账密登录
    async passwordLogin(loginData) {
      try {
        const result = await apiPost('/login', loginData, {
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'ClientName': 'PC'
            // 移除浏览器自动设置的头部：Origin, Referer, User-Agent
          }
        })
        
        if (result.success && result.data.token) {
          // 登录成功，缓存token
          this.token = result.data.token
          this.isLoggedIn = true
          this.user = {
            username: loginData.username,
            loginTime: new Date().toISOString(),
            loginType: 'password'
          }
          this.loginError = null
          
          // 缓存到localStorage
          localStorage.setItem('userAuthToken', result.data.token)
          localStorage.setItem('userAuthInfo', JSON.stringify(this.user))
          
          // 🔄 【修复】通知主进程登录状态变化
          await this.notifyMainProcessLogin(true)
          
          return { success: true, message: '登录成功' }
        } else {
          this.loginError = result.error || '登录失败'
          return { success: false, message: result.error || '登录失败' }
        }
      } catch (error) {
        console.error('账密登录错误:', error)
        this.loginError = '登录过程中发生错误'
        return { success: false, message: '登录过程中发生错误' }
      }
    },

    // 第三方登录 - 使用code换取token
    async ssoLogin(code) {
      try {
        console.log('🔗 收到授权码，开始换取token:', code)
        
        const redirectUri = 'ai-cognidesk://auth/sso/callback'
        const ts = Date.now().toString()
        const sign = this.calculateSign(code, redirectUri, ts)
        
        const result = await apiPost('/login-by-code', {
          code,
          redirectUri,
          sign,
          ts
        }, {
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'ClientName': 'PC'
            // 移除浏览器自动设置的头部：Origin, Referer, User-Agent
          }
        })

        console.log('🔗 API响应:', result)
        
        if (response.ok && result.code === 200 && result.token) {
          // 登录成功，获取用户信息
          const userInfo = await this.getUserInfo(result.token)
          
          if (userInfo.success) {
            this.token = result.token
            this.isLoggedIn = true
            this.user = {
              username: userInfo.data.nickName || userInfo.data.phonenumber || userInfo.data.userName,
              nickname: userInfo.data.nickName,
              mobile: userInfo.data.phonenumber,
              userId: userInfo.data.userId,
              tenantId: userInfo.data.tenantId,
              permissions: userInfo.data.permissions,
              roles: userInfo.data.roles,
              loginTime: new Date().toISOString(),
              loginType: 'sso',
              code: code // 保存授权码以便后续使用
            }
            this.loginError = null
            
            // 缓存到localStorage
            localStorage.setItem('userAuthToken', result.token)
            localStorage.setItem('userAuthInfo', JSON.stringify(this.user))
            
            // 🔄 通知主进程登录状态变化
            await this.notifyMainProcessLogin(true)
            
            console.log('🔗 SSO登录成功:', this.user)
            return { success: true, message: '登录成功' }
          } else {
            this.loginError = userInfo.message || '获取用户信息失败'
            console.error('🔗 获取用户信息失败:', userInfo.message)
            return { success: false, message: userInfo.message || '获取用户信息失败' }
          }
        } else {
          this.loginError = result.msg || result.message || '第三方登录失败'
          console.error('🔗 第三方登录失败:', result)
          return { success: false, message: result.msg || result.message || '第三方登录失败' }
        }
      } catch (error) {
        console.error('🔗 第三方登录错误:', error)
        this.loginError = '第三方登录过程中发生错误'
        return { success: false, message: '第三方登录过程中发生错误' }
      }
    },

    // 获取用户信息
    async getUserInfo(token) {
      try {
        console.log('🔗 获取用户信息，token:', token ? token.substring(0, 20) + '...' : 'null')
        
        const result = await apiGet('/getInfo', {}, {
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'ClientName': 'PC'
            // 移除浏览器自动设置的头部：Origin, Referer, User-Agent
          }
        })

        console.log('🔗 用户信息API响应:', result)
        
        if (result.success) {
          return { 
            success: true, 
            data: {
              ...result.data.user,
              permissions: result.data.permissions,
              roles: result.data.roles
            }
          }
        } else {
          console.error('🔗 获取用户信息失败:', result.error)
          return { success: false, message: result.error || '获取用户信息失败' }
        }
      } catch (error) {
        console.error('🔗 获取用户信息错误:', error)
        return { success: false, message: '获取用户信息失败' }
      }
    },

    // 验证token是否过期
    async verifyToken() {
      try {
        const token = localStorage.getItem('userAuthToken')
        if (!token) {
          console.log('🔗 未找到token，需要重新登录')
          return { success: false, message: '未找到token' }
        }

        console.log('🔗 验证token:', token.substring(0, 20) + '...')
        
        const result = await apiGet('/getInfo', {}, {
          headers: {
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'ClientName': 'PC'
            // 移除浏览器自动设置的头部：Origin, Referer, User-Agent
          }
        })

        console.log('🔗 token验证响应:', result)
        
        if (result.success && result.data && result.data.code === 200) {
          // token有效，恢复登录状态
          this.token = token
          this.isLoggedIn = true
          
          // 尝试从localStorage恢复用户信息
          const userInfo = localStorage.getItem('userAuthInfo')
          if (userInfo) {
            this.user = JSON.parse(userInfo)
            console.log('🔗 从缓存恢复用户信息:', this.user)
          } else {
            // 如果没有用户信息，从API获取
            const userInfoResult = await this.getUserInfo(token)
            if (userInfoResult.success) {
              this.user = {
                username: userInfoResult.data.nickName || userInfoResult.data.phonenumber || userInfoResult.data.userName,
                nickname: userInfoResult.data.nickName,
                mobile: userInfoResult.data.phonenumber,
                userId: userInfoResult.data.userId,
                tenantId: userInfoResult.data.tenantId,
                permissions: userInfoResult.data.permissions,
                roles: userInfoResult.data.roles,
                loginTime: new Date().toISOString(),
                loginType: 'auto'
              }
              localStorage.setItem('userAuthInfo', JSON.stringify(this.user))
              console.log('🔗 从API获取用户信息:', this.user)
            }
          }
          
          // 🔄 通知主进程登录状态变化
          await this.notifyMainProcessLogin(true)
          
          console.log('🔗 token验证成功')
          return { success: true, message: 'token有效' }
        } else {
          // token过期，清除缓存
          console.log('🔗 token已过期，清除认证信息')
          this.clearAuth()
          return { success: false, message: result.msg || result.message || 'token已过期' }
        }
      } catch (error) {
        console.error('🔗 验证token错误:', error)
        this.clearAuth()
        return { success: false, message: '验证token失败' }
      }
    },

    // 清除认证信息
    async clearAuth() {
      console.log('🔗 清除认证信息')
      this.isLoggedIn = false
      this.user = null
      this.token = null
      this.loginError = null
      
      // 清除所有可能的认证相关存储
      localStorage.removeItem('userAuthToken')
      localStorage.removeItem('userAuthInfo')
      localStorage.removeItem('agreementAccepted')
      localStorage.removeItem('userAuthenticatedStatus')
      localStorage.removeItem('userAuthCredentials')
      
      // 清除sessionStorage
      sessionStorage.clear()
      
      // 清除用户ID
      localStorage.removeItem('nezha_user_id')
      
      // 通知主进程清除登录状态
      try {
        await this.notifyMainProcessLogin(false)
        // 额外调用主进程的logout方法确保彻底清除
        if (window.electronAPI && window.electronAPI.invoke) {
          await window.electronAPI.invoke('logout')
        }
      } catch (error) {
        console.error('🔗 通知主进程清除认证信息失败:', error)
      }
    },

    // 🔄 通知主进程登录状态变化
    async notifyMainProcessLogin(isLoggedIn) {
      try {
        if (window.electronAPI && window.electronAPI.invoke) {
          // 🔄 确保传递的数据是可序列化的纯对象，深度清理复杂对象
          const loginData = {
            isLoggedIn: isLoggedIn,
            user: this.user ? {
              username: this.user.username || null,
              nickname: this.user.nickname || null,
              mobile: this.user.mobile || null,
              userId: this.user.userId || null,
              tenantId: this.user.tenantId || null,
              // 序列化权限数组，只保留字符串
              permissions: Array.isArray(this.user.permissions) 
                ? this.user.permissions.filter(p => typeof p === 'string')
                : [],
              // 序列化角色数组，只保留基本信息
              roles: Array.isArray(this.user.roles) 
                ? this.user.roles.map(role => ({
                    roleId: role.roleId,
                    roleName: role.roleName,
                    roleKey: role.roleKey
                  }))
                : [],
              loginTime: this.user.loginTime || null,
              loginType: this.user.loginType || null,
              code: this.user.code || null
            } : null,
            token: typeof this.token === 'string' ? this.token : null
          }
          
          // 使用通用的 invoke 方法调用主进程
          await window.electronAPI.invoke('set-login-status', loginData)
          console.log('🔄 已通知主进程登录状态:', isLoggedIn, loginData.user ? loginData.user.username : '无用户信息')
        } else {
          console.warn('🔄 electronAPI不可用，无法通知主进程')
        }
      } catch (error) {
        console.error('🔄 通知主进程登录状态失败:', error)
      }
    },

    // 兼容旧的登录方法
    async login(credentials) {
      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.login(credentials)
          
          if (result.success) {
            this.isLoggedIn = true
            this.user = {
              username: credentials.username,
              loginTime: new Date().toISOString(),
              loginType: 'legacy'
            }
            this.loginError = null
            return { success: true, message: result.message }
          } else {
            this.loginError = result.message
            return { success: false, message: result.message }
          }
        }
      } catch (error) {
        console.error('登录错误:', error)
        this.loginError = '登录过程中发生错误'
        return { success: false, message: '登录过程中发生错误' }
      }
    },

    async logout() {
      try {
        console.log('🚪 开始执行退出登录')
        
        // 先清除本地状态，防止状态不一致
        this.isLoggedIn = false
        this.user = null
        this.token = null
        this.loginError = null
        
        // 调用主进程的登出API
        if (window.electronAPI && window.electronAPI.invoke) {
          try {
            await window.electronAPI.invoke('logout')
            console.log('🚪 主进程登出API调用成功')
          } catch (error) {
            console.warn('🚪 主进程登出API调用失败:', error)
          }
        }
        
        // 清除所有认证信息
        await this.clearAuth()
        
        // 强制清除所有相关的localStorage
        const keysToRemove = [
          'userAuthToken',
          'userAuthInfo', 
          'agreementAccepted',
          'userAuthenticatedStatus',
          'userAuthCredentials',
          'nezha_user_id'
        ]
        
        keysToRemove.forEach(key => {
          localStorage.removeItem(key)
          console.log('🧹 清除localStorage:', key)
        })
        
        // 清除所有可能的认证相关localStorage
        Object.keys(localStorage).forEach(key => {
          if (key.includes('auth') || key.includes('login') || key.includes('user') || key.includes('token')) {
            localStorage.removeItem(key)
            console.log('🧹 清除相关localStorage:', key)
          }
        })
        
        // 清除sessionStorage
        sessionStorage.clear()
        console.log('🧹 sessionStorage已清除')
        
        // 清除可能的缓存
        if ('caches' in window) {
          try {
            const cacheNames = await caches.keys()
            await Promise.all(cacheNames.map(name => caches.delete(name)))
            console.log('🚪 浏览器缓存已清除')
          } catch (error) {
            console.warn('🚪 清除浏览器缓存失败:', error)
          }
        }
        
        // 强制刷新页面以确保状态完全清除
        setTimeout(() => {
          window.location.reload()
        }, 100)
        
        console.log('🚪 退出登录完成，页面将在100ms后刷新')
        
        return { success: true, message: '退出登录成功' }
      } catch (error) {
        console.error('🚪 退出登录错误:', error)
        // 即使出错也要清除本地状态
        this.isLoggedIn = false
        this.user = null
        this.token = null
        this.loginError = null
        
        // 强制清除存储
        localStorage.clear()
        sessionStorage.clear()
        
        // 强制刷新页面
        setTimeout(() => {
          window.location.reload()
        }, 100)
        
        return { success: false, message: '退出登录失败' }
      }
    },

    async checkLoginStatus() {
      // 防止重复检查
      if (this.isCheckingLoginStatus) {
        console.log('🔗 正在检查登录状态，跳过重复调用')
        return
      }
      
      this.isCheckingLoginStatus = true
      
      try {
        console.log('🔗 开始检查登录状态')
        
        // 首先检查token是否有效
        const tokenResult = await this.verifyToken()
        if (tokenResult.success) {
          console.log('🔗 token验证成功，登录状态已恢复')
          return
        }

        // 如果token验证失败，清除认证信息，不再使用旧的方式
        console.log('🔗 token验证失败，清除所有认证信息')
        await this.clearAuth()
        
      } catch (error) {
        console.error('🔗 检查登录状态错误:', error)
        await this.clearAuth()
      } finally {
        this.isCheckingLoginStatus = false
      }
    },

    clearError() {
      this.loginError = null
    }
  }
}) 
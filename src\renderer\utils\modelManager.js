/**
 * 模型管理工具
 * 用于管理和切换不同的大模型
 */

import { getApiUrl } from './apiConfig.js'

// 本地存储键名
const STORAGE_KEY = 'nezha-model-name'

// 可用模型列表
const MODEL_LIST = [
  { id: 'system-default', name: '系统默认', baseURL: getApiUrl('/api'), apiKey: '', isSystemDefault: true },
  { id: 'Qwen/Qwen2.5-72B-Instruct-128K', name: '硅基流动千问2.5', baseURL: getApiUrl('/api'), apiKey: '' },
  { id: 'qwen2.5-72b', name: '电信千问2.5', baseURL: getApiUrl('/api'), apiKey: '' },
  { id: 'qwen-max-latest', name: '阿里云百炼千问MAX', baseURL: getApiUrl('/api'), apiKey: '' },
  { id: 'Pro/deepseek-ai/DeepSeek-V3', name: 'DeepSeek V3 Pro', baseURL: getApiUrl('/api'), apiKey: '' }
]

/**
 * 模型管理器
 */
export default {
  /**
   * 获取当前选择的模型ID
   * @returns {String} 模型ID
   */
  getModelId() {
    try {
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        const config = JSON.parse(stored)
        return config.modelId || 'system-default'
      }
      return 'system-default' // 默认模型改为系统默认
    } catch (error) {
      console.error('获取模型ID失败:', error)
      return 'system-default'
    }
  },

  /**
   * 获取当前模型名称
   * @returns {String} 模型显示名称
   */
  getModelName() {
    const id = this.getModelId()
    const model = MODEL_LIST.find(m => m.id === id)
    return model ? model.name : '未知模型'
  },

  /**
   * 获取可用模型列表
   * @returns {Array} 模型列表
   */
  getModelList() {
    return MODEL_LIST
  },

  /**
   * 设置当前模型
   * @param {String} modelId - 模型ID
   * @returns {Boolean} 是否设置成功
   */
  setModel(modelId) {
    try {
      // 验证模型ID是否有效
      if (!MODEL_LIST.some(m => m.id === modelId)) {
        throw new Error('无效的模型ID: ' + modelId)
      }
      
      // 获取现有配置
      let config = {}
      const stored = localStorage.getItem(STORAGE_KEY)
      if (stored) {
        config = JSON.parse(stored)
      }
      
      // 更新模型ID
      config.modelId = modelId
      
      // 保存配置
      localStorage.setItem(STORAGE_KEY, JSON.stringify(config))
      
      console.log('模型已切换为:', this.getModelName())
      return true
    } catch (error) {
      console.error('设置模型失败:', error)
      return false
    }
  },

  /**
   * 获取当前模型的配置
   * @returns {Object} 模型配置
   */
  getModelConfig() {
    const modelId = this.getModelId()
    const model = MODEL_LIST.find(m => m.id === modelId) || MODEL_LIST[0]
    
    return {
      id: model.id,
      name: model.name,
      baseURL: model.baseURL,
      apiKey: model.apiKey,
      isSystemDefault: model.isSystemDefault || false
    }
  },

  /**
   * 检查当前模型是否为系统默认
   * @returns {Boolean} 是否为系统默认模型
   */
  isSystemDefault() {
    const modelId = this.getModelId()
    return modelId === 'system-default'
  }
} 
/**
 * 用户身份管理模块
 * 提供用户ID和会话ID的生成与管理功能
 */

/**
 * 获取用户标识符
 * @returns {string} 用户唯一标识符
 */
export function getUserIdentifier() {
  // 1. 优先从本地存储获取已有的用户ID
  let userId = localStorage.getItem('nezha_user_id')
  
  if (!userId) {
    // 2. 如果没有，生成新的用户ID
    userId = generateUserId()
    localStorage.setItem('nezha_user_id', userId)
    console.log('🆔 生成新的用户ID:', userId)
  } else {
    console.log('🆔 使用已有的用户ID:', userId)
  }
  
  return userId
}

/**
 * 生成用户ID
 * @returns {string} 唯一的用户标识符
 */
function generateUserId() {
  // 使用时间戳 + 随机数生成唯一ID
  const timestamp = Date.now()
  const randomStr = Math.random().toString(36).substring(2, 15)
  const deviceInfo = getDeviceInfo()
  
  // 格式: nezha_[设备类型]_[时间戳]_[随机字符串]
  return `nezha_${deviceInfo}_${timestamp}_${randomStr}`
}

/**
 * 获取设备信息
 * @returns {string} 设备类型标识
 */
function getDeviceInfo() {
  const platform = navigator.platform.toLowerCase()
  const userAgent = navigator.userAgent.toLowerCase()
  
  // 简化的设备识别
  if (platform.includes('win')) return 'win'
  if (platform.includes('mac')) return 'mac'
  if (platform.includes('linux')) return 'linux'
  if (userAgent.includes('mobile')) return 'mobile'
  
  return 'unknown'
}

/**
 * 获取会话标识符
 * @returns {string} 当前会话的唯一标识符
 */
export function getSessionIdentifier() {
  // 每次页面加载生成新的会话ID
  if (!window.nezhaSessionId) {
    const timestamp = Date.now()
    const randomStr = Math.random().toString(36).substring(2, 10)
    window.nezhaSessionId = `session_${timestamp}_${randomStr}`
    console.log('🆔 生成新的会话ID:', window.nezhaSessionId)
  }
  
  return window.nezhaSessionId
}

/**
 * 重置用户ID（生成新的用户ID）
 */
export function resetUserIdentifier() {
  const newUserId = generateUserId()
  localStorage.setItem('nezha_user_id', newUserId)
  console.log('🆔 重置用户ID:', newUserId)
  return newUserId
}

/**
 * 清除用户ID
 */
export function clearUserIdentifier() {
  localStorage.removeItem('nezha_user_id')
  console.log('🆔 已清除用户ID')
}

/**
 * 获取用户身份信息摘要
 * @returns {Object} 用户身份信息
 */
export function getUserInfo() {
  return {
    userId: getUserIdentifier(),
    sessionId: getSessionIdentifier(),
    deviceInfo: getDeviceInfo(),
    platform: navigator.platform,
    userAgent: navigator.userAgent,
    timestamp: Date.now()
  }
}

/**
 * 为API请求添加用户标识字段
 * @param {Object} requestData - 原始请求数据
 * @param {Object} options - 可选配置
 * @returns {Object} 增强后的请求数据
 */
export function enhanceRequestWithUserInfo(requestData, options = {}) {
  const enhanced = { ...requestData }
  
  // 添加用户ID
  if (options.includeUserId !== false) {
    enhanced.user = getUserIdentifier()
  }
  
  // 可选：添加会话ID到metadata或自定义字段
  if (options.includeSessionId) {
    enhanced.metadata = {
      ...enhanced.metadata,
      sessionId: getSessionIdentifier()
    }
  }
  
  return enhanced
}

// 在控制台提供调试工具
if (typeof window !== 'undefined') {
  window.nezhaUserIdentity = {
    getUserInfo,
    resetUser: resetUserIdentifier,
    clearUser: clearUserIdentifier,
    getUser: getUserIdentifier,
    getSession: getSessionIdentifier
  }
} 
// 启动浏览器MCP服务的简化脚本
const path = require('path');
const os = require('os');
const fs = require('fs');
const { execSync } = require('child_process');

console.log('🚀 启动浏览器MCP服务...');

// 设置环境变量
process.env.PLAYWRIGHT_BROWSERS_PATH = process.env.PLAYWRIGHT_BROWSERS_PATH || 
                                      path.join(os.homedir(), 'AppData', 'Local', 'ms-playwright');

console.log('📂 Playwright浏览器路径:', process.env.PLAYWRIGHT_BROWSERS_PATH);

async function startMCPServer() {
  try {
    // 检查并安装@playwright/mcp
    try {
      require.resolve('@playwright/mcp');
      console.log('✅ @playwright/mcp已安装');
    } catch (err) {
      console.log('📦 安装@playwright/mcp...');
      execSync('npm install @playwright/mcp@latest --save', { stdio: 'inherit' });
      console.log('✅ @playwright/mcp安装完成');
    }

    // 检查并安装Playwright浏览器
    try {
      console.log('🌐 检查Playwright浏览器...');
      execSync('npx playwright install chromium --with-deps', { 
        stdio: 'inherit',
        env: {
          ...process.env,
          PLAYWRIGHT_BROWSERS_PATH: process.env.PLAYWRIGHT_BROWSERS_PATH
        }
      });
      console.log('✅ Playwright浏览器准备就绪');
    } catch (browserError) {
      console.warn('⚠️ 浏览器安装失败，但继续启动服务:', browserError.message);
    }

    // 直接使用npx启动服务
    console.log('🚀 启动浏览器MCP服务器...');
    console.log('🔧 配置: Chrome浏览器，非无头模式');
    
    // 使用npx命令启动
    const startCommand = 'npx @playwright/mcp --browser chrome';
    console.log('📋 执行命令:', startCommand);
    
    // 启动子进程
    const { spawn } = require('child_process');
    const childProcess = spawn('npx', ['@playwright/mcp', '--browser', 'chrome'], {
      stdio: 'inherit',
      env: {
        ...process.env,
        PLAYWRIGHT_BROWSERS_PATH: process.env.PLAYWRIGHT_BROWSERS_PATH
      },
      shell: true
    });

    // 处理进程事件
    childProcess.on('spawn', () => {
      console.log('✅ 浏览器MCP服务器已启动');
      console.log('--SERVER-STATUS: Server started on http://localhost:3333/');
    });

    childProcess.on('error', (error) => {
      console.error('❌ 启动失败:', error.message);
      process.exit(1);
    });

    childProcess.on('exit', (code) => {
      console.log(`👋 服务器进程退出，退出码: ${code}`);
      process.exit(code);
    });

    // 优雅关闭
    process.on('SIGINT', () => {
      console.log('🛑 收到终止信号，关闭服务...');
      childProcess.kill('SIGINT');
    });

    process.on('SIGTERM', () => {
      console.log('🛑 收到终止信号，关闭服务...');
      childProcess.kill('SIGTERM');
    });

    // 保持进程运行
    await new Promise(() => {});

  } catch (error) {
    console.error('❌ 浏览器MCP服务启动失败:', error.message);
    process.exit(1);
  }
}

// 启动服务器
startMCPServer().catch(err => {
  console.error('💥 致命错误:', err.message);
  process.exit(1);
}); 
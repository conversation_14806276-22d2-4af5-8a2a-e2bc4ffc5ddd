<template>
  <div class="config-container">
    <header class="config-header">
      <div class="header-left">
        <img :src="logoUrl" alt="Logo" class="header-logo" />
        <h1>犇犇数字员工助手</h1>
      </div>
      <div class="header-right">
        <span class="user-info">欢迎，{{ authStore.user?.username }}</span>
        <button @click="handleLogout" class="logout-btn">登出</button>
      </div>
    </header>

    <main class="config-main">
      <aside class="sidebar">
        <nav class="nav-menu">
          <button 
            v-for="item in menuItems" 
            :key="item.id"
            :class="['nav-item', { active: activeTab === item.id }]"
            @click="activeTab = item.id"
          >
            <span class="nav-icon">{{ item.icon }}</span>
            <span class="nav-label">{{ item.label }}</span>
          </button>
        </nav>
      </aside>

      <section class="content">
        <!-- 基本设置 -->
        <div v-if="activeTab === 'basic'" class="tab-content">
          <h2>基本设置</h2>
          <!-- <div class="setting-group">
            <label>动漫人物形象</label>
            <div class="character-selector">
              <div 
                v-for="character in characters" 
                :key="character.id"
                :class="['character-option', { selected: selectedCharacter === character.id }]"
                @click="selectedCharacter = character.id"
              >
                <img :src="character.image" :alt="character.name" />
                <span>{{ character.name }}</span>
              </div>
            </div>
          </div> -->
          
          <div class="setting-group">
            <label>启动设置</label>
            <div class="checkbox-list">
              <label class="checkbox-item">
                <input type="checkbox" v-model="settings.autoStart" />
                <span>开机自动启动</span>
              </label>
              <label class="checkbox-item">
                <input type="checkbox" v-model="settings.showOnStartup" />
                <span>启动时显示主窗口</span>
              </label>
            </div>
          </div>


          
          <div class="setting-group">
            <label>开发设置</label>
            <div class="checkbox-list">
              <label class="checkbox-item">
                <input type="checkbox" v-model="settings.enableDevTools" @change="toggleDevTools" />
                <span>启用开发者工具</span>
              </label>
            </div>
            <p class="setting-description">
              开启后可以在应用中使用F12或Ctrl+Shift+I打开开发者工具，用于调试和检查页面元素。
            </p>
          </div>
        </div>

        <!-- 知识库设置 -->
        <div v-if="activeTab === 'knowledge'" class="tab-content">
          <h2>知识库管理</h2>
          
          <div class="setting-group">
            <label>功能介绍</label>
            <p class="setting-description">
              犇犇的智能知识库可以索引您的文档，在对话时自动检索相关内容来回答问题。
              支持Word、PDF、TXT、Markdown等格式的文档。
            </p>
          </div>

          <div class="setting-group">
            <label>文档路径设置</label>
            <div class="path-setting">
              <div class="path-input-group">
                <input 
                  type="text" 
                  v-model="knowledgeSettings.documentsPath" 
                  placeholder="请选择文档目录，如：C:\Users\<USER>\Desktop\Documents"
                  class="path-input"
                  readonly
                />
                <button @click="selectDocumentsPath" class="path-btn">选择目录</button>
              </div>
              <p class="setting-description">
                请选择包含您文档的文件夹，犇犇将自动索引其中的文档内容。
              </p>
            </div>
          </div>

          <div class="setting-group">
            <label>知识库状态</label>
            <div class="status-card">
              <div class="status-row">
                <span class="status-label">数据库状态：</span>
                <span :class="['status-value', knowledgeStatus.isInitialized ? 'success' : 'warning']">
                  {{ knowledgeStatus.isInitialized ? '已初始化' : '未初始化' }}
                </span>
              </div>
              <div class="status-row">
                <span class="status-label">已索引文档：</span>
                <span class="status-value">{{ knowledgeStats.totalFiles }} 个文件</span>
              </div>
              <div class="status-row">
                <span class="status-label">文档片段：</span>
                <span class="status-value">{{ knowledgeStats.totalSegments }} 个片段</span>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label>手动操作</label>
            <div class="action-buttons">
              <button 
                @click="initializeKnowledge" 
                :disabled="knowledgeStatus.isInitializing || knowledgeStatus.isRebuilding"
                class="action-btn primary"
              >
                {{ knowledgeStatus.isInitializing ? '初始化中...' : '初始化知识库' }}
              </button>
              <button 
                @click="rebuildKnowledge" 
                :disabled="knowledgeStatus.isRebuilding || knowledgeStatus.isInitializing"
                class="action-btn warning"
              >
                {{ knowledgeStatus.isRebuilding ? '重建中...' : '重建知识库' }}
              </button>
              <button 
                @click="indexDocuments" 
                :disabled="knowledgeStatus.isIndexing || knowledgeStatus.isRebuilding || !knowledgeSettings.documentsPath"
                class="action-btn success"
              >
                {{ knowledgeStatus.isIndexing ? '索引中...' : '重新索引文档' }}
              </button>
              <button 
                @click="clearKnowledge" 
                :disabled="knowledgeStatus.isClearing || knowledgeStatus.isRebuilding"
                class="action-btn danger"
              >
                {{ knowledgeStatus.isClearing ? '清理中...' : '清空知识库' }}
              </button>
              <button 
                @click="debugKnowledge" 
                class="action-btn info"
              >
                调试检查
              </button>
            </div>
          </div>
<!-- 
          <div class="setting-group">
            <label>测试搜索</label>
            <div class="test-search">
              <div class="search-input-group">
                <input 
                  type="text" 
                  v-model="testQuery" 
                  placeholder="输入问题测试知识库搜索..."
                  class="search-input"
                  @keyup.enter="testKnowledgeSearch"
                />
                <button @click="testKnowledgeSearch" class="search-btn">搜索测试</button>
              </div>
              <div v-if="testResults.length > 0" class="test-results">
                <h4>搜索结果：</h4>
                <div v-for="(result, index) in testResults" :key="index" class="result-item">
                  <div class="result-similarity">相似度: {{ (result.similarity * 100).toFixed(1) }}%</div>
                  <div class="result-content">{{ result.content.substring(0, 200) }}...</div>
                </div>
              </div>
            </div>
          </div>

          <div class="setting-group">
            <label>高级设置</label>
            <div class="advanced-settings">
              <div class="setting-item">
                <label class="checkbox-item">
                  <input type="checkbox" v-model="knowledgeSettings.autoIndex" />
                  <span>自动索引新文档</span>
                </label>
                <p class="setting-hint">开启后，犇犇会自动检测并索引文档目录中的新文件</p>
              </div>
              <div class="setting-item">
                <label>搜索结果数量</label>
                <select v-model="knowledgeSettings.searchLimit" class="select-input">
                  <option value="3">3个</option>
                  <option value="5">5个</option>
                  <option value="8">8个</option>
                  <option value="10">10个</option>
                </select>
              </div>
            </div>
          </div> -->
        </div>

        <!-- 语音设置 -->
        <div v-if="activeTab === 'voice'" class="tab-content">
          <h2>语音设置</h2>
          <div class="setting-group">
            <label>语音识别语言</label>
            <select v-model="settings.voiceLanguage" class="select-input">
              <option value="zh-CN">中文（简体）</option>
              <option value="zh-TW">中文（繁体）</option>
              <option value="en-US">英语</option>
              <option value="ja-JP">日语</option>
            </select>
          </div>
          
          <div class="setting-group">
            <label>语音识别敏感度</label>
            <input 
              type="range" 
              v-model="settings.voiceSensitivity" 
              min="1" 
              max="10" 
              class="range-input"
            />
            <span class="range-value">{{ settings.voiceSensitivity }}</span>
          </div>

          <div class="setting-group">
            <label>测试语音识别</label>
            <button @click="testVoiceRecognition" class="test-btn">
              {{ isRecording ? '停止录音' : '开始测试' }}
            </button>
            <div v-if="voiceTestResult" class="test-result">
              识别结果：{{ voiceTestResult }}
            </div>
          </div>
        </div>

        <!-- 待办事项 -->
        <div v-if="activeTab === 'todo'" class="tab-content">
          <h2>邮件待办事项</h2>
          <div class="setting-group">
            <label>功能介绍</label>
            <p class="setting-description">
              犇犇会自动检查您的邮件，智能筛选出包含待办事项的邮件并提醒您。
              支持会议邀请、任务分配、截止日期提醒等多种类型的待办事项。
            </p>
          </div>

          <!-- 邮箱配置 -->
          <div class="setting-group">
            <label>邮箱配置</label>
            <EmailConfig />
          </div>
          
          <div class="setting-group">
            <label>邮件检查状态</label>
            <div class="status-card">
              <div class="status-row">
                <span class="status-label">邮件服务：</span>
                <span :class="['status-value', emailServiceStatus.isInitialized ? 'success' : 'warning']">
                  {{ emailServiceStatus.isInitialized ? '已启用' : '未启用' }}
                </span>
              </div>
              <div class="status-row">
                <span class="status-label">检查间隔：</span>
                <span class="status-value">5分钟</span>
              </div>
              <div class="status-row">
                <span class="status-label">待办事项：</span>
                <span class="status-value">{{ todoStats.totalTodos }} 个</span>
              </div>
              <div class="status-row">
                <span class="status-label">已完成：</span>
                <span class="status-value">{{ todoStats.completedTodos }} 个</span>
              </div>
            </div>
          </div>
          
          <!-- 嵌入待办事项面板 -->
          <div class="setting-group">
            <label>待办事项列表</label>
            <div class="todo-panel-container">
              <TodoListPanel 
                @status-update="handleTodoStatusUpdate"
                @error="handleTodoError"
                @todo-updated="handleTodoUpdated"
              />
            </div>
          </div>
          
          <div class="setting-group">
            <label>手动操作</label>
            <div class="action-buttons">
              <button 
                @click="checkEmailsManually" 
                :disabled="isCheckingEmails"
                class="action-btn primary"
              >
                {{ isCheckingEmails ? '检查中...' : '立即检查邮件' }}
              </button>
            </div>
          </div>
        </div>

        <!-- MCP聊天 -->
        <div v-if="activeTab === 'chat'" class="tab-content">
          <h2>MCP智能聊天</h2>
          <div class="setting-group">
            <label>功能介绍</label>
            <p class="setting-description">
              犇犇现在具备了MCP (Model Context Protocol) 文件操作能力！
              可以帮您搜索、打开和管理文件。
            </p>
            <div class="feature-list">
              <div class="feature-item">
                <span class="feature-icon">🔍</span>
                <span class="feature-text">智能文件搜索</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">📁</span>
                <span class="feature-text">文件快速打开</span>
              </div>
              <div class="feature-item">
                <span class="feature-icon">💬</span>
                <span class="feature-text">自然语言交互</span>
              </div>
            </div>
          </div>
          
          <div class="setting-group">
            <label>开始聊天</label>
            <p class="setting-description">
              点击下方按钮进入聊天界面，体验MCP文件操作功能。
            </p>
            <button @click="openChatInterface" class="chat-btn">
              🚀 开始聊天
            </button>
          </div>

          <div class="setting-group">
            <label>使用示例</label>
            <div class="example-list">
              <div class="example-item">
                <span class="example-text">"帮我找桌面上的报告文件"</span>
              </div>
              <div class="example-item">
                <span class="example-text">"打开文档文件夹里的项目文档"</span>
              </div>
              <div class="example-item">
                <span class="example-text">"搜索包含'会议'的文件"</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 外观设置 -->
        <div v-if="activeTab === 'appearance'" class="tab-content">
          <h2>外观设置</h2>
          <div class="setting-group">
            <label>主题颜色</label>
            <div class="color-picker">
              <div 
                v-for="color in themeColors" 
                :key="color"
                :class="['color-option', { selected: settings.themeColor === color }]"
                :style="{ backgroundColor: color }"
                @click="settings.themeColor = color"
              ></div>
            </div>
          </div>
          
          <div class="setting-group">
            <label>悬浮窗透明度</label>
            <input 
              type="range" 
              v-model="settings.opacity" 
              min="0.3" 
              max="1" 
              step="0.1" 
              class="range-input"
            />
            <span class="range-value">{{ Math.round(settings.opacity * 100) }}%</span>
          </div>
        </div>

        <!-- 关于 -->
        <div v-if="activeTab === 'about'" class="tab-content">
          <h2>关于</h2>
          <div class="about-info">
            <img :src="logoUrl" alt="Logo" class="about-logo" />
            <h3>犇犇数字员工助手</h3>
            <p class="version">版本 1.0.0</p>
          </div>
        </div>

        <!-- 聊天设置弹窗 -->
        <ChatSettings v-if="showChatSettings" @close="showChatSettings = false" />
      </section>
    </main>
  </div>
</template>

<script>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAuthStore } from '../stores/auth.js'
import { useChatStore } from '../stores/chat.js'
import ChatSettings from '../components/ChatSettings.vue'
import TodoListPanel from '../components/TodoListPanel.vue'
import EmailConfig from '../components/EmailConfig.vue'
import logoUrl from '/assets/logo.png?url'
import { 
  initKnowledgeDatabase, 
  indexDocument, 
  searchKnowledge, 
  getKnowledgeStats, 
  clearKnowledgeBase, 
  rebuildKnowledgeBase 
} from '../utils/knowledge/knowledgeClient.js'
import { 
  getDocumentsPath, 
  setDocumentsPath, 
  getDefaultDocumentsPath 
} from '../utils/knowledge/config.js'
import { 
  getFilePathsConfig, 
  updateFilePathsConfig 
} from '../utils/config/modelConfig.js'

export default {
  name: 'Config',
  components: {
    ChatSettings,
    TodoListPanel,
    EmailConfig
  },
  setup() {
    const authStore = useAuthStore()
    const chatStore = useChatStore()
    const activeTab = ref('basic')
    const showChatSettings = ref(false)
    const isRecording = ref(false)
    const voiceTestResult = ref('')
    const selectedCharacter = ref('benben')

    const menuItems = [
      { id: 'basic', label: '基本设置', icon: '⚙️' },
      { id: 'knowledge', label: '知识库', icon: '📚' },
      { id: 'todo', label: '邮件服务', icon: '📋' },
      // { id: 'chat', label: 'MCP聊天', icon: '🤖' },
      // { id: 'voice', label: '语音设置', icon: '🎤' },
      // { id: 'appearance', label: '外观设置', icon: '🎨' },
      { id: 'about', label: '关于', icon: 'ℹ️' }
    ]

    const characters = [
              { id: 'benben', name: '犇犇', image: '/assets/characters/benben.png' },
      { id: 'wukong', name: '悟空', image: '/assets/characters/wukong.png' },
      { id: 'luffy', name: '路飞', image: '/assets/characters/luffy.png' }
    ]

    const themeColors = [
      '#667eea', '#764ba2', '#f093fb', '#f5576c',
      '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
    ]

    const settings = reactive({
      autoStart: true,
      showOnStartup: false,
      enableDevTools: false,
      voiceLanguage: 'zh-CN',
      voiceSensitivity: 5,
      themeColor: '#667eea',
      opacity: 0.9
    })

    // 知识库相关状态
    const knowledgeSettings = reactive({
      documentsPath: '',
      autoIndex: true,
      searchLimit: 3
    })

    const knowledgeStatus = reactive({
      isInitialized: false,
      isInitializing: false,
      isIndexing: false,
      isClearing: false,
      isRebuilding: false
    })

    const knowledgeStats = reactive({
      totalFiles: 0,
      totalSegments: 0
    })

    const testQuery = ref('')
    const testResults = ref([])

    // 待办事项相关状态
    const emailServiceStatus = reactive({
      isInitialized: false,
      lastCheckTime: null
    })

    const todoStats = reactive({
      totalTodos: 0,
      completedTodos: 0
    })

    const isCheckingEmails = ref(false)
    const isTestingEmailService = ref(false)
    

    


    // 聊天统计信息
    const chatStats = computed(() => chatStore.getChatStats())

    const handleLogout = async () => {
      await authStore.logout()
    }

    const openChatInterface = () => {
      // 触发导航到聊天界面
      if (window.electronAPI) {
        window.electronAPI.showMainWindow('Chat')
      }
    }

    const clearChatHistory = () => {
      if (confirm('确定要清空所有聊天历史吗？此操作不可撤销。')) {
        chatStore.clearHistory()
        alert('聊天历史已清空')
      }
    }

    const testVoiceRecognition = () => {
      if (isRecording.value) {
        // 停止录音
        isRecording.value = false
        voiceTestResult.value = '测试完成：您好，这是语音识别测试'
      } else {
        // 开始录音
        isRecording.value = true
        voiceTestResult.value = ''
        
        // 模拟录音过程
        setTimeout(() => {
          if (isRecording.value) {
            isRecording.value = false
            voiceTestResult.value = '识别成功：您好，犇犇助手'
          }
        }, 3000)
      }
    }

    const toggleDevTools = () => {
      if (window.electronAPI) {
        window.electronAPI.toggleDevTools(settings.enableDevTools)
      }
    }

    const openUrl = (url) => {
      if (url.startsWith('mailto:')) {
        alert('请发送邮件至：<EMAIL>')
      } else {
        alert('即将打开：' + url)
      }
    }

    // 知识库相关方法
    const initializeKnowledge = async () => {
      knowledgeStatus.isInitializing = true
      try {
        const result = await initKnowledgeDatabase()
        if (result.success) {
          knowledgeStatus.isInitialized = true
          await refreshKnowledgeStats()
          alert('知识库初始化成功！')
        } else {
          alert('知识库初始化失败：' + result.error)
        }
      } catch (error) {
        console.error('知识库初始化错误:', error)
        alert('知识库初始化失败：' + error.message)
      } finally {
        knowledgeStatus.isInitializing = false
      }
    }

    const selectDocumentsPath = async () => {
      try {
        if (window.electronAPI) {
          const result = await window.electronAPI.selectDirectory()
          if (result.filePaths && result.filePaths.length > 0) {
            const selectedPath = result.filePaths[0]
            knowledgeSettings.documentsPath = selectedPath
            setDocumentsPath(selectedPath)
            console.log('已选择文档路径:', selectedPath)
          }
        } else {
          // 开发环境fallback
          const path = prompt('请输入文档目录路径:', 'C:\\Users\\<USER>\\Desktop\\Documents')
          if (path) {
            knowledgeSettings.documentsPath = path
            setDocumentsPath(path)
          }
        }
      } catch (error) {
        console.error('选择目录失败:', error)
        alert('选择目录失败：' + error.message)
      }
    }

    const indexDocuments = async () => {
      if (!knowledgeSettings.documentsPath) {
        alert('请先选择文档目录')
        return
      }

      knowledgeStatus.isIndexing = true
      try {
        console.log('📂 开始索引文档，目录:', knowledgeSettings.documentsPath)
        
        // 获取目录下的所有文档文件
        const files = await getDocumentFiles(knowledgeSettings.documentsPath)
        console.log('📋 找到的文件:', files)
        
        if (files.length === 0) {
          alert(`在目录 "${knowledgeSettings.documentsPath}" 中没有找到支持的文档文件\n\n支持的格式：.txt, .md, .docx, .doc`)
          return
        }

        let successCount = 0
        let totalCount = files.length
        const errors = []

        console.log(`🚀 开始索引 ${totalCount} 个文件`)

        for (const filePath of files) {
          try {
            console.log(`📄 正在索引: ${filePath}`)
            const result = await indexDocument(filePath)
            if (result.success) {
              successCount++
              console.log(`✅ 索引成功: ${result.fileName}`)
            } else {
              errors.push(`${filePath}: ${result.error}`)
              console.error(`❌ 索引失败: ${filePath}`, result.error)
            }
          } catch (error) {
            errors.push(`${filePath}: ${error.message}`)
            console.error(`❌ 索引文件异常 ${filePath}:`, error)
          }
        }

        await refreshKnowledgeStats()
        
        let message = `文档索引完成！\n成功索引: ${successCount}/${totalCount} 个文件`
        if (errors.length > 0) {
          message += `\n\n失败的文件:\n${errors.slice(0, 3).join('\n')}`
          if (errors.length > 3) {
            message += `\n... 还有 ${errors.length - 3} 个文件失败`
          }
        }
        
        alert(message)
      } catch (error) {
        console.error('❌ 索引文档失败:', error)
        alert('索引文档失败：' + error.message)
      } finally {
        knowledgeStatus.isIndexing = false
      }
    }

    const rebuildKnowledge = async () => {
      if (!confirm('确定要重建知识库吗？这将删除现有数据并重新创建表结构，此操作不可撤销！')) {
        return
      }

      knowledgeStatus.isRebuilding = true
      try {
        const result = await rebuildKnowledgeBase()
        if (result.success) {
          knowledgeStatus.isInitialized = true
          await refreshKnowledgeStats()
          alert('知识库重建成功！现在可以重新索引文档了。')
        } else {
          alert('知识库重建失败：' + result.error)
        }
      } catch (error) {
        console.error('知识库重建失败:', error)
        alert('知识库重建失败：' + error.message)
      } finally {
        knowledgeStatus.isRebuilding = false
      }
    }

    const clearKnowledge = async () => {
      if (!confirm('确定要清空整个知识库吗？此操作不可撤销！')) {
        return
      }

      knowledgeStatus.isClearing = true
      try {
        const result = await clearKnowledgeBase()
        if (result.success) {
          await refreshKnowledgeStats()
          alert('知识库已清空')
        } else {
          alert('清空知识库失败：' + result.error)
        }
      } catch (error) {
        console.error('清空知识库失败:', error)
        alert('清空知识库失败：' + error.message)
      } finally {
        knowledgeStatus.isClearing = false
      }
    }

    const debugKnowledge = async () => {
      try {
        const debugInfo = await window.electronAPI.invoke('knowledge-debug')
        
        if (debugInfo.error) {
          alert('调试检查失败：' + debugInfo.error)
          return
        }
        
        let message = `知识库调试信息：\n\n`
        message += `✅ 数据库表: ${debugInfo.tables.join(', ')}\n`
        message += `📁 文件数量: ${debugInfo.fileCount}\n`
        message += `📄 片段数量: ${debugInfo.embdCount}\n\n`
        
        if (debugInfo.sampleFiles.length > 0) {
          message += `示例文件:\n`
          debugInfo.sampleFiles.forEach((file, index) => {
            message += `${index + 1}. ${file.file_name}\n`
          })
          message += `\n`
        }
        
        if (debugInfo.sampleEmbds.length > 0) {
          message += `示例片段:\n`
          debugInfo.sampleEmbds.forEach((embd, index) => {
            message += `${index + 1}. 长度: ${embd.length}, 内容: ${embd.content}\n`
          })
        }
        
        console.log('知识库调试信息:', debugInfo)
        alert(message)
      } catch (error) {
        console.error('调试检查失败:', error)
        alert('调试检查失败：' + error.message)
      }
    }

    const testKnowledgeSearch = async () => {
      if (!testQuery.value.trim()) {
        alert('请输入搜索内容')
        return
      }

      try {
        const results = await searchKnowledge(testQuery.value, knowledgeSettings.searchLimit)
        testResults.value = results
        
        if (results.length === 0) {
          alert('没有找到相关内容')
        }
      } catch (error) {
        console.error('测试搜索失败:', error)
        alert('搜索失败：' + error.message)
      }
    }

    const refreshKnowledgeStats = async () => {
      try {
        const stats = await getKnowledgeStats()
        knowledgeStats.totalFiles = stats.totalFiles
        knowledgeStats.totalSegments = stats.totalSegments
      } catch (error) {
        console.error('获取知识库统计失败:', error)
      }
    }

    // 获取目录下的文档文件
    const getDocumentFiles = async (dirPath) => {
      try {
        console.log('🔍 前端请求获取文件列表:', dirPath)
        if (window.electronAPI && window.electronAPI.invoke) {
          const files = await window.electronAPI.invoke('get-directory-files', dirPath)
          console.log('📁 主进程返回文件列表:', files)
          return files
        } else {
          // 开发环境模拟
          console.log('🔧 使用开发环境模拟文件列表')
          return [
            dirPath + '\\示例文档1.txt',
            dirPath + '\\示例文档2.docx'
          ]
        }
      } catch (error) {
        console.error('❌ 获取文件列表失败:', error)
        return []
      }
    }

    // 初始化知识库设置
    const loadKnowledgeSettings = async () => {
      try {
        // 加载保存的文档路径
        const savedPath = getDocumentsPath()
        if (savedPath) {
          knowledgeSettings.documentsPath = savedPath
        } else {
          // 设置默认路径
          try {
            if (window.electronAPI && window.electronAPI.invoke) {
              const desktop = await window.electronAPI.invoke('get-path', 'desktop')
              const defaultPath = `${desktop}\\Documents`
              knowledgeSettings.documentsPath = defaultPath
            } else {
              knowledgeSettings.documentsPath = 'C:\\Users\\<USER>\\Desktop\\Documents'
            }
          } catch (error) {
            console.warn('获取默认路径失败:', error)
            knowledgeSettings.documentsPath = 'C:\\Users\\<USER>\\Desktop\\Documents'
          }
        }

        // 刷新统计信息
        await refreshKnowledgeStats()
        
        // 检查知识库是否已初始化
        if (knowledgeStats.totalFiles > 0 || knowledgeStats.totalSegments > 0) {
          knowledgeStatus.isInitialized = true
        }
      } catch (error) {
        console.error('加载知识库设置失败:', error)
      }
    }

    // 待办事项相关方法
    const checkEmailsManually = async () => {
      isCheckingEmails.value = true
      try {
        const result = await window.electronAPI.checkEmailsManual()
        if (result.success) {
          // 更新统计
          await refreshTodoStats()
          alert('邮件检查完成: ' + result.message)
        } else {
          alert('检查邮件失败: ' + result.error)
        }
      } catch (error) {
        console.error('手动检查邮件失败:', error)
        alert('检查邮件时出错: ' + error.message)
      } finally {
        isCheckingEmails.value = false
      }
    }

    const testEmailService = async () => {
      isTestingEmailService.value = true
      try {
        const result = await window.electronAPI.invoke('test-email-service')
        if (result.success) {
          alert('邮件服务测试成功: ' + result.message)
        } else {
          alert('邮件服务测试失败: ' + result.error)
        }
      } catch (error) {
        console.error('测试邮件服务失败:', error)
        alert('测试邮件服务失败: ' + error.message)
      } finally {
        isTestingEmailService.value = false
      }
    }

    const refreshTodoStats = async () => {
      try {
        const result = await window.electronAPI.invoke('get-todo-stats')
        if (result.success) {
          todoStats.totalTodos = result.stats.totalTodos
          todoStats.completedTodos = result.stats.completedTodos
        }
      } catch (error) {
        console.error('获取待办事项统计失败:', error)
      }
    }

    const refreshEmailServiceStatus = async () => {
      try {
        const result = await window.electronAPI.invoke('get-email-service-status')
        if (result.success) {
          emailServiceStatus.isInitialized = result.status.isInitialized
          emailServiceStatus.lastCheckTime = result.status.lastCheckTime
        }
      } catch (error) {
        console.error('获取邮件服务状态失败:', error)
      }
    }

    const handleTodoStatusUpdate = (message) => {
      // 显示状态更新信息
      console.log('待办事项状态更新:', message)
    }

    const handleTodoError = (error) => {
      // 显示错误信息
      console.error('待办事项错误:', error)
      alert('待办事项操作失败: ' + error)
    }

    const handleTodoUpdated = (todo) => {
      // 处理待办事项更新
      console.log('待办事项已更新:', todo)
      refreshTodoStats()
    }

    onMounted(() => {
      // 加载用户设置
      console.log('加载配置页面')
      // 初始化知识库设置
      loadKnowledgeSettings()
      // 初始化待办事项状态
      refreshTodoStats()
      refreshEmailServiceStatus()
    })

    return {
      authStore,
      chatStore,
      activeTab,
      showChatSettings,
      chatStats,
      menuItems,
      characters,
      themeColors,
      settings,
      selectedCharacter,
      isRecording,
      voiceTestResult,
      handleLogout,
      openChatInterface,
      clearChatHistory,
      testVoiceRecognition,
      toggleDevTools,
      openUrl,
      logoUrl,

      // 知识库相关
      knowledgeSettings,
      knowledgeStatus,
      knowledgeStats,
      testQuery,
      testResults,
      initializeKnowledge,
      rebuildKnowledge,
      selectDocumentsPath,
      indexDocuments,
      clearKnowledge,
      debugKnowledge,
      testKnowledgeSearch,
      // 待办事项相关
      emailServiceStatus,
      todoStats,
      isCheckingEmails,
      isTestingEmailService,
      checkEmailsManually,
      testEmailService,
      handleTodoStatusUpdate,
      handleTodoError,
      handleTodoUpdated
    }
  }
}
</script>

<style lang="scss" scoped>
.config-container {
  width: 100%;
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f5f7fa;
}

.config-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 30px;
  background: white;
  border-bottom: 1px solid #e1e5e9;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  
  .header-left {
    display: flex;
    align-items: center;
    
    .header-logo {
      width: 40px;
      height: 40px;
      border-radius: 50%;
      margin-right: 15px;
    }
    
    h1 {
      font-size: 24px;
      color: #333;
      margin: 0;
    }
  }
  
  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;
    
    .user-info {
      color: #666;
      font-size: 14px;
    }
    
    .logout-btn {
      padding: 8px 16px;
      background: #e74c3c;
      color: white;
      border: none;
      border-radius: 6px;
      cursor: pointer;
      font-size: 14px;
      transition: background 0.3s ease;
      
      &:hover {
        background: #c0392b;
      }
    }
  }
}

.config-main {
  display: flex;
  flex: 1;
  overflow: hidden;
}

.sidebar {
  width: 250px;
  background: white;
  border-right: 1px solid #e1e5e9;
  padding: 20px 0;
  
  .nav-menu {
    .nav-item {
      width: 100%;
      display: flex;
      align-items: center;
      padding: 15px 25px;
      border: none;
      background: none;
      text-align: left;
      cursor: pointer;
      transition: all 0.3s ease;
      color: #666;
      font-size: 16px;
      
      &:hover {
        background: #f8f9fa;
        color: #333;
      }
      
      &.active {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        
        &:hover {
          background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
      }
      
      .nav-icon {
        margin-right: 12px;
        font-size: 18px;
      }
    }
  }
}

.content {
  flex: 1;
  padding: 30px;
  overflow-y: auto;
}

.tab-content {
  max-width: 800px;
  
  h2 {
    color: #333;
    margin-bottom: 30px;
    font-size: 28px;
    font-weight: 600;
  }
}

.setting-group {
  margin-bottom: 30px;
  
  > label {
    display: block;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
    font-size: 16px;
  }
  
  .select-input {
    width: 100%;
    max-width: 300px;
    padding: 10px 15px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    
    &:focus {
      outline: none;
      border-color: #667eea;
    }
  }
  
  .range-input {
    width: 300px;
    margin-right: 15px;
  }
  
  .range-value {
    color: #666;
    font-weight: 500;
  }
}

.character-selector {
  display: flex;
  gap: 20px;
  
  .character-option {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 15px;
    border: 2px solid #e1e5e9;
    border-radius: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    
    &:hover {
      border-color: #667eea;
    }
    
    &.selected {
      border-color: #667eea;
      background: rgba(102, 126, 234, 0.1);
    }
    
    img {
      width: 60px;
      height: 60px;
      border-radius: 50%;
      margin-bottom: 10px;
    }
    
    span {
      font-size: 14px;
      color: #333;
    }
  }
}

.checkbox-list {
  .checkbox-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
    cursor: pointer;
    
    input {
      margin-right: 10px;
    }
    
    span {
      color: #666;
    }
  }
}

.color-picker {
  display: flex;
  gap: 10px;
  
  .color-option {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    border: 3px solid transparent;
    transition: all 0.3s ease;
    
    &:hover {
      transform: scale(1.1);
    }
    
    &.selected {
      border-color: #333;
    }
  }
}

.test-btn {
  padding: 10px 20px;
  background: #667eea;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  
  &:hover {
    background: #5a6fd8;
  }
}

.test-result {
  margin-top: 15px;
  padding: 10px;
  background: #f8f9fa;
  border-radius: 8px;
  color: #333;
}

.about-info {
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
  
  .about-logo {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    margin-bottom: 20px;
  }
  
  h3 {
    font-size: 24px;
    color: #333;
    margin-bottom: 10px;
  }
  
  .version {
    color: #666;
    font-size: 16px;
    margin-bottom: 20px;
  }
  
  .description {
    color: #666;
    line-height: 1.6;
    margin-bottom: 30px;
  }
  
  .links {
    display: flex;
    justify-content: center;
    gap: 30px;
    
    a {
      color: #667eea;
      text-decoration: none;
      
      &:hover {
        text-decoration: underline;
      }
    }
  }
}

.setting-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 15px;
  line-height: 1.5;
}

.config-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
  }
}

.danger-btn {
  padding: 10px 20px;
  background: #e74c3c;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.3s ease;
  
  &:hover {
    background: #c0392b;
    transform: translateY(-1px);
  }
}

.chat-stats {
  background: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
  margin-bottom: 15px;
  
  .stat-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .stat-label {
      color: #666;
      font-size: 14px;
    }
    
    .stat-value {
      color: #333;
      font-weight: 600;
      font-size: 16px;
    }
  }
}

// MCP功能相关样式
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-top: 15px;
  
  .feature-item {
    display: flex;
    align-items: center;
    padding: 12px 16px;
    background: rgba(102, 126, 234, 0.1);
    border-radius: 8px;
    border-left: 4px solid #667eea;
    
    .feature-icon {
      font-size: 20px;
      margin-right: 12px;
    }
    
    .feature-text {
      color: #333;
      font-weight: 500;
      font-size: 14px;
    }
  }
}

.chat-btn {
  padding: 16px 32px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 12px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 600;
  transition: all 0.3s ease;
  box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
  
  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }
  
  &:active {
    transform: translateY(0);
  }
}

.example-list {
  display: flex;
  flex-direction: column;
  gap: 10px;
  margin-top: 15px;
  
  .example-item {
    padding: 12px 16px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e1e5e9;
    
    .example-text {
      color: #007AFF;
      font-style: italic;
      font-size: 14px;
      
      &::before {
        content: "💬 ";
        margin-right: 8px;
      }
    }
  }
}

// 知识库相关样式
.path-setting {
  .path-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
    
    .path-input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      background: #f8f9fa;
      color: #666;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }
    
    .path-btn {
      padding: 12px 20px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
      }
    }
  }
}

.status-card {
  background: #f8f9fa;
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  padding: 20px;
  
  .status-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .status-label {
      color: #666;
      font-size: 14px;
      font-weight: 500;
    }
    
    .status-value {
      font-weight: 600;
      font-size: 14px;
      
      &.success {
        color: #27ae60;
      }
      
      &.warning {
        color: #f39c12;
      }
      
      &.danger {
        color: #e74c3c;
      }
    }
  }
}

.action-buttons {
  display: flex;
  gap: 12px;
  flex-wrap: wrap;
  
  .action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    
    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
      transform: none !important;
    }
    
    &.primary {
      background: #667eea;
      color: white;
      
      &:hover:not(:disabled) {
        background: #5a6fd8;
        transform: translateY(-1px);
      }
    }
    
    &.success {
      background: #27ae60;
      color: white;
      
      &:hover:not(:disabled) {
        background: #2ecc71;
        transform: translateY(-1px);
      }
    }
    
    &.warning {
      background: #f39c12;
      color: white;
      
      &:hover:not(:disabled) {
        background: #e67e22;
        transform: translateY(-1px);
      }
    }
    
    &.danger {
      background: #e74c3c;
      color: white;
      
      &:hover:not(:disabled) {
        background: #c0392b;
        transform: translateY(-1px);
      }
    }
    
    &.info {
      background: #3498db;
      color: white;
      
      &:hover:not(:disabled) {
        background: #2980b9;
        transform: translateY(-1px);
      }
    }
  }
}

.test-search {
  .search-input-group {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    
    .search-input {
      flex: 1;
      padding: 12px 16px;
      border: 2px solid #e1e5e9;
      border-radius: 8px;
      font-size: 14px;
      
      &:focus {
        outline: none;
        border-color: #667eea;
      }
    }
    
    .search-btn {
      padding: 12px 20px;
      background: #667eea;
      color: white;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      font-size: 14px;
      font-weight: 500;
      transition: all 0.3s ease;
      
      &:hover {
        background: #5a6fd8;
        transform: translateY(-1px);
      }
    }
  }
  
  .test-results {
    background: #f8f9fa;
    border-radius: 12px;
    padding: 20px;
    border: 1px solid #e1e5e9;
    
    h4 {
      color: #333;
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: 600;
    }
    
    .result-item {
      background: white;
      border-radius: 8px;
      padding: 15px;
      margin-bottom: 12px;
      border-left: 4px solid #667eea;
      
      &:last-child {
        margin-bottom: 0;
      }
      
      .result-similarity {
        color: #667eea;
        font-size: 12px;
        font-weight: 600;
        margin-bottom: 8px;
      }
      
      .result-content {
        color: #666;
        font-size: 14px;
        line-height: 1.5;
      }
    }
  }
}

.advanced-settings {
  .setting-item {
    margin-bottom: 20px;
    
    &:last-child {
      margin-bottom: 0;
    }
    
    .setting-hint {
      color: #999;
      font-size: 12px;
      margin-top: 5px;
      line-height: 1.4;
    }
  }
}

// 待办事项面板样式
.todo-panel-container {
  border: 1px solid #e1e5e9;
  border-radius: 12px;
  overflow: hidden;
  background: white;
  
  .todo-panel {
    border: none;
    box-shadow: none;
  }
}


</style> 
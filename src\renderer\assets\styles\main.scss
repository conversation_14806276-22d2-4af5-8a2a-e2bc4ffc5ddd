// CSS变量定义 - 明亮主题
:root {
  // 主题颜色
  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --primary-rgb: 102, 126, 234;
  --accent-color: #f093fb;

  // 状态颜色
  --success-color: #28a745;
  --warning-color: #ffc107;
  --error-color: #dc3545;
  --info-color: #17a2b8;

  // 背景颜色
  --bg-primary: linear-gradient(150deg, #4768DB 0%, #B06AB3 100%);
  --bg-secondary: rgba(255, 255, 255, 0.95);
  --bg-tertiary: rgba(255, 255, 255, 0.9);
  --bg-quaternary: #f8f9fa;
  --content-bg: rgba(255, 255, 255, 0.95);
  --content-bg-secondary: rgba(255, 255, 255, 0.85);
  --view-content-bg: #F1F5FD;

  // 文本颜色
  --text-primary: #fff;
  --text-secondary: #5a5a5a;
  --text-muted: #777777;
  --text-light: #999999;
  --text-dark: #333333;
  --text-inverse: #ffffff;

  // 边框颜色
  --border-color: rgba(255, 255, 255, 1);
  --border-color-light: rgba(255, 255, 255, 0.2);
  --border-color-medium: rgba(255, 255, 255, 0.4);
  --border-color-dark: #e1e5e9;
  --border-color-muted: #dee2e6;

  // 阴影
  --shadow-light: rgba(0, 0, 0, 0.1);
  --shadow-medium: rgba(0, 0, 0, 0.15);
  --shadow-dark: rgba(0, 0, 0, 0.25);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.1);
  --shadow-modal: 0 8px 32px rgba(0, 0, 0, 0.15);

  // 侧边栏颜色
  --sidebar-bg: rgba(255, 255, 255, 0.9);
  --sidebar-hover: rgba(255, 255, 255, 0.7);
  --sidebar-active: rgba(255, 255, 255, 0.8);
  --sidebar-text: #5a5a5a;
  --sidebar-text-active: #4768DB;

  // 卡片颜色
  --card-bg: rgba(255, 255, 255, 0.95);
  --card-bg-hover: rgba(255, 255, 255, 1);
  --card-border: rgba(255, 255, 255, 0.3);
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);

  // 按钮颜色
  --btn-primary-bg: var(--primary-color);
  --btn-primary-hover: #5a6fd8;
  --btn-secondary-bg: var(--bg-secondary);
  --btn-secondary-hover: rgba(255, 255, 255, 0.8);
  --btn-danger-bg: var(--error-color);
  --btn-danger-hover: #c82333;

  // 输入框颜色
  --input-bg: rgba(255, 255, 255, 0.9);
  --input-border: rgba(255, 255, 255, 0.3);
  --input-border-focus: var(--primary-color);
  --input-placeholder: #999999;

  // 模态框颜色
  --modal-overlay: rgba(0, 0, 0, 0.5);
  --modal-bg: rgba(255, 255, 255, 0.95);
  --modal-header-bg: rgba(255, 255, 255, 0.95);
  --modal-footer-bg: var(--bg-quaternary);

  // 状态徽章颜色
  --badge-success-bg: rgba(40, 167, 69, 0.1);
  --badge-success-text: #28a745;
  --badge-warning-bg: rgba(255, 193, 7, 0.1);
  --badge-warning-text: #e0a800;
  --badge-error-bg: rgba(220, 53, 69, 0.1);
  --badge-error-text: #dc3545;
  --badge-info-bg: rgba(23, 162, 184, 0.1);
  --badge-info-text: #17a2b8;

  // 文件夹和图标颜色
  --folder-icon-color: #ffc107;
  --folder-bg: var(--content-bg);
  --folder-border: var(--border-color);

  // 进度条颜色
  --progress-bg: rgba(233, 236, 239, 0.8);
  --progress-fill: var(--primary-color);
}

// 黑暗主题
.dark-theme {
  // 背景颜色
  --bg-primary: linear-gradient(150deg, #1a1d23 0%, #2c3040 100%);
  --bg-secondary: rgba(44, 48, 64, 0.95);
  --bg-tertiary: rgba(58, 63, 82, 0.9);
  --bg-quaternary: #2c3040;
  --content-bg: rgba(44, 48, 64, 0.95);
  --content-bg-secondary: rgba(58, 63, 82, 0.85);
  --view-content-bg: #1e2328;

  // 文本颜色
  --text-primary: #e2e8f0;
  --text-secondary: #a0aec0;
  --text-muted: #718096;
  --text-light: #9ca3af;
  --text-dark: #f1f5f9;
  --text-inverse: #1a202c;

  // 边框颜色
  --border-color: rgba(255, 255, 255, 0.1);
  --border-color-light: rgba(255, 255, 255, 0.05);
  --border-color-medium: rgba(255, 255, 255, 0.15);
  --border-color-dark: #4a5568;
  --border-color-muted: #374151;

  // 阴影
  --shadow-light: rgba(0, 0, 0, 0.3);
  --shadow-medium: rgba(0, 0, 0, 0.5);
  --shadow-dark: rgba(0, 0, 0, 0.7);
  --shadow-card: 0 2px 8px rgba(0, 0, 0, 0.3);
  --shadow-modal: 0 8px 32px rgba(0, 0, 0, 0.5);

  // 侧边栏颜色
  --sidebar-bg: #2c3040;
  --sidebar-hover: #3a3f52;
  --sidebar-active: rgba(var(--primary-rgb), 0.2);
  --sidebar-text: #a0aec0;
  --sidebar-text-active: var(--primary-color);

  // 卡片颜色
  --card-bg: rgba(44, 48, 64, 0.95);
  --card-bg-hover: rgba(58, 63, 82, 0.95);
  --card-border: rgba(255, 255, 255, 0.1);
  --card-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);

  // 按钮颜色
  --btn-primary-bg: var(--primary-color);
  --btn-primary-hover: #7c93f0;
  --btn-secondary-bg: var(--bg-secondary);
  --btn-secondary-hover: rgba(58, 63, 82, 0.95);
  --btn-danger-bg: var(--error-color);
  --btn-danger-hover: #e85569;

  // 输入框颜色
  --input-bg: rgba(58, 63, 82, 0.9);
  --input-border: rgba(255, 255, 255, 0.1);
  --input-border-focus: var(--primary-color);
  --input-placeholder: #718096;

  // 模态框颜色
  --modal-overlay: rgba(0, 0, 0, 0.7);
  --modal-bg: rgba(44, 48, 64, 0.95);
  --modal-header-bg: rgba(44, 48, 64, 0.95);
  --modal-footer-bg: var(--bg-quaternary);

  // 状态徽章颜色
  --badge-success-bg: rgba(72, 187, 120, 0.2);
  --badge-success-text: #68d391;
  --badge-warning-bg: rgba(246, 173, 85, 0.2);
  --badge-warning-text: #f6ad55;
  --badge-error-bg: rgba(252, 129, 129, 0.2);
  --badge-error-text: #fc8181;
  --badge-info-bg: rgba(99, 179, 237, 0.2);
  --badge-info-text: #63b3ed;

  // 文件夹和图标颜色
  --folder-icon-color: #f6ad55;
  --folder-bg: var(--content-bg);
  --folder-border: var(--border-color);

  // 进度条颜色
  --progress-bg: rgba(74, 85, 104, 0.8);
  --progress-fill: var(--primary-color);
}

// 主题过渡动画
* {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease;
}

// 临时移除变量导入，直接定义主要变量
$primary-color: #667eea;
$secondary-color: #764ba2;

// 全局重置样式
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html,
body {
  height: 100%;
  font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  background: var(--bg-primary);
  color: var(--text-primary);
}

#app {
  height: 100vh;
  overflow: hidden;
  background: var(--bg-primary);
}

// 滚动条样式
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;

  &:hover {
    background: #a1a1a1;
  }
}

// 选择文本样式
::selection {
  background: rgba(102, 126, 234, 0.3);
  color: inherit;
}

// 通用按钮样式
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 10px 20px;
  border: none;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s ease;
  text-decoration: none;

  &:disabled {
    opacity: 0.6;
    cursor: not-allowed;
  }

  &.btn-primary {
    background: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
    color: white;

    &:hover:not(:disabled) {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba($primary-color, 0.3);
    }
  }

  &.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;

    &:hover:not(:disabled) {
      background: #e9ecef;
      border-color: #dae0e5;
    }
  }

  &.btn-danger {
    background: #dc3545;
    color: white;

    &:hover:not(:disabled) {
      background: #c82333;
    }
  }

  &.btn-success {
    background: #28a745;
    color: white;

    &:hover:not(:disabled) {
      background: #218838;
    }
  }

  &.btn-small {
    padding: 6px 12px;
    font-size: 12px;
  }

  &.btn-large {
    padding: 15px 30px;
    font-size: 16px;
  }
}

// 输入框样式
.form-input {
  width: 100%;
  padding: 12px 16px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s ease;

  &:focus {
    outline: none;
    border-color: $primary-color;
    box-shadow: 0 0 0 3px rgba($primary-color, 0.1);
  }

  &:disabled {
    background-color: #f5f5f5;
    cursor: not-allowed;
  }

  &::placeholder {
    color: #999;
  }
}

// 卡片样式
.card {
  background: var(--bg-primary);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  box-shadow: 0 2px 10px var(--shadow-light);
  overflow: hidden;

  &.card-hover {
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 20px var(--shadow-medium);
    }
  }
}

// 标签样式
.badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border-radius: 12px;

  &.badge-primary {
    background: rgba($primary-color, 0.1);
    color: $primary-color;
  }

  &.badge-success {
    background: rgba(#28a745, 0.1);
    color: #28a745;
  }

  &.badge-warning {
    background: rgba(#ffc107, 0.1);
    color: #e0a800;
  }

  &.badge-danger {
    background: rgba(#dc3545, 0.1);
    color: #dc3545;
  }
}

// 加载动画
.loading {
  display: inline-block;
  width: 20px;
  height: 20px;
  border: 2px solid #f3f3f3;
  border-top: 2px solid $primary-color;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 淡入动画
.fade-enter-active,
.fade-leave-active {
  transition: opacity 0.3s ease;
}

.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

// 滑入动画
.slide-enter-active,
.slide-leave-active {
  transition: all 0.3s ease;
}

.slide-enter-from {
  transform: translateX(-20px);
  opacity: 0;
}

.slide-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

// 响应式工具类
.text-center {
  text-align: center;
}

.text-left {
  text-align: left;
}

.text-right {
  text-align: right;
}

.d-flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.align-center {
  align-items: center;
}

.mt-1 {
  margin-top: 0.25rem;
}

.mt-2 {
  margin-top: 0.5rem;
}

.mt-3 {
  margin-top: 1rem;
}

.mt-4 {
  margin-top: 1.5rem;
}

.mt-5 {
  margin-top: 3rem;
}

.mb-1 {
  margin-bottom: 0.25rem;
}

.mb-2 {
  margin-bottom: 0.5rem;
}

.mb-3 {
  margin-bottom: 1rem;
}

.mb-4 {
  margin-bottom: 1.5rem;
}

.mb-5 {
  margin-bottom: 3rem;
}

.p-1 {
  padding: 0.25rem;
}

.p-2 {
  padding: 0.5rem;
}

.p-3 {
  padding: 1rem;
}

.p-4 {
  padding: 1.5rem;
}

.p-5 {
  padding: 3rem;
}
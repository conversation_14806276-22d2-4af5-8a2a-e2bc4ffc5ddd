' getCalendars.vbs - 获取Outlook日历列表
Option Explicit

' Include utility functions
Dim fso, scriptDir
Set fso = CreateObject("Scripting.FileSystemObject")
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
ExecuteGlobal fso.OpenTextFile(fso.BuildPath(scriptDir, "utils.vbs"), 1).ReadAll

' Main function
Sub Main()
    ' Get the calendars
    Dim calendarsJSON
    calendarsJSON = GetAvailableCalendars()
    
    ' Output success with the calendars
    OutputSuccess calendarsJSON
End Sub

' Gets a list of available calendars
Function GetAvailableCalendars()
    On Error Resume Next
    
    ' Create Outlook objects
    Dim outlookApp, namespace, folders, folder, i
    Dim calendars, calendar
    
    ' Create Outlook application
    Set outlookApp = CreateOutlookApplication()
    
    ' Get MAPI namespace
    Set namespace = outlookApp.GetNamespace("MAPI")
    If Err.Number <> 0 Then
        OutputError "Failed to get MAPI namespace: " & Err.Description
        WScript.Quit 1
    End If
    
    ' Get all folders
    Set folders = namespace.Folders
    
    ' Build JSON array of calendars
    calendars = "["
    
    ' Add default calendar first
    calendars = calendars & "{"
    calendars = calendars & """name"":""Default Calendar"","
    calendars = calendars & """isDefault"":true,"
    calendars = calendars & """type"":""primary"""
    calendars = calendars & "}"
    
    ' Add other calendars if any
    For i = 1 To folders.Count
        Set folder = folders.Item(i)
        
        ' Try to get calendar folder from this folder
        On Error Resume Next
        Set calendar = folder.GetDefaultFolder(olFolderCalendar)
        
        If Err.Number = 0 And Not calendar Is Nothing Then
            If folder.Name <> namespace.DefaultStore.DisplayName Then
                calendars = calendars & ",{"
                calendars = calendars & """name"":""" & EscapeJSON(folder.Name) & ""","
                calendars = calendars & """isDefault"":false,"
                calendars = calendars & """type"":""secondary"""
                calendars = calendars & "}"
            End If
        End If
        
        On Error GoTo 0
    Next
    
    calendars = calendars & "]"
    
    If Err.Number <> 0 Then
        OutputError "Failed to retrieve calendars: " & Err.Description
        WScript.Quit 1
    End If
    
    ' Clean up
    Set folders = Nothing
    Set namespace = Nothing
    Set outlookApp = Nothing
    
    GetAvailableCalendars = calendars
End Function

' Run the main function
Main 
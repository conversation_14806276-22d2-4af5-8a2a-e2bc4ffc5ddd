// 知识库主模块 - 统一导出所有知识库相关功能

// 配置模块
const config = require('./config')

// 数据库模块
const db = require('./db')

// 嵌入向量模块
const embedding = require('./embedding')

// 服务模块
const service = require('./service')

// 索引模块
const indexer = require('./indexer')

// 设置获取用户token的函数
function setGetCurrentUserTokenFunction(tokenFunction) {
  embedding.setGetCurrentUserTokenFunction(tokenFunction)
}

// 初始化知识库
async function initializeKnowledge() {
  try {
    console.log('🧠 初始化知识库...')
    
    // 初始化数据库
    const dbSuccess = await db.initKnowledgeDatabase()
    if (!dbSuccess) {
      throw new Error('数据库初始化失败')
    }
    
    // 设置知识库为已初始化状态
    service.setKnowledgeInitialized(true)
    
    console.log('✅ 知识库初始化完成')
    return true
  } catch (error) {
    console.error('❌ 知识库初始化失败:', error)
    return false
  }
}

// 统一导出所有功能
module.exports = {
  // 配置相关
  ...config,
  
  // 数据库相关
  ...db,
  
  // 嵌入向量相关
  ...embedding,
  
  // 服务相关
  ...service,
  
  // 索引相关
  ...indexer,
  
  // 初始化函数
  setGetCurrentUserTokenFunction,
  initializeKnowledge
} 
import { getKnowledgeConfig, getEmbeddingConfig } from '../config/modelConfig.js'

// 获取知识库配置
export function getKnowledgeConfiguration() {
  return {
    ...getKnowledgeConfig(),
    embedding: getEmbeddingConfig()
  }
}

// 向后兼容的导出
export const KNOWLEDGE_CONFIG = getKnowledgeConfiguration()

// 获取用户桌面文档路径
export async function getDefaultDocumentsPath() {
  try {
    // 通过Electron API获取用户桌面路径
    if (window.electronAPI && window.electronAPI.getPath) {
      const desktop = await window.electronAPI.getPath('desktop')
      return `${desktop}\\Documents` // Windows路径
    }
  } catch (error) {
    console.warn('无法获取默认文档路径:', error)
  }
  return 'C:\\Users\\<USER>\\Desktop\\Documents' // fallback
}

// 设置用户自定义文档路径
export function setDocumentsPath(path) {
  localStorage.setItem('knowledge_documents_path', path)
}

// 获取用户设置的文档路径
export function getDocumentsPath() {
  return localStorage.getItem('knowledge_documents_path') || KNOWLEDGE_CONFIG.defaultDocumentsPath
} 
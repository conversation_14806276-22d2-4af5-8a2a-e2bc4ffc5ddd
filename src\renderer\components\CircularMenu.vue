<template>
  <div class="circular-menu" :class="{ show: show }">
    <div 
      v-for="(item, index) in menuItems" 
      :key="item.id"
      :class="['menu-item', `item-${index}`]"
      :style="getItemStyle(index)"
      @click="handleItemClick(item.id)"
      :title="item.title"
    >
      <span class="menu-icon">{{ item.icon }}</span>
      <span class="menu-label">{{ item.label }}</span>
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'CircularMenu',
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  emits: ['close', 'menu-click'],
  setup(props, { emit }) {
    const menuItems = [
      // { id: 'config', icon: '⚙️', label: '设置', title: '打开设置页面' },
      // { id: 'help', icon: '❓', label: '帮助', title: '查看帮助信息' },
      // { id: 'about', icon: 'ℹ️', label: '关于', title: '查看关于信息' },
      { id: 'exit', icon: '❌', label: '退出', title: '退出应用' }
    ]

    const handleItemClick = (itemId) => {
      emit('menu-click', itemId)
      emit('close')
    }

    const getItemStyle = (index) => {
      const angle = (index * 90) - 45 // 从-45度开始，每个间隔90度
      const radius = 80
      const radian = (angle * Math.PI) / 180
      const x = Math.cos(radian) * radius
      const y = Math.sin(radian) * radius

      return {
        transform: `translate(${x}px, ${y}px)`,
        transitionDelay: `${index * 0.1}s`
      }
    }

    return {
      menuItems,
      handleItemClick,
      getItemStyle
    }
  }
}
</script>

<style lang="scss" scoped>
.circular-menu {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 200px;
  height: 200px;
  pointer-events: none;
  
  &.show {
    pointer-events: auto;
    
    .menu-item {
      opacity: 1;
      transform: translate(var(--x), var(--y)) scale(1);
    }
  }
}

.menu-item {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 50%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
  transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  opacity: 0;
  transform: translate(-50%, -50%) scale(0);
  border: 2px solid rgba(102, 126, 234, 0.2);
  
  &:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translate(var(--x), var(--y)) scale(1.1);
    box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
  }
  
  .menu-icon {
    font-size: 18px;
    margin-bottom: 2px;
  }
  
  .menu-label {
    font-size: 8px;
    font-weight: 500;
    text-align: center;
    line-height: 1;
  }
}

// 为每个菜单项设置CSS变量
.item-0 { --x: 56px; --y: -56px; }  // 右上
.item-1 { --x: 56px; --y: 56px; }   // 右下
.item-2 { --x: -56px; --y: 56px; }  // 左下
.item-3 { --x: -56px; --y: -56px; } // 左上
</style> 
Write-Host "🔄 正在启动Electron开发环境..." -ForegroundColor Cyan

Write-Host "🛑 正在杀掉所有Electron进程..." -ForegroundColor Yellow
try {
    Get-Process -Name "electron" -ErrorAction SilentlyContinue | Stop-Process -Force
    Get-Process -Name "ai-cognidesk-client" -ErrorAction SilentlyContinue | Stop-Process -Force
    Write-Host "✅ Electron进程已清理完成" -ForegroundColor Green
} catch {
    Write-Host "ℹ️ 没有找到需要清理的Electron进程" -ForegroundColor Gray
}

Write-Host "⏳ 等待进程完全退出..." -ForegroundColor Yellow
Start-Sleep -Seconds 2

Write-Host "🚀 正在启动 npm run electron:dev..." -ForegroundColor Green
npm run electron:dev

Write-Host "📝 脚本执行完成" -ForegroundColor Cyan 
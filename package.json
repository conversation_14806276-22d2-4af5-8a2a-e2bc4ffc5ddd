{"name": "nezha-ai-desktop", "version": "1.0.4-beta", "description": "犇犇数字员工助手 - Windows 桌面应用", "main": "src/main/index.js", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview", "electron:dev": "concurrently \"vite\" \"wait-on http://localhost:6913 && electron .\"", "electron:pack": "vite build && electron-builder", "electron:dist": "vite build && electron-builder --publish=never", "electron:debug": "cross-env ELECTRON_DEBUG=true vite build && electron-builder --publish=never", "start": "start-electron-dev.bat", "dist": "npm run electron:dist", "build-dist": "powershell -ExecutionPolicy Bypass -File ./build-dist-simple.ps1", "build-dist-advanced": "powershell -ExecutionPolicy Bypass -File ./build-dist.ps1", "distcmd": "build-dist.bat", "kill-electron": "powershell -ExecutionPolicy Bypass -File ./kill-electron.ps1", "clean": "clean-electron.cmd", "install-word-mcp": "node install-word-mcp.js", "install-weather-mcp": "node install-weather-mcp.js", "install-outlook-calendar-mcp": "node install-outlook-calendar-mcp.js", "install-outlook-calendar-scripts": "node install-outlook-calendar-scripts.js", "start-outlook-calendar-mcp": "node start-outlook-calendar-mcp.js", "setup-python": "node setup-python.js", "test-mcp": "node test-mcp.js", "test-weather-mcp-new": "node test-weather-mcp-new.js", "test-browser-mcp": "node test-browser-mcp.js", "start-browser-mcp": "node start-browser-mcp.js", "install-browser-mcp": "node install-browser-mcp.js", "test-agreement": "node test-agreement.js", "simple-prebuild": "node simple-prebuild.js", "edit-agreement": "start privacy-agreement-editor.html", "test-simple-build": "node test-simple-build.js", "register-protocol": "node register-protocol.js", "prebuild": "npm run setup-python", "postinstall": "electron-builder install-app-deps && npm run install-word-mcp && npm run install-browser-mcp && npm run install-weather-mcp && npm run install-outlook-calendar-mcp && npm run install-outlook-calendar-scripts"}, "keywords": ["electron", "vue3", "desktop", "nezha"], "author": "Nezha Team", "license": "MIT", "devDependencies": {"@vitejs/plugin-vue": "^4.6.2", "concurrently": "^8.2.2", "cross-env": "^7.0.3", "electron": "^28.3.3", "electron-builder": "^24.13.3", "sass": "^1.69.7", "vite": "^4.5.3", "vite-plugin-electron": "^0.28.6", "vite-plugin-electron-renderer": "^0.14.5", "wait-on": "^7.2.0"}, "dependencies": {"@libsql/client": "^0.15.9", "@modelcontextprotocol/sdk": "^0.5.0", "@playwright/mcp": "^0.0.29", "animejs": "^3.2.2", "axios": "^1.10.0", "crypto-js": "^4.2.0", "electron-store": "^8.2.0", "mammoth": "^1.9.1", "openai": "^5.8.2", "outlook-calendar-mcp": "latest", "pinia": "^2.1.7", "playwright": "^1.53.2", "sherpa-onnx-node": "^1.12.6", "turndown": "^7.2.0", "vue": "^3.4.21", "ws": "^8.14.2"}, "optionalDependencies": {"naudiodon2": "^2.5.0"}, "engines": {"node": ">=18.0.0"}}
<template>
  <div v-if="visible" class="email-confirm-modal" @click="handleBackdropClick">
    <div class="modal-content" @click.stop>
      <div class="modal-header">
        <h2>邮件发送确认</h2>
        <button @click="closeModal" class="close-button">×</button>
      </div>
      
      <div class="modal-body">
        <div class="email-form">
          <!-- 收件人 -->
          <div class="form-group">
            <label>收件人:</label>
            <div class="email-list">
              <div v-for="(email, index) in emailData.to" :key="index" class="email-item">
                <input 
                  v-model="emailData.to[index]" 
                  type="email" 
                  placeholder="请输入收件人邮箱"
                  class="email-input"
                />
                <button @click="removeRecipient(index)" class="remove-btn">×</button>
              </div>
              <button @click="addRecipient" class="add-btn">+ 添加收件人</button>
            </div>
          </div>
          
          <!-- 抄送人 -->
          <div class="form-group">
            <label>抄送:</label>
            <div class="email-list">
              <div v-for="(email, index) in emailData.cc" :key="index" class="email-item">
                <input 
                  v-model="emailData.cc[index]" 
                  type="email" 
                  placeholder="请输入抄送人邮箱"
                  class="email-input"
                />
                <button @click="removeCc(index)" class="remove-btn">×</button>
              </div>
              <button @click="addCc" class="add-btn">+ 添加抄送</button>
            </div>
          </div>
          
          <!-- 主题 -->
          <div class="form-group">
            <label>主题:</label>
            <input 
              v-model="emailData.subject" 
              type="text" 
              placeholder="请输入邮件主题"
              class="subject-input"
            />
          </div>
          
          <!-- 正文 -->
          <div class="form-group">
            <label>正文:</label>
            <textarea 
              v-model="emailData.message" 
              placeholder="请输入邮件内容"
              class="message-textarea"
              rows="10"
            ></textarea>
          </div>
        </div>
      </div>
      
      <div class="modal-footer">
        <button @click="closeModal" class="cancel-btn">取消</button>
        <button @click="sendEmail" :disabled="!canSend || isSending" class="send-btn">
          {{ isSending ? '发送中...' : '发送邮件' }}
        </button>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'EmailConfirmModal',
  
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    initialData: {
      type: Object,
      default: () => ({
        to: [],
        cc: [],
        subject: '',
        message: ''
      })
    }
  },
  
  data() {
    return {
      emailData: {
        to: [],
        cc: [],
        subject: '',
        message: ''
      },
      isSending: false
    }
  },
  
  mounted() {
    console.log('📧 [EMAIL_MODAL] EmailConfirmModal组件已挂载')
    console.log('📧 [EMAIL_MODAL] 初始visible状态:', this.visible)
    console.log('📧 [EMAIL_MODAL] 初始initialData:', this.initialData)
    
    // 🔧 修复：如果组件挂载时visible就是true，需要手动初始化数据
    if (this.visible && this.initialData) {
      console.log('📧 [EMAIL_MODAL] 组件挂载时visible为true，手动初始化邮件数据')
      this.initializeEmailData()
    }
  },

  computed: {
    canSend() {
      return this.emailData.to.length > 0 && 
             this.emailData.to.every(email => email.trim()) &&
             this.emailData.subject.trim() &&
             this.emailData.message.trim()
    }
  },
  
  watch: {
    visible(newVal) {
      console.log('📧 [EMAIL_MODAL] visible changed:', newVal)
      if (newVal) {
        console.log('📧 [EMAIL_MODAL] 初始化邮件数据...')
        this.initializeEmailData()
      }
    },
    
    initialData: {
      handler(newVal) {
        if (newVal && this.visible) {
          this.initializeEmailData()
        }
      },
      deep: true
    }
  },
  
  methods: {
    initializeEmailData() {
      console.log('📧 [EMAIL_MODAL] initializeEmailData被调用')
      console.log('📧 [EMAIL_MODAL] this.initialData:', this.initialData)
      console.log('📧 [EMAIL_MODAL] initialData详细结构:', JSON.stringify(this.initialData, null, 2))
      
      this.emailData = {
        to: this.initialData.to && this.initialData.to.length > 0 ? [...this.initialData.to] : [''],
        cc: this.initialData.cc && this.initialData.cc.length > 0 ? [...this.initialData.cc] : [],
        subject: this.initialData.subject || '',
        message: this.initialData.message || ''
      }
      
      console.log('📧 [EMAIL_MODAL] 初始化后的emailData:', this.emailData)
    },
    
    addRecipient() {
      this.emailData.to.push('')
    },
    
    removeRecipient(index) {
      if (this.emailData.to.length > 1) {
        this.emailData.to.splice(index, 1)
      }
    },
    
    addCc() {
      this.emailData.cc.push('')
    },
    
    removeCc(index) {
      this.emailData.cc.splice(index, 1)
    },
    
    handleBackdropClick(event) {
      if (event.target === event.currentTarget) {
        this.closeModal()
      }
    },
    
    closeModal() {
      this.$emit('close')
    },
    
    async sendEmail() {
      if (!this.canSend || this.isSending) return
      
      this.isSending = true
      
      try {
        // 过滤空的邮箱地址
        const to = this.emailData.to.filter(email => email.trim())
        const cc = this.emailData.cc.filter(email => email.trim())
        
        const emailPayload = {
          to,
          cc: cc.length > 0 ? cc : undefined,
          sub: this.emailData.subject.trim(),
          message: this.emailData.message.trim(),
          is_ok: true // 用户已确认
        }
        
        this.$emit('send', emailPayload)
        
      } catch (error) {
        console.error('发送邮件时出错:', error)
        this.$emit('error', '发送邮件时出错: ' + error.message)
      } finally {
        this.isSending = false
      }
    }
  }
}
</script>

<style scoped>
.email-confirm-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(4px);
}

.modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  max-width: 800px;
  max-height: 90vh;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid #e1e5e9;
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: #f8f9fa;
}

.modal-header h2 {
  margin: 0;
  color: #2c3e50;
  font-size: 20px;
  font-weight: 600;
}

.close-button {
  background: none;
  border: none;
  font-size: 24px;
  cursor: pointer;
  color: #6c757d;
  padding: 0;
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.close-button:hover {
  background: #e9ecef;
  color: #495057;
}

.modal-body {
  flex: 1;
  padding: 24px;
  overflow-y: auto;
}

.email-form {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-weight: 600;
  color: #2c3e50;
  font-size: 14px;
}

.email-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.email-item {
  display: flex;
  align-items: center;
  gap: 8px;
}

.email-input {
  flex: 1;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.email-input:focus {
  outline: none;
  border-color: #007AFF;
}

.remove-btn {
  background: #dc3545;
  color: white;
  border: none;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  cursor: pointer;
  font-size: 14px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.remove-btn:hover {
  background: #c82333;
}

.add-btn {
  background: #28a745;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.2s;
  align-self: flex-start;
}

.add-btn:hover {
  background: #218838;
}

.subject-input {
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  transition: border-color 0.2s;
}

.subject-input:focus {
  outline: none;
  border-color: #007AFF;
}

.message-textarea {
  padding: 12px;
  border: 2px solid #e1e5e9;
  border-radius: 6px;
  font-size: 14px;
  
  resize: vertical;
  min-height: 150px;
  transition: border-color 0.2s;
}

.message-textarea:focus {
  outline: none;
  border-color: #007AFF;
}

.modal-footer {
  padding: 20px 24px;
  border-top: 1px solid #e1e5e9;
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: #f8f9fa;
}

.cancel-btn {
  padding: 10px 20px;
  border: 2px solid #6c757d;
  background: transparent;
  color: #6c757d;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.cancel-btn:hover {
  background: #6c757d;
  color: white;
}

.send-btn {
  padding: 10px 20px;
  border: 2px solid #007AFF;
  background: #007AFF;
  color: white;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  transition: all 0.2s;
}

.send-btn:hover:not(:disabled) {
  background: #0056b3;
  border-color: #0056b3;
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}
</style> 
/**
 * 基于阿里云ASR的智能对话管理器
 * 实现唤醒词检测 + 语音对话功能（使用原生JavaScript TTS）
 * 支持 Sherpa-ONNX 和阿里云ASR两种唤醒词检测引擎
 */
import { AliyunWebAudioSpeechRecognizer } from './AliyunWebAudioSpeechRecognizer.js'
import { ALIYUN_ASR_STATES } from './AliyunWebSocketSpeechRecognizer.js'
import { NewSherpaOnnxWakeWordDetector } from './NewSherpaOnnxWakeWordDetector.js'
import { useChatStore } from '../stores/chat.js'
import { DEFAULT_SMART_VOICE_CONFIG } from './smart-voice-config.js'
import { sendMCPChatRequest } from './mcpChatAPI.js'
import { MCP_SYSTEM_PROMPT } from './mcpToolDefinitions.js'
import { getUserIdentifier, getSessionIdentifier } from './userIdentity.js'

// 对话状态常量
export const ALIYUN_CONVERSATION_STATES = {
  IDLE: 'idle',
  LISTENING_WAKE_WORD: 'listening_wake_word',  // 监听唤醒词
  WAKE_WORD_DETECTED: 'wake_word_detected',    // 检测到唤醒词
  LISTENING_COMMAND: 'listening_command',       // 监听指令
  PROCESSING: 'processing',                     // 处理中
  RESPONDING: 'responding',                     // 回复中
  ERROR: 'error'
}

export class AliyunConversationManager {
  constructor(options = {}) {
    console.log('🚀 初始化阿里云智能对话管理器')
    console.log('🔧 配置选项:', options)

    // 基础配置
    this.config = {
      // 阿里云ASR配置
      accessKeyId: options.accessKeyId || '',
      accessKeySecret: options.accessKeySecret || '',
      appkey: options.appkey || '',
      hotwordId: options.hotwordId || '',
      websocketUrl: options.websocketUrl || 'wss://nls-gateway.cn-shanghai.aliyuncs.com/ws/v1',
      region: options.region || 'cn-shanghai',
      
      // 音频配置
      sampleRate: options.sampleRate || 16000,
      format: options.format || 'pcm',
      channels: options.channels || 1,
      bitsPerSample: options.bitsPerSample || 16,
      
      // 识别配置
      enablePunctuationPrediction: options.enablePunctuationPrediction !== undefined ? options.enablePunctuationPrediction : true,
      enableIntermediateResult: options.enableIntermediateResult !== undefined ? options.enableIntermediateResult : true,
      enableInverseTextNormalization: options.enableInverseTextNormalization !== undefined ? options.enableInverseTextNormalization : true,
      enableDisfluency: options.enableDisfluency !== undefined ? options.enableDisfluency : false,
      enableVoiceDetection: options.enableVoiceDetection !== undefined ? options.enableVoiceDetection : true,
      maxEndSilence: options.maxEndSilence || 800,
      maxSingleSegmentTime: options.maxSingleSegmentTime || 60000,
      noiseThreshold: options.noiseThreshold || 0.4,
      max_speak_time: options.max_speak_time || 30000,

      // TTS配置
      enableTTS: options.enableTTS !== undefined ? options.enableTTS : true,
      ttsRate: options.ttsRate || 2.4,
      ttsPitch: options.ttsPitch || 1.0,
      ttsVolume: options.ttsVolume || 0.8,
      ttsLang: options.ttsLang || 'zh-CN',

      // 唤醒词引擎配置
      wakeWordEngine: options.wakeWordEngine || 'sherpa-onnx',
      
      // 唤醒词配置
      wakeWords: options.wakeWords || ['犇犇', '奔奔', '笨笨'],
      checkInterval: options.checkInterval || 1000,
      recordDuration: options.recordDuration || 2000,

      // Sherpa-ONNX 配置
      sherpaOnnxConfig: options.sherpaOnnxConfig || {
        sensitivity: 0.5,
        numThreads: 1,
        debug: false,
        modelPath: 'assets/wasm'
      },

      // 行为配置
      autoResumeListening: options.autoResumeListening !== undefined ? options.autoResumeListening : true,
      autoRestartAfterError: options.autoRestartAfterError !== undefined ? options.autoRestartAfterError : true,
      debug: options.debug || false
    }

    // 状态管理
    this.currentState = ALIYUN_CONVERSATION_STATES.IDLE
    this.isEnabled = false
    this.isWakeWordListening = false
    this.isCommandListening = false
    this.isResponding = false

    // ASR实例
    this.wakeWordASR = null
    this.commandASR = null
    this.ttsWakeWordASR = null // TTS期间的唤醒词检测
    this.ttsWakeWordDetectionActive = false // TTS期间是否使用Sherpa-ONNX检测
    
    // 新的 Sherpa-ONNX 检测器实例
    this.sherpaDetector = null

    // TTS状态
    this.isTTSPlaying = false
    this.ttsUtterance = null
    this.ttsStartTime = null

    // 唤醒词检测状态
    this.isProcessingWakeWord = false
    this.lastWakeWordTime = 0
    this.lastWakeWordText = ''
    this.ttsWakeWordDetectionDisabled = false // 控制TTS期间的唤醒词检测

    // 指令识别状态
    this.isProcessingCommand = false
    this.finalResults = []
    this.currentSentence = ''
    this.commandTimeout = null
    this.earlyProcessTimer = null // 提前处理定时器

    // 回调函数
    this.onStateChange = null
    this.onWakeWordDetected = null
    this.onResult = null
    this.onError = null
    this.onVolume = null
    this.onPartialResult = null

    // 存储chatStore实例
    this.chatStore = null

    console.log('✅ 阿里云智能对话管理器初始化完成')
  }

  /**
   * 初始化智能对话管理器
   */
  async init() {
    try {
      console.log('🔄 初始化阿里云智能对话管理器...')
      
      // 获取chatStore实例
      this.chatStore = useChatStore()
      
      console.log('✅ 阿里云智能对话管理器初始化成功')
      
    } catch (error) {
      console.error('❌ 初始化阿里云智能对话管理器失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 启用智能对话功能
   */
  async enable() {
    try {
      console.log('🔄 启用阿里云智能对话功能...')
      
      this.isEnabled = true
      this.updateState(ALIYUN_CONVERSATION_STATES.IDLE)
      
      // 获取chatStore实例
      this.chatStore = useChatStore()
      
      // 开始唤醒词监听
      await this.startWakeWordListening()
      
      console.log('✅ 阿里云智能对话功能已启用')
      
    } catch (error) {
      console.error('❌ 启用阿里云智能对话功能失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 启动智能对话功能
   */
  async start() {
    try {
      console.log('🚀 启动阿里云智能对话功能...')
      
      // 启用对话功能
      await this.enable()
      
      console.log('✅ 阿里云智能对话功能已启动')
      
    } catch (error) {
      console.error('❌ 启动阿里云智能对话功能失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 禁用智能对话功能
   */
  async disable() {
    try {
      console.log('🔄 禁用阿里云智能对话功能...')
      
      this.isEnabled = false
      
      // 停止所有ASR实例
      await this.stopAllASRInstances()
      
      // 停止Sherpa-ONNX检测器
      if (this.sherpaDetector) {
        await this.sherpaDetector.stopListening()
        this.sherpaDetector = null
      }
      
      // 停止TTS
      this.stopTTS()
      
      this.updateState(ALIYUN_CONVERSATION_STATES.IDLE)
      
      console.log('✅ 阿里云智能对话功能已禁用')
      
    } catch (error) {
      console.error('❌ 禁用阿里云智能对话功能失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 停止智能对话功能
   */
  async stop() {
    try {
      console.log('⏹️ 停止阿里云智能对话功能...')
      
      // 禁用对话功能
      await this.disable()
      
      console.log('✅ 阿里云智能对话功能已停止')
      
    } catch (error) {
      console.error('❌ 停止阿里云智能对话功能失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 开始唤醒词监听
   */
  async startWakeWordListening() {
    try {
      console.log('🎧 开始阿里云唤醒词监听...')
      
      if (!this.isEnabled) {
        console.log('⚠️ 对话功能未启用，跳过唤醒词监听')
        return
      }
      
      this.updateState(ALIYUN_CONVERSATION_STATES.LISTENING_WAKE_WORD)
      this.isWakeWordListening = true
      
      // 根据配置选择唤醒词引擎
      if (this.config.wakeWordEngine === 'sherpa-onnx') {
        try {
          await this.startSherpaOnnxWakeWordDetection()
        } catch (sherpaError) {
          console.warn('⚠️ Sherpa-ONNX 启动失败，自动回退到阿里云ASR:', sherpaError.message)
          console.log('🔄 正在启动阿里云ASR作为回退选项...')
          await this.startAliyunASRWakeWordDetection()
        }
      } else {
        await this.startAliyunASRWakeWordDetection()
      }
      
      console.log('✅ 阿里云唤醒词监听已启动')
      
    } catch (error) {
      console.error('❌ 启动阿里云唤醒词监听失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 启动 Sherpa-ONNX 唤醒词检测
   */
  async startSherpaOnnxWakeWordDetection() {
    try {
      console.log('🚀 启动 Sherpa-ONNX 唤醒词检测...')
      
      // 创建新的 Sherpa-ONNX 检测器
      if (!this.sherpaDetector) {
        this.sherpaDetector = new NewSherpaOnnxWakeWordDetector({
          keywords: this.config.wakeWords,
          sensitivity: this.config.sherpaOnnxConfig.sensitivity,
          numThreads: this.config.sherpaOnnxConfig.numThreads,
          debug: this.config.sherpaOnnxConfig.debug || this.config.debug,
          verboseDebug: true, // 🔧 【增强】启用详细调试
          showAllResults: true, // 🔧 【增强】显示所有识别结果
          modelPath: this.config.sherpaOnnxConfig.modelPath
        })
        
        // 设置回调函数
        this.sherpaDetector.onKeywordDetected = (result) => {
          console.log('🎯 Sherpa-ONNX 检测到关键词:', result)
          this.handleWakeWordDetected(result.keyword)
        }
        
        this.sherpaDetector.onError = (error) => {
          console.error('❌ Sherpa-ONNX 检测器错误:', error)
          
          // 如果系统仍然启用，尝试重新启动
          if (this.isEnabled && !this.isCommandListening) {
            setTimeout(() => {
              this.continueWakeWordListening()
            }, 1000)
          }
        }
        
        // 初始化检测器
        const initSuccess = await this.sherpaDetector.initialize()
        if (!initSuccess) {
          throw new Error('Sherpa-ONNX 检测器初始化失败')
        }
      }
      
      // 开始监听
      const startSuccess = await this.sherpaDetector.startListening()
      if (!startSuccess) {
        throw new Error('Sherpa-ONNX 检测器启动失败')
      }
      
      console.log('✅ Sherpa-ONNX 唤醒词检测已启动')
      
    } catch (error) {
      console.error('❌ 启动 Sherpa-ONNX 唤醒词检测失败:', error)
      throw error
    }
  }

  /**
   * 启动阿里云ASR唤醒词检测
   */
  async startAliyunASRWakeWordDetection() {
    try {
      console.log('🚀 启动阿里云ASR唤醒词检测...')
      
      // 创建唤醒词检测ASR实例
      this.wakeWordASR = new AliyunWebAudioSpeechRecognizer({
        // 阿里云ASR配置
        accessKeyId: this.config.accessKeyId,
        accessKeySecret: this.config.accessKeySecret,
        appkey: this.config.appkey,
        hotwordId: this.config.hotwordId,
        websocketUrl: this.config.websocketUrl,
        region: this.config.region,
        
        // 音频配置
        sampleRate: this.config.sampleRate,
        format: this.config.format,
        channels: this.config.channels,
        bitsPerSample: this.config.bitsPerSample,
        
        // 识别配置 - 优化唤醒词检测
        enablePunctuationPrediction: true,
        enableIntermediateResult: true,
        enableInverseTextNormalization: true,
        enableDisfluency: false,
        enableVoiceDetection: true,
        maxEndSilence: 600,
        maxSingleSegmentTime: 5000,
        noiseThreshold: this.config.noiseThreshold
      }, this.config.debug)

      // 设置唤醒词检测回调
      this.wakeWordASR.OnRecognitionStart = (res) => {
        console.log('🎙️ 阿里云唤醒词检测开始')
      }

      // 统一的唤醒词检测处理函数
      const processWakeWordText = (text, source) => {
        if (!text || !text.trim()) return
        
        const cleanText = text.trim()
        console.log(`🔍 [${source}] 检测文本:`, cleanText)
        
        // 使用原始ASR文本，不做任何标准化处理
        console.log(`🔄 [${source}] 使用原始ASR文本:`, cleanText)
        
        // 判断是否包含唤醒词
        const matchedWakeWord = this.containsWakeWord(cleanText)
        if (matchedWakeWord) {
          console.log(`🎯 [${source}] 检测到唤醒词:`, { 原文: cleanText, 匹配词: matchedWakeWord })
          this.handleWakeWordDetected(cleanText)
        }
      }

      this.wakeWordASR.OnRecognitionResultChange = (res) => {
        if (res.result && res.result.voice_text_str) {
          // 只在实时识别中检测，避免重复
          processWakeWordText(res.result.voice_text_str, 'REALTIME')
        }
      }

      this.wakeWordASR.OnSentenceEnd = (res) => {
        if (res.result && res.result.voice_text_str) {
          const text = res.result.voice_text_str.trim()
          console.log('📝 阿里云唤醒词检测句子结束:', text)
          
          // 关键优化：句子结束时不再检测唤醒词，避免重复处理
          const now = Date.now()
          if (!this.lastWakeWordText || text !== this.lastWakeWordText || now - this.lastWakeWordTime > 2000) {
            processWakeWordText(text, 'SENTENCE_END')
          } else {
            console.log('⚠️ 跳过重复的句子结束检测:', { text, lastText: this.lastWakeWordText, timeDiff: now - this.lastWakeWordTime })
          }
        }
      }

      this.wakeWordASR.OnRecognitionComplete = (res) => {
        console.log('🏁 阿里云唤醒词检测完成')
        
        // 如果没有检测到唤醒词且系统仍然启用，继续监听
        if (this.isEnabled && !this.isCommandListening) {
          setTimeout(() => {
            this.continueWakeWordListening()
          }, 100)
        }
      }

      this.wakeWordASR.OnError = (error) => {
        console.warn('⚠️ 阿里云唤醒词检测错误:', error)
        
        // 对于唤醒词检测，某些错误是正常的，继续监听
        if (this.isEnabled && !this.isCommandListening) {
          setTimeout(() => {
            this.continueWakeWordListening()
          }, 500)
        }
      }

      // 开始识别
      await this.wakeWordASR.start()
      
      console.log('✅ 阿里云ASR唤醒词检测已启动')
      
    } catch (error) {
      console.error('❌ 启动阿里云ASR唤醒词检测失败:', error)
      throw error
    }
  }

  /**
   * 检查文本是否包含唤醒词
   */
  containsWakeWord(text) {
    if (!text || typeof text !== 'string') return null
    
    const normalizedText = text.toLowerCase().trim()
    
    for (const wakeWord of this.config.wakeWords) {
      const normalizedWakeWord = wakeWord.toLowerCase()
      if (normalizedText.includes(normalizedWakeWord)) {
        return wakeWord
      }
    }
    
    return null
  }

  /**
   * 处理唤醒词检测
   */
  async handleWakeWordDetected(wakeWord) {
    try {
      console.log('🎯 处理唤醒词检测:', wakeWord)
      
      // 防止重复处理
      const now = Date.now()
      if (this.isProcessingWakeWord || (now - this.lastWakeWordTime < 2000)) {
        console.log('⚠️ 跳过重复的唤醒词检测')
        return
      }
      
      this.isProcessingWakeWord = true
      this.lastWakeWordTime = now
      this.lastWakeWordText = wakeWord
      
      // 🔧 【修复】清空ASR存储的文本和状态，避免累积
      console.log('🧹 清空ASR存储的文本和状态')
      this.finalResults = []
      this.currentSentence = ''
      this.isProcessingCommand = false
      
      // 🔧 【修复ASR冲突】确保互斥：停止所有ASR实例，只保留Sherpa-ONNX
      console.log('🔧 确保ASR互斥：停止所有阿里云ASR实例')
      await this.stopAllASRInstances()
      
      // 停止唤醒词监听
      await this.stopWakeWordListening()
      
      // 更新状态
      this.updateState(ALIYUN_CONVERSATION_STATES.WAKE_WORD_DETECTED)
      
      // 播放唤醒确认音（等待完成）
      await this.playWakeWordConfirmation()
      
      // 确保TTS播放完成后再开始指令监听
      if (this.isTTSPlaying) {
        console.log('⏳ 等待TTS播放完成...')
        await new Promise(resolve => {
          const checkTTS = () => {
            if (!this.isTTSPlaying) {
              resolve()
            } else {
              setTimeout(checkTTS, 100)
            }
          }
          checkTTS()
        })
      }
      
      // 开始指令监听
      await this.startCommandListening()
      
      // 通知回调
      if (this.onWakeWordDetected) {
        this.onWakeWordDetected(wakeWord)
      }
      
    } catch (error) {
      console.error('❌ 处理唤醒词检测失败:', error)
      this.handleError(error)
    } finally {
      this.isProcessingWakeWord = false
    }
  }

  /**
   * 播放唤醒确认音（优化版本）
   */
  async playWakeWordConfirmation() {
    try {
      console.log('🔊 播放简短唤醒确认音...')
      
      if (!this.config.enableTTS) {
        console.log('⚠️ TTS已禁用，跳过唤醒确认音')
        return
      }
      
      // 🚀 使用指定的确认回复内容
      const confirmResponses = [
        "我在呢，有啥吩咐？",
        "在的呢～你说，我听着！",
        "来咯～今天我也要努力帮你！",
        "召唤成功！今天我为你效劳～",
        "全力待命，主人～",
        "来啦！今天也要帮你发光发热～"
      ]
      const randomResponse = confirmResponses[Math.floor(Math.random() * confirmResponses.length)]
      
      // 🚀 使用不可打断TTS，快速播放
      await this.playUninterruptibleTTS(randomResponse)
      
      // 🚀 优化：减少等待时间
      await new Promise(resolve => setTimeout(resolve, 50))
      
      console.log('✅ 简短唤醒确认音播放完成')
      
    } catch (error) {
      console.error('❌ 播放唤醒确认音失败:', error)
      // 不抛出错误，继续后续流程
    }
  }

  /**
   * 开始指令监听
   */
  async startCommandListening() {
    try {
      console.log('🎧 开始阿里云指令监听...')
      
      if (!this.isEnabled) {
        console.log('⚠️ 对话功能未启用，跳过指令监听')
        return
      }
      
      // 确保TTS播放完成后再启动指令监听
      if (this.isTTSPlaying) {
        console.log('⏳ 等待TTS播放完成后再启动指令监听...')
        await new Promise(resolve => {
          const checkTTS = () => {
            if (!this.isTTSPlaying) {
              resolve()
            } else {
              setTimeout(checkTTS, 100)
            }
          }
          checkTTS()
        })
      }
      
      // 🔧 【修复ASR冲突】启动指令监听前，确保停止Sherpa-ONNX唤醒词检测
      console.log('🔧 启动指令监听：停止Sherpa-ONNX检测器以避免冲突')
      if (this.sherpaDetector && this.sherpaDetector.isListening) {
        await this.sherpaDetector.stopListening()
        console.log('✅ Sherpa-ONNX检测器已停止，避免与阿里云ASR冲突')
      }
      
      this.updateState(ALIYUN_CONVERSATION_STATES.LISTENING_COMMAND)
      this.isCommandListening = true
      
      // 重置指令识别状态
      this.finalResults = []
      this.currentSentence = ''
      this.isProcessingCommand = false
      
      // 🚀 优化：设置指令监听超时，给用户更多时间思考
      this.commandTimeout = setTimeout(() => {
        console.log('⏰ 指令监听超时，回到唤醒词监听')
        this.backToWakeWordListening()
      }, Math.min(this.config.max_speak_time, 30000)) // 最大30秒
      
      // 创建指令识别ASR实例
      this.commandASR = new AliyunWebAudioSpeechRecognizer({
        // 阿里云ASR配置
        accessKeyId: this.config.accessKeyId,
        accessKeySecret: this.config.accessKeySecret,
        appkey: this.config.appkey,
        hotwordId: this.config.hotwordId,
        websocketUrl: this.config.websocketUrl,
        region: this.config.region,
        
        // 音频配置
        sampleRate: this.config.sampleRate,
        format: this.config.format,
        channels: this.config.channels,
        bitsPerSample: this.config.bitsPerSample,
        
        // 识别配置 - 优化指令识别（停止1秒没有说话）
        enablePunctuationPrediction: true,
        enableIntermediateResult: true,
        enableInverseTextNormalization: true,
        enableDisfluency: false,
        enableVoiceDetection: true,
        maxEndSilence: 1000, // 🚀 设置为1秒静音后自动结束
        maxSingleSegmentTime: Math.min(this.config.maxRecordingTime || 30000, 60000),
        noiseThreshold: this.config.noiseThreshold
      }, this.config.debug)

      console.log('🎙️ 指令识别ASR实例创建完成，准备启动...')

      // 设置指令识别回调
      this.commandASR.OnRecognitionStart = (res) => {
        console.log('🎙️ 阿里云指令识别开始')
        console.log('🎙️ 识别任务ID:', res.task_id)
        // 有语音输入，清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
      }

      this.commandASR.OnSentenceBegin = (res) => {
        const timestamp = new Date().toISOString()
        console.log(`📝 [${timestamp}] 开始说话`)
        this.currentSentence = ''
        // 确保清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
      }

      this.commandASR.OnRecognitionResultChange = (res) => {
        if (res.result && res.result.voice_text_str) {
          this.currentSentence = res.result.voice_text_str
          const timestamp = new Date().toISOString()
          console.log(`🔄 [${timestamp}] 阿里云指令识别中:`, this.currentSentence)
          
          // 🚀 优化：如果识别到相对完整的句子且停顿时间足够，考虑提前处理
          if (this.currentSentence.length > 5 && 
              (this.currentSentence.includes('？') || 
               this.currentSentence.includes('。') || 
               this.currentSentence.includes('！') ||
               this.currentSentence.length > 15)) {
            // 设置短暂延迟，如果没有更多输入就处理当前句子
            if (this.earlyProcessTimer) {
              clearTimeout(this.earlyProcessTimer)
            }
            this.earlyProcessTimer = setTimeout(() => {
              if (this.isCommandListening && this.currentSentence && 
                  this.currentSentence === res.result.voice_text_str) {
                console.log('🚀 [EARLY] 检测到完整句子，提前处理:', this.currentSentence)
                this.finalResults = [this.currentSentence]
                this.handleBackupCommandComplete()
              }
            }, 800) // 800ms没有新的识别结果就处理
          }
          
          // 通知部分识别结果
          if (this.onPartialResult) {
            this.onPartialResult(this.currentSentence)
          }
        }
      }

      this.commandASR.OnSentenceEnd = (res) => {
        if (res.result && res.result.voice_text_str) {
          const finalText = res.result.voice_text_str.trim()
          const timestamp = new Date().toISOString()
          console.log(`✅ [${timestamp}] 阿里云指令句子结束:`, finalText)
          
          if (finalText) {
            this.finalResults.push(finalText)
            console.log('📝 添加到结果数组:', this.finalResults)
            
            // 🚀 优化：缩短备用超时时间，更快响应
            setTimeout(() => {
              if (this.isCommandListening && this.finalResults.length > 0) {
                console.log('🚨 [BACKUP] OnRecognitionComplete未被调用，手动触发结果处理')
                console.log('🚨 [BACKUP] 当前状态:', {
                  isCommandListening: this.isCommandListening,
                  finalResults: this.finalResults,
                  isProcessingCommand: this.isProcessingCommand
                })
                this.handleBackupCommandComplete()
              }
            }, 1000) // 🚀 从2秒减少到1秒
          }
        }
      }

      this.commandASR.OnRecognitionComplete = (res) => {
        console.log('🏁 [CRITICAL] 阿里云指令识别完成被调用！')
        
        // 防止重复处理
        if (this.isProcessingCommand) {
          console.log('⚠️ 正在处理指令，跳过OnRecognitionComplete')
          return
        }
        this.isProcessingCommand = true
        
        this.isCommandListening = false
        
        // 清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
        
        // 优化识别结果处理逻辑
        let completeText = ''
        
        // 优先使用finalResults中的完整句子
        if (this.finalResults.length > 0) {
          completeText = this.finalResults.join('').trim()
          console.log('📝 使用finalResults拼接:', completeText)
        }
        
        // 如果没有完整句子，但有当前句子，也尝试使用
        if (!completeText && this.currentSentence) {
          completeText = this.currentSentence.trim()
          console.log('📝 使用currentSentence:', completeText)
        }
        
        // 尝试从OnRecognitionComplete的结果中获取
        if (!completeText && res.result && res.result.voice_text_str) {
          completeText = res.result.voice_text_str.trim()
          console.log('📝 使用Complete结果:', completeText)
        }
        
        // 清理结果
        this.finalResults = []
        this.currentSentence = ''
        
        console.log('🎯 阿里云最终识别结果:', completeText)
        
        // 处理识别结果
        if (completeText && this.isValidSpeechResult(completeText)) {
          console.log('✅ 识别结果有效，调用大模型:', completeText)
          this.handleCommandResult(completeText)
        } else {
          console.log('❌ 指令识别结果无效')
          this.backToWakeWordListening()
        }
        
        // 重置处理标志
        this.isProcessingCommand = false
      }

      this.commandASR.OnError = (error) => {
        console.error('❌ 阿里云指令识别错误:', error)
        this.isCommandListening = false
        
        // 清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
        
        if (this.earlyProcessTimer) {
          clearTimeout(this.earlyProcessTimer)
          this.earlyProcessTimer = null
        }
        
        // 🚀 优化：针对连接超时错误的特殊处理
        if (error.message && error.message.includes('连接空闲超时')) {
          console.log('🔄 检测到连接超时，立即重新启动指令识别')
          // 延迟重启，给系统时间清理
          setTimeout(async () => {
            if (this.isEnabled && !this.isCommandListening) {
              try {
                console.log('🚀 重新启动指令识别...')
                await this.startCommandListening()
              } catch (restartError) {
                console.error('❌ 重启指令识别失败:', restartError)
                this.backToWakeWordListening()
              }
            }
          }, 500)
        } else {
          // 其他错误，回到唤醒词监听
          this.backToWakeWordListening()
        }
      }

      // 开始识别
      console.log('🚀 启动阿里云指令识别ASR...')
      await this.commandASR.start()
      console.log('✅ 阿里云指令识别ASR启动成功')
      console.log('✅ 阿里云指令监听已启动')
      
    } catch (error) {
      console.error('❌ 开始阿里云指令监听失败:', error)
      this.isCommandListening = false
      
      // 清除超时
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      this.backToWakeWordListening()
    }
  }

  /**
   * 处理备用指令完成
   */
  handleBackupCommandComplete() {
    if (this.isProcessingCommand) return
    
    this.isProcessingCommand = true
    this.isCommandListening = false
    
    // 清除超时
    if (this.commandTimeout) {
      clearTimeout(this.commandTimeout)
      this.commandTimeout = null
    }
    
    if (this.earlyProcessTimer) {
      clearTimeout(this.earlyProcessTimer)
      this.earlyProcessTimer = null
    }
    
    // 清除提前处理定时器
    if (this.earlyProcessTimer) {
      clearTimeout(this.earlyProcessTimer)
      this.earlyProcessTimer = null
    }
    
    // 处理结果
    let completeText = ''
    if (this.finalResults.length > 0) {
      completeText = this.finalResults.join('').trim()
    }
    
    // 清理结果
    this.finalResults = []
    this.currentSentence = ''
    
    if (completeText && this.isValidSpeechResult(completeText)) {
      this.handleCommandResult(completeText)
    } else {
      this.backToWakeWordListening()
    }
    
    this.isProcessingCommand = false
  }

  /**
   * 处理用户指令 - 统一使用与文本输入相同的MCP处理流程
   */
  async handleCommandResult(command) {
    const startTime = Date.now()
    const timestamp = new Date().toISOString()
    console.log(`🎯📨 [${timestamp}] 处理用户语音指令开始（统一MCP流程）:`, command)
    console.log(`⏱️ [时间] 指令处理开始时间: ${timestamp}`)
    
    try {
      this.updateState(ALIYUN_CONVERSATION_STATES.PROCESSING)
      
      // 使用原始ASR指令，不做任何标准化处理
      console.log('🔄 [ASR_ORIGINAL] 使用原始ASR指令:', command)
      
      // 指令识别完毕，播放简短确认语音
      console.log('🔊 [COMMAND_CONFIRM] 指令识别完毕，播放简短确认语音：好的（快速不可打断）')
      try {
        await this.playUninterruptibleTTS('好的')
        console.log('✅ [COMMAND_CONFIRM] 确认语音播放完成')
        
        // 🚀 优化：取消等待时间，立即处理指令
        console.log('🔧 [COMMAND_CONFIRM] 立即处理指令，无需等待')
        
      } catch (ttsError) {
        console.warn('⚠️ [COMMAND_CONFIRM] 确认语音播放失败，继续处理指令:', ttsError.message)
      }
      
      console.log('🔄 [UNIFIED_FLOW] 语音指令将使用与文本输入相同的MCP处理流程')
      
      // 通知外部组件处理用户语音指令
      if (this.onResult) {
        this.onResult({
          type: 'user_command',
          text: command.trim(),
          timestamp: Date.now(),
          isVoiceInput: true
        })
      } else {
        console.warn('⚠️ onResult回调未设置，无法处理指令')
        this.backToWakeWordListening()
      }
      
    } catch (error) {
      console.error('❌ 处理用户指令失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 检查是否为有效语音结果
   */
  isValidSpeechResult(text) {
    if (!text || typeof text !== 'string') {
      return false
    }
    
    const cleanText = text.trim()
    
    if (cleanText.length === 0) {
      return false
    }
    
    // 检查是否包含有意义的字符
    const meaningfulChars = cleanText.match(/[\u4e00-\u9fa5a-zA-Z0-9]/g)
    if (!meaningfulChars || meaningfulChars.length === 0) {
      return false
    }
    
    // 放宽验证条件：只要包含一个有效字符就认为有效
    if (meaningfulChars.length >= 1) {
      return true
    }
    
    return true
  }

  /**
   * 继续唤醒词监听
   */
  async continueWakeWordListening() {
    if (!this.isEnabled || this.isCommandListening) {
      return
    }
    
    try {
      console.log('🔄 继续阿里云唤醒词监听...')
      
      // 停止当前的唤醒词监听
      await this.stopWakeWordListening()
      
      // 短暂延迟后重新开始
      setTimeout(async () => {
        if (this.isEnabled && !this.isCommandListening) {
          await this.startWakeWordListening()
        }
      }, 500)
      
    } catch (error) {
      console.error('❌ 继续阿里云唤醒词监听失败:', error)
    }
  }

  /**
   * 回到唤醒词监听
   */
  async backToWakeWordListening() {
    try {
      console.log('🔄 回到阿里云唤醒词监听...')
      
      // 🔧 【修复ASR冲突】停止所有阿里云ASR实例，确保互斥
      console.log('🔧 回到唤醒词监听：停止所有阿里云ASR实例')
      await this.stopCommandListening()
      await this.stopAllASRInstances()
      
      // 重置状态
      this.isCommandListening = false
      this.isProcessingCommand = false
      
      // 如果系统仍然启用，重新开始唤醒词监听（仅使用Sherpa-ONNX）
      if (this.isEnabled && this.config.autoResumeListening) {
        console.log('🔧 重新启动Sherpa-ONNX唤醒词监听')
        await this.startWakeWordListening()
      }
      
    } catch (error) {
      console.error('❌ 回到阿里云唤醒词监听失败:', error)
      this.handleError(error)
    }
  }

  /**
   * 停止唤醒词监听
   */
  async stopWakeWordListening() {
    try {
      console.log('🛑 停止阿里云唤醒词监听...')
      
      this.isWakeWordListening = false
      
      // 停止阿里云ASR唤醒词检测
      if (this.wakeWordASR) {
        await this.wakeWordASR.stop()
        this.wakeWordASR = null
      }
      
          // 停止Sherpa-ONNX检测器
    if (this.sherpaDetector) {
      await this.sherpaDetector.stopListening()
    }
      
      console.log('✅ 阿里云唤醒词监听已停止')
      
    } catch (error) {
      console.error('❌ 停止阿里云唤醒词监听失败:', error)
    }
  }

  /**
   * 停止指令监听
   */
  async stopCommandListening() {
    try {
      console.log('🛑 停止阿里云指令监听...')
      
      this.isCommandListening = false
      
      // 清除超时
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      // 停止指令ASR
      if (this.commandASR) {
        await this.commandASR.stop()
        this.commandASR = null
      }
      
      // 清理状态
      this.finalResults = []
      this.currentSentence = ''
      this.isProcessingCommand = false
      
      console.log('✅ 阿里云指令监听已停止')
      
    } catch (error) {
      console.error('❌ 停止阿里云指令监听失败:', error)
    }
  }

  /**
   * 停止所有ASR实例
   */
  async stopAllASRInstances() {
    try {
      console.log('🛑 停止所有阿里云ASR实例...')
      
      // 停止唤醒词监听
      await this.stopWakeWordListening()
      
      // 停止指令监听
      await this.stopCommandListening()
      
      // 停止TTS期间的唤醒词检测
      if (this.ttsWakeWordASR) {
        await this.ttsWakeWordASR.stop()
        this.ttsWakeWordASR = null
      }
      
      this.ttsWakeWordDetectionActive = false
      
      console.log('✅ 所有阿里云ASR实例已停止')
      
    } catch (error) {
      console.error('❌ 停止所有阿里云ASR实例失败:', error)
    }
  }

  /**
   * 播放不可中断的TTS回复
   * @param {string} text - 要播放的文本
   * @returns {Promise<void>}
   */
  async playUninterruptibleTTS(text) {
    const startTime = Date.now()
    const timestamp = new Date().toISOString()
    console.log(`🔊 [${timestamp}] 开始播放不可中断确认回复:`, text)
    
    // 临时禁用唤醒词检测 - 防止确认回复被打断
    const wasWakeWordEnabled = this.isWakeWordEnabled
    this.isWakeWordEnabled = false
    console.log(`⏸️ [${timestamp}] 已禁用唤醒词检测，防止确认回复被打断`)
    
    try {
      // 播放TTS
      const playStartTime = Date.now()
      await this.playTTS(text)
      const playEndTime = Date.now()
      console.log(`⏱️ [时间] TTS播放耗时: ${playEndTime - playStartTime}ms`)
      
      // 短暂延迟确保播放完成
      await new Promise(resolve => setTimeout(resolve, 200))
      
    } catch (error) {
      console.error('🔊❌ 播放确认回复失败:', error)
    } finally {
      // 恢复唤醒词检测
      this.isWakeWordEnabled = wasWakeWordEnabled
      const totalTime = Date.now() - startTime
      console.log(`✅ [${timestamp}] 确认回复播放完成，总耗时: ${totalTime}ms，已恢复唤醒词检测`)
    }
  }

  /**
   * 播放TTS并支持唤醒词检测
   */
  async playTTSWithWakeWordDetection(text) {
    try {
      console.log('🔊 播放TTS并监听唤醒词:', text)
      
      // 开始TTS期间的唤醒词检测
      await this.startWakeWordDetectionDuringTTS()
      
      // 播放TTS
      await this.playTTS(text)
      
      // 停止TTS期间的唤醒词检测
      await this.stopWakeWordDetectionDuringTTS()
      
      console.log('✅ TTS播放完成，唤醒词检测已恢复')
      
    } catch (error) {
      console.error('❌ 播放TTS失败:', error)
      
      // 确保停止TTS期间的唤醒词检测
      await this.stopWakeWordDetectionDuringTTS()
      
      throw error
    }
  }

  /**
   * 在TTS播放期间启动唤醒词检测
   */
  async startWakeWordDetectionDuringTTS() {
    if (this.ttsWakeWordDetectionActive || !this.isEnabled || this.ttsWakeWordDetectionDisabled) {
      return
    }

    try {
      console.log('🎧 TTS播放期间继续使用 Sherpa-ONNX 监听唤醒词...')
      
      // 🔧 【修复ASR冲突】TTS期间只使用Sherpa-ONNX，不再创建阿里云ASR实例
      if (this.sherpaDetector && this.sherpaDetector.isInitialized) {
        console.log('✅ TTS期间继续使用 Sherpa-ONNX 检测器')
        this.ttsWakeWordDetectionActive = true
        return
      }
      
      // 如果Sherpa-ONNX不可用，不再回退到阿里云ASR，避免冲突
      console.log('⚠️ Sherpa-ONNX 不可用，TTS期间禁用唤醒词检测以避免冲突')
      this.ttsWakeWordDetectionDisabled = true
      
      // 不再创建TTS期间的阿里云ASR实例，避免冲突
      return
      
    } catch (error) {
      console.error('❌ TTS期间启动唤醒词检测失败:', error)
    }
  }

  /**
   * 停止TTS期间的唤醒词检测
   */
  async stopWakeWordDetectionDuringTTS() {
    if (!this.ttsWakeWordDetectionActive) {
      return
    }

    try {
      console.log('🛑 停止TTS期间的唤醒词检测...')
      
      // 停止TTS期间的ASR
      if (this.ttsWakeWordASR) {
        await this.ttsWakeWordASR.stop()
        this.ttsWakeWordASR = null
      }
      
      this.ttsWakeWordDetectionActive = false
      
      console.log('✅ TTS期间的唤醒词检测已停止')
      
    } catch (error) {
      console.error('❌ 停止TTS期间的唤醒词检测失败:', error)
    }
  }

  /**
   * 处理TTS期间检测到的唤醒词
   */
  async handleWakeWordDetectedDuringTTS(wakeWord) {
    try {
      console.log('🎯 TTS期间检测到唤醒词，打断播放:', wakeWord)
      
      // 立即停止TTS
      this.stopTTS()
      
      // 停止TTS期间的唤醒词检测
      await this.stopWakeWordDetectionDuringTTS()
      
      // 处理唤醒词
      await this.handleWakeWordDetected(wakeWord)
      
    } catch (error) {
      console.error('❌ 处理TTS期间唤醒词失败:', error)
    }
  }

  /**
   * 播放TTS
   */
  async playTTS(text) {
    return new Promise((resolve, reject) => {
      try {
        if (!this.config.enableTTS) {
          console.log('⚠️ TTS已禁用，跳过播放')
          resolve()
          return
        }
        
        console.log('🔊 播放TTS:', text)
        
        // 停止当前TTS
        this.stopTTS()
        
        // 创建语音合成
        this.ttsUtterance = new SpeechSynthesisUtterance(text)
        this.ttsUtterance.rate = this.config.ttsRate
        this.ttsUtterance.pitch = this.config.ttsPitch
        this.ttsUtterance.volume = this.config.ttsVolume
        this.ttsUtterance.lang = this.config.ttsLang
        
        // 设置回调
        this.ttsUtterance.onstart = () => {
          console.log('🔊 TTS开始播放')
          this.isTTSPlaying = true
          this.ttsStartTime = Date.now()
          this.updateState(ALIYUN_CONVERSATION_STATES.RESPONDING)
        }
        
        this.ttsUtterance.onend = () => {
          console.log('🔊 TTS播放结束')
          this.isTTSPlaying = false
          this.ttsUtterance = null
          this.ttsStartTime = null
          resolve()
        }
        
        this.ttsUtterance.onerror = (error) => {
          console.log('🔊 TTS播放事件:', error.type, error.error)
          this.isTTSPlaying = false
          this.ttsUtterance = null
          this.ttsStartTime = null
          
          // 🔧 【修复】区分正常打断和真正的错误
          if (error.error === 'interrupted') {
            console.log('🛑 TTS被正常打断（唤醒词检测或用户操作）')
            resolve() // 正常打断不作为错误处理
          } else {
            console.error('❌ TTS播放真正错误:', error)
            reject(error)
          }
        }
        
        // 开始播放
        speechSynthesis.speak(this.ttsUtterance)
        
      } catch (error) {
        console.error('❌ 播放TTS失败:', error)
        this.isTTSPlaying = false
        this.ttsUtterance = null
        this.ttsStartTime = null
        reject(error)
      }
    })
  }

  /**
   * 停止TTS
   */
  stopTTS() {
    try {
      if (this.isTTSPlaying) {
        console.log('🛑 停止TTS播放')
        speechSynthesis.cancel()
        this.isTTSPlaying = false
        this.ttsUtterance = null
        this.ttsStartTime = null
      }
    } catch (error) {
      console.error('❌ 停止TTS失败:', error)
    }
  }

  /**
   * 更新状态
   */
  updateState(newState) {
    const oldState = this.currentState
    this.currentState = newState
    
    console.log(`🔄 阿里云对话状态变更: ${oldState} -> ${newState}`)
    
    if (this.onStateChange) {
      this.onStateChange(newState, oldState)
    }
  }

  /**
   * 处理错误
   */
  handleError(error) {
    console.error('❌ 阿里云对话管理器错误:', error)
    
    this.updateState(ALIYUN_CONVERSATION_STATES.ERROR)
    
    if (this.onError) {
      this.onError(error)
    }
    
    // 如果启用了自动重启，尝试重新启动
    if (this.config.autoRestartAfterError && this.isEnabled) {
      setTimeout(() => {
        this.backToWakeWordListening()
      }, 2000)
    }
  }

  /**
   * 获取当前状态
   */
  getState() {
    return {
      currentState: this.currentState,
      isEnabled: this.isEnabled,
      isWakeWordListening: this.isWakeWordListening,
      isCommandListening: this.isCommandListening,
      isResponding: this.isResponding,
      isTTSPlaying: this.isTTSPlaying,
      ttsWakeWordDetectionActive: this.ttsWakeWordDetectionActive,
      lastWakeWordTime: this.lastWakeWordTime,
      lastWakeWordText: this.lastWakeWordText
    }
  }

  /**
   * 处理AI回复（语音问答） - 简化为只处理TTS播放
   */
  async handleAIResponse(responseText, response = null) {
    console.log('🗣️ 处理语音问答AI回复（TTS播放）:', responseText)
    
    // 🔧 防止重复回复
    if (this.isResponding) {
      console.log('⚠️ 正在回复中，跳过重复回复处理')
      return
    }
    
    this.isResponding = true
    
    this.updateState(ALIYUN_CONVERSATION_STATES.RESPONDING)
    
    try {
      console.log('🔊 开始语音问答的语音回复（带唤醒词检测）')
      
      // 确保没有其他TTS正在播放，等待一小段时间
      if (this.isTTSPlaying || window.speechSynthesis?.speaking) {
        console.log('⚠️ 检测到其他TTS正在播放，强制停止并等待')
        this.stopTTS()
        await new Promise(resolve => setTimeout(resolve, 200))
      }
      
      // 开始语音回复，期间监听唤醒词以便打断
      await this.playTTSWithWakeWordDetection(responseText)
      console.log('✅ 语音问答的语音回复播放完成')
      
    } finally {
      // 🔧 确保重置响应状态
      this.isResponding = false

      // 🔧 【修复】TTS播放完成后，立即更新状态并回到唤醒词监听
      if (this.isEnabled && !this.isCommandListening) {
        console.log('🔄 AI回复完成，立即回到唤醒词监听状态')
        // 立即更新状态，不等待延迟
        this.updateState(ALIYUN_CONVERSATION_STATES.LISTENING_WAKE_WORD)
        // 🚀 优化：进一步减少延迟时间，快速恢复监听
        setTimeout(async () => {
          if (this.isEnabled && !this.isCommandListening) {
            await this.backToWakeWordListening()
          }
        }, 50) // 进一步减少延迟时间
      }
    }
  }

  /**
   * 销毁实例
   */
  async destroy() {
    try {
      console.log('🗑️ 销毁阿里云对话管理器...')
      
      // 禁用功能
      await this.disable()
      
      // 清理回调
      this.onStateChange = null
      this.onWakeWordDetected = null
      this.onResult = null
      this.onError = null
      this.onVolume = null
      this.onPartialResult = null
      
      // 清理状态
      this.chatStore = null
      this.finalResults = []
      this.currentSentence = ''
      
      console.log('✅ 阿里云对话管理器已销毁')
      
    } catch (error) {
      console.error('❌ 销毁阿里云对话管理器失败:', error)
    }
  }
}

console.log('✅ 阿里云智能对话管理器已加载') 
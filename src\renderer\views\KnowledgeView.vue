<template>
  <div class="knowledge-view">
    <div class="view-header">
      <div class="header-content">
        <h1 class="view-title">知识库</h1>
      </div>
    </div>

    <div class="view-content">
      <!-- 我的知识库标题 -->
      <div class="knowledge-header">
        <h2 class="knowledge-title">我的知识库</h2>
        <div class="header-actions">
          <!-- <button @click="refreshStats" class="action-btn" :disabled="loading">
            <img v-if="!loading" src="/assets/icon-sm.png" alt="刷新" class="action-icon">
            <span v-else>刷新中...</span>
          </button> -->
          <!-- <button @click="fixKnowledgeDatabase" class="action-btn info" :disabled="loading">
            🔧 修复数据库
          </button> -->
          <button @click="showClearDialog = true" class="action-btn"
            :disabled="loading || stats.totalFiles === 0">
            <img :src="iconCleanImg" alt="清空知识库" class="action-icon">
          </button>
          <button @click="rebuildKnowledgeBase" class="action-btn" :disabled="loading">
            <img :src="iconPathImg" alt="重建知识库" class="action-icon">
          </button>
          <button @click="showAddDocuments = true" class="action-btn" :disabled="loading">
            <img :src="iconSmImg" alt="添加文档" class="action-icon">
          </button>
        </div>
      </div>

      <!-- 知识库文件夹 -->
      <!-- <div class="knowledge-folders">
        <div class="folder-item">
          <div class="folder-icon">
            📁
          </div>
          <div class="folder-info">
            <span class="folder-name">知识库文档</span>
            <span class="folder-path">{{ userDataPath }}</span>
          </div>
        </div>
      </div> -->

      <!-- 知识库统计卡片 -->
      <div class="knowledge-stats">
        <div v-if="documents.length > 0" class="stats-grid">
          <div v-for="doc in documents" :key="doc.id" class="stat-card">
            <div class="card-header">
              <h3 class="card-title">{{ doc.fileName }}</h3>
              <span class="status-badge">已归档</span>
            </div>
            <div class="card-content">
              <div class="stat-line">
                <span class="stat-text">{{ doc.segmentCount || 0 }}分段/{{ formatSize(doc.fileSize || 0) }}</span>
              </div>
              <div class="stat-line">
                <span class="stat-label">文件大小:</span>
                <span class="stat-value">{{ formatSize(doc.fileSize || 0) }}</span>
              </div>
              <div class="stat-line">
                <span class="stat-label">更新时间:</span>
                <span class="stat-value">{{ formatDate(doc.createTime) }}</span>
              </div>
            </div>
            <!-- <div class="card-actions">
              <button @click="deleteDocument(doc.id)" class="delete-btn" :disabled="loading">
                删除
              </button>
            </div> -->
          </div>
        </div>
        <div v-else-if="!loading" class="empty-state">
          <div class="empty-icon">📄</div>
          <h3>暂无知识库文档</h3>
          <p>点击"添加文档"开始构建您的知识库</p>
        </div>
      </div>

      <!-- 总体统计 -->
      <div class="summary-stats">
        <div class="summary-item">
          <span class="summary-label">总文档数:</span>
          <span class="summary-value">{{ stats.totalFiles }}</span>
        </div>
        <div class="summary-item">
          <span class="summary-label">总片段数:</span>
          <span class="summary-value">{{ stats.totalSegments }}</span>
        </div>
      </div>
    </div>

    <!-- 添加文档对话框 -->
    <div v-if="showAddDocuments" class="modal-overlay" @click="closeAddDialog">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h3>添加文档到知识库</h3>
          <button @click="closeAddDialog" class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <div class="add-options">
            <button @click="selectFiles" class="option-btn primary">
              📁 选择文件
            </button>
            <button @click="selectDirectory" class="option-btn">
              📂 选择文件夹
            </button>
          </div>

          <div v-if="selectedFiles.length > 0" class="selected-files">
            <h4>已选择文件 ({{ selectedFiles.length }} 个)</h4>
            <div class="file-list">
              <div v-for="(file, index) in selectedFiles" :key="index" class="file-item">
                <span class="file-name">{{ getFileName(file) }}</span>
                <button @click="removeFile(index)" class="remove-btn">移除</button>
              </div>
            </div>
          </div>

          <div v-if="indexProgress.show" class="index-progress">
            <h4>正在索引文档...</h4>
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: indexProgress.percentage + '%' }"></div>
            </div>
            <p class="progress-text">
              {{ indexProgress.current }} / {{ indexProgress.total }}
              - {{ indexProgress.currentFile }}
            </p>
          </div>
        </div>
        <div class="modal-footer">
          <button @click="closeAddDialog" class="modal-btn secondary" :disabled="indexing">
            取消
          </button>
          <button @click="startIndexing" class="modal-btn primary" :disabled="selectedFiles.length === 0 || indexing">
            开始索引 ({{ selectedFiles.length }} 个文件)
          </button>
        </div>
      </div>
    </div>

    <!-- 清空确认对话框 -->
    <div v-if="showClearDialog" class="modal-overlay" @click="showClearDialog = false">
      <div class="modal-content small" @click.stop>
        <div class="modal-header">
          <h3>确认清空知识库</h3>
          <button @click="showClearDialog = false" class="modal-close">×</button>
        </div>
        <div class="modal-body">
          <p>此操作将删除所有文档和知识片段，不可恢复。确定要继续吗？</p>
        </div>
        <div class="modal-footer">
          <button @click="showClearDialog = false" class="modal-btn secondary">
            取消
          </button>
          <button @click="clearKnowledgeBase" class="modal-btn danger">
            确认清空
          </button>
        </div>
      </div>
    </div>

    <!-- 状态消息 -->
    <div v-if="statusMessage" class="status-message" :class="statusType">
      {{ statusMessage }}
    </div>
  </div>
</template>

<script>
import { ref, reactive, onMounted } from 'vue'
import {
  initKnowledgeDatabase,
  getKnowledgeStats,
  indexDocuments,
  selectDocumentDirectory,
  getDirectoryFiles,
  listFiles,
  deleteFile,
  clearKnowledgeBase as clearKnowledgeBaseAPI,
  rebuildKnowledgeBase as rebuildKnowledgeBaseAPI,
  fixKnowledgeDatabase as fixKnowledgeDatabaseAPI
} from '../utils/knowledge/knowledgeClient.js'

// 导入自定义对话框
import { showDeleteConfirm, showConfirm } from '../utils/dialog.js'

// 导入图标资源 - 确保打包后路径正确
import iconSmImg from '/assets/icon-sm.png'
import iconCleanImg from '/assets/icon-clean.png'
import iconPathImg from '/assets/icon-path.png'
import iconRefseImg from '/assets/icon-refse.png'

export default {
  name: 'KnowledgeView',
  setup() {
    const loading = ref(false)
    const statusMessage = ref('')
    const statusType = ref('info')

    // 统计信息
    const stats = reactive({
      totalFiles: 0,
      totalSegments: 0
    })

    // 文档管理
    const documents = ref([])
    const userDataPath = ref('')

    // 添加文档
    const showAddDocuments = ref(false)
    const selectedFiles = ref([])
    const indexing = ref(false)
    const indexProgress = reactive({
      show: false,
      current: 0,
      total: 0,
      percentage: 0,
      currentFile: ''
    })

    // 清空确认
    const showClearDialog = ref(false)

    // 显示状态消息
    const showStatus = (message, type = 'info') => {
      statusMessage.value = message
      statusType.value = type
      setTimeout(() => {
        statusMessage.value = ''
      }, 3000)
    }

    // 获取用户数据路径
    const getUserDataPath = async () => {
      try {
        const path = await window.electronAPI.invoke('get-path', 'userData')
        userDataPath.value = path ? path + '/knowledge.db' : '未知路径'
      } catch (error) {
        console.error('获取用户数据路径失败:', error)
        userDataPath.value = '未知路径'
      }
    }

    // 初始化
    const initialize = async () => {
      loading.value = true

      try {
        await initKnowledgeDatabase()
        await refreshStats()
        await refreshDocuments()
        await getUserDataPath()
      } catch (error) {
        console.error('知识库初始化失败:', error)
        showStatus('知识库初始化失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 刷新统计
    const refreshStats = async () => {
      try {
        const result = await getKnowledgeStats()
        if (result.success !== false) {
          stats.totalFiles = result.totalFiles || 0
          stats.totalSegments = result.totalSegments || 0
        }
      } catch (error) {
        console.error('获取统计信息失败:', error)
      }
    }

    // 刷新文档
    const refreshDocuments = async () => {
      try {
        const result = await listFiles({ pageSize: 50 })
        // 使用真实的分段数和文件大小
        documents.value = (result.rows || []).map(doc => ({
          ...doc,
          segmentCount: doc.segmentCount || 0, // 使用真实分段数
          fileSize: doc.fileSize || 0 // 使用真实文件大小
        }))
      } catch (error) {
        console.error('获取文档列表失败:', error)
      }
    }

    // 选择文件
    const selectFiles = () => {
      const input = document.createElement('input')
      input.type = 'file'
      input.multiple = true
      input.accept = '.txt,.md,.docx,.doc,.pdf'

      input.onchange = (e) => {
        const files = Array.from(e.target.files)
        selectedFiles.value.push(...files)
      }

      input.click()
    }

    // 选择目录
    const selectDirectory = async () => {
      try {
        const result = await selectDocumentDirectory()
        if (result.success && result.directory) {
          const files = await getDirectoryFiles(result.directory)
          selectedFiles.value.push(...files.map(filePath => ({ path: filePath })))
        }
      } catch (error) {
        console.error('选择目录失败:', error)
        showStatus('选择目录失败', 'error')
      }
    }

    // 移除文件
    const removeFile = (index) => {
      selectedFiles.value.splice(index, 1)
    }

    // 开始索引
    const startIndexing = async () => {
      if (selectedFiles.value.length === 0) return

      indexing.value = true
      indexProgress.show = true
      indexProgress.current = 0
      indexProgress.total = selectedFiles.value.length
      indexProgress.percentage = 0

      try {
        const filePaths = selectedFiles.value.map(file => file.path || file.name)

        await indexDocuments(filePaths, 1, (current, total, currentFile) => {
          indexProgress.current = current
          indexProgress.total = total
          indexProgress.percentage = Math.round((current / total) * 100)
          indexProgress.currentFile = getFileName(currentFile)
        })

        showStatus('文档索引完成', 'success')
        await refreshStats()
        await refreshDocuments()
        closeAddDialog()

      } catch (error) {
        console.error('文档索引失败:', error)
        showStatus('文档索引失败', 'error')
      } finally {
        indexing.value = false
        indexProgress.show = false
      }
    }

    // 删除文档
    const deleteDocument = async (id) => {
      try {
        await showDeleteConfirm('确定要删除这个文档吗？')
      } catch {
        return // 用户取消
      }

      loading.value = true

      try {
        await deleteFile(id)
        showStatus('文档删除成功', 'success')
        await refreshStats()
        await refreshDocuments()
      } catch (error) {
        console.error('删除文档失败:', error)
        showStatus('删除文档失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 清空知识库
    const clearKnowledgeBase = async () => {
      showClearDialog.value = false
      loading.value = true

      try {
        await clearKnowledgeBaseAPI()
        showStatus('知识库已清空', 'success')
        await refreshStats()
        await refreshDocuments()
      } catch (error) {
        console.error('清空知识库失败:', error)
        showStatus('清空知识库失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 重建知识库
    const rebuildKnowledgeBase = async () => {
      try {
        await showConfirm('重建知识库将删除所有现有数据，确定继续吗？', {
          title: '重建知识库',
          variant: 'warning',
          confirmText: '确定重建',
          cancelText: '取消'
        })
      } catch {
        return // 用户取消
      }

      loading.value = true

      try {
        await rebuildKnowledgeBaseAPI()
        showStatus('知识库重建完成', 'success')
        await refreshStats()
        await refreshDocuments()
      } catch (error) {
        console.error('重建知识库失败:', error)
        showStatus('重建知识库失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 修复知识库数据库
    const fixKnowledgeDatabase = async () => {
      try {
        await showConfirm('修复数据库将重新初始化数据库结构，确定继续吗？', {
          title: '修复知识库数据库',
          variant: 'info',
          confirmText: '确定修复',
          cancelText: '取消'
        })
      } catch {
        return // 用户取消
      }

      loading.value = true

      try {
        const result = await fixKnowledgeDatabaseAPI()
        if (result.success) {
          showStatus('知识库数据库修复完成', 'success')
          await refreshStats()
          await refreshDocuments()
        } else {
          showStatus('知识库数据库修复失败: ' + (result.error || '未知错误'), 'error')
        }
      } catch (error) {
        console.error('修复知识库数据库失败:', error)
        showStatus('修复知识库数据库失败', 'error')
      } finally {
        loading.value = false
      }
    }

    // 关闭添加对话框
    const closeAddDialog = () => {
      showAddDocuments.value = false
      selectedFiles.value = []
      indexProgress.show = false
    }

    // 获取文件名
    const getFileName = (file) => {
      if (typeof file === 'string') {
        return file.split(/[\\\/]/).pop()
      }
      return file.name || file.path?.split(/[\\\/]/).pop() || '未知文件'
    }

    // 格式化日期
    const formatDate = (dateString) => {
      return new Date(dateString).toLocaleDateString('zh-CN', {
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit'
      })
    }

    // 格式化文件大小
    const formatSize = (bytes) => {
      if (bytes === 0) return '0B'
      const k = 1024
      const sizes = ['B', 'KB', 'MB', 'GB']
      const i = Math.floor(Math.log(bytes) / Math.log(k))
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + sizes[i]
    }

    onMounted(() => {
      initialize()
    })

    return {
      loading,
      statusMessage,
      statusType,
      stats,
      documents,
      userDataPath,
      showAddDocuments,
      selectedFiles,
      indexing,
      indexProgress,
      showClearDialog,
      refreshStats,
      selectFiles,
      selectDirectory,
      removeFile,
      startIndexing,
      deleteDocument,
      clearKnowledgeBase,
      rebuildKnowledgeBase,
      fixKnowledgeDatabase,
      closeAddDialog,
      getFileName,
      formatDate,
      formatSize,
      // 导出图标资源
      iconSmImg,
      iconCleanImg,
      iconPathImg,
      iconRefseImg
    }
  }
}
</script>

<style lang="scss" scoped>
.knowledge-view {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: transparent;
}

.view-header {
  padding: 6px 16px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.header-content {
  .view-title {
    margin: 8px 0 8px 0;
    font-size: 20px;
    font-weight: 700;
    color: var(--text-primary);
  }
}

.header-actions {
  display: flex;
  gap: 12px;
}

.action-btn {
  padding: 12px;
  border: 1px solid var(--border-color);
  border-radius: 50%;
  // background: var(--btn-secondary-bg);
  background: none;
  backdrop-filter: blur(10px);
  color: var(--text-dark);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;

  &:hover:not(:disabled) {
    // background: var(--btn-secondary-hover);
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.primary {
    background: var(--btn-primary-bg);
    color: var(--text-inverse);
    border-color: var(--btn-primary-bg);

    &:hover:not(:disabled) {
      background: var(--btn-primary-hover);
      border-color: var(--btn-primary-hover);
    }
  }

  &.danger {
    background: var(--btn-danger-bg);
    color: var(--text-inverse);
    border-color: var(--btn-danger-bg);

    &:hover:not(:disabled) {
      background: var(--btn-danger-hover);
      border-color: var(--btn-danger-hover);
    }
  }

  &.warning {
    background: var(--warning-color);
    color: var(--text-inverse);
    border-color: var(--warning-color);

    &:hover:not(:disabled) {
      background: #e0a800;
      border-color: #e0a800;
    }
  }

  &.info {
    background: #2196f3;
    color: white;
    border-color: #2196f3;

    &:hover:not(:disabled) {
      background: #1976d2;
      border-color: #1976d2;
    }
  }
}

.action-icon {
  width: 16px;
  height: 16px;
  object-fit: contain;
}

.view-content {
  flex: 1;
  padding: 24px;
  border-radius: 24px 0 0 0;
  overflow: auto;
  background: var(--view-content-bg);
}

.knowledge-header {
  margin-bottom: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  .knowledge-title {
    margin: 0;
    font-size: 20px;
    font-weight: 600;
    color: var(--text-dark);
  }
}

.knowledge-folders {
  margin-bottom: 32px;
}

.folder-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--folder-bg);
  border: 1px solid var(--folder-border);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  transition: all 0.2s ease;

  &:hover {
    transform: translateY(-1px);
    box-shadow: var(--shadow-card);
  }
}

.folder-icon {
  font-size: 24px;
  color: var(--folder-icon-color);
}

.folder-info {
  display: flex;
  flex-direction: column;
  gap: 4px;

  .folder-name {
    font-size: 16px;
    font-weight: 500;
    color: var(--text-dark);
  }

  .folder-path {
    font-size: 12px;
    color: var(--text-muted);
  }
}

.knowledge-stats {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  // grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 20px;
}

.stat-card {
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  padding: 10px 20px;
  backdrop-filter: blur(10px);
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    background: var(--card-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-modal);
  }
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;

  .card-title {
    margin: 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
  }

  .status-badge {
    padding: 4px 8px;
    background: var(--badge-success-bg);
    color: var(--badge-success-text);
    border-radius: 4px;
    font-size: 12px;
    font-weight: 500;
  }
}

.card-content {
  margin-bottom: 16px;
}

.stat-line {
  display: flex;
  justify-content: space-between;
  margin-bottom: 8px;
  font-size: 14px;

  &:last-child {
    margin-bottom: 0;
  }

  .stat-text {
    color: var(--text-dark);
    font-weight: 500;
  }

  .stat-label {
    color: var(--text-secondary);
  }

  .stat-value {
    color: var(--text-dark);
    font-weight: 500;
  }
}

.card-actions {
  display: flex;
  justify-content: flex-end;
}

.delete-btn {
  padding: 6px 12px;
  border: 1px solid var(--btn-danger-bg);
  border-radius: 6px;
  background: var(--btn-danger-bg);
  color: var(--text-inverse);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover:not(:disabled) {
    background: var(--btn-danger-hover);
    border-color: var(--btn-danger-hover);
    transform: translateY(-1px);
  }

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }
}

.empty-state {
  text-align: center;
  padding: 60px 24px;
  color: var(--text-secondary);

  .empty-icon {
    font-size: 64px;
    margin-bottom: 16px;
    opacity: 0.6;
  }

  h3 {
    margin: 0 0 8px 0;
    color: var(--text-dark);
  }

  p {
    margin: 0;
    font-size: 14px;
    color: var(--text-muted);
  }
}

.summary-stats {
  display: flex;
  gap: 24px;
  padding: 20px;
  background: var(--card-bg);
  border: 1px solid var(--card-border);
  border-radius: 12px;
  backdrop-filter: blur(10px);
  box-shadow: var(--card-shadow);
  transition: all 0.2s ease;

  &:hover {
    box-shadow: var(--shadow-modal);
  }
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;

  .summary-label {
    font-size: 14px;
    color: var(--text-secondary);
  }

  .summary-value {
    font-size: 16px;
    font-weight: 600;
    color: var(--primary-color);
  }
}

// 模态框样式
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--modal-overlay);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(2px);
}

.modal-content {
  background: var(--modal-bg);
  backdrop-filter: blur(15px);
  border: 1px solid var(--border-color-light);
  border-radius: 12px;
  width: 100%;
  max-width: 600px;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--shadow-modal);
  animation: modalSlideIn 0.3s ease-out;

  &.small {
    max-width: 400px;
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.95);
  }

  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--border-color-light);
  display: flex;
  justify-content: space-between;
  align-items: center;
  background: var(--modal-header-bg);
  backdrop-filter: blur(10px);

  h3 {
    margin: 0;
    font-size: 18px;
    font-weight: 600;
    color: var(--text-dark);
  }
}

.modal-close {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 50%;
  background: transparent;
  color: var(--text-secondary);
  font-size: 20px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--border-color-light);
    color: var(--text-dark);
    transform: scale(1.1);
  }
}

.modal-body {
  padding: 24px;
  max-height: 60vh;
  overflow-y: auto;
  color: var(--text-dark);
  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--border-color-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;

    &:hover {
      background: var(--text-secondary);
    }
  }
}

.modal-footer {
  padding: 16px 24px;
  border-top: 1px solid var(--border-color-light);
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  background: var(--modal-footer-bg);
}

.modal-btn {
  padding: 10px 20px;
  border: 1px solid var(--border-color-dark);
  border-radius: 6px;
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;

  &:disabled {
    opacity: 0.5;
    cursor: not-allowed;
  }

  &.secondary {
    background: var(--btn-secondary-bg);
    color: var(--text-dark);
    border-color: var(--border-color-dark);

    &:hover:not(:disabled) {
      background: var(--btn-secondary-hover);
      transform: translateY(-1px);
    }
  }

  &.primary {
    background: var(--btn-primary-bg);
    color: var(--text-inverse);
    border-color: var(--btn-primary-bg);

    &:hover:not(:disabled) {
      background: var(--btn-primary-hover);
      border-color: var(--btn-primary-hover);
      transform: translateY(-1px);
    }
  }

  &.danger {
    background: var(--btn-danger-bg);
    color: var(--text-inverse);
    border-color: var(--btn-danger-bg);

    &:hover:not(:disabled) {
      background: var(--btn-danger-hover);
      border-color: var(--btn-danger-hover);
      transform: translateY(-1px);
    }
  }
}

.add-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 16px;
  margin-bottom: 24px;
}

.option-btn {
  padding: 16px;
  border: 2px dashed var(--border-color-medium);
  border-radius: 8px;
  background: var(--content-bg-secondary);
  color: var(--text-dark);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;

  &:hover {
    border-color: var(--primary-color);
    background: var(--card-bg-hover);
    transform: translateY(-2px);
    box-shadow: var(--shadow-card);
  }

  &.primary {
    border-color: var(--primary-color);
    background: rgba(var(--primary-rgb), 0.1);
    color: var(--primary-color);
  }
}

.selected-files {
  margin-bottom: 24px;

  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
  }
}

.file-list {
  max-height: 200px;
  overflow-y: auto;
  border: 1px solid var(--border-color-dark);
  border-radius: 6px;
  background: var(--content-bg-secondary);

  // 自定义滚动条
  &::-webkit-scrollbar {
    width: 6px;
  }

  &::-webkit-scrollbar-track {
    background: var(--border-color-light);
    border-radius: 3px;
  }

  &::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 3px;
  }
}

.file-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 12px;
  border-bottom: 1px solid var(--border-color-light);
  transition: background 0.2s ease;

  &:last-child {
    border-bottom: none;
  }

  &:hover {
    background: var(--border-color-light);
  }
}

.file-name {
  font-size: 14px;
  color: var(--text-dark);
}

.remove-btn {
  padding: 4px 8px;
  border: 1px solid var(--btn-danger-bg);
  border-radius: 4px;
  background: transparent;
  color: var(--btn-danger-bg);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;

  &:hover {
    background: var(--btn-danger-bg);
    color: var(--text-inverse);
    transform: translateY(-1px);
  }
}

.index-progress {
  h4 {
    margin: 0 0 12px 0;
    font-size: 16px;
    font-weight: 600;
    color: var(--text-dark);
  }
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: var(--progress-bg);
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: var(--progress-fill);
  border-radius: 4px;
  transition: width 0.3s ease;
  box-shadow: 0 0 8px rgba(var(--primary-rgb), 0.3);
}

.progress-text {
  margin: 0;
  font-size: 14px;
  color: var(--text-secondary);
}

.status-message {
  position: fixed;
  bottom: 20px;
  right: 20px;
  padding: 12px 20px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  box-shadow: var(--shadow-modal);
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: statusSlideIn 0.3s ease-out;

  &.success {
    background: var(--badge-success-bg);
    color: var(--badge-success-text);
    border: 1px solid var(--badge-success-text);
  }

  &.error {
    background: var(--badge-error-bg);
    color: var(--badge-error-text);
    border: 1px solid var(--badge-error-text);
  }

  &.info {
    background: var(--badge-info-bg);
    color: var(--badge-info-text);
    border: 1px solid var(--badge-info-text);
  }
}

@keyframes statusSlideIn {
  from {
    opacity: 0;
    transform: translateX(100px);
  }

  to {
    opacity: 1;
    transform: translateX(0);
  }
}

// 全局滚动条样式
.knowledge-view {
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: var(--border-color-light);
    border-radius: 4px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--text-muted);
    border-radius: 4px;

    &:hover {
      background: var(--text-secondary);
    }
  }

  ::-webkit-scrollbar-corner {
    background: var(--border-color-light);
  }
}

// 暗黑主题下的特殊处理
.dark-theme .knowledge-view {
  .folder-icon {
    filter: brightness(1.2);
  }

  .empty-icon {
    filter: brightness(0.8);
  }

  .progress-fill {
    box-shadow: 0 0 8px rgba(var(--primary-rgb), 0.5);
  }

  .stat-card:hover {
    border-color: var(--border-color-medium);
  }

  .modal-content {
    border-color: var(--border-color-medium);
  }
}

// 加载状态样式
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(var(--primary-rgb), 0.1);
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--border-color-light);
  border-top: 3px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

// 焦点样式增强
.action-btn:focus,
.modal-btn:focus,
.option-btn:focus,
.delete-btn:focus,
.remove-btn:focus {
  outline: 2px solid var(--primary-color);
  outline-offset: 2px;
}

// 选择文本样式
.knowledge-view {
  ::selection {
    background: rgba(var(--primary-rgb), 0.3);
    color: inherit;
  }

  ::-moz-selection {
    background: rgba(var(--primary-rgb), 0.3);
    color: inherit;
  }
}
</style>
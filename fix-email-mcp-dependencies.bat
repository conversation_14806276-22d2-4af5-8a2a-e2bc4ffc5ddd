@echo off
chcp 65001 >nul
echo.
echo 🔧 开始修复 Email MCP Python 依赖问题...
echo.

echo 📋 步骤1: 检查当前 MCP 导入状态...
python/py/python/python.exe -c "try: import mcp.server.fastmcp; print('SUCCESS')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ MCP 依赖已正常，无需修复
    pause
    exit /b 0
)

echo ❌ 检测到 MCP 导入问题
echo.

echo 📋 步骤2: 检查当前 pydantic 版本...
python/py/python/python.exe -m pip list | findstr pydantic
echo.

echo 📋 步骤3: 卸载有冲突的 pydantic 包...
python/py/python/python.exe -m pip uninstall -y pydantic pydantic-core pydantic-settings
echo.

echo 📋 步骤4: 安装兼容版本的 pydantic 包...
python/py/python/python.exe -m pip install "pydantic>=2.7,<3.0" "pydantic-core>=2.33,<3.0" "pydantic-settings>=2.5"
if %errorlevel% neq 0 (
    echo ❌ 安装失败！
    pause
    exit /b 1
)
echo.

echo 📋 步骤5: 验证修复结果...
python/py/python/python.exe -c "try: import mcp.server.fastmcp; print('MCP导入成功！')" >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ 修复成功！Email MCP 依赖问题已解决
    echo.
    echo 📋 当前 pydantic 版本:
    python/py/python/python.exe -m pip list | findstr pydantic
) else (
    echo ❌ 修复失败，请手动检查 Python 环境配置
    pause
    exit /b 1
)

echo.
echo 🎉 修复完成！现在可以重启应用测试 Email MCP 功能
echo.
pause 
<template>
  <div class="chat-settings">
    <div class="settings-header">
      <h3>大模型聊天设置</h3>
      <button @click="$emit('close')" class="close-btn">✕</button>
    </div>
    
    <div class="settings-content">
      <!-- API 配置 -->
      <div class="setting-group">
        <h4>API 配置</h4>
        <div class="setting-item">
          <label>接入地址：</label>
          <input 
            v-model="localSettings.baseURL" 
            type="text" 
            placeholder="http://192.168.34.146:8080/v1"
            class="setting-input"
          />
        </div>
        <div class="setting-item">
          <label>模型名称：</label>
          <input 
            v-model="localSettings.model" 
            type="text"
            placeholder="Qwen/Qwen2.5-72B-Instruct"
            class="setting-input"
          />
        </div>
        <div class="setting-item">
          <label>API 密钥：</label>
          <input 
            v-model="localSettings.apiKey" 
            type="password" 
            placeholder="输入您的 API 密钥"
            class="setting-input"
          />
        </div>
      </div>

      <!-- 聊天参数 -->
      <div class="setting-group">
        <h4>聊天参数</h4>
        <div class="setting-item">
          <label>最大令牌数：</label>
          <input 
            v-model.number="localSettings.maxTokens" 
            type="number" 
            min="100" 
            max="4000"
            class="setting-input"
          />
        </div>
        <div class="setting-item">
          <label>温度 (0-1)：</label>
          <input 
            v-model.number="localSettings.temperature" 
            type="number" 
            min="0" 
            max="1" 
            step="0.1"
            class="setting-input"
          />
          <small>较低的值会让回答更保守，较高的值会让回答更有创意</small>
        </div>
        <div class="setting-item">
          <label>历史消息长度：</label>
          <input 
            v-model.number="localSettings.maxHistoryLength" 
            type="number" 
            min="5" 
            max="50"
            class="setting-input"
          />
          <small>保留的历史对话轮数</small>
        </div>
      </div>

      <!-- 系统提示 -->
      <div class="setting-group">
        <h4>系统提示</h4>
        <div class="setting-item">
          <label>角色设定：</label>
          <textarea 
            v-model="localSettings.systemPrompt" 
            rows="6"
            placeholder="输入角色设定和行为指导..."
            class="setting-textarea"
          ></textarea>
          <small>这将决定犇犇的性格和回答风格</small>
        </div>
      </div>
    </div>

    <div class="settings-actions">
      <button @click="testConnection" class="test-btn" :disabled="isTesting">
        {{ isTesting ? '测试中...' : '测试连接' }}
      </button>
      <button @click="resetToDefaults" class="reset-btn">恢复默认</button>
      <button @click="saveSettings" class="save-btn">保存设置</button>
    </div>

    <!-- 测试结果提示 -->
    <div v-if="testResult" class="test-result" :class="testResult.success ? 'success' : 'error'">
      {{ testResult.message }}
    </div>
  </div>
</template>

<script>
import { ref, computed, onMounted } from 'vue'
import { useChatStore } from '../stores/chat.js'
import { createUserMessage, getDefaultSystemPrompt } from '../utils/chatAPI.js'
import modelConfig, { testChatConnection } from '../utils/config/modelConfig.js'

export default {
  name: 'ChatSettings',
  emits: ['close'],
  setup(props, { emit }) {
    const chatStore = useChatStore()
    const isTesting = ref(false)
    const testResult = ref(null)
    
    // 本地设置相关

    // 本地设置副本 - 从统一配置管理器加载
    const localSettings = ref({
      ...modelConfig.getChatConfig(),
      ...modelConfig.getChatSettings()
    })

    // 从统一配置管理器加载设置
    const loadSettings = () => {
      try {
        // 从统一配置管理器加载最新配置
        localSettings.value = {
          ...modelConfig.getChatConfig(),
          ...modelConfig.getChatSettings()
        }
        
        console.log('从统一配置管理器加载设置:', localSettings.value)
      } catch (error) {
        console.error('加载设置失败:', error)
      }
    }

    // 加载设置
    
    // 保存设置
    const saveSettings = () => {
      try {
        // 使用统一配置管理器保存
        const chatConfig = {
          baseURL: localSettings.value.baseURL,
          model: localSettings.value.model,
          apiKey: localSettings.value.apiKey,
          maxTokens: localSettings.value.maxTokens,
          temperature: localSettings.value.temperature
        }
        
        const chatSettings = {
          maxHistoryLength: localSettings.value.maxHistoryLength,
          systemPrompt: localSettings.value.systemPrompt
        }
        
        // 更新统一配置管理器
        modelConfig.updateChatConfig(chatConfig)
        modelConfig.updateChatSettings(chatSettings)
        
        // 更新聊天存储（保持兼容性）
        chatStore.updateSettings({
          maxTokens: localSettings.value.maxTokens,
          temperature: localSettings.value.temperature,
          maxHistoryLength: localSettings.value.maxHistoryLength
        })
        chatStore.updateSystemPrompt(localSettings.value.systemPrompt)

        // 通知主进程配置更新
        if (window.electronAPI && window.electronAPI.updateChatConfig) {
          window.electronAPI.updateChatConfig(chatConfig)
        }

        testResult.value = { success: true, message: '设置已保存！' }
        setTimeout(() => {
          testResult.value = null
          emit('close')
        }, 1500)
      } catch (error) {
        console.error('保存设置失败:', error)
        testResult.value = { success: false, message: '保存设置失败：' + error.message }
      }
    }

    // 测试连接
    const testConnection = async () => {
      if (isTesting.value) return

      isTesting.value = true
      testResult.value = null

      try {
        // 临时更新配置管理器进行测试
        const tempConfig = {
          baseURL: localSettings.value.baseURL,
          model: localSettings.value.model,
          apiKey: localSettings.value.apiKey,
          maxTokens: localSettings.value.maxTokens,
          temperature: localSettings.value.temperature
        }
        
        // 创建临时配置实例进行测试
        const testResult_response = await testChatConnection()
        testResult.value = testResult_response
      } catch (error) {
        console.error('测试连接失败:', error)
        testResult.value = { 
          success: false, 
          message: '连接测试失败：' + error.message 
        }
      } finally {
        isTesting.value = false
        // 3秒后清除测试结果
        setTimeout(() => {
          testResult.value = null
        }, 3000)
      }
    }

    // 恢复默认设置
    const resetToDefaults = () => {
      // 重置统一配置管理器到默认值
      modelConfig.resetToDefaults()
      // 重新加载本地设置
      loadSettings()
    }

    onMounted(() => {
      loadSettings()
    })

    return {
      localSettings,
      isTesting,
      testResult,
      saveSettings,
      testConnection,
      resetToDefaults
    }
  }
}
</script>

<style lang="scss" scoped>
.chat-settings {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(15px);
  border-radius: 20px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
  border: 2px solid rgba(102, 126, 234, 0.2);
  width: 500px;
  max-width: 90vw;
  max-height: 80vh;
  overflow-y: auto;
  z-index: 1000;
}

.settings-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px 25px 15px;
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  h3 {
    margin: 0;
    color: #333;
    font-size: 18px;
    font-weight: 600;
  }

  .close-btn {
    background: none;
    border: none;
    font-size: 20px;
    cursor: pointer;
    color: #666;
    padding: 5px;
    border-radius: 50%;
    transition: all 0.3s ease;

    &:hover {
      background: rgba(0, 0, 0, 0.1);
      color: #333;
    }
  }
}

.settings-content {
  padding: 20px 25px;
}

.setting-group {
  margin-bottom: 30px;

  h4 {
    margin: 0 0 15px 0;
    color: #555;
    font-size: 16px;
    font-weight: 600;
    border-bottom: 2px solid #667eea;
    padding-bottom: 5px;
  }
}

.setting-item {
  margin-bottom: 15px;

  label {
    display: block;
    margin-bottom: 5px;
    color: #333;
    font-weight: 500;
    font-size: 14px;
  }

  .setting-input {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }
  
  &.setting-input-readonly {
    background-color: #f5f5f5;
    cursor: not-allowed;
    color: #777;
  }

  .setting-select {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    background-color: white;
    cursor: pointer;
    transition: border-color 0.3s ease;
    appearance: none;
    background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23555555' d='M6 8.825L1.763 4.5 3 3.25 6 6.325 9 3.25 10.237 4.5z'/%3E%3C/svg%3E");
    background-repeat: no-repeat;
    background-position: right 12px center;
    padding-right: 30px;
    
    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  .setting-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 2px solid #e1e5e9;
    border-radius: 8px;
    font-size: 14px;
    
    resize: vertical;
    min-height: 120px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;

    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
  }

  small {
    display: block;
    margin-top: 5px;
    color: #666;
    font-size: 12px;
  }
}

.setting-select {
  width: 100%;
  padding: 10px 12px;
  border: 2px solid #e1e5e9;
  border-radius: 8px;
  font-size: 14px;
  background-color: white;
  cursor: pointer;
  transition: border-color 0.3s ease;
  appearance: none;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='12' height='12' viewBox='0 0 12 12'%3E%3Cpath fill='%23555555' d='M6 8.825L1.763 4.5 3 3.25 6 6.325 9 3.25 10.237 4.5z'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: right 12px center;
  padding-right: 30px;
  
  &:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
  }
}

.settings-actions {
  display: flex;
  gap: 10px;
  padding: 15px 25px 20px;
  border-top: 1px solid rgba(0, 0, 0, 0.1);

  button {
    flex: 1;
    padding: 10px 15px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-weight: 500;

    &:disabled {
      opacity: 0.6;
      cursor: not-allowed;
    }
  }

  .test-btn {
    background: #f39c12;
    color: white;

    &:hover:not(:disabled) {
      background: #e67e22;
      transform: translateY(-1px);
    }
  }

  .reset-btn {
    background: #95a5a6;
    color: white;

    &:hover {
      background: #7f8c8d;
      transform: translateY(-1px);
    }
  }

  .save-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      transform: translateY(-1px);
      box-shadow: 0 4px 12px rgba(102, 126, 234, 0.3);
    }
  }
}

.test-result {
  margin: 0 25px 20px;
  padding: 10px 15px;
  border-radius: 8px;
  font-size: 14px;
  text-align: center;

  &.success {
    background: rgba(46, 204, 113, 0.1);
    color: #27ae60;
    border: 1px solid rgba(46, 204, 113, 0.3);
  }

  &.error {
    background: rgba(231, 76, 60, 0.1);
    color: #e74c3c;
    border: 1px solid rgba(231, 76, 60, 0.3);
  }
}
</style> 
<template>
  <div id="app">
    <!-- 版本检查阶段 -->
    <AppVersionCheck 
      v-if="showVersionCheck"
      ref="versionCheckRef"
      @version-check-complete="handleAppVersionCheckComplete" 
    />
    
    <!-- 主应用内容 - 只有在版本检查完成后才显示 -->
    <component v-else :is="currentView" :initialPage="initialPage" />
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useAuthStore } from './stores/auth.js'
import { useThemeStore } from './stores/theme.js'
import Login from './views/Login.vue'
import MainLayout from './components/MainLayout.vue'
import AppVersionCheck from './components/AppVersionCheck.vue'

export default {
  name: 'App',
  components: {
    Login,
    MainLayout,
    AppVersionCheck
  },
  setup() {
    const authStore = useAuthStore()
    const themeStore = useThemeStore()
    const currentView = ref('Login')
    const initialPage = ref(null)
    const versionCheckRef = ref(null)
    const showVersionCheck = ref(true) // 默认显示版本检查

    onMounted(async () => {
      // 初始化主题
      themeStore.applyTheme()
      
      // 开始版本检查
      if (versionCheckRef.value) {
        await versionCheckRef.value.startVersionCheck()
      }
      
      // 注意：登录状态检查现在由版本检查组件控制
      // 只有在版本检查完成后且不需要更新时，才会检查登录状态

      // 监听导航事件
      if (window.electronAPI) {
        window.electronAPI.onNavigateTo((page) => {
          console.log('Navigation event received:', page)
          
          // 如果用户已登录，设置初始页面并显示MainLayout
          if (authStore.isLoggedIn) {
            currentView.value = 'MainLayout'
            initialPage.value = page
          } else {
            // 如果用户未登录，先显示登录页面
            console.log('User not logged in, showing login first')
            currentView.value = 'Login'
            // 保存页面，登录后跳转
            initialPage.value = page
          }
        })
        
        // 监听语音播报事件
        window.electronAPI.onSpeakText?.((speechData) => {
          speakText(speechData.text, speechData)
        })
        
        // 监听主进程console日志并转发到浏览器控制台
        window.electronAPI.onMainConsoleLog?.((logData) => {
          const timestamp = new Date(logData.timestamp).toLocaleTimeString()
          const message = `[主进程 ${timestamp}] ${logData.message}`
          
          // 根据日志级别调用相应的console方法
          switch (logData.level) {
            case 'error':
              console.error(message)
              break
            case 'warn':
              console.warn(message)
              break
            case 'info':
              console.info(message)
              break
            case 'debug':
              console.debug(message)
              break
            default:
              console.log(message)
              break
          }
        })
        
        // 监听主进程通知事件
        window.electronAPI.onShowNotification?.((notification) => {
          showNotification(notification)
        })
        
        // 监听SSO回调
        window.electronAPI.onSSOCallback?.((data) => {
          console.log('🔗 收到SSO回调:', data)
          if (data.code) {
            // 处理SSO登录
            handleSSOCallback(data.code)
          }
        })
      }
    })

    // 语音播报函数
    const speakText = (text, options = {}) => {
      if ('speechSynthesis' in window) {
        // 停止当前播报
        window.speechSynthesis.cancel()
        
        const utterance = new SpeechSynthesisUtterance(text)
        utterance.lang = 'zh-CN'
        utterance.rate = options.rate || 1.0
        utterance.pitch = options.pitch || 1.0
        utterance.volume = options.volume || 1.0
        
        utterance.onstart = () => {
          console.log('🔊 开始语音播报:', text)
        }
        
        utterance.onend = () => {
          console.log('🔊 语音播报完成')
        }
        
        utterance.onerror = (error) => {
          console.error('🔊 语音播报错误:', error)
        }
        
        window.speechSynthesis.speak(utterance)
      } else {
        console.warn('🔊 浏览器不支持语音播报')
      }
    }

    // 显示通知函数
    const showNotification = (notification) => {
      console.log('🔔 收到通知:', notification)
      
      // 使用浏览器原生通知API
      if ('Notification' in window) {
        // 检查通知权限
        if (Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: './assets/logo.ico'
          })
        } else if (Notification.permission !== 'denied') {
          // 请求通知权限
          Notification.requestPermission().then(permission => {
            if (permission === 'granted') {
              new Notification(notification.title, {
                body: notification.message,
                icon: './assets/logo.ico'
              })
            }
          })
        }
      }
      
      // 同时在控制台显示通知（作为备用）
      console.log(`🔔 [${notification.type || 'info'}] ${notification.title}: ${notification.message}`)
      
      // 可以根据需要添加更多通知方式，比如弹窗、Toast等
    }

    // 处理应用版本检查完成
    const handleAppVersionCheckComplete = (result) => {
      console.log('📱 应用版本检查完成:', result)
      
      // 只有在不需要更新、用户选择跳过、强制完成或超时时才进入主应用
      if (result.noUpdate || result.skipped || result.forced || result.timeout) {
        console.log('🔄 版本检查通过，进入主应用')
        showVersionCheck.value = false
        
        // 如果用户已登录，直接显示主布局
        if (authStore.isLoggedIn) {
          currentView.value = 'MainLayout'
        } else {
          currentView.value = 'Login'
        }
      } else {
        console.log('🔄 版本需要更新，保持版本检查界面')
      }
    }

    // 处理SSO回调
    const handleSSOCallback = async (code) => {
      console.log('🔗 处理SSO回调，授权码:', code)
      
      // 🔄 防重复调用：检查是否正在处理相同的code
      if (window._processingSSOCode === code) {
        console.log('🔗 正在处理相同的授权码，跳过重复调用:', code)
        return
      }
      
      // 标记正在处理的code
      window._processingSSOCode = code
      
      try {
        const result = await authStore.ssoLogin(code)
        if (result.success) {
          console.log('🔗 SSO登录成功')
          // 登录成功后会自动切换到MainLayout
        } else {
          console.error('🔗 SSO登录失败:', result.message)
        }
      } catch (error) {
        console.error('🔗 SSO登录异常:', error)
      } finally {
        // 清除处理标记
        window._processingSSOCode = null
      }
    }

    // 监听登录状态变化
    authStore.$subscribe((mutation, state) => {
      if (state.isLoggedIn) {
        currentView.value = 'MainLayout'
        // 如果有保存的页面，传递给MainLayout
        if (initialPage.value) {
          console.log('User logged in, navigating to saved page:', initialPage.value)
        }
      } else {
        currentView.value = 'Login'
        initialPage.value = null
      }
    })

    return {
      currentView,
      initialPage,
      versionCheckRef,
      showVersionCheck,
      handleAppVersionCheckComplete
    }
  }
}
</script>

<style lang="scss">
#app {
  width: 100%;
  height: 100vh;
  font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  background-color: var(--bg-primary);
  overflow: hidden;
}
</style> 
# 简化版本的PowerShell构建脚本
# 用法：在项目根目录运行 .\build-dist-simple.ps1

Write-Host ""
Write-Host "======================================="
Write-Host "    犇犇数字员工助手 - 构建脚本 (简化版)"
Write-Host "======================================="
Write-Host ""

# 1. 删除dist文件夹
Write-Host "[1/3] 删除dist文件夹..."
if (Test-Path "dist") {
    Remove-Item -Recurse -Force "dist" -ErrorAction SilentlyContinue
    Write-Host "✅ dist文件夹已删除"
} else {
    Write-Host "ℹ️  dist文件夹不存在，跳过删除"
}

Write-Host ""

# 2. 杀掉electron进程
Write-Host "[2/3] 杀掉electron进程..."
$processes = Get-Process -Name "electron", "犇犇数字员工助手", "nezha-ai-desktop" -ErrorAction SilentlyContinue

if ($processes) {
    Write-Host "🔍 发现electron进程，正在终止..."
    $processes | Stop-Process -Force -ErrorAction SilentlyContinue
    Write-Host "✅ electron进程已终止"
} else {
    Write-Host "ℹ️  没有发现electron进程"
}

Write-Host ""

# 3. 运行构建命令
Write-Host "[3/3] 开始构建应用..."
Write-Host "🚀 运行 npm run electron:dist..."
Write-Host ""

& npm run electron:dist

Write-Host ""
if ($LASTEXITCODE -eq 0) {
    Write-Host "✅ 构建完成！"
    Write-Host "📦 生成的文件位于 dist 文件夹"
    Write-Host ""
    
    # 询问是否打开dist文件夹
    $response = Read-Host "是否打开dist文件夹？(y/n)"
    if ($response -eq "y" -or $response -eq "Y") {
        if (Test-Path "dist") {
            explorer "dist"
        } else {
            Write-Host "❌ dist文件夹不存在"
        }
    }
} else {
    Write-Host "❌ 构建失败！"
    Write-Host "请检查错误信息并修复后重试"
}

Write-Host ""
Write-Host "======================================="
Write-Host "       构建脚本执行完成"
Write-Host "======================================="
Write-Host ""

# 暂停以查看结果
Write-Host "按回车键继续..."
Read-Host 
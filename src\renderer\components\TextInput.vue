<template>
  <div class="text-input-panel">
    <div class="input-container">
      <div class="input-area">
        <textarea
          ref="textInput"
          v-model="inputText"
          placeholder="请输入您想说的话..."
          @keydown="handleKeyDown"
          @input="adjustHeight"
          class="text-area"
          rows="3"
          maxlength="500"
        ></textarea>
        
        <button 
          @click="sendText"
          class="send-btn"
          :disabled="!inputText.trim()"
          title="发送"
        >
          <img src="/assets/send.png" alt="发送" class="send-icon" />
        </button>
      </div>
    </div>
  </div>
</template>

<script>
import { ref, nextTick, onMounted, watch } from 'vue'

export default {
  name: 'TextInput',
  props: {
    initialText: {
      type: String,
      default: ''
    }
  },
  emits: ['close', 'result'],
  setup(props, { emit }) {
    const inputText = ref('')
    const textInput = ref(null)

    // 监听初始文本变化
    watch(() => props.initialText, (newText) => {
      if (newText) {
        inputText.value = newText
        nextTick(() => {
          adjustHeight()
        })
      }
    }, { immediate: true })

    const handleKeyDown = (event) => {
      if (event.ctrlKey && event.key === 'Enter') {
        event.preventDefault()
        sendText()
      } else if (event.key === 'Escape') {
        closePanel()
      }
    }

    const adjustHeight = () => {
      if (textInput.value) {
        textInput.value.style.height = 'auto'
        textInput.value.style.height = Math.min(textInput.value.scrollHeight, 150) + 'px'
      }
    }

    const sendText = () => {
      const text = inputText.value.trim()
      if (text) {
        emit('result', text)
        // 发送后清空输入框内容
        inputText.value = ''
        // 重新调整高度
        nextTick(() => {
          adjustHeight()
          // 重新聚焦到输入框
          textInput.value?.focus()
        })
      }
    }

    const closePanel = () => {
      emit('close')
    }

    onMounted(() => {
      nextTick(() => {
        textInput.value?.focus()
        adjustHeight()
      })
    })

    return {
      inputText,
      textInput,
      handleKeyDown,
      adjustHeight,
      sendText,
      closePanel
    }
  }
}
</script>

<style lang="scss" scoped>
.text-input-panel {
  position: absolute;
  left: 0px;
  bottom: 50px;
  border-radius: 15px;
  width: 300px;
  z-index: 2000; /* 确保在历史消息弹框之上 */
}

.input-container {
  width: 100%;
}

.input-area {
  position: relative;
  
  .text-area {
    width: 100%;
    padding: 12px 50px 12px 12px; /* 为发送按钮留出空间 */
    border: 2px solid #e1e5e9;
    border-radius: 10px;
    font-size: 14px;
    
    resize: none;
    overflow-y: auto;
    min-height: 80px;
    max-height: 150px;
    transition: border-color 0.3s ease;
    box-sizing: border-box;
    
    &:focus {
      outline: none;
      border-color: #667eea;
      box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    }
    
    &::placeholder {
      color: #999;
    }
  }
  
  .send-btn {
    position: absolute;
    bottom: 8px;
    right: 8px;
    width: 36px;
    height: 36px;
    border: none;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 0;
    
    .send-icon {
      width: 36px;
      height: 36px;
      object-fit: contain;
    }
    
    &:hover:not(:disabled) {
      transform: scale(1.05);
    }
    
    &:disabled {
      opacity: 0.5;
      cursor: not-allowed;
      
      .send-icon {
        opacity: 0.7;
      }
    }
  }
}

@keyframes slideLeft {
  from {
    opacity: 0;
    transform: translateY(-50%) translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(-50%) translateX(0);
  }
}
</style> 
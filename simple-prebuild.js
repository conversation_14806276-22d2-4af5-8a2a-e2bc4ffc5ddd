const fs = require('fs')
const path = require('path')

function createDefaultAgreement() {
  const defaultContent = `犇犇数字员工助手隐私协议

欢迎使用犇犇数字员工助手！

为了向您提供更好的服务，本应用需要收集和使用您的一些信息。请您仔细阅读本隐私协议：

1. 信息收集
   - 我们可能收集您的基本使用信息以改善服务质量
   - 语音数据仅用于AI对话功能，不会永久存储
   - 聊天记录仅保存在您的本地设备上

2. 信息使用
   - 收集的信息仅用于提供和改善服务
   - 我们不会向第三方出售您的个人信息
   - 语音处理通过加密通道传输

3. 数据安全
   - 我们采用行业标准的安全措施保护您的数据
   - 您可以随时删除本地存储的数据
   - 我们会及时修复发现的安全漏洞

4. 您的权利
   - 您有权了解我们收集的信息
   - 您可以随时停止使用本应用
   - 您可以联系我们删除相关数据

如果您对本隐私协议有任何疑问，请联系我们。

使用本应用即表示您同意本隐私协议。

最后更新日期：2024年`;

  return defaultContent;
}

async function simplePrebuild() {
  console.log('开始简化预构建流程...')
  
  try {
    const agreementPath = path.join(__dirname, 'agreement.txt')
    
    // 检查是否存在协议文件
    if (fs.existsSync(agreementPath)) {
      const content = fs.readFileSync(agreementPath, 'utf-8').trim()
      
      if (content) {
        console.log('✅ 发现现有协议文件')
        console.log('文件大小:', content.length, '字符')
        console.log('前100个字符:', content.substring(0, 100))
      } else {
        console.log('⚠️  协议文件为空，使用默认内容')
        const defaultContent = createDefaultAgreement()
        fs.writeFileSync(agreementPath, defaultContent, 'utf-8')
        console.log('✅ 已生成默认协议文件')
      }
    } else {
      console.log('⚠️  协议文件不存在，创建默认协议文件')
      const defaultContent = createDefaultAgreement()
      fs.writeFileSync(agreementPath, defaultContent, 'utf-8')
      console.log('✅ 已生成默认协议文件')
    }
    
    console.log('简化预构建流程完成')
    console.log('')
    console.log('提示：您可以通过以下方式编辑协议内容：')
    console.log('1. 直接编辑 agreement.txt 文件')
    console.log('2. 打开 privacy-agreement-editor.html 使用可视化编辑器')
    console.log('')
    
  } catch (error) {
    console.error('简化预构建流程失败:', error)
    process.exit(1)
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  simplePrebuild()
}

module.exports = { simplePrebuild, createDefaultAgreement } 
import { defineStore } from 'pinia'
import { ref, watch } from 'vue'

export const useThemeStore = defineStore('theme', () => {
  const isDarkMode = ref(false)
  const themeColor = ref('#667eea')
  
  // 从本地存储恢复主题设置
  const savedTheme = localStorage.getItem('app-theme')
  const savedColor = localStorage.getItem('app-theme-color')
  
  if (savedTheme) {
    isDarkMode.value = savedTheme === 'dark'
  }
  if (savedColor) {
    themeColor.value = savedColor
  }
  
  // 切换主题模式
  const toggleTheme = () => {
    isDarkMode.value = !isDarkMode.value
  }
  
  // 设置主题颜色
  const setThemeColor = (color) => {
    themeColor.value = color
  }
  
  // 应用主题到DOM
  const applyTheme = () => {
    const root = document.documentElement
    
    if (isDarkMode.value) {
      root.classList.add('dark-theme')
      root.classList.remove('light-theme')
    } else {
      root.classList.add('light-theme')
      root.classList.remove('dark-theme')
    }
    
    // 设置CSS变量
    root.style.setProperty('--primary-color', themeColor.value)
    root.style.setProperty('--primary-rgb', hexToRgb(themeColor.value))
  }
  
  // 转换十六进制颜色为RGB
  const hexToRgb = (hex) => {
    const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
    return result ? 
      `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` : 
      '102, 126, 234'
  }
  
  // 监听主题变化并保存到本地存储
  watch(isDarkMode, (newValue) => {
    localStorage.setItem('app-theme', newValue ? 'dark' : 'light')
    applyTheme()
  }, { immediate: true })
  
  watch(themeColor, (newValue) => {
    localStorage.setItem('app-theme-color', newValue)
    applyTheme()
  }, { immediate: true })
  
  return {
    isDarkMode,
    themeColor,
    toggleTheme,
    setThemeColor,
    applyTheme
  }
}) 
const path = require('path')
const { getKnowledgeConfig } = require('./config')

// 延迟获取 app 实例，避免启动时的依赖问题
function getApp() {
  try {
    const { app } = require('electron')
    return app
  } catch (error) {
    console.error('❌ 无法获取 Electron app 实例:', error)
    return null
  }
}

// 延迟导入，避免启动时的依赖问题
let createClient = null
let libsqlClient = null
let openaiClient = null
let mammoth = null
let TurndownService = null

/**
 * 初始化知识库依赖
 */
async function initializeKnowledgeDependencies() {
  try {
    console.log('🔧 初始化知识库依赖...')

    // 动态导入依赖
    console.log('📦 导入 @libsql/client...')
    const libsql = await import('@libsql/client')
    console.log('✅ @libsql/client 导入成功')
    createClient = libsql.createClient
    console.log('✅ createClient 设置成功')

    // 不再需要OpenAI模块，使用代理模式
    // const openaiModule = await import('openai')
    // OpenAI = openaiModule.default

    const mammothModule = await import('mammoth')
    mammoth = mammothModule.default

    const turndownModule = await import('turndown')
    TurndownService = turndownModule.default

    // 设置数据库路径（用户数据目录）
    const appInstance = getApp()
    if (!appInstance) {
      throw new Error('无法获取 Electron app 实例')
    }
    
    const userDataPath = appInstance.getPath('userData')
    const dbPath = path.join(userDataPath, 'knowledge.db')
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    KNOWLEDGE_CONFIG.database.url = `file:${dbPath}`

    console.log('📂 知识库数据库路径:', dbPath)
    console.log('🔧 数据库配置:', {
      url: KNOWLEDGE_CONFIG.database.url,
      timeout: KNOWLEDGE_CONFIG.database.timeout
    })

    // 创建数据库客户端
    console.log('🔧 创建数据库客户端...')
    libsqlClient = createClient({
      url: KNOWLEDGE_CONFIG.database.url,
      timeout: KNOWLEDGE_CONFIG.database.timeout
    })
    console.log('✅ 数据库客户端创建成功')

    // 使用代理模式，不需要创建OpenAI客户端
    // embedding调用将通过代理服务器进行
    openaiClient = null // 标记为null，表示使用代理模式

    console.log('✅ 知识库依赖初始化成功')
    console.log('🔧 libsqlClient 状态:', libsqlClient ? '已创建' : '未创建')
    return true
  } catch (error) {
    console.error('❌ 知识库依赖初始化失败:', error)
    console.error('❌ 错误详情:', error.stack)
    return false
  }
}

/**
 * 初始化知识库数据库
 */
async function initKnowledgeDatabase() {
  try {
    if (!libsqlClient) {
      console.log('🔧 libsqlClient 为 null，开始初始化依赖...')
      const success = await initializeKnowledgeDependencies()
      if (!success) {
        console.error('❌ 依赖初始化失败，libsqlClient 仍然是 null')
        throw new Error('依赖初始化失败')
      }
      console.log('🔧 依赖初始化完成，检查 libsqlClient:', libsqlClient ? '已创建' : '仍然是 null')
      if (!libsqlClient) {
        throw new Error('依赖初始化成功但 libsqlClient 仍然是 null')
      }
    }

    console.log('📊 初始化知识库数据库...')

    // 检查表是否已存在，避免重复创建
    console.log('🗂️ 检查数据库表结构...')

    try {
      // 检查表是否存在
      const tablesResult = await libsqlClient.execute(`
        SELECT name FROM sqlite_master 
        WHERE type='table' AND name IN ('user_file', 'user_file_embd')
      `)

      const existingTables = tablesResult.rows.map(row => row.name)
      console.log('🗂️ 现有表:', existingTables)

      // 只有当表不存在时才创建
      if (!existingTables.includes('user_file') || !existingTables.includes('user_file_embd')) {
        console.log('🗂️ 创建缺失的数据库表结构...')

        // 创建新的表结构
        await libsqlClient.batch([
          `CREATE TABLE IF NOT EXISTS user_file (
            id INTEGER PRIMARY KEY,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            source_file_path TEXT NOT NULL,
            file_preview TEXT NOT NULL,
            remark TEXT NOT NULL,
            file_size INTEGER DEFAULT 0,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
          )`,
          `CREATE TABLE IF NOT EXISTS user_file_embd (
            id INTEGER PRIMARY KEY,
            file_id INTEGER NOT NULL,
            file_content TEXT NOT NULL,
            embedding F32_BLOB(1024),
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
          )`
        ], 'write')

        console.log('🗂️ 创建表结构完成，正在创建向量索引...')

        // 单独创建向量索引，并添加错误处理
        try {
          await libsqlClient.execute(
            'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
          )
          console.log('✅ 向量索引创建成功')
        } catch (indexError) {
          console.error('⚠️ 向量索引创建失败:', indexError.message)
          console.log('🔄 尝试不使用向量索引继续运行...')
          // 不抛出错误，允许在没有向量索引的情况下继续运行
        }
      } else {
        console.log('✅ 数据库表结构已存在，跳过创建')
      }
    } catch (error) {
      console.error('❌ 检查表结构时出错:', error.message)
      // 如果检查失败，尝试创建表
      console.log('🔄 尝试创建表结构...')
      
      try {
        await libsqlClient.batch([
          `CREATE TABLE IF NOT EXISTS user_file (
            id INTEGER PRIMARY KEY,
            file_name TEXT NOT NULL,
            file_path TEXT NOT NULL,
            source_file_path TEXT NOT NULL,
            file_preview TEXT NOT NULL,
            remark TEXT NOT NULL,
            file_size INTEGER DEFAULT 0,
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
          )`,
          `CREATE TABLE IF NOT EXISTS user_file_embd (
            id INTEGER PRIMARY KEY,
            file_id INTEGER NOT NULL,
            file_content TEXT NOT NULL,
            embedding F32_BLOB(1024),
            create_time DATETIME DEFAULT CURRENT_TIMESTAMP
          )`
        ], 'write')
        
        console.log('✅ 表结构创建成功')
      } catch (createError) {
        console.error('❌ 创建表结构失败:', createError.message)
        throw createError
      }
    }

    console.log('✅ 知识库数据库初始化完成')
    return true
  } catch (error) {
    console.error('❌ 知识库数据库初始化失败:', error)
    return false
  }
}

/**
 * 列出知识库文件
 * @param {string} fileType - 文件类型过滤
 * @param {string} fileName - 文件名过滤
 * @param {number} pageSize - 每页大小
 * @param {number} pageNum - 页码
 * @returns {Promise<Object>} 分页结果
 */
async function listKnowledgeFiles(fileType = null, fileName = null, pageSize = 10, pageNum = 1) {
  try {
    if (!libsqlClient) {
      console.log('🔧 数据库客户端为 null，尝试重新初始化...')
      const initSuccess = await initKnowledgeDatabase()
      if (!initSuccess) {
        throw new Error('数据库重新初始化失败')
      }
      if (!libsqlClient) {
        throw new Error('数据库客户端仍然未初始化')
      }
    }

    // 构建查询条件
    let whereConditions = []
    let args = []

    if (fileType !== null && fileType !== undefined) {
      whereConditions.push('file_type = ?')
      args.push(fileType)
    }

    if (fileName && fileName.trim()) {
      whereConditions.push('file_name LIKE ?')
      args.push(`%${fileName.trim()}%`)
    }

    const whereClause = whereConditions.length > 0 ? `WHERE ${whereConditions.join(' AND ')}` : ''

    // 获取总数
    const countSql = `SELECT COUNT(*) as total FROM user_file ${whereClause}`
    const countResult = await libsqlClient.execute({ sql: countSql, args })
    const total = countResult.rows[0].total

    // 获取分页数据，包含每个文件的分段数和文件大小
    const offset = (pageNum - 1) * pageSize
    const dataSql = `
      SELECT
        uf.id,
        uf.file_name as fileName,
        uf.file_path as filePath,
        uf.source_file_path as sourceFilePath,
        uf.file_preview as filePreview,
        uf.remark,
        COALESCE(uf.file_size, 0) as fileSize,
        uf.create_time as createTime,
        COUNT(ufe.id) as segmentCount
      FROM user_file uf
      LEFT JOIN user_file_embd ufe ON uf.id = ufe.file_id
      ${whereClause}
      GROUP BY uf.id, uf.file_name, uf.file_path, uf.source_file_path, uf.file_preview, uf.remark, uf.create_time
      ORDER BY uf.create_time DESC
      LIMIT ? OFFSET ?
    `
    const dataArgs = [...args, pageSize, offset]
    const dataResult = await libsqlClient.execute({ sql: dataSql, args: dataArgs })

    const result = {
      total,
      rows: dataResult.rows,
      pagination: {
        pageSize,
        pageNum,
        totalPages: Math.ceil(total / pageSize)
      }
    }

    console.log(`🧠 文件列表查询完成: 总数=${total}, 当前页=${pageNum}, 返回=${dataResult.rows.length}条`)
    return result
  } catch (error) {
    console.error('🧠 列出文件失败:', error)
    return {
      total: 0,
      rows: [],
      pagination: { pageSize: 10, pageNum: 1, totalPages: 0 }
    }
  }
}

/**
 * 删除知识库文件
 * @param {string} fileId - 文件ID
 * @returns {Promise<Object>} 删除结果
 */
async function deleteKnowledgeFile(fileId) {
  try {
    if (!libsqlClient) {
      console.log('🔧 数据库客户端为 null，尝试重新初始化...')
      const initSuccess = await initKnowledgeDatabase()
      if (!initSuccess) {
        throw new Error('数据库重新初始化失败')
      }
      if (!libsqlClient) {
        throw new Error('数据库客户端仍然未初始化')
      }
    }

    // 先删除文件的所有embedding片段
    await libsqlClient.execute({
      sql: 'DELETE FROM user_file_embd WHERE file_id = ?',
      args: [fileId]
    })

    // 再删除文件记录
    const result = await libsqlClient.execute({
      sql: 'DELETE FROM user_file WHERE id = ?',
      args: [fileId]
    })

    console.log(`🧠 文件删除完成: fileId=${fileId}`)
    return { success: true, deletedId: fileId }
  } catch (error) {
    console.error('🧠 删除文件失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 修复知识库数据库
 * @returns {Promise<Object>} 修复结果
 */
async function fixKnowledgeDatabase() {
  try {
    console.log('🔧 开始修复知识库数据库...')

    // 重新初始化数据库
    const initSuccess = await initKnowledgeDatabase()
    if (!initSuccess) {
      throw new Error('知识库重新初始化失败')
    }

    console.log('✅ 知识库数据库修复完成')
    return { success: true, message: '知识库数据库修复成功' }
  } catch (error) {
    console.error('❌ 知识库数据库修复失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 清空知识库
 * @returns {Promise<Object>} 清空结果
 */
async function clearKnowledgeBase() {
  try {
    if (!libsqlClient) {
      console.log('🔧 数据库客户端为 null，尝试重新初始化...')
      const initSuccess = await initKnowledgeDatabase()
      if (!initSuccess) {
        throw new Error('数据库重新初始化失败')
      }
      if (!libsqlClient) {
        throw new Error('数据库客户端仍然未初始化')
      }
    }

    console.log('🧠 开始清空知识库...')

    // 删除所有embedding数据
    await libsqlClient.execute('DELETE FROM user_file_embd')

    // 删除所有文件记录
    await libsqlClient.execute('DELETE FROM user_file')

    console.log('✅ 知识库清空完成')
    return { success: true, message: '知识库已清空' }
  } catch (error) {
    console.error('❌ 清空知识库失败:', error)
    return { success: false, error: error.message }
  }
}

module.exports = {
  initializeKnowledgeDependencies,
  initKnowledgeDatabase,
  listKnowledgeFiles,
  deleteKnowledgeFile,
  fixKnowledgeDatabase,
  clearKnowledgeBase,
  // 导出客户端实例供其他模块使用
  get libsqlClient() { return libsqlClient },
  get mammoth() { return mammoth },
  get TurndownService() { return TurndownService },
  get openaiClient() { return openaiClient }
}
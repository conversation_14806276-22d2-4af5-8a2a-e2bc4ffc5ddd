<template>
  <div class="voice-wave-container" @click="handleClick">
    <svg class="voice-wave-svg" :class="[
      `state-${state}`,
      { 'tts-playing': isTtsPlaying, 'clickable': isClickable }
    ]" viewBox="0 0 240 120" xmlns="http://www.w3.org/2000/svg">
      <!-- 渐变定义 -->
      <defs>
        <!-- 蓝色渐变（正常状态） -->
        <linearGradient id="blueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#46AEF7;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#1DD5E6;stop-opacity:1" />
        </linearGradient>

        <!-- 天蓝色渐变（活跃状态） -->
        <linearGradient id="skyBlueGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#87CEEB;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#00BFFF;stop-opacity:1" />
        </linearGradient>

        <!-- 响应状态渐变 -->
        <linearGradient id="responseGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#4ECDC4;stop-opacity:1" />
        </linearGradient>

        <!-- 错误状态渐变 -->
        <linearGradient id="errorGradient" x1="0%" y1="0%" x2="100%" y2="0%">
          <stop offset="0%" style="stop-color:#FF4757;stop-opacity:1" />
          <stop offset="100%" style="stop-color:#FF6B9D;stop-opacity:1" />
        </linearGradient>
      </defs>

      <!-- 音波线条 -->
      <g class="wave-lines">
        <!-- 第1条线 -->
        <rect x="30" y="50" width="6" height="40" rx="3" :fill="currentGradient" class="wave-line line-1">
          <animate attributeName="height" :values="getWaveValues(1)" :dur="getRandomDuration(1)"
            repeatCount="indefinite" :begin="getRandomDelay(1)" />
          <animate attributeName="y" :values="getYValues(1)" :dur="getRandomDuration(1)" repeatCount="indefinite"
            :begin="getRandomDelay(1)" />
        </rect>

        <!-- 第2条线 -->
        <rect x="45" y="40" width="6" height="60" rx="3" :fill="currentGradient" class="wave-line line-2">
          <animate attributeName="height" :values="getWaveValues(2)" :dur="getRandomDuration(2)"
            repeatCount="indefinite" :begin="getRandomDelay(2)" />
          <animate attributeName="y" :values="getYValues(2)" :dur="getRandomDuration(2)" repeatCount="indefinite"
            :begin="getRandomDelay(2)" />
        </rect>

        <!-- 第3条线 -->
        <rect x="60" y="30" width="6" height="80" rx="3" :fill="currentGradient" class="wave-line line-3">
          <animate attributeName="height" :values="getWaveValues(3)" :dur="getRandomDuration(3)"
            repeatCount="indefinite" :begin="getRandomDelay(3)" />
          <animate attributeName="y" :values="getYValues(3)" :dur="getRandomDuration(3)" repeatCount="indefinite"
            :begin="getRandomDelay(3)" />
        </rect>

        <!-- 第4条线 -->
        <rect x="75" y="25" width="6" height="90" rx="3" :fill="currentGradient" class="wave-line line-4">
          <animate attributeName="height" :values="getWaveValues(4)" :dur="getRandomDuration(4)"
            repeatCount="indefinite" :begin="getRandomDelay(4)" />
          <animate attributeName="y" :values="getYValues(4)" :dur="getRandomDuration(4)" repeatCount="indefinite"
            :begin="getRandomDelay(4)" />
        </rect>

        <!-- 第5条线（中心最高） -->
        <rect x="90" y="15" width="6" height="110" rx="3" :fill="currentGradient" class="wave-line line-5">
          <animate attributeName="height" :values="getWaveValues(5)" :dur="getRandomDuration(5)"
            repeatCount="indefinite" :begin="getRandomDelay(5)" />
          <animate attributeName="y" :values="getYValues(5)" :dur="getRandomDuration(5)" repeatCount="indefinite"
            :begin="getRandomDelay(5)" />
        </rect>

        <!-- 第6条线 -->
        <rect x="105" y="20" width="6" height="100" rx="3" :fill="currentGradient" class="wave-line line-6">
          <animate attributeName="height" :values="getWaveValues(6)" :dur="getRandomDuration(6)"
            repeatCount="indefinite" :begin="getRandomDelay(6)" />
          <animate attributeName="y" :values="getYValues(6)" :dur="getRandomDuration(6)" repeatCount="indefinite"
            :begin="getRandomDelay(6)" />
        </rect>

        <!-- 第7条线 -->
        <rect x="120" y="25" width="6" height="90" rx="3" :fill="currentGradient" class="wave-line line-7">
          <animate attributeName="height" :values="getWaveValues(7)" :dur="getRandomDuration(7)"
            repeatCount="indefinite" :begin="getRandomDelay(7)" />
          <animate attributeName="y" :values="getYValues(7)" :dur="getRandomDuration(7)" repeatCount="indefinite"
            :begin="getRandomDelay(7)" />
        </rect>

        <!-- 第8条线 -->
        <rect x="135" y="30" width="6" height="80" rx="3" :fill="currentGradient" class="wave-line line-8">
          <animate attributeName="height" :values="getWaveValues(8)" :dur="getRandomDuration(8)"
            repeatCount="indefinite" :begin="getRandomDelay(8)" />
          <animate attributeName="y" :values="getYValues(8)" :dur="getRandomDuration(8)" repeatCount="indefinite"
            :begin="getRandomDelay(8)" />
        </rect>

        <!-- 第9条线 -->
        <rect x="150" y="35" width="6" height="70" rx="3" :fill="currentGradient" class="wave-line line-9">
          <animate attributeName="height" :values="getWaveValues(9)" :dur="getRandomDuration(9)"
            repeatCount="indefinite" :begin="getRandomDelay(9)" />
          <animate attributeName="y" :values="getYValues(9)" :dur="getRandomDuration(9)" repeatCount="indefinite"
            :begin="getRandomDelay(9)" />
        </rect>

        <!-- 第10条线 -->
        <rect x="165" y="40" width="6" height="60" rx="3" :fill="currentGradient" class="wave-line line-10">
          <animate attributeName="height" :values="getWaveValues(10)" :dur="getRandomDuration(10)"
            repeatCount="indefinite" :begin="getRandomDelay(10)" />
          <animate attributeName="y" :values="getYValues(10)" :dur="getRandomDuration(10)" repeatCount="indefinite"
            :begin="getRandomDelay(10)" />
        </rect>

        <!-- 第11条线 -->
        <rect x="180" y="45" width="6" height="50" rx="3" :fill="currentGradient" class="wave-line line-11">
          <animate attributeName="height" :values="getWaveValues(11)" :dur="getRandomDuration(11)"
            repeatCount="indefinite" :begin="getRandomDelay(11)" />
          <animate attributeName="y" :values="getYValues(11)" :dur="getRandomDuration(11)" repeatCount="indefinite"
            :begin="getRandomDelay(11)" />
        </rect>

        <!-- 第12条线 -->
        <rect x="195" y="50" width="6" height="40" rx="3" :fill="currentGradient" class="wave-line line-12">
          <animate attributeName="height" :values="getWaveValues(12)" :dur="getRandomDuration(12)"
            repeatCount="indefinite" :begin="getRandomDelay(12)" />
          <animate attributeName="y" :values="getYValues(12)" :dur="getRandomDuration(12)" repeatCount="indefinite"
            :begin="getRandomDelay(12)" />
        </rect>

        <!-- 第13条线 -->
        <rect x="210" y="55" width="6" height="30" rx="3" :fill="currentGradient" class="wave-line line-13">
          <animate attributeName="height" :values="getWaveValues(13)" :dur="getRandomDuration(13)"
            repeatCount="indefinite" :begin="getRandomDelay(13)" />
          <animate attributeName="y" :values="getYValues(13)" :dur="getRandomDuration(13)" repeatCount="indefinite"
            :begin="getRandomDelay(13)" />
        </rect>
      </g>

      <!-- TTS播放时的点击提示圆圈 -->
      <circle v-if="isTtsPlaying && isClickable" cx="140" cy="70" r="60" fill="none" stroke="rgba(255, 255, 255, 0.6)"
        stroke-width="3" stroke-dasharray="8,8" class="click-hint-circle">
        <animate attributeName="r" values="60;75;60" dur="2s" repeatCount="indefinite" />
        <animate attributeName="stroke-opacity" values="0.6;1;0.6" dur="2s" repeatCount="indefinite" />
      </circle>

      <!-- TTS播放时的停止图标 -->
      <g v-if="isTtsPlaying && isClickable" class="stop-icon" transform="translate(140, 70)">
        <rect x="-10" y="-10" width="8" height="20" rx="2" fill="white" opacity="0.9" />
        <rect x="2" y="-10" width="8" height="20" rx="2" fill="white" opacity="0.9" />
      </g>
    </svg>

    <!-- 状态说明文字 -->
    <div class="voice-status-text" v-if="showStatusText">
      {{ statusText }}
    </div>
  </div>
</template>

<script>
import { computed } from 'vue'

export default {
  name: 'VoiceWaveAnimation',
  props: {
    state: {
      type: String,
      default: 'idle'
    },
    isTtsPlaying: {
      type: Boolean,
      default: false
    },
    isEnabled: {
      type: Boolean,
      default: false
    },
    showStatusText: {
      type: Boolean,
      default: false
    }
  },
  emits: ['click'],
  setup(props, { emit }) {
    // 当前渐变
    const currentGradient = computed(() => {
      if (!props.isEnabled) return 'url(#blueGradient)'

      switch (props.state) {
        case 'listening_wake_word':
          return 'url(#blueGradient)'
        case 'wake_word_detected':
        case 'listening_command':
          return 'url(#skyBlueGradient)'
        case 'processing':
          return 'url(#skyBlueGradient)'
        case 'responding':
          return 'url(#responseGradient)'
        case 'error':
          return 'url(#errorGradient)'
        default:
          return 'url(#blueGradient)'
      }
    })

    // 是否可点击（TTS播放时）
    const isClickable = computed(() => {
      return props.isTtsPlaying
    })

    // 状态文字
    const statusText = computed(() => {
      if (props.isTtsPlaying) {
        return '点击停止播放'
      }

      switch (props.state) {
        case 'listening_wake_word':
          return '监听唤醒词'
        case 'wake_word_detected':
          return '唤醒成功'
        case 'listening_command':
          return '请说话'
        case 'processing':
          return '思考中'
        case 'responding':
          return '回复中'
        case 'error':
          return '出错了'
        default:
          return '待机中'
      }
    })

    // 生成随机动画时长
    const getRandomDuration = (lineIndex) => {
      const baseDurations = [1.5, 1.3, 1.1, 0.9, 0.7, 0.8, 1.0, 1.2, 1.4, 1.6, 1.1, 1.3, 1.5]
      const baseDur = baseDurations[lineIndex - 1] || 1.0
      const randomOffset = (Math.random() - 0.5) * 0.4 // ±0.2秒的随机偏移
      return `${(baseDur + randomOffset).toFixed(1)}s`
    }

    // 生成随机延迟
    const getRandomDelay = (lineIndex) => {
      const delays = [0, 0.1, 0.2, 0.3, 0.4, 0.35, 0.25, 0.15, 0.05, 0.45, 0.5, 0.6, 0.7]
      const baseDelay = delays[lineIndex - 1] || 0
      const randomOffset = Math.random() * 0.3 // 0-0.3秒的随机延迟
      return `${(baseDelay + randomOffset).toFixed(2)}s`
    }

    // 根据状态获取音波动画值（增加幅度和随机性）
    const getWaveValues = (lineIndex) => {
      if (!props.isEnabled) {
        return '20;20;20' // 静态状态
      }

      const baseHeights = [40, 60, 80, 90, 110, 100, 90, 80, 70, 60, 50, 40, 30]
      const baseHeight = baseHeights[lineIndex - 1] || 40

      // 添加随机波动
      const randomFactor1 = 0.7 + Math.random() * 0.6 // 0.7-1.3
      const randomFactor2 = 1.2 + Math.random() * 0.8 // 1.2-2.0
      const randomFactor3 = 0.5 + Math.random() * 0.8 // 0.5-1.3

      switch (props.state) {
        case 'idle':
          return `${baseHeight * 0.2 * randomFactor1};${baseHeight * 0.3 * randomFactor2};${baseHeight * 0.2 * randomFactor3}`

        case 'listening_wake_word':
          return `${baseHeight * 0.4 * randomFactor1};${baseHeight * 0.8 * randomFactor2};${baseHeight * 0.5 * randomFactor3}`

        case 'wake_word_detected':
        case 'listening_command':
          return `${baseHeight * 0.8 * randomFactor1};${baseHeight * 1.5 * randomFactor2};${baseHeight * 0.9 * randomFactor3}`

        case 'processing':
          return `${baseHeight * 0.6 * randomFactor1};${baseHeight * 1.2 * randomFactor2};${baseHeight * 0.7 * randomFactor3}`

        case 'responding':
          return `${baseHeight * 1.0 * randomFactor1};${baseHeight * 1.8 * randomFactor2};${baseHeight * 1.1 * randomFactor3}`

        case 'error':
          return `${baseHeight * 0.3 * randomFactor1};${baseHeight * 1.0 * randomFactor2};${baseHeight * 0.4 * randomFactor3}`

        default:
          return `${baseHeight * 0.5 * randomFactor1};${baseHeight * 0.8 * randomFactor2};${baseHeight * 0.6 * randomFactor3}`
      }
    }

    // 根据状态获取Y位置值（更大的变化幅度）
    const getYValues = (lineIndex) => {
      if (!props.isEnabled) {
        return '60;60;60' // 静态位置
      }

      const baseHeights = [40, 60, 80, 90, 110, 100, 90, 80, 70, 60, 50, 40, 30]
      const baseHeight = baseHeights[lineIndex - 1] || 40
      const centerY = 70 // SVG中心Y位置

      // 添加随机波动
      const randomFactor1 = 0.7 + Math.random() * 0.6
      const randomFactor2 = 1.2 + Math.random() * 0.8
      const randomFactor3 = 0.5 + Math.random() * 0.8

      switch (props.state) {
        case 'idle':
          const idleHeight1 = baseHeight * 0.2 * randomFactor1
          const idleHeight2 = baseHeight * 0.3 * randomFactor2
          const idleHeight3 = baseHeight * 0.2 * randomFactor3
          return `${centerY - idleHeight1 / 2};${centerY - idleHeight2 / 2};${centerY - idleHeight3 / 2}`

        case 'listening_wake_word':
          const listenHeight1 = baseHeight * 0.4 * randomFactor1
          const listenHeight2 = baseHeight * 0.8 * randomFactor2
          const listenHeight3 = baseHeight * 0.5 * randomFactor3
          return `${centerY - listenHeight1 / 2};${centerY - listenHeight2 / 2};${centerY - listenHeight3 / 2}`

        case 'wake_word_detected':
        case 'listening_command':
          const wakeHeight1 = baseHeight * 0.8 * randomFactor1
          const wakeHeight2 = baseHeight * 1.5 * randomFactor2
          const wakeHeight3 = baseHeight * 0.9 * randomFactor3
          return `${centerY - wakeHeight1 / 2};${centerY - wakeHeight2 / 2};${centerY - wakeHeight3 / 2}`

        case 'processing':
          const procHeight1 = baseHeight * 0.6 * randomFactor1
          const procHeight2 = baseHeight * 1.2 * randomFactor2
          const procHeight3 = baseHeight * 0.7 * randomFactor3
          return `${centerY - procHeight1 / 2};${centerY - procHeight2 / 2};${centerY - procHeight3 / 2}`

        case 'responding':
          const respHeight1 = baseHeight * 1.0 * randomFactor1
          const respHeight2 = baseHeight * 1.8 * randomFactor2
          const respHeight3 = baseHeight * 1.1 * randomFactor3
          return `${centerY - respHeight1 / 2};${centerY - respHeight2 / 2};${centerY - respHeight3 / 2}`

        case 'error':
          const errHeight1 = baseHeight * 0.3 * randomFactor1
          const errHeight2 = baseHeight * 1.0 * randomFactor2
          const errHeight3 = baseHeight * 0.4 * randomFactor3
          return `${centerY - errHeight1 / 2};${centerY - errHeight2 / 2};${centerY - errHeight3 / 2}`

        default:
          const defHeight1 = baseHeight * 0.5 * randomFactor1
          const defHeight2 = baseHeight * 0.8 * randomFactor2
          const defHeight3 = baseHeight * 0.6 * randomFactor3
          return `${centerY - defHeight1 / 2};${centerY - defHeight2 / 2};${centerY - defHeight3 / 2}`
      }
    }

    // 处理点击事件
    const handleClick = () => {
      console.log('🎵 音波组件被点击，当前状态:', {
        isTtsPlaying: props.isTtsPlaying,
        isClickable: isClickable.value,
        state: props.state
      })

      if (isClickable.value) {
        console.log('🛑 点击音波，发出停止TTS信号')
        emit('click')
      }
    }

    return {
      currentGradient,
      isClickable,
      statusText,
      getWaveValues,
      getYValues,
      getRandomDuration,
      getRandomDelay,
      handleClick
    }
  }
}
</script>

<style lang="scss" scoped>
.voice-wave-container {
  position: relative;
  width: 100%;
  height: 60px; // 从80px减小到60px
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin: 0 auto;
  position: absolute;
  top: 50%;
  left: 50%;
  margin-top: -30px;
  margin-left: -140px;
}

.voice-wave-svg {
  width: 100%;
  height: 100%;
  transition: all 0.3s ease;
  padding-left: 20px;
  box-sizing: border-box;

  // 默认状态
  .wave-line {
    transition: all 0.3s ease;
  }

  // 不同状态的样式
  &.state-idle {
    opacity: 0.6;

    .wave-line {
      animation-play-state: paused;
    }
  }

  &.state-listening_wake_word {
    opacity: 1;

    .wave-line {
      animation-play-state: running;
    }
  }

  &.state-wake_word_detected,
  &.state-listening_command {
    opacity: 1;
    filter: drop-shadow(0 0 12px rgba(135, 206, 235, 0.6));

    .wave-line {
      animation-play-state: running;
    }
  }

  &.state-processing {
    opacity: 1;
    filter: drop-shadow(0 0 8px rgba(70, 174, 247, 0.4));

    .wave-line {
      animation-play-state: running;
    }
  }

  &.state-responding {
    opacity: 1;
    filter: drop-shadow(0 0 15px rgba(255, 107, 107, 0.4));

    .wave-line {
      animation-play-state: running;
    }
  }

  &.state-error {
    opacity: 0.8;
    filter: drop-shadow(0 0 12px rgba(255, 71, 87, 0.6));

    .wave-line {
      animation-play-state: running;
    }
  }

  // TTS播放时的样式
  &.tts-playing {
    filter: drop-shadow(0 0 18px rgba(255, 107, 107, 0.6));

    .wave-line {
      animation-duration: 0.4s !important; // 加快动画速度
    }
  }

  // 可点击状态
  &.clickable {
    cursor: pointer;
    transition: transform 0.2s ease;

    &:hover {
      transform: scale(1.05);
      filter: drop-shadow(0 0 20px rgba(255, 255, 255, 0.8));
    }

    &:active {
      transform: scale(0.95);
    }
  }
}

// 点击提示圆圈动画
.click-hint-circle {
  pointer-events: none;
}

// 停止图标
.stop-icon {
  pointer-events: none;
  transition: opacity 0.2s ease;
}

// 状态文字
.voice-status-text {
  position: absolute;
  bottom: -30px;
  left: 50%;
  transform: translateX(-50%);
  font-size: 12px;
  color: #666;
  background: rgba(255, 255, 255, 0.9);
  padding: 6px 12px;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  white-space: nowrap;
  opacity: 0;
  transition: opacity 0.3s ease;

  .voice-wave-container:hover & {
    opacity: 1;
  }
}
</style>
// 应用配置管理
// 支持开发环境和生产环境的不同配置

// 检测当前环境
const isDevelopment = window.location.protocol === 'http:' || window.location.hostname === 'localhost'
const isProduction = !isDevelopment

console.log('🌍 环境检测:', {
  isDevelopment,
  isProduction,
  protocol: window.location.protocol,
  hostname: window.location.hostname,
  href: window.location.href
})

// 腾讯云ASR代理配置
// 使用代理服务器而不是直接调用腾讯云API
const getTencentConfig = () => {
  return {
    // 代理服务器配置
    proxyUrl: 'ws://*************:9603/prod-api/api/asr',
    proxyParams: {
      id: '6',
      convert_num_mode: '1',
      debug: 'false',
      filter_dirty: '1',
      filter_modal: '1',
      filter_punc: '0',
      hotword_id: '',
      max_speak_time: '30000',
      needvad: '1',
      noise_threshold: '0.4',
      voice_format: '1',
      word_info: '2'
    },
    
    // 启用token认证
    enableTokenAuth: true,
    
    // 保留原有配置用于兼容性（但不会被使用）
    secretId: 'AKID3zjGbidNwIVWQDnAML8i5HH4g8OQFuNc',
    secretKey: '4ygBYr8NbfYmtrdBWt8Aze8SoAKI6nib',
    appId: 1305690769
  }
}

// 获取腾讯云配置
export const TENCENT_CONFIG = getTencentConfig()

// 阿里云ASR配置
// 注意：在生产环境中，这些密钥应该通过更安全的方式获取
const getAliyunConfig = () => {
  // 开发环境配置
  return {
    accessKeyId: 'LTAI5tLYqLP9ep6X4kkYPJVk',
    accessKeySecret: '******************************',
    appkey: 'uappWLUEjGcvRLZP',
    hotwordId: 'c8a07d7c7d3f451cab30624519c218f7',
    llmApiKey: 'sk-64244749e4db4ec2ad347a7ee0e7334e'
  }
}

// 获取阿里云配置
export const ALIYUN_CONFIG = getAliyunConfig()

// 调试配置
export const DEBUG_CONFIG = {
  enableVoiceDebug: isDevelopment,
  enableCryptoJSDebug: isDevelopment,
  enableWebSocketDebug: isDevelopment,
  enableMCPDebug: isDevelopment
}

// 语音识别配置
export const VOICE_CONFIG = {
  maxRetryAttempts: 3,
  retryDelay: 1000,
  connectionTimeout: 10000,
  cryptoJSLoadTimeout: 10000
}

// 验证配置完整性
export const validateConfig = () => {
  const config = TENCENT_CONFIG
  const errors = []

  // 验证代理配置
  if (!config.proxyUrl) {
    errors.push('腾讯云代理URL未配置')
  }

  if (!config.proxyParams) {
    errors.push('腾讯云代理参数未配置')
  }

  if (errors.length > 0) {
    console.error('❌ 配置验证失败:', errors)
    return { valid: false, errors }
  }

  console.log('✅ 配置验证通过', {
    hasProxyUrl: !!config.proxyUrl,
    hasProxyParams: !!config.proxyParams,
    proxyUrl: config.proxyUrl,
    isDevelopment
  })

  return { valid: true, errors: [] }
}

// 打印配置状态（不包含敏感信息）
console.log('📋 应用配置加载完成:', {
  environment: isDevelopment ? 'development' : 'production',
  tencentConfig: {
    hasProxyUrl: !!TENCENT_CONFIG.proxyUrl,
    hasProxyParams: !!TENCENT_CONFIG.proxyParams,
    proxyUrl: TENCENT_CONFIG.proxyUrl
  },
  aliyunConfig: {
    hasAccessKeyId: !!ALIYUN_CONFIG.accessKeyId,
    hasAppkey: !!ALIYUN_CONFIG.appkey
  }
}) 
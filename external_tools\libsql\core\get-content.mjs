import TurndownService from 'turndown';
import mammoth from 'mammoth';

/**
 * 获取文件内容
 * @param {*} file
 */
export async function getContent(file) {
    let fileContent;

    // 如果是 docx 文件，先转换为 markdown
    if (file.name.endsWith('.docx')) {
        const arrayBuffer = await file.arrayBuffer();
        try {
            const htmlResult = await mammoth.convertToHtml(
                {arrayBuffer},
                {
                    convertImage: (image) => {
                        try {
                            return mammoth.docx.paragraph({
                                children: [
                                    mammoth.docx.textRun({
                                        text: ''
                                    })
                                ]
                            });
                        } catch (error) {
                            console.error(`[getContent] 图片转换错误: ${file.name}`, error);
                            // 返回一个简单的替代文本，避免转换失败
                            return {
                                altText: '[图片]'
                            };
                        }
                    }
                }
            );
            
            try {
                const turndownService = new TurndownService();
                fileContent = turndownService.turndown(htmlResult.value);
            } catch (markdownError) {
                console.error(`[getContent] HTML转Markdown错误: ${file.name}`, markdownError);
                // 回退到原始HTML
                fileContent = htmlResult.value;
            }
        } catch (conversionError) {
            console.error(`[getContent] 文档转换失败: ${file.name}`, conversionError);
            // 回退方案：使用extractRawText
            console.log(`[getContent] 尝试提取纯文本...`);
            const textResult = await mammoth.extractRawText({arrayBuffer});
            fileContent = textResult.value;
        }
    } else {
        // 对于 md 和 txt 文件，直接读取内容
        fileContent = await file.text();
    }

    return fileContent;
}
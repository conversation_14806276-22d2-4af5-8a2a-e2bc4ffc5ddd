/**
 * Markdown文档解析模块
 */

/**
 * 获取文档内容
 * 支持多种格式的文档处理
 * @param {File|string} fileSource - 文件对象或文件路径
 * @returns {Promise<string>} - 处理后的markdown内容
 */
export async function getDocumentContent(fileSource) {
  try {
    let fileContent = '';
    let fileName = '';
    
    // 判断输入类型
    if (typeof fileSource === 'string') {
      // 文件路径
      fileName = fileSource.split(/[\\\/]/).pop();
      
      // 通过Electron API读取文件
      if (window.electronAPI && window.electronAPI.readFile) {
        const fileData = await window.electronAPI.readFile(fileSource);
        
        if (fileName.toLowerCase().endsWith('.docx') || fileName.toLowerCase().endsWith('.doc')) {
          // 处理Word文档
          fileContent = await processWordDocument(fileData);
        } else {
          // 处理其他文本文件
          fileContent = fileData;
        }
      } else {
        throw new Error('Electron API不可用');
      }
    } else if (fileSource instanceof File) {
      // 文件对象（来自文件选择器）
      fileName = fileSource.name;
      
      if (fileName.toLowerCase().endsWith('.docx')) {
        // 处理Word文档
        const arrayBuffer = await fileSource.arrayBuffer();
        fileContent = await processWordDocumentFromBuffer(arrayBuffer);
      } else {
        // 处理其他文本文件
        fileContent = await fileSource.text();
      }
    } else {
      throw new Error('不支持的文件源类型');
    }
    
    return fileContent;
  } catch (error) {
    console.error('获取文档内容失败:', error);
    throw error;
  }
}

/**
 * 处理Word文档（通过Electron API）
 * @param {ArrayBuffer} fileData - 文件数据
 * @returns {Promise<string>} - 转换后的markdown内容
 */
async function processWordDocument(fileData) {
  try {
    // 通过Electron API处理Word文档
    if (window.electronAPI && window.electronAPI.convertWordToMarkdown) {
      return await window.electronAPI.convertWordToMarkdown(fileData);
    } else {
      throw new Error('Word文档转换功能不可用');
    }
  } catch (error) {
    console.error('Word文档处理失败:', error);
    throw error;
  }
}

/**
 * 处理Word文档（从ArrayBuffer）
 * @param {ArrayBuffer} arrayBuffer - 文件数据
 * @returns {Promise<string>} - 转换后的markdown内容
 */
async function processWordDocumentFromBuffer(arrayBuffer) {
  try {
    // 这里需要在主进程中实现mammoth转换
    // 暂时返回错误提示，后续实现
    throw new Error('浏览器环境暂不支持Word文档处理，请使用文件路径方式');
  } catch (error) {
    console.error('Word文档缓冲区处理失败:', error);
    throw error;
  }
}

/**
 * 提取文档大纲
 * @param {string} text - Markdown文本
 * @returns {Array} - 提取的大纲数组
 */
function extractOutline(text) {
  const outlineRegex = /^(#{1,6})\s+(.+?)(?:\s*\{#[\w-]+\})?\s*$/gm;
  const outline = [];
  let match;

  while ((match = outlineRegex.exec(text)) !== null) {
    const level = match[1].length;
    const title = match[2].trim();

    outline.push({
      level,
      title,
      position: match.index
    });
  }

  return outline;
}

/**
 * 根据标题分割文档
 * @param {string} text - Markdown文本
 * @param {Array} outline - 文档大纲
 * @returns {Array} - 按标题分割的段落数组
 */
function splitByHeadings(text, outline) {
  if (outline.length === 0) {
    return [
      {
        heading: null,
        level: 0,
        content: text,
        position: 0
      }
    ];
  }

  const sections = [];

  // 添加第一个标题前的内容（如果有）
  if (outline[0].position > 0) {
    const frontMatter = text.substring(0, outline[0].position).trim();
    if (frontMatter.length > 0) {
      sections.push({
        heading: null,
        level: 0,
        content: frontMatter,
        position: 0
      });
    }
  }

  // 分割每个标题的内容
  for (let i = 0; i < outline.length; i++) {
    const current = outline[i];
    const next = i < outline.length - 1 ? outline[i + 1] : null;

    const headingLine = text.substring(current.position).split('\n')[0];
    const startPos = current.position + headingLine.length + 1;
    const endPos = next ? next.position : text.length;

    let content = text.substring(startPos, endPos).trim();

    sections.push({
      heading: current.title,
      level: current.level,
      content: content,
      position: current.position
    });
  }

  return sections;
}

export {
  extractOutline,
  splitByHeadings
}; 
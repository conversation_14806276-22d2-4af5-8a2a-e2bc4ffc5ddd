const AuthIPCHandler = require('./auth')
const AppIPCHandler = require('./app')
const KnowledgeIPCHandler = require('./knowledge')
const MCPIPCHandler = require('./mcp')
const EmailIPCHandler = require('./email')
const FloatingIPCHandler = require('./floating')

/**
 * IPC管理器 - 统一管理所有IPC处理程序
 */
class IPCManager {
  constructor(appManager) {
    this.appManager = appManager
    
    // 初始化各个IPC处理程序
    this.authHandler = new AuthIPCHandler(appManager)
    this.appHandler = new AppIPCHandler(appManager)
    this.knowledgeHandler = new KnowledgeIPCHandler(appManager)
    this.mcpHandler = new MCPIPCHandler(appManager)
    this.emailHandler = new EmailIPCHandler(appManager)
    this.floatingHandler = new FloatingIPCHandler(appManager)
  }

  /**
   * 注册所有IPC处理程序
   */
  registerAll() {
    console.log('🔧 开始注册所有IPC处理程序...')

    try {
      // 注册各个模块的IPC处理程序
      this.authHandler.register()
      this.appHandler.register()
      this.knowledgeHandler.register()
      this.mcpHandler.register()
      this.emailHandler.register()
      this.floatingHandler.register()

      console.log('✅ 所有IPC处理程序注册完成')
    } catch (error) {
      console.error('❌ IPC处理程序注册失败:', error)
      throw error
    }
  }

  /**
   * 获取特定的IPC处理程序
   */
  getHandler(type) {
    switch (type) {
      case 'auth':
        return this.authHandler
      case 'app':
        return this.appHandler
      case 'knowledge':
        return this.knowledgeHandler
      case 'mcp':
        return this.mcpHandler
      case 'email':
        return this.emailHandler
      case 'floating':
        return this.floatingHandler
      default:
        throw new Error(`未知的IPC处理程序类型: ${type}`)
    }
  }

  /**
   * 销毁所有IPC处理程序
   */
  destroy() {
    console.log('🔧 销毁IPC管理器...')
    
    // 这里可以添加清理逻辑，比如移除事件监听器等
    // 目前Electron的ipcMain会在应用退出时自动清理
    
    console.log('✅ IPC管理器已销毁')
  }
}

module.exports = IPCManager

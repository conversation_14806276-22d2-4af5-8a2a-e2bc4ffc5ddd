import { createApp } from 'vue'
import { createPinia } from 'pinia'
import App from './App.vue'
import './assets/styles/main.scss'

console.log('Main.js is loading...')

const app = createApp(App)
const pinia = createPinia()

app.use(pinia)
app.mount('#app')

console.log('Vue app mounted successfully')

// 添加AI请求调试监听器
if (window.electronAPI) {
  // 监听AI请求开始
  if (window.electronAPI.onAIRequestStart) {
    window.electronAPI.onAIRequestStart((requestInfo) => {
      console.group('🤖 AI请求开始')
      console.log('请求ID:', requestInfo.requestId)
      console.log('请求URL:', requestInfo.url)
      console.log('请求方法:', requestInfo.method)
      console.log('请求头:', requestInfo.headers)
      console.log('请求体:', requestInfo.body)
      console.log('时间戳:', requestInfo.timestamp)
      console.groupEnd()
    })
  }

  // 监听AI请求完成
  if (window.electronAPI.onAIRequestComplete) {
    window.electronAPI.onAIRequestComplete((responseInfo) => {
      console.group('🤖 AI请求完成')
      console.log('请求ID:', responseInfo.requestId)
      console.log('响应状态:', responseInfo.status)
      console.log('响应头:', responseInfo.headers)
      console.log('响应数据:', responseInfo.data)
      console.log('耗时:', responseInfo.duration + 'ms')
      console.log('时间戳:', responseInfo.timestamp)
      console.groupEnd()
    })
  }

  // 监听AI请求错误
  if (window.electronAPI.onAIRequestError) {
    window.electronAPI.onAIRequestError((errorInfo) => {
      console.group('🤖 AI请求错误')
      console.error('请求ID:', errorInfo.requestId)
      console.error('错误信息:', errorInfo.error)
      console.error('耗时:', errorInfo.duration + 'ms')
      console.error('时间戳:', errorInfo.timestamp)
      console.groupEnd()
    })
  }
} 
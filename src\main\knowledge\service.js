const fs = require('fs')
const path = require('path')
const { getKnowledgeConfig } = require('./config')
const db = require('./db')
const { getKnowledgeEmbedding, rerank } = require('./embedding')

// 全局变量
let isKnowledgeInitialized = false

/**
 * 查找相似片段
 * @param {string} description - 查询描述
 * @param {string} fileType - 文件类型过滤
 * @param {number} limit - 返回数量限制
 * @returns {Promise<Array>} 相似片段数组
 */
async function findSimilarChunks(description, fileType = null, limit = 10) {
  const queryEmbedding = await getKnowledgeEmbedding(description);

  let sql, args;

  if (fileType) {
    // 查询指定fileType的file
    const files = await db.libsqlClient.execute({
      sql: `SELECT id FROM user_file WHERE file_type = ?`,
      args: [fileType]
    });

    if (!files.rows.length) {
      return [];
    }

    const fileIds = files.rows.map(row => row.id);

    sql = `WITH vector_scores AS (
      SELECT rowid AS id,
             file_id,
             file_content,
             embedding,
             1 - vector_distance_cos(embedding, vector32(?)) AS similarity
      FROM user_file_embd
      WHERE file_id IN (${Array(fileIds.length).fill('?').join(',')})
      ORDER BY similarity DESC
      LIMIT ?
    )
    SELECT v.id,
           v.file_id AS fileId,
           v.file_content AS content,
           v.similarity,
           f.file_name,
           f.source_file_path AS filePath
    FROM vector_scores v
    LEFT JOIN user_file f ON v.file_id = f.id`;

    args = [JSON.stringify(Array.from(queryEmbedding)), ...fileIds, limit];
  } else {
    // 查询所有文件
    sql = `WITH vector_scores AS (
      SELECT rowid AS id,
             file_id,
             file_content,
             embedding,
             1 - vector_distance_cos(embedding, vector32(?)) AS similarity
      FROM user_file_embd
      ORDER BY similarity DESC
      LIMIT ?
    )
    SELECT v.id,
           v.file_id AS fileId,
           v.file_content AS content,
           v.similarity,
           f.file_name,
           f.source_file_path AS filePath
    FROM vector_scores v
    LEFT JOIN user_file f ON v.file_id = f.id`;

    args = [JSON.stringify(Array.from(queryEmbedding)), limit];
  }

  const results = await db.libsqlClient.execute({ sql, args });
  return results.rows;
}

/**
 * 搜索知识库 - 增强版本，支持重排序和动态阈值
 * @param {string} query - 查询文本
 * @param {number} limit - 返回数量限制
 * @param {string} fileType - 文件类型过滤
 * @returns {Promise<Array>} 搜索结果数组
 */
async function searchKnowledge(query, limit = null, fileType = null) {
  try {
    // 只有在数据库还未初始化时才进行初始化
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      const { initKnowledgeDatabase } = require('./db')
      await initKnowledgeDatabase()
      isKnowledgeInitialized = true
    }

    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    const searchLimit = limit || KNOWLEDGE_CONFIG.search.defaultLimit

    console.log(`🔍 搜索知识库: "${query}"${fileType ? ` (文件类型: ${fileType})` : ''}`)

    // 首先检查数据库中是否有数据
    const countResult = await db.libsqlClient.execute('SELECT COUNT(*) as count FROM user_file_embd')
    const totalEmbeddings = countResult.rows[0].count
    console.log(`📊 数据库中共有 ${totalEmbeddings} 个embedding片段`)

    if (totalEmbeddings === 0) {
      console.log('⚠️ 数据库中没有embedding数据，请先索引文档')
      return []
    }

    // 尝试使用增强搜索
    let searchResults = []
    let useVectorSearch = true

    try {
      // 第一步：使用向量搜索找到候选结果
      console.log('🔍 第一步：向量搜索候选结果...')
      let similarChunks = await findSimilarChunks(query, fileType, searchLimit * 2)

      console.log(`🔍 向量搜索结果: ${similarChunks.length} 个候选`)

      if (similarChunks.length === 0) {
        console.log('⚠️ 向量搜索没有找到结果')
        return []
      }

      // 第二步：使用重排序模型优化结果
      console.log('🔄 第二步：重排序优化结果...')
      const rerankedChunks = await rerank(similarChunks, query)

      console.log(`✨ 重排序后结果: ${rerankedChunks.length} 个`)

      // 转换为标准格式
      searchResults = rerankedChunks.map(row => ({
        id: row.id,
        file_id: row.fileId || row.file_id,
        content: row.content,
        similarity: row.similarity,
        file_name: row.file_name,
        source_file_path: row.filePath || row.source_file_path
      }))

    } catch (vectorError) {
      console.error('⚠️ 增强搜索失败，降级到简单文本搜索:', vectorError.message)
      useVectorSearch = false

      // 降级到基于文本的简单搜索
      try {
        const textSearchResults = await db.libsqlClient.execute({
          sql: `
            SELECT rowid as id,
                   file_id,
                   file_content as content,
                   0.5 as similarity
            FROM user_file_embd
            WHERE file_content LIKE ? OR file_content LIKE ?
            ORDER BY 
            CASE 
              WHEN file_content LIKE ? THEN 1
              WHEN file_content LIKE ? THEN 2
              ELSE 3
            END
            LIMIT ?
          `,
          args: [`%${query}%`, `%${query.split(' ')[0]}%`, `%${query}%`, `%${query.split(' ')[0]}%`, searchLimit]
        })

        searchResults = textSearchResults.rows.map(row => ({
          id: row.id,
          file_id: row.file_id,
          content: row.content,
          similarity: row.similarity,
          file_name: '未知文件',
          source_file_path: '未知路径'
        }))

        console.log(`📝 文本搜索结果: ${searchResults.length} 个`)
      } catch (textError) {
        console.error('❌ 文本搜索也失败了:', textError.message)
        return []
      }
    }

    // 应用相似度阈值过滤
    const minSimilarity = KNOWLEDGE_CONFIG.search.minSimilarityThreshold
    const filteredResults = searchResults.filter(result => result.similarity >= minSimilarity)

    console.log(`🔍 相似度过滤: ${searchResults.length} -> ${filteredResults.length} (阈值: ${minSimilarity})`)

    // 限制返回数量
    const finalResults = filteredResults.slice(0, searchLimit)

    console.log(`✅ 搜索完成，返回 ${finalResults.length} 个结果`)
    return finalResults

  } catch (error) {
    console.error('❌ 知识库搜索失败:', error)
    return []
  }
}

/**
 * 获取知识库统计信息
 * @returns {Promise<Object>} 统计信息对象
 */
async function getKnowledgeStats() {
  try {
    if (!isKnowledgeInitialized) {
      console.log('🔧 知识库未初始化，开始初始化...')
      const { initKnowledgeDatabase } = require('./db')
      await initKnowledgeDatabase()
      isKnowledgeInitialized = true
    }

    console.log('📊 获取知识库统计信息...')

    // 检查数据库客户端
    let libsqlClient = db.libsqlClient
    if (!libsqlClient) {
      console.log('🔧 数据库客户端为 null，尝试重新初始化...')
      const { initKnowledgeDatabase } = require('./db')
      const initSuccess = await initKnowledgeDatabase()
      if (!initSuccess) {
        throw new Error('数据库重新初始化失败')
      }
      libsqlClient = db.libsqlClient
      if (!libsqlClient) {
        throw new Error('数据库客户端仍然未初始化')
      }
    }

    const stats = {}

    // 获取文件数量
    const fileCountResult = await libsqlClient.execute('SELECT COUNT(*) as count FROM user_file')
    stats.fileCount = fileCountResult.rows[0].count

    // 获取embedding片段数量
    const embeddingCountResult = await libsqlClient.execute('SELECT COUNT(*) as count FROM user_file_embd')
    stats.embeddingCount = embeddingCountResult.rows[0].count

    // 获取数据库大小
    const dbSizeResult = await libsqlClient.execute('SELECT page_count * page_size as size FROM pragma_page_count(), pragma_page_size()')
    stats.databaseSize = dbSizeResult.rows[0].size

    // 获取最近添加的文件
    const recentFilesResult = await libsqlClient.execute(`
      SELECT file_name, create_time 
      FROM user_file 
      ORDER BY create_time DESC 
      LIMIT 5
    `)
    stats.recentFiles = recentFilesResult.rows

    console.log('✅ 知识库统计信息获取完成:', stats)
    return stats

  } catch (error) {
    console.error('❌ 获取知识库统计信息失败:', error)
    return {
      fileCount: 0,
      embeddingCount: 0,
      databaseSize: 0,
      recentFiles: []
    }
  }
}

/**
 * 设置知识库初始化状态
 * @param {boolean} initialized - 初始化状态
 */
function setKnowledgeInitialized(initialized) {
  isKnowledgeInitialized = initialized
}

/**
 * 获取知识库初始化状态
 * @returns {boolean} 初始化状态
 */
function getKnowledgeInitializedStatus() {
  return isKnowledgeInitialized
}

module.exports = {
  findSimilarChunks,
  searchKnowledge,
  getKnowledgeStats,
  setKnowledgeInitialized,
  isKnowledgeInitialized: getKnowledgeInitializedStatus
} 
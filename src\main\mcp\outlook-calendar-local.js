#!/usr/bin/env node

/**
 * outlook-calendar-local.js - 本地Outlook日历MCP服务器
 * 使用本地VBS脚本而不是外部包
 */

// MCP SDK模块将在异步函数中动态导入
let Server, StdioServerTransport, CallToolRequestSchema, ErrorCode, ListToolsRequestSchema, McpError;

const fs = require('fs');
const path = require('path');
const { exec } = require('child_process');

/**
 * 本地Outlook日历MCP服务器类
 */
class LocalOutlookCalendarServer {
  constructor() {
    this.server = null;
    this.initialized = false;
  }

  /**
   * 检查是否为开发环境
   */
  checkDevelopmentEnvironment() {
    // 多种方式检测开发环境
    if (process.env.NODE_ENV === 'development') {
      return true;
    }

    try {
      const electron = require('electron');
      if (electron?.app?.isPackaged === false) {
        return true;
      }
    } catch (error) {
      // 无法加载electron模块，可能在独立Node.js环境中
    }

    // 检查路径特征
    if (__dirname.includes('src\\main\\mcp') && !__dirname.includes('app.asar')) {
      return true;
    }

    return false;
  }

  /**
   * 异步初始化MCP服务器
   */
  async initialize() {
    if (this.initialized) return;

    try {
      // 动态导入MCP SDK
      console.log('📦 动态导入MCP SDK...');
      console.log('🔧 当前工作目录:', process.cwd());
      console.log('🔧 __dirname:', __dirname);
      console.log('🔧 Node.js版本:', process.version);

      // 检测环境
      const isDev = this.checkDevelopmentEnvironment();
      console.log('🔧 环境检测结果:', isDev ? '开发环境' : '生产环境');

      const serverModule = await import('@modelcontextprotocol/sdk/server/index.js');
      const stdioModule = await import('@modelcontextprotocol/sdk/server/stdio.js');
      const typesModule = await import('@modelcontextprotocol/sdk/types.js');

      Server = serverModule.Server;
      StdioServerTransport = stdioModule.StdioServerTransport;
      CallToolRequestSchema = typesModule.CallToolRequestSchema;
      ErrorCode = typesModule.ErrorCode;
      ListToolsRequestSchema = typesModule.ListToolsRequestSchema;
      McpError = typesModule.McpError;

      console.log('✅ MCP SDK导入成功');

      // 初始化MCP服务器
      this.server = new Server(
        {
          name: 'local-outlook-calendar-server',
          version: '1.0.0',
        },
        {
          capabilities: {
            tools: {},
          },
        }
      );

      // 设置脚本路径
      this.setupScriptPaths();

      // 设置工具处理器
      this.setupToolHandlers();

      // 错误处理
      this.server.onerror = (error) => console.error('[MCP Error]', error);
      process.on('SIGINT', async () => {
        await this.server.close();
        process.exit(0);
      });

      this.initialized = true;
      console.log('✅ Outlook日历MCP服务器初始化完成');

    } catch (error) {
      console.error('❌ MCP服务器初始化失败:', error);
      throw error;
    }
  }

  /**
   * 设置VBS脚本路径
   */
  setupScriptPaths() {
    // 确定脚本路径
    let scriptsPath;
    
    // 检查是否为开发环境
    let isDev;
    try {
      const electron = require('electron');
      isDev = process.env.NODE_ENV === 'development' || !electron?.app?.isPackaged;
    } catch (error) {
      // 如果无法加载electron模块，根据环境变量判断
      isDev = process.env.NODE_ENV === 'development';
    }

    console.log('🔧 环境检测:', isDev ? '开发环境' : '生产环境');
    
    if (isDev) {
      // 开发环境路径
      scriptsPath = path.join(__dirname, '..', '..', 'renderer', 'scripts');
    } else {
      // 打包环境路径 - 确定resourcesPath
      let resourcesPath = process.resourcesPath;
      if (!resourcesPath) {
        // 如果process.resourcesPath未定义，从execPath推导
        resourcesPath = path.join(path.dirname(process.execPath), 'resources');
        console.log('🔧 从execPath推导resourcesPath:', resourcesPath);
      }

      // 尝试多个可能的路径
      const altPaths = [
        path.join(resourcesPath, 'scripts'),
        path.join(resourcesPath, 'app', 'scripts'),
        path.join(process.cwd(), 'scripts'),
        path.join(__dirname, '..', '..', '..', 'scripts'),
        path.join(__dirname, '..', '..', '..', '..', 'scripts')
      ];

      console.log('🔧 生产环境脚本路径查找...');
      console.log('🔧 process.resourcesPath:', process.resourcesPath);
      console.log('🔧 __dirname:', __dirname);
      console.log('🔧 process.cwd():', process.cwd());
      console.log('🔧 process.execPath:', process.execPath);
      console.log('🔧 process.argv[0]:', process.argv[0]);

      let foundPath = null;
      for (const altPath of altPaths) {
        console.log('🔍 检查脚本路径:', altPath);
        try {
          if (fs.existsSync(altPath)) {
            foundPath = altPath;
            scriptsPath = altPath;
            console.log('✅ 找到脚本路径:', altPath);
            break;
          } else {
            console.log('❌ 路径不存在:', altPath);
          }
        } catch (error) {
          console.log('❌ 检查路径时出错:', altPath, error.message);
        }
      }

      if (!foundPath) {
        console.error('❌ 未找到脚本路径，尝试的路径:');
        altPaths.forEach(p => console.error('  -', p));
        console.error('❌ 这将导致MCP服务器无法正常工作');

        // 不要抛出错误，而是设置一个默认路径并继续
        scriptsPath = path.join(process.resourcesPath, 'scripts');
        console.error('⚠️ 使用默认脚本路径:', scriptsPath);
        console.error('⚠️ MCP服务器将启动，但VBS脚本调用可能失败');
      }
    }

    this.scriptsPath = scriptsPath;
    console.log('✅ 找到Outlook VBS脚本路径:', this.scriptsPath);

    // 验证必要的脚本文件存在
    const requiredScripts = ['createEvent.vbs', 'listEvents.vbs', 'utils.vbs'];
    let missingScripts = [];

    for (const script of requiredScripts) {
      const scriptPath = path.join(this.scriptsPath, script);
      if (!fs.existsSync(scriptPath)) {
        console.error(`❌ 缺少必要的脚本文件: ${scriptPath}`);
        missingScripts.push(script);
      } else {
        console.log(`✅ 验证脚本文件存在: ${script}`);
      }
    }

    if (missingScripts.length > 0) {
      console.error(`⚠️ 缺少 ${missingScripts.length} 个脚本文件:`, missingScripts);
      console.error('⚠️ MCP服务器将启动，但相关功能可能不可用');
      // 不要抛出错误，让服务器继续启动
    }
  }

  /**
   * 执行Outlook VBS脚本
   */
  async executeOutlookScript(scriptName, params = {}) {
    return new Promise((resolve, reject) => {
      const scriptPath = path.join(this.scriptsPath, `${scriptName}.vbs`);
      let command = `cscript //NoLogo "${scriptPath}"`;

      // 添加参数
      for (const [key, value] of Object.entries(params)) {
        if (value !== undefined && value !== null && value !== '') {
          // 处理特殊字符 - 更全面的转义
          let escapedValue = value.toString()
            .replace(/\\/g, '\\\\')  // 转义反斜杠
            .replace(/"/g, '\\"')    // 转义双引号
            .replace(/\n/g, '\\n')   // 转义换行符
            .replace(/\r/g, '\\r')   // 转义回车符
            .replace(/\t/g, '\\t');  // 转义制表符

          command += ` /${key}:"${escapedValue}"`;
        }
      }

      console.log('🚀 执行Outlook脚本命令:', command);

      exec(command, (error, stdout, stderr) => {
        console.log('📋 脚本输出:', stdout);
        if (stderr) console.log('📋 脚本错误输出:', stderr);

        // 检查执行错误
        if (error && !stdout.includes('SUCCESS:')) {
          return reject(new Error(`脚本执行失败: ${error.message}`));
        }

        // 检查脚本错误
        if (stdout.includes('ERROR:')) {
          const errorMessage = stdout.substring(stdout.indexOf('ERROR:') + 6).trim();
          return reject(new Error(`脚本错误: ${errorMessage}`));
        }

        // 处理成功输出
        if (stdout.includes('SUCCESS:')) {
          try {
            const jsonStr = stdout.substring(stdout.indexOf('SUCCESS:') + 8).trim();
            const result = JSON.parse(jsonStr);
            return resolve(result);
          } catch (parseError) {
            return reject(new Error(`解析脚本输出失败: ${parseError.message}`));
          }
        }

        // 如果没有明确的成功标识，但也没有错误，则认为成功
        resolve({ success: true, message: stdout.trim() });
      });
    });
  }

  /**
   * 转换事件参数格式
   */
  convertEventParams(eventDetails) {
    const params = {};

    // 基本参数
    if (eventDetails.subject) params.subject = eventDetails.subject;
    if (eventDetails.body) params.body = eventDetails.body;
    if (eventDetails.location) params.location = eventDetails.location;
    if (eventDetails.calendar) params.calendar = eventDetails.calendar;

    // 处理开始时间 - 使用本地时间
    if (eventDetails.start) {
      const startDate = new Date(eventDetails.start);
      // 使用本地时间进行格式化
      const year = startDate.getFullYear();
      const month = String(startDate.getMonth() + 1).padStart(2, '0');
      const day = String(startDate.getDate()).padStart(2, '0');
      const hours = String(startDate.getHours()).padStart(2, '0');
      const minutes = String(startDate.getMinutes()).padStart(2, '0');
      const seconds = String(startDate.getSeconds()).padStart(2, '0');

      params.startDate = `${year}-${month}-${day}`;
      params.startTime = `${hours}:${minutes}:${seconds}`;
    }

    // 处理结束时间 - 使用本地时间
    if (eventDetails.end) {
      const endDate = new Date(eventDetails.end);
      // 使用本地时间进行格式化
      const year = endDate.getFullYear();
      const month = String(endDate.getMonth() + 1).padStart(2, '0');
      const day = String(endDate.getDate()).padStart(2, '0');
      const hours = String(endDate.getHours()).padStart(2, '0');
      const minutes = String(endDate.getMinutes()).padStart(2, '0');
      const seconds = String(endDate.getSeconds()).padStart(2, '0');

      params.endDate = `${year}-${month}-${day}`;
      params.endTime = `${hours}:${minutes}:${seconds}`;
    }

    // 其他参数
    if (eventDetails.isAllDay !== undefined) params.isAllDay = eventDetails.isAllDay.toString();
    if (eventDetails.reminder !== undefined) params.reminder = eventDetails.reminder.toString();
    if (eventDetails.attendees) params.attendees = Array.isArray(eventDetails.attendees) ? eventDetails.attendees.join(';') : eventDetails.attendees;
    if (eventDetails.isMeeting !== undefined) params.isMeeting = eventDetails.isMeeting.toString();

    return params;
  }

  /**
   * 设置工具处理器
   */
  setupToolHandlers() {
    // 列出工具
    this.server.setRequestHandler(ListToolsRequestSchema, async () => {
      return {
        tools: [
          {
            name: 'create_event',
            description: '创建新的日历事件或会议',
            inputSchema: {
              type: 'object',
              properties: {
                subject: { type: 'string', description: '事件标题' },
                start: { type: 'string', description: '开始时间 (ISO格式)' },
                end: { type: 'string', description: '结束时间 (ISO格式)' },
                body: { type: 'string', description: '事件内容' },
                location: { type: 'string', description: '地点' },
                calendar: { type: 'string', description: '日历名称' },
                isAllDay: { type: 'boolean', description: '是否全天事件' },
                reminder: { type: 'number', description: '提醒时间（分钟）' },
                attendees: { type: 'array', description: '参会者邮箱列表' },
                isMeeting: { type: 'boolean', description: '是否为会议' }
              },
              required: ['subject', 'start']
            }
          },
          {
            name: 'list_events',
            description: '列出指定日期范围内的日历事件',
            inputSchema: {
              type: 'object',
              properties: {
                startDate: { type: 'string', description: '开始日期 (YYYY-MM-DD)' },
                endDate: { type: 'string', description: '结束日期 (YYYY-MM-DD)' },
                calendar: { type: 'string', description: '日历名称' }
              }
            }
          },
          {
            name: 'get_calendars',
            description: '获取可用的日历列表',
            inputSchema: {
              type: 'object',
              properties: {}
            }
          }
        ]
      };
    });

    // 调用工具
    this.server.setRequestHandler(CallToolRequestSchema, async (request) => {
      const { name, arguments: args } = request.params;

      try {
        switch (name) {
          case 'create_event':
            const eventParams = this.convertEventParams(args);
            console.log('📅 创建日历事件，转换后参数:', eventParams);
            const result = await this.executeOutlookScript('createEvent', eventParams);
            console.log('✅ 日历事件创建成功:', result);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify({ success: true, eventId: result.eventId || 'unknown', ...result })
                }
              ]
            };

          case 'list_events':
            const listResult = await this.executeOutlookScript('listEvents', args);
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(listResult)
                }
              ]
            };

          case 'get_calendars':
            const calendarsResult = await this.executeOutlookScript('getCalendars');
            return {
              content: [
                {
                  type: 'text',
                  text: JSON.stringify(calendarsResult)
                }
              ]
            };

          default:
            throw new McpError(ErrorCode.MethodNotFound, `Unknown tool: ${name}`);
        }
      } catch (error) {
        console.error(`❌ 工具调用失败: ${name}`, error);
        return {
          content: [
            {
              type: 'text',
              text: JSON.stringify({ success: false, error: error.message })
            }
          ],
          isError: true
        };
      }
    });
  }

  /**
   * 启动服务器
   */
  async start() {
    // 确保已初始化
    if (!this.initialized) {
      await this.initialize();
    }

    const transport = new StdioServerTransport();
    await this.server.connect(transport);
    console.log('Local Outlook Calendar MCP server running on stdio');
  }
}

// 启动服务器
if (require.main === module) {
  const server = new LocalOutlookCalendarServer();
  server.start().catch(console.error);
}

module.exports = LocalOutlookCalendarServer;

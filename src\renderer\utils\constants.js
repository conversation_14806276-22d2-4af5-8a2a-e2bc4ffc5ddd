// 应用常量定义

// 角色状态
export const CHARACTER_STATES = {
  IDLE: 'idle',
  SPEAKING: 'speaking', 
  LISTENING: 'listening',
  THINKING: 'thinking'
}

// 菜单项
export const MENU_ITEMS = {
  CONFIG: 'config',
  HELP: 'help', 
  ABOUT: 'about',
  EXIT: 'exit'
}

// 存储键名
export const STORAGE_KEYS = {
  IS_LOGGED_IN: 'userAuthenticatedStatus',
  USER_CREDENTIALS: 'userAuthCredentials',
  APP_SETTINGS: 'appSettings',
  LAST_CLOSED_AT: 'lastClosedAt'
}

// 默认设置
export const DEFAULT_SETTINGS = {
  autoStart: true,
  showOnStartup: false,
  voiceLanguage: 'zh-CN',
  voiceSensitivity: 5,
  themeColor: '#667eea',
  opacity: 0.9,
  selectedCharacter: 'nezha'
}

// 支持的语言
export const SUPPORTED_LANGUAGES = [
  { code: 'zh-CN', name: '中文（简体）' },
  { code: 'zh-TW', name: '中文（繁体）' },
  { code: 'en-US', name: '英语' },
  { code: 'ja-JP', name: '日语' }
]

// 主题颜色
export const THEME_COLORS = [
  '#667eea', '#764ba2', '#f093fb', '#f5576c',
  '#4facfe', '#00f2fe', '#43e97b', '#38f9d7'
]

// 快捷短语
export const QUICK_PHRASES = [
  '你好',
  '谢谢', 
  '帮助',
  '设置',
  '退出'
] 
import axios from 'axios'
import { MCP_TOOLS, MCP_SYSTEM_PROMPT, MCP_TOOL_MAPPING } from './mcpToolDefinitions.js'
import { searchKnowledge, initKnowledgeDatabase, askKnowledge } from './knowledge/knowledgeClient.js'
import { getUserIdentifier, getSessionIdentifier } from './userIdentity.js'
import { getChatConfig, getChatSettings } from './config/modelConfig.js'
import { performWebSearch, formatSearchReferences } from './webSearchAPI.js'
import {
  analyzeToolIntent,
  buildOptimizedRequest,
  hasValidToolCalls,
  toolCallStats,
  MCP_OPTIMIZATION_CONFIG,
  generateErrorMessage,
  checkAmbiguousSearchRequest
} from './mcpOptimization.js'
import modelManager from './modelManager.js'

/**
 * 获取城市对应的正确行政区划参数
 * @param {string} city - 城市名称
 * @returns {string} - 对应的adm参数值
 */
function getCorrectAdmForCity(city) {
  // 直辖市：adm参数应该是城市本身
  const municipalities = ['北京', '上海', '天津', '重庆']
  
  // 港澳台：adm参数应该是地区本身
  const specialRegions = ['香港', '澳门', '台北', '高雄', '台中', '台南']
  
  // 常见省份和对应城市的映射
  const provinceCityMap = {
    '广州': '广东', '深圳': '广东', '佛山': '广东', '东莞': '广东', '中山': '广东', '珠海': '广东',
    '杭州': '浙江', '宁波': '浙江', '温州': '浙江', '嘉兴': '浙江', '绍兴': '浙江',
    '南京': '江苏', '苏州': '江苏', '无锡': '江苏', '常州': '江苏', '徐州': '江苏', '南通': '江苏',
    '武汉': '湖北', '宜昌': '湖北', '襄阳': '湖北',
    '长沙': '湖南', '株洲': '湖南', '湘潭': '湖南',
    '成都': '四川', '绵阳': '四川', '德阳': '四川',
    '西安': '陕西', '宝鸡': '陕西', '咸阳': '陕西',
    '济南': '山东', '青岛': '山东', '烟台': '山东', '潍坊': '山东',
    '郑州': '河南', '洛阳': '河南', '开封': '河南',
    '石家庄': '河北', '唐山': '河北', '秦皇岛': '河北',
    '太原': '山西', '大同': '山西',
    '沈阳': '辽宁', '大连': '辽宁', '鞍山': '辽宁',
    '长春': '吉林', '吉林': '吉林',
    '哈尔滨': '黑龙江', '大庆': '黑龙江',
    '合肥': '安徽', '芜湖': '安徽',
    '福州': '福建', '厦门': '福建', '泉州': '福建',
    '南昌': '江西', '赣州': '江西',
    '昆明': '云南', '大理': '云南',
    '贵阳': '贵州',
    '兰州': '甘肃',
    '西宁': '青海',
    '银川': '宁夏',
    '乌鲁木齐': '新疆',
    '拉萨': '西藏',
    '海口': '海南', '三亚': '海南',
    '南宁': '广西', '桂林': '广西',
    '呼和浩特': '内蒙古', '包头': '内蒙古'
  }
  
  // 检查是否为直辖市
  if (municipalities.includes(city)) {
    return city
  }
  
  // 检查是否为港澳台地区
  if (specialRegions.includes(city)) {
    if (city === '香港') return '香港'
    if (city === '澳门') return '澳门'
    if (['台北', '高雄', '台中', '台南'].includes(city)) return '台湾'
  }
  
  // 检查是否在省份城市映射中
  if (provinceCityMap[city]) {
    return provinceCityMap[city]
  }
  
  // 默认返回城市名称（适用于不在映射中的情况）
  console.warn(`⚠️ 未找到城市 ${city} 的省份映射，使用城市名称作为adm参数`)
  return city
}

// 获取用户token
function getUserToken() {
  try {
    return localStorage.getItem('userAuthToken') || ''
  } catch (error) {
    console.error('获取用户token失败:', error)
    return ''
  }
}

// 获取配置并创建 axios 实例
function createApiClient() {
  const config = getChatConfig()
  const modelConfig = modelManager.getModelConfig()
  
  // 所有模型都使用同一个地址和用户token认证
  const headers = {
    'Content-Type': 'application/json'
  }
  
  // 获取用户token进行认证
  const userToken = getUserToken()
  if (userToken) {
    headers['Authorization'] = `Bearer ${userToken}`
  }
  
  return axios.create({
    baseURL: modelConfig.baseURL,
    headers: headers,
    timeout: config.timeout
  })
}

/**
 * 解析可能包含嵌套JSON的content
 * @param {string} content - 原始content内容
 * @returns {string} 解析后的content
 */
function parseNestedContent(content) {
  try {
    // 如果content不是字符串，直接返回
    if (typeof content !== 'string') {
      return content || ''
    }

    // 尝试解析JSON
    const parsed = JSON.parse(content)

    // 如果解析成功且包含content字段，则返回内层的content
    if (parsed && typeof parsed === 'object' && parsed.content) {
      console.log('🔄 MCP检测到嵌套JSON格式，提取内层content:', parsed.content)
      return parsed.content
    }

    // 如果不包含content字段但解析成功，返回原内容
    return content
  } catch (error) {
    // 如果不是JSON格式，直接返回原内容
    return content
  }
}

/**
 * 智能检测用户意图并建议工具调用
 * @param {string} userMessage - 用户消息内容
 * @returns {Object} 意图分析结果
 */
function analyzeUserIntent(userMessage) {
  const message = userMessage.toLowerCase()

  // 数字员工产品检测 - 优先级最高
  const digitalEmployeeProducts = ['晓主任', '晓文宣', '晓招聘', '晓策通', '数字员工市场', '晓律师', '晓客服', '晓助理', '晓秘书']
  const hasDigitalEmployeeProduct = digitalEmployeeProducts.some(product => message.includes(product.toLowerCase()))

  // 网络操作意图（包含数字员工产品）
  const webKeywords = ['网站', '打开', '访问', '浏览', '链接', 'http', 'www']
  const hasWebIntent = hasDigitalEmployeeProduct || webKeywords.some(keyword => message.includes(keyword))

  // 文件操作意图（排除数字员工产品）
  const fileKeywords = ['文件', '文档', '搜索', '查找', '找', '看', '读取', '内容']
  const hasFileIntent = !hasDigitalEmployeeProduct && fileKeywords.some(keyword => message.includes(keyword))

  // 搜索意图
  const searchKeywords = ['搜索', '查询', '找', '百度', '搜一下', '查一下']
  const hasSearchIntent = searchKeywords.some(keyword => message.includes(keyword))

  // 机票查询意图
  const flightKeywords = ['机票', '航班', '飞机', '出差', '旅行', '订票']
  const hasFlightIntent = flightKeywords.some(keyword => message.includes(keyword))

  // 天气查询意图
  const weatherKeywords = ['天气', '气温', '温度', '下雨', '晴天', '预报']
  const hasWeatherIntent = weatherKeywords.some(keyword => message.includes(keyword))

  // 邮件操作意图
  const emailKeywords = ['邮件', '发邮件', '写邮件', '查邮件', '收件']
  const hasEmailIntent = emailKeywords.some(keyword => message.includes(keyword))

  return {
    hasFileIntent,
    hasWebIntent,
    hasSearchIntent,
    hasFlightIntent,
    hasWeatherIntent,
    hasEmailIntent,
    originalMessage: userMessage,
    suggestedTools: getSuggestedTools({
      hasFileIntent,
      hasWebIntent,
      hasSearchIntent,
      hasFlightIntent,
      hasWeatherIntent,
      hasEmailIntent
    })
  }
}

/**
 * 根据意图分析结果建议工具
 * @param {Object} intents - 意图分析结果
 * @returns {Array} 建议的工具列表
 */
function getSuggestedTools(intents) {
  const suggestions = []

  if (intents.hasFileIntent) {
    suggestions.push('search_files', 'open_file', 'read_file')
  }
  if (intents.hasWebIntent) {
    suggestions.push('browser_navigate')
  }
  if (intents.hasSearchIntent && !intents.hasFlightIntent && !intents.hasWeatherIntent) {
    suggestions.push('baidu_search')
  }
  if (intents.hasFlightIntent) {
    suggestions.push('query_flights')
  }
  if (intents.hasWeatherIntent) {
    suggestions.push('get_weather_forecast')
  }
  if (intents.hasEmailIntent) {
    suggestions.push('send_email', 'list_email')
  }

  return [...new Set(suggestions)] // 去重
}

// 全局变量存储当前正在进行的请求
let currentAbortController = null

/**
 * 取消当前正在进行的AI请求
 */
export function cancelCurrentAIRequest() {
  if (currentAbortController) {
    console.log('🛑 取消当前AI请求')
    currentAbortController.abort()
    currentAbortController = null
    return true
  }
  return false
}

/**
 * 发送支持MCP工具的聊天请求
 * @param {Array} messages - 消息历史数组
 * @param {Object} options - 可选参数
 * @returns {Promise} API 响应
 */
export async function sendMCPChatRequest(messages, options = {}) {
  // 🔧 将变量提升到函数顶部，避免 catch 块中未定义的错误
  let knowledgeSources = []
  let webSearchSources = []
  
  try {
    // 🛑 如果已有正在进行的请求，取消它
    if (currentAbortController) {
      console.log('🛑 取消前一个请求')
      currentAbortController.abort()
    }
    
    // 创建新的 AbortController
    currentAbortController = new AbortController()
    const signal = currentAbortController.signal
    
    // 确保第一条消息是系统消息
    let enhancedMessages = [...messages]
    if (enhancedMessages.length === 0 || enhancedMessages[0].role !== 'system') {
      enhancedMessages.unshift({
        role: 'system',
        content: MCP_SYSTEM_PROMPT
      })
    }

    // 🎯 使用优化的意图分析
    const lastUserMessage = messages
      .slice()
      .reverse()
      .find(msg => msg.role === 'user')

    let intentAnalysis = null
    let ambiguousSearchCheck = null

    if (lastUserMessage) {
      intentAnalysis = analyzeToolIntent(lastUserMessage.content)
      ambiguousSearchCheck = checkAmbiguousSearchRequest(lastUserMessage.content, enhancedMessages)

      console.log('🎯 优化的用户意图分析:', intentAnalysis)
      console.log('🔍 模糊搜索检测:', ambiguousSearchCheck)

      // 记录请求统计
      toolCallStats.recordRequest(intentAnalysis.hasToolIntent)



      // 如果是模糊搜索请求，直接返回澄清提示
      if (ambiguousSearchCheck.isAmbiguous) {
        console.log('🚨 检测到模糊搜索请求，返回澄清提示')
        return {
          success: true,
          message: ambiguousSearchCheck.suggestedClarification,
          type: 'clarification_needed',
          toolsUsed: [],
          isNewSession: false,
          needsClarification: true
        }
      }
    }

    // 🎯 如果检测到明确的工具调用意图，在系统消息中强化提示
    if (intentAnalysis && intentAnalysis.detectedTools.length > 0) {
      const toolHints = intentAnalysis.detectedTools.join(', ')
      enhancedMessages[0].content += `\n\n🎯 当前用户请求分析：检测到可能需要使用工具：${toolHints}。请优先考虑调用相应的工具函数来完成用户请求。置信度：${JSON.stringify(intentAnalysis.confidence)}`
    }

    // 🔍 增强搜索：知识库 + 联网搜索
    const enhanceOptions = {
      enableWebSearch: options.enableWebSearch || false,
      enableKnowledge: options.enableKnowledge !== false,
      knowledgeLimit: options.knowledgeLimit || 3,
      enableDeepSearch: options.enableDeepSearch || false,
      searchMode: options.searchMode || "auto"
    }
    
    const knowledgeEnhanced = await enhanceWithKnowledge(enhancedMessages, enhanceOptions)
    
    // 🎯 【优化】如果有增强数据，清空对话上下文，只保留当前用户消息
    if (knowledgeEnhanced.success && (knowledgeEnhanced.hasKnowledge || knowledgeEnhanced.hasWeb)) {
      console.log('🎯 检测到增强数据，清空对话上下文以优化提示词')
      const currentUserMessage = enhancedMessages.find(msg => msg.role === 'user')
      if (currentUserMessage) {
        enhancedMessages = [currentUserMessage] // 只保留当前用户消息
      }
    }
    
    if (knowledgeEnhanced.success) {
      console.log('🎯 增强搜索成功:', {
        hasKnowledge: knowledgeEnhanced.hasKnowledge,
        hasWeb: knowledgeEnhanced.hasWeb,
        knowledgeCount: knowledgeEnhanced.knowledgeCount,
        webReferencesCount: knowledgeEnhanced.webReferencesCount
      })
      
      // 🎯 【优化】更新系统提示，包含增强内容
      if (enhancedMessages.length > 0 && enhancedMessages[0].role === 'system') {
        enhancedMessages[0].content = knowledgeEnhanced.enhancedSystemPrompt
      } else {
        // 如果没有系统消息，在开头添加
        enhancedMessages.unshift({
          role: 'system',
          content: knowledgeEnhanced.enhancedSystemPrompt
        })
      }
      
      // 保存来源信息，用于后续添加到响应中
      knowledgeSources = knowledgeEnhanced.knowledgeResults || []
      webSearchSources = knowledgeEnhanced.webSearchResult?.references || []
    }

    // 🆔 获取用户标识符（添加错误保护）
    let userId
    try {
      userId = getUserIdentifier()
    } catch (userIdError) {
      console.warn('🔧 获取用户标识符失败，使用默认值:', userIdError.message)
      userId = 'default-user'
    }

    // 获取配置（添加错误保护）
    let config
    try {
      config = getChatConfig()
    } catch (configError) {
      console.error('🔧 获取聊天配置失败:', configError.message)
      throw new Error('聊天配置不可用')
    }

    // 创建API客户端（添加错误保护）
    let apiClient
    try {
      apiClient = createApiClient()
    } catch (createError) {
      console.error('🔧 创建API客户端失败:', createError.message)
      
      // 手动创建客户端作为后备方案
      const modelConfig = modelManager.getModelConfig()
      if (config && modelConfig.baseURL) {
        const headers = {
          'Content-Type': 'application/json'
        }
        
        // 所有模型都使用用户token认证
        const userToken = getUserToken()
        if (userToken) {
          headers['Authorization'] = `Bearer ${userToken}`
        }
        
        apiClient = axios.create({
          baseURL: modelConfig.baseURL,
          headers: headers,
          timeout: config.timeout || 30000
        })
      } else {
        throw new Error('API客户端创建失败且配置不完整')
      }
    }
    
    // 🎯 使用优化配置构建请求参数
    const modelConfig = modelManager.getModelConfig()
    
    // 🎯 【优化】根据是否有增强数据动态调整工具列表和模型选择
    let toolsToSend = MCP_TOOLS
    const hasEnhancedData = knowledgeEnhanced.success && (knowledgeEnhanced.hasKnowledge || knowledgeEnhanced.hasWeb)
    
    if (hasEnhancedData) {
      // 有知识库或联网搜索数据时，只发送基础工具，移除复杂的MCP工具
      toolsToSend = MCP_TOOLS.filter(tool => {
        const toolName = tool.function?.name
        // 保留基础工具，移除复杂的MCP工具
        const basicTools = [
          'search_files',
          'open_file', 
          'read_file',
          'list_directory',
          'directory_tree'
        ]
        return basicTools.includes(toolName)
      })
      console.log('🎯 增强模式：使用简化工具列表，工具数量:', toolsToSend.length)
    } else {
      console.log('🎯 标准模式：使用完整工具列表，工具数量:', MCP_TOOLS.length)
    }
    
    const baseRequestData = {
      messages: enhancedMessages,
      tools: toolsToSend,
      tool_choice: "auto",
      max_tokens: options.maxTokens || 2000,
      temperature: options.temperature || config.temperature,
      stream: false,
      user: userId
    }
    
    // 🎯 【新增】根据是否有知识库或联网搜索数据选择模型
    if (hasEnhancedData) {
      // 有知识库或联网搜索数据时，固定使用 DeepSeek V3 Pro 模型
      console.log('🎯 检测到知识库或联网搜索数据，使用 DeepSeek V3 Pro 模型')
      baseRequestData.model = 'Pro/deepseek-ai/DeepSeek-V3'
      baseRequestData.reasoning = false
      baseRequestData.thinking_mode = "off"
    } else {
      // 没有增强数据时，使用原有的模型选择逻辑
      if (modelConfig.isSystemDefault) {
        // 系统默认模型：model传空字符串
        baseRequestData.model = ''
      } else {
        // 其他模型：传具体的model名称
        baseRequestData.model = config.model
        // 添加其他模型特有的参数
        baseRequestData.reasoning = false
        baseRequestData.thinking_mode = "off"
      }
    }

    // 应用优化配置
    const requestData = buildOptimizedRequest(baseRequestData, intentAnalysis)
    console.log('🎯 优化后的请求参数:', {
      temperature: requestData.temperature,
      top_p: requestData.top_p,
      hasToolIntent: intentAnalysis?.hasToolIntent,
      detectedTools: intentAnalysis?.detectedTools
    })

    console.log('发送MCP聊天请求:', requestData)

    const response = await apiClient.post('/chat/completions', requestData, {
      signal: signal
    })
    
    // 处理嵌套的响应结构：response.data.data
    const apiResponseData = response.data.data || response.data
    const message = apiResponseData.choices[0].message

    console.log('🔍 ====== AI响应详细检查 ======')
    console.log('  - message.tool_calls存在:', !!message.tool_calls)
    console.log('  - tool_calls长度:', message.tool_calls?.length || 0)
    console.log('  - message.content存在:', !!message.content)
    console.log('  - 用户意图分析:', intentAnalysis?.suggestedTools || '无')

    // 检查标准的tool_calls格式
    if (hasValidToolCalls(message)) {
      console.log('✅ 检测到标准tool_calls格式，数量:', message.tool_calls.length)
      console.log('✅ 工具调用详情:', JSON.stringify(message.tool_calls, null, 2))

      // 记录成功的工具调用
      toolCallStats.recordToolCallSuccess()

      return await handleMCPToolCalls(message, enhancedMessages, knowledgeSources)
    }

    // 🚨 如果用户意图明确需要工具调用但AI没有调用工具，尝试重新引导
    if (intentAnalysis && intentAnalysis.hasToolIntent && MCP_OPTIMIZATION_CONFIG.detection.enableRetryOnMiss) {
      console.log('🚨 检测到工具调用意图但AI未调用工具，尝试重新引导')

      // 记录工具调用失败
      toolCallStats.recordToolCallFailure()

      // 构建更强的引导提示
      const toolGuidance = `用户请求需要调用工具函数。根据分析，建议使用：${intentAnalysis.detectedTools.join(', ')}。请重新分析用户需求并调用相应的工具函数。`

      // 添加引导消息并重试
      const guidedMessages = [
        ...enhancedMessages,
        {
          role: 'assistant',
          content: '我需要调用相应的工具来帮助您完成这个请求。'
        },
        {
          role: 'user',
          content: toolGuidance
        }
      ]

      try {
        console.log('🔄 使用工具引导重试请求...')

        // 记录重试尝试
        toolCallStats.recordRetryAttempt()

        const retryRequestData = {
          ...requestData,
          messages: guidedMessages,
          temperature: MCP_OPTIMIZATION_CONFIG.retry.retryTemperature,
          max_tokens: MCP_OPTIMIZATION_CONFIG.retry.retryMaxTokens
        }

        const retryResponse = await apiClient.post('/chat/completions', retryRequestData)
        
        // 处理嵌套的响应结构：response.data.data
        const retryApiResponseData = retryResponse.data.data || retryResponse.data
        const retryMessage = retryApiResponseData.choices[0].message

        console.log('🔄 重试响应检查:', {
          hasToolCalls: hasValidToolCalls(retryMessage),
          toolCallsCount: retryMessage.tool_calls?.length || 0
        })

        if (hasValidToolCalls(retryMessage)) {
          console.log('✅ 重试成功，检测到工具调用')
          toolCallStats.recordRetrySuccess()
          return await handleMCPToolCalls(retryMessage, guidedMessages, knowledgeSources)
        }
      } catch (retryError) {
        console.warn('🔄 工具引导重试失败:', retryError.message)
      }
    }

    console.log('📄 没有工具调用，检查是否为函数调用格式的文本响应')
    
    // 检查是否是函数调用格式的文本（修复大模型返回错误格式的问题）
    let parsedContent = parseNestedContent(message.content)
    
    // 检测函数调用格式的文本模式（如：open_file("path")）
    const functionCallPattern = /^([a-zA-Z_]\w*)\s*\(\s*['"](.*?)['"]\s*\)$/
    const match = parsedContent.match(functionCallPattern)
    
    if (match) {
      const [, functionName, filePath] = match
      console.log('🔧 检测到函数调用格式的文本响应，尝试修复:', {
        functionName,
        filePath,
        originalContent: parsedContent
      })
      
      // 如果是open_file函数调用，直接执行
      if (functionName === 'open_file' && filePath) {
        try {
          console.log('🔧 自动执行文件打开操作:', filePath)
          const openResult = await window.electronAPI.executeMCPTool('open_file', { path: filePath })
          
          if (openResult.success) {
            parsedContent = `已为您打开文件`
          } else {
            parsedContent = `打开文件失败：${openResult.error || '未知错误'}`
          }
          
          console.log('🔧 函数调用格式文本修复结果:', parsedContent)
        } catch (error) {
          console.error('🔧 执行函数调用格式文本时出错:', error)
          parsedContent = `执行操作时出错：${error.message}`
        }
      } else {
        // 其他函数调用格式，转为自然语言
        parsedContent = `我理解您的需求，但请使用自然语言重新描述。`
      }
    }

    const responseData = {
      success: true,
      message: parsedContent,
      usage: response.data.usage,
      type: 'text_response',
      toolsUsed: [], // 🔧 确保始终有toolsUsed字段，即使为空数组
      isNewSession: false, // 🔄 纯文本响应默认不新开会话
      sessionInfo: null
    }

    // 🔍 添加搜索结果引用信息
    if (knowledgeSources.length > 0 || webSearchSources.length > 0) {
      responseData.hasSearchResults = true
      responseData.hasKnowledgeBase = knowledgeSources.length > 0
      responseData.hasWebSearch = webSearchSources.length > 0
      
      // 知识库引用
      if (knowledgeSources.length > 0) {
        responseData.knowledgeSources = knowledgeSources
        responseData.fileReferences = knowledgeSources.map(source => ({
          fileName: source.file_name,
          filePath: source.filePath || source.source_file_path,
          similarity: source.similarity,
          type: 'knowledge'
        })).filter(ref => ref.filePath) // 只包含有文件路径的引用
      }
      
      // 联网搜索引用
      if (webSearchSources.length > 0) {
        responseData.webSearchSources = webSearchSources
        responseData.webReferences = webSearchSources.map(source => ({
          title: source.title,
          url: source.url,
          content: source.content,
          type: 'web'
        }))
      }

      console.log('🔍 添加搜索引用信息:', {
        fileReferences: responseData.fileReferences?.length || 0,
        webReferences: responseData.webReferences?.length || 0
      })
    }

    return responseData
  } catch (error) {
    console.error('MCP聊天请求失败:', error)
    
    // 🛑 检查是否是请求取消错误
    if (error.name === 'AbortError' || error.code === 'ECONNABORTED') {
      console.log('🛑 AI请求已被取消')
      return {
        success: false,
        message: '请求已取消',
        error: 'Request cancelled',
        type: 'cancelled',
        toolsUsed: [],
        isNewSession: false,
        cancelled: true
      }
    }
    
    const errorResponseData = {
      success: false,
      message: '抱歉，处理您的请求时出现错误。请稍后再试。',
      error: error.message,
      type: 'error',
      toolsUsed: [], // 🔧 确保始终有toolsUsed字段，即使错误时为空数组
      isNewSession: false, // 🔄 错误情况下默认不新开会话
      

    }

    // 🔍 即使在捕获错误时也添加搜索引用信息
    if ((knowledgeSources && knowledgeSources.length > 0) || (webSearchSources && webSearchSources.length > 0)) {
      errorResponseData.hasSearchResults = true
      errorResponseData.hasKnowledgeBase = knowledgeSources && knowledgeSources.length > 0
      errorResponseData.hasWebSearch = webSearchSources && webSearchSources.length > 0
      
      // 知识库引用
      if (knowledgeSources && knowledgeSources.length > 0) {
        errorResponseData.knowledgeSources = knowledgeSources
        errorResponseData.fileReferences = knowledgeSources.map(source => ({
          fileName: source.file_name,
          filePath: source.filePath || source.source_file_path,
          similarity: source.similarity,
          type: 'knowledge'
        })).filter(ref => ref.filePath) // 只包含有文件路径的引用
      }
      
      // 联网搜索引用
      if (webSearchSources && webSearchSources.length > 0) {
        errorResponseData.webSearchSources = webSearchSources
        errorResponseData.webReferences = webSearchSources.map(source => ({
          title: source.title,
          url: source.url,
          content: source.content,
          type: 'web'
        }))
      }

      console.log('🔍 catch块错误响应中添加搜索引用信息:', {
        fileReferences: errorResponseData.fileReferences?.length || 0,
        webReferences: errorResponseData.webReferences?.length || 0
      })
    }

    return errorResponseData
  } finally {
    // 清理 AbortController
    if (currentAbortController) {
      currentAbortController = null
    }
  }
}

/**
 * 使用知识库和联网搜索增强对话
 * @param {Array} messages - 消息历史数组
 * @param {Object} options - 可选参数
 * @returns {Promise<Object>} 增强结果
 */
async function enhanceWithKnowledge(messages, options = {}) {
  try {
    // 获取最后一条用户消息
    const lastUserMessage = messages
      .slice()
      .reverse()
      .find(msg => msg.role === 'user')

    if (!lastUserMessage || !lastUserMessage.content) {
      console.log('🧠 没有找到用户消息，跳过增强搜索')
      return { success: false }
    }

    const userQuery = lastUserMessage.content
    console.log(`🔍 开始增强搜索，用户问题: "${userQuery}"`)
    console.log(`🔍 搜索选项:`, { 
      enableWebSearch: options.enableWebSearch, 
      enableKnowledge: options.enableKnowledge !== false 
    })

    // 并行执行知识库搜索和联网搜索
    const searchPromises = []
    let knowledgePromise = null
    let webSearchPromise = null

    // 知识库搜索
    if (options.enableKnowledge !== false) {
      knowledgePromise = (async () => {
        try {
          console.log('🧠 开始知识库搜索')
          // 检查是否在Electron环境中
          if (window.electronAPI && window.electronAPI.invoke) {
            await initKnowledgeDatabase()
            const knowledgeResults = await searchKnowledge(userQuery, Math.min(options.knowledgeLimit || 3, 3))
            
            if (knowledgeResults && knowledgeResults.length > 0) {
              // 获取知识库配置中的相似度阈值
              const { getKnowledgeConfig } = await import('./config/modelConfig.js')
              const knowledgeConfig = getKnowledgeConfig()
              const similarityThreshold = knowledgeConfig.search.similarityThreshold || 0.4
              
              // 过滤掉相似度过低的结果
              const highQualityResults = knowledgeResults.filter(result =>
                result.similarity >= similarityThreshold
              )
              
              if (highQualityResults.length > 0) {
                console.log(`🧠 知识库搜索成功，找到 ${highQualityResults.length} 条高质量结果`)
                return {
                  success: true,
                  type: 'knowledge',
                  results: highQualityResults,
                  count: highQualityResults.length
                }
              }
            }
          }
          
          console.log('🧠 知识库搜索无结果')
          return { success: false, type: 'knowledge' }
        } catch (error) {
          console.warn('🧠 知识库搜索失败:', error.message)
          return { success: false, type: 'knowledge', error: error.message }
        }
      })()
      searchPromises.push(knowledgePromise)
    }

    // 联网搜索
    if (options.enableWebSearch === true) {
      webSearchPromise = (async () => {
        try {
          console.log('🌐 开始联网搜索')
          const webSearchResult = await performWebSearch(userQuery, {
            enableDeepSearch: options.enableDeepSearch || false,
            searchMode: options.searchMode || "auto"
          })
          
          if (webSearchResult.success && webSearchResult.content) {
            console.log('🌐 联网搜索成功')
            return {
              success: true,
              type: 'web',
              content: webSearchResult.content,
              references: webSearchResult.references || [],
              usage: webSearchResult.usage
            }
          }
          
          console.log('🌐 联网搜索无结果')
          return { success: false, type: 'web', error: webSearchResult.error }
        } catch (error) {
          console.error('🌐 联网搜索失败:', error.message)
          return { success: false, type: 'web', error: error.message }
        }
      })()
      searchPromises.push(webSearchPromise)
    }

    // 等待所有搜索完成
    const searchResults = await Promise.all(searchPromises)
    
    // 处理搜索结果
    const knowledgeResult = searchResults.find(r => r.type === 'knowledge')
    const webResult = searchResults.find(r => r.type === 'web')
    
    console.log('🔍 搜索结果汇总:', {
      knowledge: knowledgeResult?.success || false,
      web: webResult?.success || false,
      knowledgeCount: knowledgeResult?.count || 0,
      webReferencesCount: webResult?.references?.length || 0
    })

    // 如果都没有结果，返回失败
    if ((!knowledgeResult?.success) && (!webResult?.success)) {
      console.log('🔍 所有搜索都无结果')
      return { success: false }
    }

    // 构建增强的系统提示
    let enhancedContent = ''
    const sources = []
    const hasBothData = knowledgeResult?.success && webResult?.success

    // 当同时有知识库和联网搜索数据时，添加使用指导
    if (hasBothData) {
      enhancedContent += `\n🎯 **数据源使用指导：**
- 知识库数据：包含用户文档、内部资料等特定信息
- 联网搜索结果：包含最新信息、外部资料、实时数据等
- 请平衡使用两种数据源，提供全面的回答
- 对于重复信息，优先使用更详细或更新的版本\n`
    }

    // 添加知识库内容
    if (knowledgeResult?.success && knowledgeResult.results) {
      console.log('🧠 添加知识库内容到提示词')
      const knowledgeContext = knowledgeResult.results
        .map((result, index) => {
          return `【文档${index + 1}】相似度${(result.similarity * 100).toFixed(0)}%${result.file_name ? ` - ${result.file_name}` : ''}\n${result.content}`
        })
        .join('\n\n')
      
      enhancedContent += `\n📚 **知识库数据:**\n${knowledgeContext}\n`
      sources.push(...knowledgeResult.results)
    }

    // 添加联网搜索内容
    if (webResult?.success && webResult.content) {
      console.log('🌐 添加联网搜索内容到提示词')
      enhancedContent += `\n🌐 **联网搜索结果:**\n${webResult.content}\n`
      
      if (webResult.references && webResult.references.length > 0) {
        const referencesText = formatSearchReferences(webResult.references)
        enhancedContent += `\n**搜索引用:**\n${referencesText}\n`
        sources.push(...webResult.references.map(ref => ({
          type: 'web',
          title: ref.title,
          url: ref.url,
          content: ref.content
        })))
      }
    }

    // 生成最终的系统提示
    const originalSystemPrompt = messages[0]?.content || MCP_SYSTEM_PROMPT
    
    // 🎯 【优化】根据是否有增强数据动态调整系统提示词
    let enhancedSystemPrompt
    const hasEnhancedData = (knowledgeResult?.success) || (webResult?.success)
    
    if (hasEnhancedData) {
      // 有知识库或联网搜索数据时，使用简化的系统提示词
      const userId = getUserIdentifier()
      const dataSource = hasBothData ? '知识库和联网搜索' : knowledgeResult?.success ? '知识库数据' : '联网搜索结果'
      const simplifiedSystemPrompt = generateSimplifiedSystemPrompt(userId, dataSource, hasBothData)

      enhancedSystemPrompt = `${simplifiedSystemPrompt}

🚨 **关键指令：增强数据已提供，必须严格遵守以下要求**
${enhancedContent}
⚠️ **强制要求:**
1. 必须基于上述数据回答问题，优先使用最相关的信息
2. 当用户询问的信息在提供的数据中存在时，直接提供具体内容
3. 禁止使用"请问您是想了解..."等模糊回复
4. 必须完整展示相关信息
5. 回答开头必须说明"根据${dataSource}"${hasBothData ? `
6. **平衡使用数据源**：必须同时参考知识库和联网搜索的数据
7. **信息整合**：将两种数据源的信息进行整合，避免只使用单一数据源
8. **明确标注来源**：在回答中适当标注信息来源（知识库/联网搜索）` : ''}`
    } else {
      // 没有增强数据时，使用完整的系统提示词
      enhancedSystemPrompt = `${originalSystemPrompt}

🚨 **关键指令：增强数据已提供，必须严格遵守以下要求**
${enhancedContent}
⚠️ **强制要求:**
1. 必须基于上述数据回答问题，优先使用最相关的信息
2. 当用户询问的信息在提供的数据中存在时，直接提供具体内容
3. 禁止使用"请问您是想了解..."等模糊回复
4. 必须完整展示相关信息
5. 回答开头必须说明"根据${dataSource}"${hasBothData ? `
6. **平衡使用数据源**：必须同时参考知识库和联网搜索的数据
7. **信息整合**：将两种数据源的信息进行整合，避免只使用单一数据源
8. **明确标注来源**：在回答中适当标注信息来源（知识库/联网搜索）` : ''}`
    }

    console.log('🎯 增强完成:', {
      hasKnowledge: !!knowledgeResult?.success,
      hasWeb: !!webResult?.success,
      totalSources: sources.length,
      promptLength: enhancedSystemPrompt.length
    })

    return {
      success: true,
      enhancedSystemPrompt,
      knowledgeResults: knowledgeResult?.results || [],
      webSearchResult: webResult || null,
      sources,
      hasKnowledge: !!knowledgeResult?.success,
      hasWeb: !!webResult?.success,
      knowledgeCount: knowledgeResult?.count || 0,
      webReferencesCount: webResult?.references?.length || 0
    }
  } catch (error) {
    console.error('🔍 增强搜索失败:', error)
    return { success: false, error: error.message }
  }
}





/**
 * 检查是否需要新开会话的工具
 * @param {string} toolName - 工具名称
 * @param {Object} toolResult - 工具执行结果
 * @param {Array} conversationHistory - 对话历史
 * @returns {boolean} 是否需要新开会话
 */
function needsNewSession(toolName, toolResult = null, conversationHistory = []) {
  const alwaysNewSessionTools = [
    // 'search_files',          // 🔧 移除文件搜索，避免多文件选择时上下文被清空
    'word_create',
    'send_email'
  ]

  // 这些工具总是需要新会话
  if (alwaysNewSessionTools.includes(toolName)) {
    return true
  }

  // 🔧 特殊处理文件搜索：只有在单文件自动打开时才需要新会话
  if (toolName === 'search_files') {
    // 如果是单文件自动打开，需要新会话
    if (toolResult && toolResult.result && toolResult.result.singleFileAutoOpened) {
      console.log('🔄 单文件自动打开，创建新会话')
      return true
    }
    // 如果是多文件或无文件，保持当前会话以便用户选择
    console.log('🔄 多文件或无文件情况，保持当前会话')
    return false
  }





  // 特殊处理：百度搜索
  if (toolName === 'baidu_search') {
    return true // 百度搜索后清空上下文
  }

  // 其他浏览器操作默认清空上下文
  const browserTools = ['browser_navigate', 'browser_close', 'browser_take_screenshot']
  if (browserTools.includes(toolName)) {
    return true
  }

  return false
}

/**
 * 创建新会话（保留历史但重置当前对话上下文）
 * @param {Array} conversationHistory - 当前对话历史
 * @param {Array} toolResults - 工具执行结果
 * @returns {Array} 新会话的消息历史
 */
function createNewSessionHistory(conversationHistory, toolResults = []) {
  console.log('🔄 创建新会话，保留历史但重置对话上下文')

  // 默认行为：只保留系统消息，移除之前的对话上下文
  const systemMessage = conversationHistory.find(msg => msg.role === 'system')
  const newHistory = systemMessage ? [systemMessage] : []

  console.log('📋 标准新会话历史:', {
    原始历史长度: conversationHistory.length,
    新历史长度: newHistory.length,
    保留系统消息: !!systemMessage
  })

  return newHistory
}
  
/**
 * 分析用户原始意图是否包含"打开"操作
 * @param {Array} conversationHistory - 对话历史
 * @returns {Object} 用户意图分析结果
 */
function getUserOpenIntent(conversationHistory) {
  // 获取最后一条用户消息
  const lastUserMessage = conversationHistory
    .slice()
    .reverse()
    .find(msg => msg.role === 'user')

  if (!lastUserMessage) {
    return { hasOpenIntent: false, content: '' }
  }

  const content = lastUserMessage.content.toLowerCase()
  const openKeywords = ['打开', '开启', 'open', '启动', '运行']

  // 🔄 【修复打开意图检测】
  // 1. 明确包含打开关键词
  const hasExplicitOpenKeyword = openKeywords.some(keyword => content.includes(keyword))

  // 2. 如果用户只提供了文件名或关键词（没有明确的搜索词），也视为打开意图
  const hasSearchKeywords = content.includes('搜索') || content.includes('查找') || content.includes('找一下') || content.includes('寻找')
  const isFileNameOnly = !hasSearchKeywords && content.length < 20 // 短文本且无搜索关键词

  // 3. 综合判断打开意图
  const hasOpenIntent = hasExplicitOpenKeyword || isFileNameOnly

  console.log('🎯 用户消息分析:', {
    原始消息: lastUserMessage.content,
    包含打开意图: hasOpenIntent,
    明确打开关键词: hasExplicitOpenKeyword,
    仅文件名: isFileNameOnly,
    包含搜索关键词: hasSearchKeywords,
    匹配的关键词: openKeywords.filter(k => content.includes(k))
  })

  return {
    hasOpenIntent,
    content: lastUserMessage.content,
    originalMessage: lastUserMessage
  }
}

/**
 * 自动执行文件打开操作（仅用于单个文件）
 * @param {Array} searchResults - 搜索结果
 * @param {Object} userIntent - 用户意图
 * @returns {Promise} 打开结果
 */
async function autoOpenFile(searchResults, userIntent) {
  console.log('📂 autoOpenFile 被调用，参数:', {
    hasSearchResults: !!searchResults,
    hasFiles: !!searchResults?.files,
    filesLength: searchResults?.files?.length,
    userIntent: userIntent
  })

  if (!searchResults || !searchResults.files || searchResults.files.length === 0) {
    console.log('📂 自动打开: 没有找到文件，跳过自动打开')
    return {
      success: false,
      error: '没有找到文件',
      autoOpened: false
    }
  }

  // 选择要打开的文件（优先选择第一个）
  const fileToOpen = searchResults.files[0]
  console.log('📂 自动打开: 选择文件:', {
    name: fileToOpen.name,
    path: fileToOpen.path,
    size: fileToOpen.size
  })

  try {
    console.log('📂 调用 window.electronAPI.executeMCPTool("open_file")...')
    console.log('📂 传递的参数:', {
      path: fileToOpen.path,  // 🔧 修复后的参数名
      fileToOpenPath: fileToOpen?.path,
      fileToOpenObject: fileToOpen
    })

    const openResult = await window.electronAPI.executeMCPTool('open_file', {
      path: fileToOpen.path  // 🔧 修复：主进程期望 'path' 而不是 'filePath'
    })

    console.log('📂 window.electronAPI.executeMCPTool 返回结果:', openResult)

    const finalResult = {
      ...openResult,
      openedFile: fileToOpen,
      autoOpened: true
    }

    console.log('📂 autoOpenFile 最终返回:', finalResult)
    return finalResult
  } catch (error) {
    console.error('📂 autoOpenFile 异常:', error)
    return {
      success: false,
      error: error.message,
      autoOpened: true,
      exception: error.name
    }
  }
}

  /**
 * 处理MCP工具调用
 * @param {Object} assistantMessage - 包含工具调用的助手消息
 * @param {Array} conversationHistory - 对话历史
 * @param {Array} knowledgeSources - 知识库来源信息（可选）
 * @returns {Promise} 处理结果
 */
async function handleMCPToolCalls(assistantMessage, conversationHistory, knowledgeSources = []) {
  try {
    console.log('🔧 === 开始处理MCP工具调用 ===')
    console.log('工具调用数量:', assistantMessage.tool_calls?.length || 0)

    // 🔧 修复：获取当前的 AbortController 的 signal
    const signal = currentAbortController?.signal

    // 确保tool_calls存在且不为空
    if (!assistantMessage.tool_calls || !Array.isArray(assistantMessage.tool_calls) || assistantMessage.tool_calls.length === 0) {
      console.warn('🔧 工具调用数据无效，返回默认响应')
      return {
        success: false,
        message: '工具调用数据无效',
        type: 'error',
        toolsUsed: [],
        isNewSession: false
      }
    }

    const allResults = []
    let hasAutoOpenedFile = false
    let openFileResult = null

    // 分析用户原始意图
    let userIntent
    try {
      userIntent = getUserOpenIntent(conversationHistory)
    } catch (intentError) {
      console.warn('🔧 获取用户意图失败:', intentError.message)
      userIntent = { hasOpenIntent: false, content: '' }
    }

    for (let i = 0; i < assistantMessage.tool_calls.length; i++) {
      const toolCall = assistantMessage.tool_calls[i]
      
      // 验证工具调用结构
      if (!toolCall || !toolCall.function) {
        console.warn(`🔧 工具调用 ${i} 结构无效，跳过`)
        continue
      }

      const toolName = toolCall.function.name
      let toolArgs
      
      try {
        toolArgs = typeof toolCall.function.arguments === 'string' 
          ? JSON.parse(toolCall.function.arguments)
          : toolCall.function.arguments
      } catch (parseError) {
        console.error(`🔧 解析工具参数失败:`, parseError.message)
        allResults.push({
          success: false,
          error: `工具参数解析失败: ${parseError.message}`,
          toolName: toolName
        })
        continue
      }

      console.log(`🔧 [${i + 1}/${assistantMessage.tool_calls.length}] 执行工具: ${toolName}`)
      console.log(`🔧 工具参数:`, toolArgs)

      let result
      try {
        // 检查electronAPI是否可用
        if (!window.electronAPI || !window.electronAPI.executeMCPTool) {
          throw new Error('Electron API不可用')
        }

        // 根据工具名称执行相应的操作
        switch (toolName) {
          case 'search_files':
            console.log(`🔍 [SEARCH] 搜索参数: query="${toolArgs.query}", directory="${toolArgs.directory || '默认'}"`)
            result = await window.electronAPI.executeMCPTool('search_files', toolArgs)

            // 🔧 修复文件数组路径问题：文件在 result.result.files 而不是 result.files
            const searchFiles = result.result?.files || result.files || []
            console.log(`🔍 [SEARCH] 搜索结果: 成功=${result.success}, 文件数=${searchFiles.length}`)
            console.log(`🔍 [SEARCH] 文件详情:`, searchFiles.map(f => f.name))

            // 根据搜索结果数量决定操作
            if (userIntent.hasOpenIntent && result.success && searchFiles.length === 1 && !hasAutoOpenedFile) {
              console.log('📂 用户意图包含打开操作，只找到一个文件，直接打开')
              console.log('📂 准备自动打开的文件:', searchFiles[0])
              try {
                // 🔧 修复：传递正确的文件数组结构给 autoOpenFile
                const searchResultForOpen = {
                  ...result,
                  files: searchFiles  // 确保 files 在正确的位置
                }
                console.log('📂 调用 autoOpenFile，参数:', {
                  filesCount: searchResultForOpen.files?.length,
                  firstFile: searchResultForOpen.files?.[0]?.name,
                  filePath: searchResultForOpen.files?.[0]?.path
                })

                openFileResult = await autoOpenFile(searchResultForOpen, userIntent)
                console.log('📂 autoOpenFile 返回结果:', openFileResult)

                if (openFileResult && openFileResult.success) {
                  hasAutoOpenedFile = true
                  console.log('📂 自动打开文件成功')
                  // 明确标记为单个文件已自动打开，避免显示文件列表
                  result.singleFileAutoOpened = true
                } else {
                  console.error('📂 自动打开文件失败:', {
                    success: openFileResult?.success,
                    error: openFileResult?.error,
                    shellResult: openFileResult?.shellResult,
                    fullResult: openFileResult
                  })
                }
              } catch (autoOpenError) {
                console.error('📂 自动打开文件异常:', autoOpenError.message)
                console.error('📂 异常详情:', autoOpenError)
              }
            } else if (userIntent.hasOpenIntent && result.success && searchFiles.length > 1) {
              console.log('📂 用户意图包含打开操作，找到多个文件，让用户选择')
              // 标记为多文件情况，让大模型列出文件供用户选择
              result.multipleFilesFound = true
              console.log('📂 多文件列表已准备，等待用户选择:', searchFiles.length)
            } else {
              console.log('📂 搜索完成，无打开意图或无文件')
              // 明确标记为不需要显示文件列表的情况
              result.noFileListNeeded = true
            }
            break

          case 'open_file':
            console.log(`📂 [OPEN_FILE] 开始执行文件打开`)
            console.log(`📂 [OPEN_FILE] 接收参数: ${JSON.stringify(toolArgs)}`)
            console.log(`📂 [OPEN_FILE] 文件路径: "${toolArgs.filePath}"`)

            if (!toolArgs.filePath) {
              console.error(`📂 [OPEN_FILE] 错误: 缺少filePath参数`)
              result = {
                success: false,
                error: '缺少文件路径参数',
                filePath: null
              }
            } else {
              // 🔧 预处理文件路径中的username占位符
              let processedFilePath = toolArgs.filePath
              
              // 获取实际用户名（在渲染进程中，我们无法直接获取os.userInfo，但可以从路径推断）
              if (processedFilePath.includes('/username/') || processedFilePath.includes('\\username\\') || 
                  processedFilePath.includes('C:/Users/<USER>/') || processedFilePath.includes('C:\\Users\\<USER>\\')) {
                console.log(`🔧 [OPEN_FILE] 检测到username占位符，将在主进程中处理`)
                console.log(`🔧 [OPEN_FILE] 原始路径: "${processedFilePath}"`)
              }
              
              result = await window.electronAPI.executeMCPTool('open_file', {
                path: processedFilePath  // 🔧 修复：主进程期望 'path' 而不是 'filePath'
              })
              console.log(`📂 [OPEN_FILE] 结果: 成功=${result.success}`)
            }
            break



          case 'read_file':
            console.log(`📄 [READ_FILE] 开始读取文件: "${toolArgs.filePath}"`)
            
            // 🔧 预处理文件路径中的username占位符
            if (toolArgs.filePath && (toolArgs.filePath.includes('/username/') || toolArgs.filePath.includes('\\username\\') || 
                toolArgs.filePath.includes('C:/Users/<USER>/') || toolArgs.filePath.includes('C:\\Users\\<USER>\\'))) {
              console.log(`🔧 [READ_FILE] 检测到username占位符，将在主进程中处理`)
              console.log(`🔧 [READ_FILE] 原始路径: "${toolArgs.filePath}"`)
            }
            
            result = await window.electronAPI.executeMCPTool('read_file', toolArgs)
            console.log(`📄 [READ_FILE] 结果: 成功=${result.success}, 内容长度=${result.content?.length || 0}`)
            break

          // Word文档相关工具
          case 'word_create':
          case 'word_insert':
          case 'word_read':
          case 'word_open':
          case 'word_edit':
            console.log(`📝 [WORD] 执行Word操作: ${toolName}`)
            
            // 🔧 预处理Word工具中的file_path参数
            if (toolArgs.file_path && (toolArgs.file_path.includes('/username/') || toolArgs.file_path.includes('\\username\\') || 
                toolArgs.file_path.includes('C:/Users/<USER>/') || toolArgs.file_path.includes('C:\\Users\\<USER>\\'))) {
              console.log(`🔧 [${toolName}] 检测到username占位符，将在主进程中处理`)
              console.log(`🔧 [${toolName}] 原始路径: "${toolArgs.file_path}"`)
            }
            
            result = await window.electronAPI.executeMCPTool(toolName, toolArgs)
            console.log(`📝 [WORD] 结果: 成功=${result.success}`)
            break

          // 浏览器相关工具处理
          case 'browser_navigate':
            console.log(`🌐 [BROWSER_NAVIGATE] 开始打开浏览器并访问URL...`)
            if (!toolArgs || !toolArgs.url) {
              throw new Error('URL参数缺失')
            }
            result = await window.electronAPI.executeMCPTool('browser_navigate', toolArgs)
            console.log(`🌐 [BROWSER_NAVIGATE] 结果:`, result)

            // 处理第一次就失败的情况，提供备用打开方案
            if (!result.success) {
              // 即使在重试前也先提供备用链接
              result.fallbackUrl = toolArgs.url
              result.suggestManualOpen = true
              result.originalError = result.error
            }

            // 如果失败并且是由于MCP不可用，尝试重启MCP
            if (!result.success && (result.error?.includes('MCP客户端不可用') || result.error?.includes('not initialized'))) {
              console.log('🔄 MCP客户端不可用，尝试重启浏览器MCP服务...')
              try {
                const restartResult = await window.electronAPI.restartBrowserMCP()
                console.log('🔄 浏览器MCP重启结果:', restartResult)

                if (restartResult.success) {
                  console.log('✅ 浏览器MCP重启成功，重试导航...')
                  await new Promise(resolve => setTimeout(resolve, 1000)) // 等待1秒让MCP完全启动
                  
                  const retryResult = await window.electronAPI.executeMCPTool('browser_navigate', toolArgs)
                  console.log('🔄 重试导航结果:', retryResult)
                  
                  if (retryResult.success) {
                    result = retryResult
                    result.mcpRestarted = true
                    result.retrySuccessful = true
                  } else {
                    // 重启后仍然失败，保留原错误信息但添加重启尝试信息
                    result.mcpRestartAttempted = true
                    result.retryError = retryResult.error
                  }
                } else {
                  result.mcpRestartFailed = true
                  result.restartError = restartResult.error
                }
              } catch (restartError) {
                console.error('🔄 重启浏览器MCP时出错:', restartError)
                result.mcpRestartException = restartError.message
              }
            }
            break



          // 邮件相关工具
          case 'send_email':
            console.log(`📧 [EMAIL] 发送邮件`)
            console.log(`📧 [EMAIL] 收件人: ${toolArgs.to}`)
            console.log(`📧 [EMAIL] 主题: ${toolArgs.sub}`)
            
            // 如果未经用户确认，触发邮件预览
            if (!toolArgs.is_ok) {
              console.log(`📧 [EMAIL] 需要用户确认，显示邮件预览`)
              result = {
                success: true,
                needEmailPreview: true,
                emailData: {
                  to: toolArgs.to,
                  cc: toolArgs.cc || [],
                  subject: toolArgs.sub,
                  message: toolArgs.message
                },
                message: '邮件预览已生成，请在历史记录中查看并确认发送'
              }
            } else {
              result = await window.electronAPI.executeMCPTool('send_email', toolArgs)
              console.log(`📧 [EMAIL] 结果: 成功=${result.success}`)
            }
            break

          case 'list_email':
            console.log(`📧 [EMAIL] 查询未读邮件`)
            console.log(`📧 [EMAIL] 开始时间: ${toolArgs.start_time || '无'}`)
            console.log(`📧 [EMAIL] 结束时间: ${toolArgs.end_time || '无'}`)
            
            result = await window.electronAPI.executeMCPTool('list_email', toolArgs)
            console.log(`📧 [EMAIL] 查询结果: 成功=${result.success}, 邮件数量=${result.emails?.length || 0}`)
            break

          case 'mark_email_as_read':
            console.log(`📧 [EMAIL] 标记邮件为已读`)
            console.log(`📧 [EMAIL] 邮件UID列表: ${toolArgs.uid_list}`)
            
            result = await window.electronAPI.executeMCPTool('mark_email_as_read', toolArgs)
            console.log(`📧 [EMAIL] 标记结果: 成功=${result.success}`)
            break



          // MCP状态检查
          case 'get_status':
            console.log(`📊 [STATUS] 获取MCP状态`)
            try {
              const mcpStatus = await window.electronAPI.getMCPStatus()
              result = {
                success: true,
                message: 'MCP状态信息',
                status: mcpStatus,
                type: 'status_info'
              }
            } catch (statusError) {
              result = {
                success: false,
                error: `获取状态失败: ${statusError.message}`,
                type: 'status_error'
              }
            }
            break

          // === 携程机票查询 ===
          case 'query_flights':
            console.log(`✈️ [FLIGHTS] 开始查询机票`)
            console.log(`✈️ [FLIGHTS] 参数:`, toolArgs)
            
            try {
              // 验证必要参数
              if (!toolArgs.departure_city || !toolArgs.arrival_city) {
                result = {
                  success: false,
                  error: '缺少必要参数：出发城市或到达城市',
                  type: 'parameter_error'
                }
                break
              }

              // 工具参数中应该已经包含转换后的机场代码
              const departureCode = toolArgs.departure_code || toolArgs.departure_city
              const arrivalCode = toolArgs.arrival_code || toolArgs.arrival_city

              // 处理日期
              let departureDate = toolArgs.departure_date
              if (!departureDate) {
                // 默认使用今天的日期
                const today = new Date()
                departureDate = today.toISOString().split('T')[0]
              }

              // 构建携程URL
              let ctripUrl
              const tripType = toolArgs.trip_type || 'oneway'
              
              if (tripType === 'round' && toolArgs.return_date) {
                // 往返机票
                ctripUrl = `https://flights.ctrip.com/online/list/round-${departureCode.toLowerCase()}-${arrivalCode.toLowerCase()}?depdate=${departureDate}_${toolArgs.return_date}`
              } else {
                // 单程机票
                ctripUrl = `https://flights.ctrip.com/online/list/oneway-${departureCode.toLowerCase()}-${arrivalCode.toLowerCase()}?depdate=${departureDate}`
              }

              console.log(`✈️ [FLIGHTS] 构建的携程URL: ${ctripUrl}`)

              // 使用浏览器打开携程网站
              const browserResult = await window.electronAPI.executeMCPTool('browser_navigate', { url: ctripUrl })
              
              if (browserResult.success) {
                result = {
                  success: true,
                  message: `已为您打开携程网站查询 ${toolArgs.departure_city} 到 ${toolArgs.arrival_city} 的${tripType === 'round' ? '往返' : '单程'}机票。`,
                  flightQuery: {
                    departure: toolArgs.departure_city,
                    arrival: toolArgs.arrival_city,
                    tripType: tripType === 'round' ? '往返' : '单程',
                    departureDate: departureDate,
                    returnDate: toolArgs.return_date || null
                  },
                  type: 'flight_query'
                }
              } else {
                result = {
                  success: false,
                  error: `打开携程网站失败: ${browserResult.error}`,
                  ctripUrl: ctripUrl,
                  suggestion: '请手动复制链接到浏览器中打开'
                }
              }

            } catch (flightError) {
              console.error(`✈️ [FLIGHTS] 查询失败:`, flightError)
              result = {
                success: false,
                error: `机票查询失败: ${flightError.message}`,
                type: 'flight_error'
              }
            }
            break

          // === 天气查询工具 ===
          case 'get_weather_forecast':
            console.log(`🌤️ [WEATHER] 开始查询天气`)
            console.log(`🌤️ [WEATHER] 参数:`, toolArgs)
            
            try {
              // 验证必要参数
              if (!toolArgs.adm || !toolArgs.city || !toolArgs.days) {
                result = {
                  success: false,
                  error: '缺少必要参数：需要提供adm（行政区划）、city（城市名称）、days（预报天数）',
                  type: 'parameter_error'
                }
                break
              }

              // 验证天数参数
              const validDays = ['3d', '7d', '10d', '15d', '30d']
              if (!validDays.includes(toolArgs.days)) {
                result = {
                  success: false,
                  error: `无效的天数参数，支持的值: ${validDays.join(', ')}`,
                  type: 'parameter_error'
                }
                break
              }

              // 设置参数
              const weatherParams = {
                adm: toolArgs.adm,
                city: toolArgs.city,
                days: toolArgs.days
              }

              console.log(`🌤️ [WEATHER] 处理后的参数:`, weatherParams)

              // 调用天气MCP工具
              const weatherResult = await window.electronAPI.executeMCPTool('get_weather_forecast', weatherParams)
              
              console.log(`🌤️ [WEATHER] 原始返回结果:`, weatherResult)
              
              // 检查返回数据格式 - 和风天气API返回格式
              if (weatherResult && weatherResult.code === "200" && weatherResult.daily) {
                // 成功返回的数据
                const weatherMessage = `已为您查询 ${toolArgs.adm} ${toolArgs.city} 的${toolArgs.days}天气预报`
                
                result = {
                  success: true,
                  message: weatherMessage,
                  weatherData: weatherResult,
                  city: toolArgs.city,
                  adm: toolArgs.adm,
                  days: toolArgs.days,
                  type: 'weather_forecast'
                }
                console.log(`🌤️ [WEATHER] 查询成功:`, result)
              } else if (weatherResult && weatherResult.error) {
                // 查询失败的情况
                result = {
                  success: false,
                  error: `天气查询失败: ${weatherResult.error}`,
                  city: toolArgs.city,
                  adm: toolArgs.adm,
                  type: 'weather_error',
                  rawResult: weatherResult
                }
                console.log(`🌤️ [WEATHER] 查询失败:`, result)
              } else if (weatherResult && weatherResult.success) {
                // 兼容可能的success格式
                result = {
                  success: true,
                  message: `已为您查询 ${toolArgs.adm} ${toolArgs.city} 的天气信息`,
                  weatherData: weatherResult.data || weatherResult,
                  city: toolArgs.city,
                  adm: toolArgs.adm,
                  days: toolArgs.days,
                  type: 'weather_forecast'
                }
                console.log(`🌤️ [WEATHER] 查询成功(兼容格式):`, result)
              } else {
                // 其他失败情况
                let errorMessage = '返回数据格式无效'
                if (weatherResult && weatherResult.info) {
                  errorMessage = weatherResult.info
                } else if (weatherResult && weatherResult.error) {
                  errorMessage = weatherResult.error
                }
                
                result = {
                  success: false,
                  error: `天气查询失败: ${errorMessage}`,
                  city: toolArgs.city,
                  adm: toolArgs.adm,
                  type: 'weather_error',
                  rawResult: weatherResult
                }
                console.log(`🌤️ [WEATHER] 查询失败:`, result)
              }

            } catch (weatherError) {
              console.error(`🌤️ [WEATHER] 查询异常:`, weatherError)
              result = {
                success: false,
                error: `天气查询异常: ${weatherError.message}`,
                city: toolArgs.city || '未知',
                adm: toolArgs.adm || '未知',
                type: 'weather_error'
              }
            }
            break

          // === 百度搜索 ===
          case 'baidu_search':
            console.log(`🔍 [BAIDU_SEARCH] 开始百度搜索`)
            console.log(`🔍 [BAIDU_SEARCH] 参数:`, toolArgs)
            
            try {
              // 验证必要参数
              if (!toolArgs.query || !toolArgs.query.trim()) {
                result = {
                  success: false,
                  error: '缺少必要参数：搜索关键词',
                  type: 'parameter_error'
                }
                break
              }

              // 对搜索关键词进行URL编码
              const encodedQuery = encodeURIComponent(toolArgs.query.trim())
              
              // 构建百度搜索URL
              const baiduUrl = `https://www.baidu.com/s?wd=${encodedQuery}`

              console.log(`🔍 [BAIDU_SEARCH] 构建的百度URL: ${baiduUrl}`)

              // 使用浏览器打开百度搜索
              const browserResult = await window.electronAPI.executeMCPTool('browser_navigate', { url: baiduUrl })
              
              if (browserResult.success) {
                result = {
                  success: true,
                  message: `已为您打开百度搜索结果：${toolArgs.query}`,
                  searchQuery: toolArgs.query,
                  type: 'baidu_search',
                  searchUrl: baiduUrl  // 保存URL用于调试，但不在回复中显示
                }
              } else {
                result = {
                  success: false,
                  error: `打开百度搜索失败: ${browserResult.error}`,
                  searchQuery: toolArgs.query,
                  baiduUrl: baiduUrl,
                  suggestion: '请手动复制链接到浏览器中打开'
                }
              }

            } catch (searchError) {
              console.error(`🔍 [BAIDU_SEARCH] 搜索失败:`, searchError)
              result = {
                success: false,
                error: `百度搜索失败: ${searchError.message}`,
                searchQuery: toolArgs.query,
                type: 'baidu_search_error'
              }
            }
            break

          default:
            console.warn(`🔧 未知的MCP工具: ${toolName}`)
            result = {
              success: false,
              error: `未支持的工具: ${toolName}`,
              toolName: toolName
            }
        }

      } catch (error) {
        console.error(`🔧 执行工具 ${toolName} 时发生错误:`, error)
        result = {
          success: false,
          error: `工具执行失败: ${error.message}`,
          toolName: toolName,
          exception: error.name
        }
      }

      // 确保result有基本结构
      if (!result || typeof result !== 'object') {
        result = {
          success: false,
          error: '工具返回了无效结果',
          toolName: toolName
        }
      }

      // 为每个结果添加工具名称
      result.toolName = toolName
      result.toolIndex = i

      // 🔄 【修复】如果search_files自动打开了文件，标记为同时使用了open_file
      if (toolName === 'search_files' && hasAutoOpenedFile && openFileResult) {
        result.autoOpenedFile = true
        result.actualToolsUsed = ['search_files', 'open_file']
        console.log('📂 search_files自动打开了文件，实际使用的工具: [search_files, open_file]')
      }

      allResults.push(result)
      console.log(`🔧 工具 ${toolName} 执行完成，结果: ${result.success ? '成功' : '失败'}`)
      
      // 🚨 如果有工具需要用户确认或邮件预览，立即返回，不继续处理
      if (result.needConfirm || result.needEmailPreview) {
        console.log(`🚨 工具 ${toolName} 需要用户确认/邮件预览，立即返回`)
        
        // 邮件预览的情况
        if (result.needEmailPreview) {
          return {
            success: true,
            message: result.message || '邮件预览已生成，请在历史记录中查看并确认发送',
            toolsUsed: [result],
            type: 'email_preview_needed',
            needEmailPreview: true,
            emailData: result.emailData
          }
        }
        
        // 原有确认逻辑
        return {
          success: true,
          message: result.message || '需要用户确认',
          toolsUsed: [result], // 直接返回需要确认的工具结果
          type: 'tool_confirmation_needed',
          needConfirm: true,
          confirmTool: result
        }
      }
    }

    // 检查是否有任何工具调用成功
    const hasSuccessfulTool = allResults.some(r => r.success)
    const hasFailedTool = allResults.some(r => !r.success)

    console.log('🔧 === MCP工具调用汇总 ===')
    console.log(`总数: ${allResults.length}`)
    console.log(`成功: ${allResults.filter(r => r.success).length}`)
    console.log(`失败: ${allResults.filter(r => !r.success).length}`)

    // 🔄 检查是否需要新建会话
    const newSessionNeeded = allResults.some(result =>
      result.success && result.toolName && needsNewSession(result.toolName, result, conversationHistory)
    )

    let sessionInfo = null
    if (newSessionNeeded) {
      console.log('🔄 检测到需要新建会话的工具，准备创建新的会话历史')
      const newHistory = createNewSessionHistory(conversationHistory, allResults)
      sessionInfo = {
        newSession: true,
        clearedHistory: newHistory,
        reason: '文档创建或重要操作需要新的会话环境'
      }
    }



    // 使用更强大的API客户端发送最终请求
    let finalResponse
    try {
      const config = getChatConfig()
      let apiClient
      
      try {
        apiClient = createApiClient()
      } catch (createError) {
        console.warn('🔧 创建API客户端失败，使用备用方案:', createError.message)
        const modelConfig = modelManager.getModelConfig()
        const headers = {
          'Content-Type': 'application/json'
        }
        
        // 所有模型都使用用户token认证
        const userToken = getUserToken()
        if (userToken) {
          headers['Authorization'] = `Bearer ${userToken}`
        }
        
        apiClient = axios.create({
          baseURL: modelConfig.baseURL,
          headers: headers,
          timeout: config.timeout || 30000
        })
      }

      // 构建工具调用结果的消息
      const toolMessages = allResults.map((result, index) => {
        const toolCall = assistantMessage.tool_calls[index]
        return {
          role: 'tool',
          tool_call_id: toolCall?.id || `call_${index}`,
          content: JSON.stringify(result)
        }
      })

      // 构建完整的对话历史
      const finalMessages = [
        ...conversationHistory,
        assistantMessage,
        ...toolMessages
      ]

      // 🆔 获取用户标识符
      let userId
      try {
        userId = getUserIdentifier()
      } catch (userIdError) {
        console.warn('🔧 获取用户标识符失败:', userIdError.message)
        userId = 'default-user'
      }

      console.log('🤖 发送最终AI请求，包含工具执行结果...')
      
      // 构建最终请求数据
      const finalRequestData = {
        messages: finalMessages,
        max_tokens: 1500,
        temperature: 0.7,
        stream: false,
        user: userId
      }
      
      // 🎯 【新增】根据是否有知识库数据选择模型
      const hasKnowledgeData = knowledgeSources && knowledgeSources.length > 0
      if (hasKnowledgeData) {
        // 有知识库数据时，固定使用 DeepSeek V3 Pro 模型
        console.log('🎯 检测到知识库数据，使用 DeepSeek V3 Pro 模型')
        finalRequestData.model = 'Pro/deepseek-ai/DeepSeek-V3'
        finalRequestData.reasoning = false
        finalRequestData.thinking_mode = "off"
      } else {
        // 没有知识库数据时，使用原有的模型选择逻辑
        const modelConfig = modelManager.getModelConfig()
        if (modelConfig.isSystemDefault) {
          // 系统默认模型：model传空字符串
          finalRequestData.model = ''
        } else {
          // 其他模型：传具体的model名称
          finalRequestData.model = config.model
          // 添加其他模型特有的参数
          finalRequestData.reasoning = false
          finalRequestData.thinking_mode = "off"
        }
      }
      
      finalResponse = await apiClient.post('/chat/completions', finalRequestData, {
        signal: signal
      })

      console.log('🤖 AI最终响应成功')
    } catch (apiError) {
      console.error('🤖 发送最终AI请求失败:', apiError.message)
      
      // API调用失败时的后备响应
      const successfulTools = allResults.filter(r => r.success)
      const failedTools = allResults.filter(r => !r.success)
      
      let fallbackMessage = ''
      if (successfulTools.length > 0) {
        fallbackMessage = `已成功执行 ${successfulTools.length} 个操作`
        if (failedTools.length > 0) {
          fallbackMessage += `，${failedTools.length} 个操作失败`
        }
      } else {
        fallbackMessage = '所有操作都执行失败'
      }
      
      return {
        success: true,
        message: fallbackMessage,
        results: allResults,
        toolResults: allResults, // 添加工具结果数组以便前端访问
        toolsUsed: allResults.flatMap(r => r.actualToolsUsed || [r.toolName]),
        type: 'mcp_tool_response',
        hasKnowledgeBase: knowledgeSources.length > 0,
        knowledgeSources: knowledgeSources,
        isNewSession: newSessionNeeded,
        sessionInfo: sessionInfo,

        apiError: apiError.message
      }
    }

    // 解析AI的最终响应
    const finalApiResponseData = finalResponse.data.data || finalResponse.data
    const finalMessage = finalApiResponseData.choices[0].message
    const parsedContent = parseNestedContent(finalMessage.content)

    const responseData = {
      success: true,
      message: parsedContent,
      results: allResults,
      toolResults: allResults, // 添加工具结果数组以便前端访问
      toolsUsed: allResults.flatMap(r => r.actualToolsUsed || [r.toolName]),
      usage: finalApiResponseData.usage,
      type: 'mcp_tool_response',
      hasKnowledgeBase: knowledgeSources.length > 0,
      knowledgeSources: knowledgeSources,
      isNewSession: newSessionNeeded,
      sessionInfo: sessionInfo,

    }

    // 🧠 添加知识库文件引用信息
    if (knowledgeSources.length > 0) {
      responseData.fileReferences = knowledgeSources.map(source => ({
        fileName: source.file_name,
        filePath: source.filePath || source.source_file_path,
        similarity: source.similarity
      })).filter(ref => ref.filePath)

            console.log('🧠 添加文件引用信息:', responseData.fileReferences)
    }

    // 如果有自动打开的文件，添加到响应中
    if (hasAutoOpenedFile && openFileResult) {
      responseData.autoOpenedFile = openFileResult
    }

    console.log('🔧 === MCP工具调用处理完成 ===')
    return responseData

  } catch (error) {
    console.error('🔧 处理MCP工具调用时发生严重错误:', error)
    return {
      success: false,
      message: '处理工具调用时发生错误，请稍后重试。',
      error: error.message,
      type: 'mcp_error',
      toolsUsed: [],
      isNewSession: false,

      exception: error.name,
      stack: error.stack
    }
  }
}

/**
 * 创建MCP系统消息
 * @returns {Object} MCP系统消息对象
 */
export function createMCPSystemMessage() {
  return {
    role: 'system',
    content: MCP_SYSTEM_PROMPT
  }
}

/**
 * 创建用户消息
 * @param {string} content - 用户输入内容
 * @returns {Object} 用户消息对象
 */
export function createUserMessage(content) {
  return {
    role: 'user',
    content: content
  }
}

/**
 * 检查MCP工具是否可用
 * @returns {Promise<boolean>} MCP状态
 */
export async function checkMCPStatus() {
  try {
    const status = await window.electronAPI.getMCPStatus()
    return status.success && Object.keys(status.clients || {}).length > 0
  } catch (error) {
    console.error('检查MCP状态失败:', error)
    return false
  }
}

// API配置现在通过统一配置管理器管理，无需单独导出

// 测试函数：直接测试MCP工具调用
window.testMCPTool = async function (toolType, args) {
  console.log(`🧪 测试MCP工具: ${toolType}`, args)

  try {
    const result = await window.electronAPI.executeMCPTool(toolType, args)
    console.log(`🧪 测试结果:`, result)
    return result
  } catch (error) {
    console.error(`🧪 测试失败:`, error)
    return { success: false, error: error.message }
  }
}

// 专门的文件搜索测试函数
window.testFileSearch = async function (query, directory = 'Desktop') {
  console.log(`🔍 直接测试文件搜索: "${query}" in ${directory}`)

  // 🎯 测试多种目录路径格式
  const directoryVariants = [
    'Desktop',
    '~/Desktop',
    'C:\\Users\\<USER>\\Desktop',
    '%USERPROFILE%\\Desktop',
    'C:\\Users\\<USER>\\Desktop',
    ''  // 空目录，搜索所有位置
  ]

  console.log(`🔧 首先测试不同的目录路径格式...`)
  for (const dirVariant of directoryVariants) {
    try {
      console.log(`🧪 测试目录: "${dirVariant}"`)
      const result = await window.electronAPI.executeMCPTool('search_files', {
        query: '',
        directory: dirVariant
      })
      console.log(`📁 目录 "${dirVariant}" 结果:`, {
        success: result.success,
        filesCount: result.files?.length || 0,
        firstFewFiles: result.files?.slice(0, 3).map(f => f.name) || []
      })

      if (result.files?.length > 0) {
        console.log(`✅ 找到有效目录: "${dirVariant}" - 包含 ${result.files.length} 个文件`)
        directory = dirVariant // 使用有效的目录路径
        break
      }
    } catch (error) {
      console.warn(`❌ 目录 "${dirVariant}" 测试失败:`, error.message)
    }
    await new Promise(resolve => setTimeout(resolve, 100))
  }

  const testCases = [
    { name: '原始查询', query: query },
    { name: '添加.docx', query: query + '.docx' },
    { name: '添加.pdf', query: query + '.pdf' },
    { name: '添加.xlsx', query: query + '.xlsx' },
    { name: '空查询(列出所有)', query: '' }
  ]

  for (const testCase of testCases) {
    try {
      console.log(`🧪 测试: ${testCase.name} - "${testCase.query}"`)
      const result = await window.electronAPI.executeMCPTool('search_files', {
        query: testCase.query,
        directory: directory
      })

      console.log(`🧪 ${testCase.name} 结果:`, {
        success: result.success,
        filesCount: result.files?.length || 0,
        files: result.files?.slice(0, 10).map(f => ({ name: f.name, path: f.path })) || [],
        error: result.error
      })

      if (result.files?.length > 10) {
        console.log(`🧪 还有 ${result.files.length - 10} 个文件未显示...`)
      }

    } catch (error) {
      console.error(`🧪 ${testCase.name} 失败:`, error.message)
    }

    // 短暂延迟避免过快请求
    await new Promise(resolve => setTimeout(resolve, 200))
  }
}

// 特定的文件打开测试
window.testOpenFile = async function (filePath) {
  console.log(`🧪 测试打开文件: "${filePath}"`)

  // 使用和主要清理逻辑相同的策略
  const cleanPath = filePath
    .replace(/\\{2,}/g, '\\')   // 2个或更多连续反斜杠变成1个
    .trim()

  console.log(`🧪 清理后路径: "${cleanPath}"`)

  return await window.testMCPTool('open_file', { filePath: cleanPath })
}

// 测试搜索功能
window.testSearchFiles = async function (query) {
  console.log(`🧪 测试搜索文件: "${query}"`)
  return await window.testMCPTool('search_files', { query: query })
}

// 完整的搜索+打开测试
window.testSearchAndOpen = async function (query, fileIndex = 0) {
  console.log(`🧪 完整测试: 搜索"${query}"并打开第${fileIndex}个文件`)

  // 1. 搜索文件
  const searchResult = await window.testSearchFiles(query)
  console.log(`🧪 搜索结果:`, searchResult)

  if (!searchResult.success || !searchResult.files || searchResult.files.length === 0) {
    console.error(`🧪 搜索失败或无结果`)
    return { success: false, error: '搜索失败或无结果' }
  }

  // 2. 选择文件
  if (fileIndex >= searchResult.files.length) {
    console.error(`🧪 文件索引超出范围: ${fileIndex} >= ${searchResult.files.length}`)
    return { success: false, error: '文件索引超出范围' }
  }

  const selectedFile = searchResult.files[fileIndex]
  console.log(`🧪 选择的文件:`, selectedFile)

  // 3. 打开文件
  const openResult = await window.testOpenFile(selectedFile.path)
  console.log(`🧪 打开结果:`, openResult)

  return {
    searchResult,
    selectedFile,
    openResult,
    success: openResult.success
  }
}

// 测试智能反斜杠清理算法
window.testBackslashCleaning = function (testString) {
  console.log('🧪 测试智能反斜杠清理...')
  console.log('🧪 输入:', testString)

  // 模拟智能清理逻辑 - 修复版本
  const backslashMatches = testString.match(/\\+/g) || []
  const maxBackslashes = Math.max(...backslashMatches.map(match => match.length), 0)
  console.log('🧠 最大连续反斜杠个数:', maxBackslashes)
  console.log('🧠 所有反斜杠组:', backslashMatches)

  let cleaned = testString

  // 🔧 正确的清理逻辑：只处理连续的多个反斜杠
  if (maxBackslashes > 1) {
    for (let count = maxBackslashes; count >= 2; count--) {
      const pattern = new RegExp('\\\\{' + count + '}', 'g')
      const replacement = '\\'  // 统一替换为单个反斜杠
      console.log(`🧠 处理 ${count} 个连续反斜杠 → 1 个反斜杠`)
      const before = cleaned
      cleaned = cleaned.replace(pattern, replacement)
      if (before !== cleaned) {
        console.log(`🧠 替换结果:`, cleaned)
      }
    }
  } else {
    console.log('🧠 没有连续的多个反斜杠，无需清理')
  }

  console.log('🧪 最终清理结果:', cleaned)

  // 尝试解析
  try {
    if (cleaned.startsWith('"{') && cleaned.endsWith('}"')) {
      const innerJson = cleaned.slice(1, -1)
      console.log('🧠 内层JSON:', innerJson)
      const parsed = JSON.parse(innerJson)
      console.log('✅ 解析成功:', parsed)

      // 验证文件路径
      if (parsed.filePath) {
        console.log('📁 文件路径检查:')
        console.log('  - 原始路径:', parsed.filePath)
        console.log('  - 包含冒号:', parsed.filePath.includes(':'))
        console.log('  - Windows格式:', /^[A-Za-z]:[\\\/]/.test(parsed.filePath))
        console.log('  - 反斜杠数量:', (parsed.filePath.match(/\\/g) || []).length)
      }

      return parsed
    } else {
      const parsed = JSON.parse(cleaned)
      console.log('✅ 直接解析成功:', parsed)
      return parsed
    }
  } catch (error) {
    console.error('❌ 解析失败:', error.message)
    return null
  }
}

// 🧪 添加专门的路径清理测试
window.testPathCleaning = function (filePath) {
  console.log('🧪 测试路径清理...')
  console.log('🧪 原始路径:', filePath)

  // 使用和主代码相同的清理逻辑
  const backslashMatches = filePath.match(/\\+/g) || []
  const maxBackslashes = Math.max(...backslashMatches.map(match => match.length), 0)
  console.log('🧠 路径中最大连续反斜杠:', maxBackslashes)
  console.log('🧠 所有反斜杠组:', backslashMatches)

  let cleanedPath = filePath

  if (maxBackslashes > 1) {
    // 应用智能清理策略：只处理连续的多个反斜杠
    for (let count = maxBackslashes; count >= 2; count--) {
      const pattern = new RegExp('\\\\{' + count + '}', 'g')
      const replacement = '\\'  // 统一替换为单个反斜杠
      console.log(`🧠 路径清理: ${count}个连续反斜杠 → 1个反斜杠`)
      cleanedPath = cleanedPath.replace(pattern, replacement)
    }
  }

  console.log('🧪 清理后路径:', cleanedPath)
  console.log('🔍 路径验证:', {
    原始长度: filePath.length,
    清理后长度: cleanedPath.length,
    包含冒号: cleanedPath.includes(':'),
    Windows格式: /^[A-Za-z]:[\\\/]/.test(cleanedPath),
    有扩展名: /\.[a-zA-Z0-9]+$/.test(cleanedPath),
    反斜杠数量: (cleanedPath.match(/\\/g) || []).length
  })

  return cleanedPath
}

// 诊断工具 - 帮助排查 open_file_mcp 问题
window.debugMCPIssue = function () {
  console.log('🔍 === MCP 诊断开始 ===')

  // 1. 检查 electronAPI 是否可用
  console.log('1. electronAPI 可用性检查:')
  console.log('  - window.electronAPI:', !!window.electronAPI)
  console.log('  - executeMCPTool:', !!window.electronAPI?.executeMCPTool)

  // 2. 测试简单的MCP调用
  console.log('2. 测试MCP状态获取...')
  if (window.electronAPI?.getMCPStatus) {
    window.electronAPI.getMCPStatus().then(status => {
      console.log('  - MCP状态:', status)
    }).catch(err => {
      console.error('  - MCP状态获取失败:', err)
    })
  } else {
    console.error('  - getMCPStatus 不可用')
  }

  // 3. 测试文件搜索
  console.log('3. 测试文件搜索...')
  if (window.electronAPI?.executeMCPTool) {
    window.electronAPI.executeMCPTool('search_files', { query: 'test' }).then(result => {
      console.log('  - 搜索测试结果:', result)
    }).catch(err => {
      console.error('  - 搜索测试失败:', err)
    })
  }

  // 4. 检查MCP工具定义
  console.log('4. MCP工具定义检查:')
  import('./mcpToolDefinitions.js').then(({ MCP_TOOLS }) => {
    console.log('  - MCP_TOOLS 已加载，工具数量:', MCP_TOOLS.length)
    MCP_TOOLS.forEach((tool, index) => {
      console.log(`    ${index + 1}. ${tool.function.name}`)
    })
  }).catch(err => {
    console.error('  - MCP工具定义加载失败:', err)
  })

  console.log('🔍 === MCP 诊断完成 ===')
}

// 模拟AI工具调用测试
window.testAIToolCall = function (testPath = 'C:\\Users\\<USER>\\Desktop\\测试壁纸.jpg') {
  console.log('🧪 === 模拟AI工具调用测试 ===')
  console.log('🧪 测试路径:', testPath)

  // 创建模拟的AI助手消息
  const mockAssistantMessage = {
    role: 'assistant',
    content: null,
    tool_calls: [
      {
        id: 'call_test_123',
        type: 'function',
        function: {
          name: 'open_file',
          arguments: JSON.stringify({ filePath: testPath })
        }
      }
    ]
  }

  console.log('🧪 模拟消息:', JSON.stringify(mockAssistantMessage, null, 2))

  // 测试工具调用处理
  return handleMCPToolCalls(mockAssistantMessage, []).then(result => {
    console.log('🧪 测试结果:', result)
    return result
  }).catch(error => {
    console.error('🧪 测试失败:', error)
    return { success: false, error: error.message }
  })
}

// 🔍 测试真实AI调用是否生成工具调用
window.testRealAICall = async function () {
  console.log('🔍 === 测试真实AI工具调用生成 ===')

  const testMessages = [
    {
      role: 'user',
      content: '打开桌面上的测试壁纸'
    }
  ]

  try {
    console.log('🔍 发送测试请求...')
    const result = await sendMCPChatRequest(testMessages)
    console.log('🔍 真实AI调用结果:', result)
    return result
  } catch (error) {
    console.error('🔍 真实AI调用失败:', error)
    return { success: false, error: error.message }
  }
}

// 🔧 专门测试API响应格式
window.testAPIResponse = async function () {
  console.log('🔧 === 测试API响应格式 ===')

  try {
    // 使用统一配置管理器获取配置
    const config = getChatConfig()
    
    // 🔧 确保createApiClient函数在这个作用域中可用
    let apiClient
    try {
      apiClient = createApiClient()
    } catch (createError) {
      console.error('🔧 创建API客户端失败:', createError)
      
      // 如果createApiClient失败，手动创建客户端
      apiClient = axios.create({
        baseURL: config.baseURL,
        headers: {
          'Authorization': `Bearer ${config.apiKey}`,
          'Content-Type': 'application/json'
        },
        timeout: config.timeout
      })
    }
    
    const requestData = {
      model: config.model,
      messages: [
        { role: 'system', content: MCP_SYSTEM_PROMPT },
        { role: 'user', content: '请打开桌面上的测试壁纸' }
      ],
      tools: MCP_TOOLS,
      tool_choice: "auto",
      max_tokens: 1000,
      temperature: 0.7,
      stream: false,
      reasoning: false,
      thinking_mode: "off",
      // enable_thinking: true,
      user: getUserIdentifier() // 🆔 测试时也使用用户标识符
    }

    console.log('🔧 API请求数据:', requestData)
    const response = await apiClient.post('/chat/completions', requestData)

    console.log('🔧 API原始响应:', response.data)
    console.log('🔧 message结构:', response.data.choices[0].message)
    console.log('🔧 是否有tool_calls:', !!response.data.choices[0].message.tool_calls)

    return response.data
  } catch (error) {
    console.error('🔧 API测试失败:', error)
    return { error: error.message }
  }
}

// 检查是否AI正在生成工具调用
window.monitorToolCalls = function () {
  console.log('📡 === 开始监控工具调用 ===')

  // 重写sendMCPChatRequest函数以添加监控
  const originalSendMCPChatRequest = window.sendMCPChatRequest || sendMCPChatRequest

  window.sendMCPChatRequest = async function (messages, options = {}) {
    console.log('📡 捕获到聊天请求:', messages.slice(-1)[0])

    const result = await originalSendMCPChatRequest(messages, options)

    console.log('📡 AI响应结果:', result)

    return result
  }

  console.log('📡 监控已启动，现在可以测试对话')
}

/**
 * 智能聊天API - 集成知识库功能
 */
export async function mcpChatWithKnowledge(messages, options = {}) {
  try {
    const {
      model = 'Qwen/Qwen2.5-7B-Instruct',
      enableKnowledge = true,
      knowledgeOptions = {
        searchLimit: 3,
        minSimilarity: 0.3
      }
    } = options

    // 获取最后一条用户消息
    const lastUserMessage = messages.filter(m => m.role === 'user').pop()

    if (!lastUserMessage || !enableKnowledge) {
      // 如果没有用户消息或禁用知识库，直接使用原有API
      return await sendMCPChatRequest(messages, options)
    }

    console.log('🧠 启用知识库增强的聊天')

    // 搜索相关知识
    let knowledgeContext = ''
    let knowledgeSources = []

    try {
      const knowledgeResult = await askKnowledge(lastUserMessage.content, knowledgeOptions)

      if (knowledgeResult.success && knowledgeResult.hasKnowledge) {
        console.log('🧠 找到相关知识，增强回答')
        knowledgeContext = knowledgeResult.knowledgeContext || ''
        knowledgeSources = knowledgeResult.sources || []

        // 在系统消息中添加知识库内容
        const enhancedMessages = [...messages]

        // 查找或创建系统消息
        let systemMessageIndex = enhancedMessages.findIndex(m => m.role === 'system')

        const knowledgeSystemPrompt = `
🚨 **关键指令：必须严格遵守知识库使用规则**

**强制要求：**
1. **必须优先使用知识库数据回答**：不得忽略知识库中的相关信息
2. **直接提供具体数据**：当用户询问的信息在知识库中存在时，直接展示具体内容
3. **禁止模糊回复**：不要使用"请问您是想了解..."等话术
4. **完整展示信息**：如果知识库中有多条相关记录，必须完整展示
5. **明确标注来源**：回答必须开头说明"根据知识库数据"

📚 **知识库内容：**
${knowledgeContext}

⚠️ **重要提醒：上述知识库数据必须作为回答的主要依据**`

        if (systemMessageIndex >= 0) {
          // 更新现有系统消息
          enhancedMessages[systemMessageIndex].content = knowledgeSystemPrompt + '\n\n' + enhancedMessages[systemMessageIndex].content
        } else {
          // 添加新的系统消息
          enhancedMessages.unshift({
            role: 'system',
            content: knowledgeSystemPrompt
          })
        }

        // 使用增强后的消息进行聊天
        const response = await sendMCPChatRequest(enhancedMessages, options)

        // 在响应中添加知识库来源信息
        if (response.success && knowledgeSources.length > 0) {
          response.knowledgeSources = knowledgeSources
          response.hasKnowledgeBase = true

          // 可选：在回答中添加来源信息
          if (response.message) {
            const sourceInfo = knowledgeSources.map((source, index) =>
              `[${index + 1}] ${source.file_name} (相似度: ${(source.similarity * 100).toFixed(1)}%)`
            ).join('\n')

            response.message += `\n\n---\n**知识库来源:**\n${sourceInfo}`

            // 添加文件引用路径信息，用于前端显示
            response.fileReferences = knowledgeSources.map(source => ({
              fileName: source.file_name,
              filePath: source.filePath || source.source_file_path,
              similarity: source.similarity
            })).filter(ref => ref.filePath) // 只包含有文件路径的引用
          }
        }

        return response
      } else {
        console.log('🧠 未找到相关知识，使用常规回答')
        const response = await sendMCPChatRequest(messages, options)
        if (response.success) {
          response.hasKnowledgeBase = false
          response.knowledgeMessage = '未在知识库中找到相关信息，以下是基于通用知识的回答。'
        }
        return response
      }
    } catch (knowledgeError) {
      console.error('🧠 知识库搜索失败，使用常规回答:', knowledgeError)
      const response = await sendMCPChatRequest(messages, options)
      if (response.success) {
        response.hasKnowledgeBase = false
        response.knowledgeMessage = '知识库暂不可用，以下是基于通用知识的回答。'
      }
      return response
    }
  } catch (error) {
    console.error('❌ 知识库增强聊天失败:', error)

    // 降级到常规聊天
    try {
      return await sendMCPChatRequest(messages, options)
    } catch (fallbackError) {
      console.error('❌ 常规聊天也失败:', fallbackError)
      return {
        success: false,
        error: fallbackError.message,
        content: '抱歉，聊天服务暂时不可用，请稍后重试。'
      }
    }
  }
}

// 🔧 测试username路径处理的功能
window.testUsernamePathHandling = function() {
  console.log('🔧 === 测试username路径处理 ===')
  
  const testPaths = [
    'C:/Users/<USER>/Downloads/test.pdf',
    'C:\\Users\\<USER>\\Downloads\\test.pdf',
    '/username/Downloads/test.pdf',
    '\\username\\Downloads\\test.pdf',
    'username/Downloads/test.pdf',
    'username\\Downloads\\test.pdf'
  ]
  
  console.log('🔧 测试路径列表:')
  testPaths.forEach((path, index) => {
    console.log(`  ${index + 1}. "${path}"`)
  })
  
  console.log('🔧 路径将在主进程中进行实际替换处理')
  console.log('🔧 您可以通过文件打开操作测试这些路径是否正确解析')
  
  return {
    testPaths,
    note: '路径处理将在主进程中进行，使用实际用户名替换username占位符'
  }
}

// 🎯 【新增】生成简化的系统提示词（用于知识库和联网搜索场景）
function generateSimplifiedSystemPrompt(userId, dataSource = '', hasBothData = false) {
  const sourceInfo = dataSource ? `\n5. 回答开头必须说明"根据${dataSource}"` : ''
  
  // 当同时有知识库和联网搜索数据时的特殊指导
  const bothDataGuidance = hasBothData ? `

# 🎯 数据源使用指导（同时有知识库和联网搜索数据）
- **平衡使用**：必须同时参考知识库数据和联网搜索结果
- **知识库优先**：对于用户文档、内部资料等特定信息，优先使用知识库数据
- **联网搜索补充**：对于最新信息、外部资料、实时数据等，使用联网搜索结果
- **信息整合**：将两种数据源的信息进行整合，提供全面的回答
- **避免重复**：如果两种数据源有重复信息，优先使用更详细或更新的版本
- **明确标注**：在回答中明确标注信息来源（知识库/联网搜索）` : ''
  
  return `# 桌面助手 - 增强模式
# 增强版 System Prompt 关键要求：

- 所有回答必须严格基于用户提供的数据，仅返回完全匹配条件的条目
- 不允许生成数据库中未出现的信息
- 若结果为空，也必须明确指出"未在数据中找到符合条件的项"
- 必须严格执行用户的筛选条件，不可模糊匹配或推测
- 禁止补充额外解释或扩展推断
- 回答必须以"根据${dataSource}"开头${bothDataGuidance}

# 用户信息
用户ID: ${userId}

# 🚨 核心指令
1. 必须基于提供的数据回答问题，优先使用最相关的信息
2. 当用户询问的信息在提供的数据中存在时，直接提供具体内容
3. 禁止使用"请问您是想了解..."等模糊回复
4. 必须完整展示相关信息${sourceInfo}
5. 保持简洁明了的回答风格
6. 如果需要执行操作（如打开文件、搜索等），请明确告知用户

# 🚨 数据过滤强制要求
7. **严格过滤**：必须严格按照用户的具体要求进行数据过滤
8. **数据准确性**：确保返回的每条数据都完全符合用户的查询条件
9. **禁止包含**：绝对不要返回不符合用户查询条件的数据
10. **数据格式**：保持原始数据格式，不要修改或重新组织数据
11. 在处理根据条件筛选数据的请求时（例如，根据年龄、技能、项目经验等），必须只输出100%满足所有查询条件的条目。
12. 严禁在结果中包含任何不符合筛选条件的条目，哪怕是为了提供额外信息或做出解释。

# 🔧 可用工具
- search_files: 搜索文件
- open_file: 打开文件
- read_file: 读取文件内容
- list_directory: 列出目录内容
- directory_tree: 获取目录树结构`
}
  
#!/usr/bin/env node

const fs = require('fs')
const path = require('path')
const { execSync } = require('child_process')
const os = require('os')

console.log('🚀 开始安装和配置Office Word MCP Server...')

async function installWordMCP() {
  try {
    // 1. 检查Python环境
    console.log('📋 检查Python环境...')
    try {
      const pythonVersion = execSync('python --version', { encoding: 'utf8' }).trim()
      console.log(`✅ Python版本: ${pythonVersion}`)
    } catch (error) {
      console.log('⚠️ 未找到Python，尝试python3...')
      try {
        const python3Version = execSync('python3 --version', { encoding: 'utf8' }).trim()
        console.log(`✅ Python3版本: ${python3Version}`)
      } catch (error2) {
        console.error('❌ 未找到Python环境，请先安装Python 3.8+')
        process.exit(1)
      }
    }

    // 2. 创建MCP服务器目录
    console.log('📁 创建MCP服务器目录...')
    const homeDir = os.homedir()
    const mcpServersDir = path.join(homeDir, '.mcp-servers')
    const wordMcpDir = path.join(mcpServersDir, 'word-mcp')
    
    if (!fs.existsSync(mcpServersDir)) {
      fs.mkdirSync(mcpServersDir, { recursive: true })
    }
    
    if (!fs.existsSync(wordMcpDir)) {
      fs.mkdirSync(wordMcpDir, { recursive: true })
    }
    
    console.log(`✅ MCP目录创建成功: ${wordMcpDir}`)

    // 3. 创建Word文档工作目录
    console.log('📁 创建Word文档工作目录...')
    const documentsDir = path.join(homeDir, 'Documents')
    const neZhaWordDir = path.join(documentsDir, 'NeZha-WordDocs')
    
    if (!fs.existsSync(neZhaWordDir)) {
      fs.mkdirSync(neZhaWordDir, { recursive: true })
      console.log(`✅ 创建Word文档目录: ${neZhaWordDir}`)
    } else {
      console.log(`✅ Word文档目录已存在: ${neZhaWordDir}`)
    }

    // 4. 创建示例配置文件
    console.log('📋 创建配置文件...')
    const configContent = {
      wordMCP: {
        enabled: true,
        defaultDocumentDir: neZhaWordDir,
        supportedFormats: ['.docx', '.doc'],
        autoBackup: true,
        maxFileSize: '10MB'
      },
      installation: {
        version: '1.0.0',
        installedAt: new Date().toISOString(),
        platform: os.platform(),
        nodeVersion: process.version
      }
    }
    
    const configPath = path.join(wordMcpDir, 'config.json')
    fs.writeFileSync(configPath, JSON.stringify(configContent, null, 2))
    console.log(`✅ 配置文件创建成功: ${configPath}`)

    // 5. 创建README文件
    console.log('📖 创建说明文档...')
    const readmeContent = `# NeZha Office Word MCP 集成

## 安装信息
- 安装时间: ${new Date().toISOString()}
- 平台: ${os.platform()}
- Node版本: ${process.version}

## 目录结构
- 配置目录: ${wordMcpDir}
- Word文档目录: ${neZhaWordDir}

## 支持的操作
1. create_word_document - 创建新Word文档
2. add_paragraph_to_word - 添加段落
3. add_heading_to_word - 添加标题
4. add_table_to_word - 插入表格
5. search_replace_word - 查找替换文本
6. get_word_document_content - 获取文档内容

## 使用示例
在NeZha聊天界面中，您可以这样使用：

- "创建一个名为'项目报告'的Word文档"
- "在报告中添加标题'第一章 概述'"
- "添加一段内容：这是项目的基本情况介绍"
- "插入一个3行4列的表格"
- "将文档中的'项目'替换为'产品'"

## 注意事项
- 文档会同时保存为.docx格式的元数据文件和.txt格式的可读文件
- 支持自动备份（如果启用）
- 最大文件大小限制：10MB
`

    const readmePath = path.join(wordMcpDir, 'README.md')
    fs.writeFileSync(readmePath, readmeContent)
    console.log(`✅ 说明文档创建成功: ${readmePath}`)

    // 6. 创建测试文档
    console.log('📝 创建测试文档...')
    const testDocPath = path.join(neZhaWordDir, '测试文档.txt')
    const testContent = `NeZha Office Word MCP 测试文档

这是一个测试文档，用于验证Word MCP集成是否正常工作。

安装信息：
- 安装时间: ${new Date().toISOString()}
- 用户: ${os.userInfo().username}
- 系统: ${os.platform()} ${os.arch()}

您可以通过NeZha聊天界面来：
1. 创建新文档
2. 编辑现有文档
3. 添加内容和格式
4. 管理文档结构

测试完成后，您可以删除此文件。
`

    fs.writeFileSync(testDocPath, testContent)
    console.log(`✅ 测试文档创建成功: ${testDocPath}`)

    // 7. 输出安装总结
    console.log('\n🎉 Office Word MCP Server 安装配置完成！')
    console.log('\n📋 安装总结:')
    console.log(`   ✅ 配置目录: ${wordMcpDir}`)
    console.log(`   ✅ Word文档目录: ${neZhaWordDir}`)
    console.log(`   ✅ 测试文档: ${testDocPath}`)
    console.log('\n🔧 下一步:')
    console.log('   1. 重启NeZha应用')
    console.log('   2. 在聊天界面中测试Word功能')
    console.log('   3. 尝试说"创建一个Word文档"来测试')
    console.log('\n💡 提示:')
    console.log('   - 所有Word文档将保存在Documents/NeZha-WordDocs目录中')
    console.log('   - 文档会生成.txt预览文件便于查看')
    console.log('   - 支持通过自然语言与AI交互编辑文档')

  } catch (error) {
    console.error('❌ 安装过程中出现错误:', error)
    console.error('请检查错误信息并重试，或联系开发团队获取支持。')
    process.exit(1)
  }
}

// 运行安装
installWordMCP().catch(console.error) 
#!/usr/bin/env node

/**
 * install-outlook-calendar-scripts.js
 * 
 * 确保Outlook日历VBS脚本在所有环境中都可用
 * 这个脚本会检查并复制内置VBS脚本到可访问的位置
 */

const fs = require('fs')
const path = require('path')

console.log('📅 开始安装Outlook日历脚本...')

// 源脚本目录
const sourceScriptsDir = path.join(__dirname, 'src', 'renderer', 'scripts')
console.log('📂 源脚本目录:', sourceScriptsDir)

// 目标目录列表（按优先级排序）
const targetDirs = [
  path.join(__dirname, 'scripts'), // 项目根目录的scripts文件夹
  path.join(__dirname, 'dist', 'scripts'), // dist目录中的scripts文件夹
]

// 要复制的脚本文件
const scriptFiles = [
  'utils.vbs',
  'createEvent.vbs', 
  'listEvents.vbs',
  'getCalendars.vbs',
  'findFreeSlots.vbs'
]

// 检查源目录是否存在
if (!fs.existsSync(sourceScriptsDir)) {
  console.error('❌ 源脚本目录不存在:', sourceScriptsDir)
  process.exit(1)
}

console.log('✅ 找到源脚本目录')

// 为每个目标目录创建脚本副本
let successCount = 0

for (const targetDir of targetDirs) {
  try {
    console.log(`📂 准备目标目录: ${targetDir}`)
    
    // 确保目标目录存在
    if (!fs.existsSync(targetDir)) {
      fs.mkdirSync(targetDir, { recursive: true })
      console.log(`✅ 创建目录: ${targetDir}`)
    }
    
    // 复制每个脚本文件
    let copiedFiles = 0
    for (const scriptFile of scriptFiles) {
      const sourcePath = path.join(sourceScriptsDir, scriptFile)
      const targetPath = path.join(targetDir, scriptFile)
      
      if (fs.existsSync(sourcePath)) {
        fs.copyFileSync(sourcePath, targetPath)
        console.log(`📋 复制脚本: ${scriptFile} -> ${targetDir}`)
        copiedFiles++
      } else {
        console.warn(`⚠️ 源脚本不存在: ${sourcePath}`)
      }
    }
    
    if (copiedFiles === scriptFiles.length) {
      console.log(`✅ 成功在 ${targetDir} 中安装了 ${copiedFiles} 个脚本文件`)
      successCount++
    } else {
      console.warn(`⚠️ 在 ${targetDir} 中只复制了 ${copiedFiles}/${scriptFiles.length} 个脚本文件`)
    }
    
  } catch (error) {
    console.error(`❌ 处理目标目录 ${targetDir} 时出错:`, error.message)
  }
}

// 创建脚本验证文件
try {
  const verificationFile = path.join(__dirname, 'outlook-scripts-installed.json')
  const verificationData = {
    timestamp: new Date().toISOString(),
    version: '1.0.0',
    installedDirectories: targetDirs.filter(dir => fs.existsSync(path.join(dir, 'createEvent.vbs'))),
    scriptFiles: scriptFiles
  }
  
  fs.writeFileSync(verificationFile, JSON.stringify(verificationData, null, 2))
  console.log('✅ 创建安装验证文件:', verificationFile)
} catch (error) {
  console.warn('⚠️ 无法创建验证文件:', error.message)
}

// 总结
console.log('\n🎯 安装总结:')
console.log(`   - 成功安装到 ${successCount}/${targetDirs.length} 个目标目录`)
console.log(`   - 脚本文件数量: ${scriptFiles.length}`)

if (successCount > 0) {
  console.log('✅ Outlook日历脚本安装成功！')
  console.log('💡 应用程序现在可以使用内置VBS脚本进行Outlook日历操作')
  process.exit(0)
} else {
  console.error('❌ 没有成功安装到任何目标目录')
  process.exit(1)
} 
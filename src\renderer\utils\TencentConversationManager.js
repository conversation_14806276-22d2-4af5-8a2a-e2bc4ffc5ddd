/**
 * 基于腾讯云ASR的智能对话管理器
 * 实现唤醒词检测 + 语音对话功能（使用原生JavaScript TTS）
 * 支持 Sherpa-ONNX 和腾讯云ASR两种唤醒词检测引擎
 */
import { TencentWebAudioSpeechRecognizer, TENCENT_ASR_STATES } from './TencentWebAudioSpeechRecognizer.js'
import { NewSherpaOnnxWakeWordDetector } from './NewSherpaOnnxWakeWordDetector.js'
import { useChatStore } from '../stores/chat.js'
import { DEFAULT_SMART_VOICE_CONFIG } from './smart-voice-config.js'
import { sendMCPChatRequest } from './mcpChatAPI.js'
import { MCP_SYSTEM_PROMPT } from './mcpToolDefinitions.js'
import { getUserIdentifier, getSessionIdentifier } from './userIdentity.js'

// 对话状态常量
export const CONVERSATION_STATES = {
  IDLE: 'idle',
  LISTENING_WAKE_WORD: 'listening_wake_word',  // 监听唤醒词
  WAKE_WORD_DETECTED: 'wake_word_detected',    // 检测到唤醒词
  LISTENING_COMMAND: 'listening_command',       // 监听指令
  PROCESSING: 'processing',                     // 处理中
  RESPONDING: 'responding',                     // 回复中
  ERROR: 'error'
}

export class TencentConversationManager {
  constructor(options = {}) {
    console.log('🚀 初始化腾讯云智能对话管理器')
    console.log('🔧 配置选项:', options)

    // 基础配置
    this.config = {
      engine_model_type: options.engine_model_type || '16k_zh',
      voice_format: options.voice_format || 1,
      needvad: options.needvad || 1,
      filter_dirty: options.filter_dirty || 1,
      filter_modal: options.filter_modal || 1,
      filter_punc: options.filter_punc || 0,
      convert_num_mode: options.convert_num_mode || 1,
      word_info: options.word_info || 2,
      noise_threshold: options.noise_threshold || 0.4,
      max_speak_time: options.max_speak_time || 30000,
      debug: options.debug || false,

      // TTS配置
      enableTTS: options.enableTTS !== undefined ? options.enableTTS : true,
      ttsRate: options.ttsRate || 2.4,
      ttsPitch: options.ttsPitch || 1.0,
      ttsVolume: options.ttsVolume || 0.8,
      ttsLang: options.ttsLang || 'zh-CN',

      // 唤醒词引擎配置
      wakeWordEngine: options.wakeWordEngine || 'sherpa-onnx',
      
      // 唤醒词配置
      wakeWords: options.wakeWords || ['犇犇', '奔奔', '笨笨'],
      checkInterval: options.checkInterval || 1000,
      recordDuration: options.recordDuration || 2000,

      // Sherpa-ONNX 配置
      sherpaOnnxConfig: options.sherpaOnnxConfig || {
        sensitivity: 0.1,
        numThreads: 1,
        debug: false,
        modelPath: 'assets/wasm'
      },

      // 行为配置
      autoResumeListening: options.autoResumeListening !== undefined ? options.autoResumeListening : true,
      autoRestartAfterError: options.autoRestartAfterError !== undefined ? options.autoRestartAfterError : true,
      debug: options.debug || false,
      
      // 腾讯云ASR词汇表配置
      hotwordId: options.hotwordId || '',
      replaceTextId: options.replaceTextId || ''
    }

    // 状态管理
    this.currentState = CONVERSATION_STATES.IDLE
    this.isEnabled = false
    this.isWakeWordListening = false
    this.isCommandListening = false
    this.isResponding = false

    // ASR实例
    this.wakeWordASR = null
    this.commandASR = null
    this.ttsWakeWordASR = null // TTS期间的唤醒词检测
    this.ttsWakeWordDetectionActive = false // TTS期间是否使用Sherpa-ONNX检测
    
    // Sherpa-ONNX 检测器实例
    this.sherpaDetector = null

    // TTS状态
    this.isTTSPlaying = false
    this.ttsUtterance = null
    this.ttsStartTime = null

    // 唤醒词检测状态
    this.isProcessingWakeWord = false
    this.lastWakeWordTime = 0
    this.lastWakeWordText = ''
    this.ttsWakeWordDetectionDisabled = false // 控制TTS期间的唤醒词检测

    // 回调函数
    this.onStateChange = null
    this.onWakeWordDetected = null
    this.onResult = null
    this.onError = null
    this.onVolume = null
    this.onPartialResult = null

    // 存储chatStore实例
    this.chatStore = null

    console.log('✅ 腾讯云智能对话管理器初始化完成')
  }

  /**
   * 初始化对话管理器
   */
  async init() {
    try {
      console.log('🚀 初始化智能对话管理器（原生JavaScript TTS）...')
      
      // 解锁音频上下文，确保TTS能正常播放
      this.unlockAudio()
      
      // 初始化聊天存储
      try {
        const { useChatStore } = await import('../stores/chat.js')
        this.chatStore = useChatStore()
      } catch (storeError) {
        console.warn('⚠️ 无法加载聊天存储, 将忽略历史记录:', storeError)
      }
      
      // 执行初始化逻辑
      this.isInitialized = true
      console.log('✅ 智能对话管理器初始化成功')
      return true
    } catch (error) {
      console.error('❌ 智能对话管理器初始化失败:', error)
      throw error
    }
  }

  /**
   * 解锁浏览器音频上下文（确保TTS播放能发出声音）
   */
  unlockAudio() {
    try {
      console.log('🔓 尝试解锁浏览器音频上下文')
      
      // 🔧 【修复】简化音频解锁逻辑，避免复杂的音频播放
      try {
        // 创建一个临时的音频上下文
        const audioContext = new (window.AudioContext || window.webkitAudioContext)()
        
        // 创建一个空的音频缓冲区
        const buffer = audioContext.createBuffer(1, 1, 22050)
        const source = audioContext.createBufferSource()
        source.buffer = buffer
        source.connect(audioContext.destination)
        
        // 使用 start 方法（代替旧的 noteOn 方法）
        if (typeof source.start !== 'undefined') {
          source.start(0)
        } else {
          source.noteOn(0)
        }
        
        console.log('✅ 音频上下文解锁成功（简化版）')
      } catch (audioContextError) {
        console.warn('⚠️ 音频上下文解锁失败:', audioContextError)
      }
      
      console.log('🔄 音频上下文解锁尝试完成')
    } catch (error) {
      console.warn('⚠️ 解锁音频上下文时出错:', error)
    }
  }

  /**
   * 启动智能对话（开始监听唤醒词）
   */
  async start() {
    if (this.isEnabled) {
      console.log('🤖 智能对话管理器已启动')
      return
    }

    try {
      console.log('🎯 启动智能对话管理器...')
      
      // 确保初始化完成
      if (!this.chatStore) {
        await this.init()
      }
      
      this.isEnabled = true
      this.updateState(CONVERSATION_STATES.LISTENING_WAKE_WORD)
      
      // 开始监听唤醒词
      await this.startWakeWordListening()
      
      console.log('🎧 智能对话管理器启动成功，开始监听唤醒词...')
      
    } catch (error) {
      console.error('❌ 启动智能对话管理器失败:', error)
      this.handleError('启动失败: ' + error.message)
    }
  }

  /**
   * 停止智能对话
   */
  stop() {
    console.log('🛑 停止智能对话管理器')
    
    this.isEnabled = false
    this.isWakeWordListening = false
    this.isCommandListening = false
    this.isResponding = false
    this.updateState(CONVERSATION_STATES.IDLE)
    
    if (this.commandTimeout) {
      clearTimeout(this.commandTimeout)
      this.commandTimeout = null
    }
    
    // 停止所有ASR实例
    this.stopWakeWordDetection()
    this.stopCommandASR()
    this.stopWakeWordDetectionDuringTTS()
    
    // 停止TTS播放
    this.stopTTS(true) // 强制停止
    
    // 清理超时定时器
    if (this.ttsTimeoutId) {
      clearTimeout(this.ttsTimeoutId)
      this.ttsTimeoutId = null
    }
    
    // 清理状态
    this.currentSentence = ''
    this.finalResults = []
    this.isProcessingCommand = false
    
    console.log('✅ 智能对话管理器已停止')
  }

  /**
   * 停止指令监听
   * 停止当前的命令识别并释放资源
   */
  async stopCommandListening() {
    try {
      console.log('🛑 停止腾讯云指令监听...')
      this.isCommandListening = false
      
      // 停止命令ASR
      this.stopCommandASR()
      
      // 清理命令超时
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      // 清理分句状态
      this.currentSentence = ''
      this.finalResults = []
      
      console.log('✅ 腾讯云指令监听已停止')
      this.updateState(CONVERSATION_STATES.IDLE)
      
    } catch (error) {
      console.error('❌ 停止腾讯云指令监听失败:', error)
      // 尝试恢复到唤醒词状态
      this.updateState(CONVERSATION_STATES.IDLE)
      this.isCommandListening = false
    }
  }

  /**
   * 停止所有ASR实例
   * 为了与AliyunConversationManager兼容，添加此方法
   */
  async stopAllASRInstances() {
    try {
      console.log('🛑 停止所有腾讯云ASR实例...')
      
      // 停止唤醒词ASR
      this.stopWakeWordASR()
      
      // 停止指令ASR
      this.stopCommandASR()
      
      // 停止TTS期间的唤醒词检测
      this.stopWakeWordDetectionDuringTTS()
      
      // 停止Sherpa-ONNX检测器
      if (this.sherpaDetector) {
        try {
          await this.sherpaDetector.stopListening()
          console.log('✅ Sherpa-ONNX检测器已停止')
        } catch (error) {
          console.warn('停止Sherpa-ONNX检测器时出错:', error)
          // 忽略此错误，继续处理其他资源
        }
      }
      
      console.log('✅ 所有腾讯云ASR实例已停止')
      
    } catch (error) {
      console.error('❌ 停止所有腾讯云ASR实例失败:', error)
    }
  }

  /**
   * 开始监听唤醒词
   */
  async startWakeWordListening() {
    if (!this.isEnabled || this.isWakeWordListening) {
      console.log('ℹ️ 跳过唤醒词监听启动：', {
        isEnabled: this.isEnabled,
        isWakeWordListening: this.isWakeWordListening
      })
      return
    }

    try {
      console.log('👂 开始监听唤醒词...')
      console.log('🔧 使用唤醒词引擎:', this.config.wakeWordEngine)
      
      this.isWakeWordListening = true
      this.updateState(CONVERSATION_STATES.LISTENING_WAKE_WORD)
      
      // 根据配置选择唤醒词检测引擎
      if (this.config.wakeWordEngine === 'sherpa-onnx') {
        try {
          await this.startSherpaOnnxWakeWordDetection()
        } catch (sherpaError) {
          console.warn('⚠️ Sherpa-ONNX 启动失败，自动回退到腾讯云ASR:', sherpaError.message)
          console.log('🔄 正在启动腾讯云ASR作为回退选项...')
          await this.startTencentASRWakeWordDetection()
        }
      } else {
        await this.startTencentASRWakeWordDetection()
      }
      
    } catch (error) {
      console.error('❌ 开始唤醒词监听失败:', error)
      this.isWakeWordListening = false
      
      // 重试
      if (this.isEnabled) {
        setTimeout(() => {
          this.startWakeWordListening()
        }, 1000)
      }
    }
  }

  /**
   * 启动 Sherpa-ONNX 唤醒词检测
   */
  async startSherpaOnnxWakeWordDetection() {
    try {
      console.log('🚀 启动 Sherpa-ONNX 唤醒词检测...')
      
      // 创建新的 Sherpa-ONNX 检测器
      if (!this.sherpaDetector) {
        this.sherpaDetector = new NewSherpaOnnxWakeWordDetector({
          keywords: this.config.wakeWords,
          sensitivity: this.config.sherpaOnnxConfig.sensitivity,
          numThreads: this.config.sherpaOnnxConfig.numThreads,
          debug: this.config.sherpaOnnxConfig.debug || this.config.debug,
          verboseDebug: true, // 🔧 【增强】启用详细调试
          showAllResults: true, // 🔧 【增强】显示所有识别结果
          modelPath: this.config.sherpaOnnxConfig.modelPath
        })
        
        // 设置回调函数
        this.sherpaDetector.onKeywordDetected = (result) => {
          console.log('🎯 Sherpa-ONNX 检测到关键词:', result)
          this.handleWakeWordDetected(result.keyword)
        }
        
        this.sherpaDetector.onError = (error) => {
          console.error('❌ Sherpa-ONNX 检测器错误:', error)
          
          // 如果系统仍然启用，尝试重新启动
          if (this.isEnabled && !this.isCommandListening) {
            setTimeout(() => {
              this.continueWakeWordListening()
            }, 1000)
          }
        }
        
        // 初始化检测器
        const initSuccess = await this.sherpaDetector.initialize()
        if (!initSuccess) {
          throw new Error('Sherpa-ONNX 检测器初始化失败')
        }
      }
      
      // 开始监听
      const startSuccess = await this.sherpaDetector.startListening()
      if (!startSuccess) {
        throw new Error('Sherpa-ONNX 检测器启动失败')
      }
      
      console.log('✅ Sherpa-ONNX 唤醒词检测已启动')
      
    } catch (error) {
      console.error('❌ 启动 Sherpa-ONNX 唤醒词检测失败:', error)
      // 重置状态，允许重试
      this.isWakeWordListening = false
      throw error
    }
  }

  /**
   * 启动腾讯云ASR唤醒词检测
   */
  async startTencentASRWakeWordDetection() {
    try {
      console.log('🚀 启动腾讯云ASR唤醒词检测...')
      
        // 创建唤醒词检测ASR实例 - 优化参数避免错误
        this.wakeWordASR = new TencentWebAudioSpeechRecognizer({
          // 基础配置
          engine_model_type: '16k_zh',
          voice_format: 1,
          needvad: 1,
          filter_dirty: 1,
          filter_modal: 0,          // 不过滤语气词，保持原始识别结果
          filter_punc: 1,           // 过滤标点符号，便于唤醒词匹配
          convert_num_mode: 1,
          word_info: 1,             // 简化词信息输出
          
          // 唤醒词检测专用配置
          max_speak_time: 5000,     // 5秒最大识别时长，避免参数错误
          noise_threshold: 0.3,     // 降低噪音阈值，提高敏感度
          
          // 词汇表配置
          hotword_id: this.config.hotwordId || '',
          replace_text_id: this.config.replaceTextId || '',
          
          // 调试模式
          debug: this.config.debug || false
        }, this.config.debug)

      // 设置唤醒词检测回调
      this.wakeWordASR.OnRecognitionStart = (res) => {
        console.log('🎙️ 唤醒词检测开始')
      }

      // 统一的唤醒词检测处理函数
      const processWakeWordText = (text, source) => {
        if (!text || !text.trim()) return
        
        const cleanText = text.trim()
        console.log(`🔍 [${source}] 检测文本:`, cleanText)
        
        // 🔄 【移除文本处理】直接使用原始ASR文本，不做任何标准化处理
        console.log(`🔄 [${source}] 使用原始ASR文本:`, cleanText)
        
        // 判断是否包含唤醒词（直接使用原始文本）
        const matchedWakeWord = this.containsWakeWord(cleanText)
        if (matchedWakeWord) {
          console.log(`🎯 [${source}] 检测到唤醒词:`, { 原文: cleanText, 匹配词: matchedWakeWord })
          this.handleWakeWordDetected(cleanText)
        }
      }

      this.wakeWordASR.OnRecognitionResultChange = (res) => {
        if (res.result && res.result.voice_text_str) {
          // 只在实时识别中检测，避免重复
          processWakeWordText(res.result.voice_text_str, 'REALTIME')
        }
      }

      this.wakeWordASR.OnSentenceEnd = (res) => {
        if (res.result && res.result.voice_text_str) {
          const text = res.result.voice_text_str.trim()
          console.log('📝 唤醒词检测句子结束:', text)
          
          // 🚨 关键优化：句子结束时不再检测唤醒词，避免重复处理
          // 只有在实时检测中没有处理过的情况下才处理
          const now = Date.now()
          if (!this.lastWakeWordText || text !== this.lastWakeWordText || now - this.lastWakeWordTime > 2000) {
            processWakeWordText(text, 'SENTENCE_END')
          } else {
            console.log('⚠️ 跳过重复的句子结束检测:', { text, lastText: this.lastWakeWordText, timeDiff: now - this.lastWakeWordTime })
          }
        }
      }

      this.wakeWordASR.OnRecognitionComplete = (res) => {
        console.log('🏁 唤醒词检测完成')
        
        // 如果没有检测到唤醒词且系统仍然启用，继续监听
        if (this.isEnabled && !this.isCommandListening) {
          setTimeout(() => {
            this.continueWakeWordListening()
          }, 100)
        }
      }

      this.wakeWordASR.OnError = (error) => {
        console.warn('⚠️ 唤醒词检测错误:', error)
        
        // 对于唤醒词检测，某些错误是正常的，继续监听
        if (this.isEnabled && !this.isCommandListening) {
          setTimeout(() => {
            this.continueWakeWordListening()
          }, 500)
        }
      }

      // 开始识别
      this.wakeWordASR.start()
      
    } catch (error) {
      console.error('❌ 开始唤醒词监听失败:', error)
      this.isWakeWordListening = false
      
      // 重试
      if (this.isEnabled) {
        setTimeout(() => {
          this.startWakeWordListening()
        }, 1000)
      }
    }
  }

  /**
   * 继续监听唤醒词
   */
  async continueWakeWordListening() {
    if (!this.isEnabled || this.isCommandListening) {
      return
    }

    // 清理之前的实例
    await this.stopWakeWordDetection()
    
    // 使用配置的检测间隔，但至少等待1秒，避免过于频繁
    const interval = Math.max(this.config.checkInterval || 3000, 1000)
    
    console.log(`🔄 ${interval}ms 后继续监听唤醒词`)
    setTimeout(() => {
      this.startWakeWordListening()
    }, interval)
  }

  /**
   * 检查文本是否包含唤醒词（直接使用原始文本）
   */
  containsWakeWord(text) {
    if (!text) return false
    
    const cleanText = text.toLowerCase().replace(/\s+/g, '')
    
    console.log('🔍 唤醒词检测:', {
      input: text,
      cleaned: cleanText
    })
    
    for (const wakeWord of this.config.wakeWords) {
      const cleanWakeWord = wakeWord.toLowerCase().replace(/\s+/g, '')
      if (cleanText.includes(cleanWakeWord)) {
        console.log(`🎯 匹配到唤醒词: "${wakeWord}" in "${text}"`)
        return wakeWord // 返回匹配的标准唤醒词
      }
    }
    
    return false
  }

  /**
   * 处理检测到唤醒词 - 强化版：立即停止所有任务
   */
  async handleWakeWordDetected(text) {
    console.log('🎯 处理唤醒词检测:', text)
    
    // 🔧 全局防重复处理机制 - 优先级最高
    const now = Date.now()
    if (this.isProcessingWakeWord) {
      console.log('⚠️ [GLOBAL] 跳过重复的唤醒词处理（正在处理中）:', {
        text: text,
        isProcessing: this.isProcessingWakeWord
      })
      return
    }
    
    // 🔧 检查是否在短时间内重复相同文本
    if (this.lastWakeWordText === text && now - this.lastWakeWordTime < 2000) {
      console.log('⚠️ [GLOBAL] 跳过重复的唤醒词处理（相同文本）:', {
        text: text,
        timeDiff: now - this.lastWakeWordTime
      })
      return
    }
    
    // 🔧 设置全局处理标志，防止任何重复触发
    this.isProcessingWakeWord = true
    this.lastWakeWordText = text
    this.lastWakeWordTime = now
    
    console.log('🛑 [NORMAL] 检测到唤醒词，立即停止所有其他任务')
    
    try {
      // 1. 强制停止所有TTS播放
      this.stopTTS()
      
      // 2. 停止TTS期间的唤醒词检测
      this.stopWakeWordDetectionDuringTTS()
      
      // 3. 停止所有ASR实例
      this.stopCommandASR()
      
      // 4. 清除所有超时器
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      if (this.wakeWordTimer) {
        clearTimeout(this.wakeWordTimer)
        this.wakeWordTimer = null
      }
      
      // 5. 重置所有状态标志
      this.isCommandListening = false
      this.isResponding = false
      this.currentSentence = ''
      this.finalResults = []
      
      console.log('✅ [NORMAL] 所有任务已停止，准备回复"Yes，Sir"')
      
      // 🔧 设置处理标志，防止重复触发
      this.isProcessingCommand = true
      
      // 立即更新状态为唤醒词检测
      this.updateState(CONVERSATION_STATES.WAKE_WORD_DETECTED)
      
      // 🔄 【移除文本处理】直接使用原始ASR文本，不做任何标准化处理
      console.log('🔄 [NORMAL] 使用原始ASR文本:', text)
      
      // 通知外部组件，使用原始文本
      if (this.onWakeWordDetected) {
        this.onWakeWordDetected(text)
      }
      
      // 🚀 播放确认回复（随机选择中文回复）
      this.updateState(CONVERSATION_STATES.WAKE_WORD_DETECTED)
      const wakeResponses = [
        "我在呢，有啥吩咐？",
        "在的呢～你说，我听着！",
        "来咯～今天我也要努力帮你！",
        "召唤成功！今天我为你效劳～",
        "全力待命，主人～",
        "来啦！今天也要帮你发光发热～"
      ]
      const randomResponse = wakeResponses[Math.floor(Math.random() * wakeResponses.length)]
      console.log('🔊 播放确认回复：', randomResponse, '（可被唤醒词打断）')
      try {
        await this.playUninterruptibleTTS(randomResponse)
        console.log('✅ 确认回复播放完成')
        
        // 🚀 优化：减少等待时间，提高响应速度
        await new Promise(resolve => setTimeout(resolve, 50))
        console.log('🔧 等待50ms确保TTS完全结束')
        
      } catch (ttsError) {
        console.warn('⚠️ 确认回复播放失败，继续启动指令监听:', ttsError.message)
      }
      
      // 🎤 切换到识别指令状态  
      console.log('🎤 [NORMAL] 准备切换到识别指令状态')
      console.log('🔍 [NORMAL] 启动指令监听前状态检查:', {
        isEnabled: this.isEnabled,
        isCommandListening: this.isCommandListening,
        isWakeWordListening: this.isWakeWordListening,
        isTTSPlaying: this.isTTSPlaying,
        isProcessingWakeWord: this.isProcessingWakeWord,
        currentState: this.currentState
      })
      
      await this.startCommandListening()
      
      console.log('✅ [NORMAL] 唤醒词处理流程完成，指令监听应已启动')
      
    } catch (error) {
      console.error('❌ [NORMAL] 处理唤醒词时发生错误:', error)
      // 即使出错也要回到唤醒词监听状态
      setTimeout(() => {
        this.backToWakeWordListening()
      }, 1000)
    } finally {
      // 🔧 确保重置处理标志
      this.isProcessingCommand = false
      // 🚀 优化：减少延迟重置时间，提高系统响应性
      setTimeout(() => {
        console.log('🔧 [NORMAL] 重置唤醒词处理标志')
        this.isProcessingWakeWord = false
      }, 800) // 进一步减少延迟时间，加快响应
    }
  }

  /**
   * 开始监听指令
   */
  async startCommandListening() {
    console.log('🎤 [DEBUG] startCommandListening被调用，当前状态:', {
      isEnabled: this.isEnabled,
      isCommandListening: this.isCommandListening,
      currentState: this.currentState,
      isProcessingWakeWord: this.isProcessingWakeWord,
      isTTSPlaying: this.isTTSPlaying,
      isResponding: this.isResponding
    })

    if (!this.isEnabled) {
      console.warn('⚠️ 系统未启用，无法开始指令监听')
      return
    }

    if (this.isCommandListening) {
      console.warn('⚠️ 已在监听指令中，跳过重复启动')
      return
    }

    try {
      console.log('🎤 开始监听用户指令...')

      // 🔧 【修复】确保所有相关状态都正确重置
      this.isCommandListening = true
      this.isResponding = false
      this.isTTSPlaying = false

      // 🔧 【修复】强制更新状态，确保UI能正确显示
      this.updateState(CONVERSATION_STATES.LISTENING_COMMAND)

      console.log('✅ [DEBUG] 指令监听状态已设置:', {
        isCommandListening: this.isCommandListening,
        currentState: this.currentState,
        isResponding: this.isResponding,
        isTTSPlaying: this.isTTSPlaying
      })
      
      // 清理之前的结果
      this.currentSentence = ''
      this.finalResults = []
      this.isProcessingCommand = false  // 重置处理标志
      
      // 🚀 优化：设置8秒超时，给用户更多思考时间
      this.commandTimeout = setTimeout(() => {
        console.log('⏰ 8秒无输入，回到唤醒词监听状态')
        this.handleCommandTimeout()
      }, 8000)
      
      // 创建指令识别ASR实例 - 优化配置
      this.commandASR = new TencentWebAudioSpeechRecognizer({
        // 基础配置
        engine_model_type: '16k_zh',
        voice_format: 1,
        needvad: 1,
        filter_dirty: 1,
        filter_modal: 1,          // 适度过滤语气词
        filter_punc: 0,           // 保留标点符号，维持语句完整性
        convert_num_mode: 1,
        word_info: 2,             // 输出详细词信息
        
        // 指令识别专用配置
        max_speak_time: Math.min(this.config.maxRecordingTime || 30000, 60000), // 限制最大60秒
        noise_threshold: 0.4,     // 平衡噪音过滤
        
        // 词汇表配置
        hotword_id: this.config.hotwordId || '',
        replace_text_id: this.config.replaceTextId || '',
        
        // 调试模式
        debug: this.config.debug || false
      }, this.config.debug)

      // 设置指令识别回调
      this.commandASR.OnRecognitionStart = (res) => {
        console.log('🎙️ 指令识别开始')
        // 有语音输入，清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
      }

      this.commandASR.OnSentenceBegin = (res) => {
        console.log('📝 开始说话')
        this.currentSentence = ''
        // 确保清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
      }

      this.commandASR.OnRecognitionResultChange = (res) => {
        if (res.result && res.result.voice_text_str) {
          this.currentSentence = res.result.voice_text_str
          console.log('🔄 指令识别中:', this.currentSentence)
          
          // 通知部分识别结果
          if (this.onPartialResult) {
            this.onPartialResult(this.currentSentence)
          }
        }
      }

      this.commandASR.OnSentenceEnd = (res) => {
        if (res.result && res.result.voice_text_str) {
          const finalText = res.result.voice_text_str.trim()
          console.log('✅ 指令句子结束:', finalText)
          console.log('🔍 句子结束验证:', {
            text: finalText,
            length: finalText.length,
            isValid: this.isValidSpeechResult(finalText)
          })
          
          if (finalText) {
            this.finalResults.push(finalText)
            console.log('📝 添加到结果数组:', this.finalResults)
            
            // 🔥 关键修复：设置备用超时机制
            // 如果OnRecognitionComplete没有被调用，2秒后手动处理结果
            console.log('⏰ 设置备用超时机制，2秒后自动处理结果')
            setTimeout(() => {
              if (this.isCommandListening && this.finalResults.length > 0) {
                console.log('🚨 [BACKUP] OnRecognitionComplete未被调用，手动触发结果处理')
                this.handleBackupCommandComplete()
              }
            }, 2000)
          }
        }
      }

      this.commandASR.OnRecognitionComplete = (res) => {
        console.log('🏁 [CRITICAL] 指令识别完成被调用！')
        
        // 防止重复处理
        if (this.isProcessingCommand) {
          console.log('⚠️ 正在处理指令，跳过OnRecognitionComplete')
          return
        }
        this.isProcessingCommand = true
        
        console.log('📊 识别完成统计:', {
          finalResults: this.finalResults,
          currentSentence: this.currentSentence,
          resultsCount: this.finalResults.length,
          res: res
        })
        
        this.isCommandListening = false
        
        // 清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
        
        // 优化识别结果处理逻辑
        let completeText = ''
        
        // 1. 优先使用finalResults中的完整句子
        if (this.finalResults.length > 0) {
          completeText = this.finalResults.join('').trim()
          console.log('📝 使用finalResults拼接:', completeText)
        }
        
        // 2. 如果没有完整句子，但有当前句子，也尝试使用
        if (!completeText && this.currentSentence) {
          completeText = this.currentSentence.trim()
          console.log('📝 使用currentSentence:', completeText)
        }
        
        // 3. 尝试从OnRecognitionComplete的结果中获取
        if (!completeText && res.result && res.result.voice_text_str) {
          completeText = res.result.voice_text_str.trim()
          console.log('📝 使用Complete结果:', completeText)
        }
        
        // 清理结果
        this.finalResults = []
        this.currentSentence = ''
        
        console.log('🎯 最终识别结果:', {
          text: completeText,
          length: completeText.length,
          isValid: this.isValidSpeechResult(completeText)
        })
        
        // 处理识别结果 - 放宽验证条件
        if (completeText && this.isValidSpeechResult(completeText)) {
          console.log('✅ 识别结果有效，调用大模型:', completeText)
          this.handleCommandResult(completeText)
        } else {
          console.log('❌ 指令识别结果无效，详细信息:', {
            hasText: !!completeText,
            text: completeText,
            isValidResult: this.isValidSpeechResult(completeText)
          })
          console.log('🔄 重新开始监听唤醒词')
          this.backToWakeWordListening()
        }
        
        // 重置处理标志
        this.isProcessingCommand = false
      }

      this.commandASR.OnError = (error) => {
        console.error('❌ 指令识别错误:', error)
        this.isCommandListening = false
        
        // 清除超时
        if (this.commandTimeout) {
          clearTimeout(this.commandTimeout)
          this.commandTimeout = null
        }
        
        // 回到唤醒词监听
        this.backToWakeWordListening()
      }

      // 开始识别
      this.commandASR.start()
      
      console.log('🎧 [DEBUG] 指令ASR已启动，等待用户语音输入...')
      console.log('🔔 [USER_PROMPT] 请现在说出您的指令！')
      
    } catch (error) {
      console.error('❌ 开始指令监听失败:', error)
      this.isCommandListening = false
      
      // 清除超时
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      this.backToWakeWordListening()
    }
  }

  /**
   * 处理指令监听超时
   */
  handleCommandTimeout() {
    console.log('⏰ 指令监听超时，无语音输入')
    
    // 停止指令监听
    this.stopCommandASR()
    this.isCommandListening = false
    
    // 清除超时
    if (this.commandTimeout) {
      clearTimeout(this.commandTimeout)
      this.commandTimeout = null
    }
    
    // 回到唤醒词监听
    this.backToWakeWordListening()
  }

  /**
   * 备用的指令识别完成处理（当OnRecognitionComplete未被调用时）
   */
  handleBackupCommandComplete() {
    console.log('🚨 [BACKUP] 备用指令识别完成处理')
    
    // 防止重复处理
    if (this.isProcessingCommand) {
      console.log('⚠️ [BACKUP] 正在处理指令，跳过备用处理')
      return
    }
    this.isProcessingCommand = true
    
    console.log('📊 备用处理统计:', {
      finalResults: this.finalResults,
      currentSentence: this.currentSentence,
      resultsCount: this.finalResults.length
    })
    
    this.isCommandListening = false
    
    // 清除超时
    if (this.commandTimeout) {
      clearTimeout(this.commandTimeout)
      this.commandTimeout = null
    }
    
    // 处理识别结果
    let completeText = ''
    
    // 1. 优先使用finalResults中的完整句子
    if (this.finalResults.length > 0) {
      completeText = this.finalResults.join('').trim()
      console.log('📝 [BACKUP] 使用finalResults拼接:', completeText)
    }
    
    // 2. 如果没有完整句子，但有当前句子，也尝试使用
    if (!completeText && this.currentSentence) {
      completeText = this.currentSentence.trim()
      console.log('📝 [BACKUP] 使用currentSentence:', completeText)
    }
    
    // 清理结果
    this.finalResults = []
    this.currentSentence = ''
    
    console.log('🎯 [BACKUP] 最终识别结果:', {
      text: completeText,
      length: completeText.length,
      isValid: this.isValidSpeechResult(completeText)
    })
    
    // 处理识别结果
    if (completeText && this.isValidSpeechResult(completeText)) {
      console.log('✅ [BACKUP] 识别结果有效，调用大模型:', completeText)
      this.handleCommandResult(completeText)
    } else {
      console.log('❌ [BACKUP] 指令识别结果无效，详细信息:', {
        hasText: !!completeText,
        text: completeText,
        isValidResult: this.isValidSpeechResult(completeText)
      })
      console.log('🔄 [BACKUP] 重新开始监听唤醒词')
      this.backToWakeWordListening()
    }
    
    // 重置处理标志
    this.isProcessingCommand = false
  }

  /**
   * 处理用户指令 - 统一使用与文本输入相同的MCP处理流程
   */
  async handleCommandResult(command) {
    console.log('🎯📨 [CRITICAL] 处理用户语音指令开始（统一MCP流程）:', {
      command: command,
      length: command.length,
      timestamp: new Date().toISOString()
    })
    
    try {
      this.updateState(CONVERSATION_STATES.PROCESSING)
      
      // 🔄 【移除文本处理】直接使用原始ASR指令，不做任何标准化处理
      console.log('🔄 [ASR_ORIGINAL] 使用原始ASR指令:', {
        原始指令: command
      })
      
      // 🗣️ 指令识别完毕，播放简短确认语音（快速不可打断）
      console.log('🔊 [COMMAND_CONFIRM] 指令识别完毕，播放简短确认语音：好的（快速不可打断）')
      try {
        await this.playUninterruptibleTTS('好的')
        console.log('✅ [COMMAND_CONFIRM] 确认语音播放完成')
        
        // 🚀 优化：取消等待时间，立即处理指令，提高响应速度
        console.log('🔧 [COMMAND_CONFIRM] 立即处理指令，无需等待')
        
      } catch (ttsError) {
        console.warn('⚠️ [COMMAND_CONFIRM] 确认语音播放失败，继续处理指令:', ttsError.message)
        // 即使TTS失败也继续处理指令，不影响主流程
      }
      
      console.log('🔄 [UNIFIED_FLOW] 语音指令将使用与文本输入相同的MCP处理流程')
      
      // 🔄 【统一流程】通知外部组件（FloatingCharacter.vue）处理用户语音指令
      // 使用与文本输入相同的处理方式，让FloatingCharacter.vue统一管理MCP调用和历史记录
      if (this.onResult) {
        this.onResult({
          type: 'user_command',
          text: command.trim(), // 🔄 【移除文本处理】直接使用原始指令文本
          timestamp: Date.now(),
          isVoiceInput: true // 标记为语音输入，便于区分处理
        })
      }
      
      console.log('🔄 [UNIFIED_FLOW] 语音指令已通过统一流程传递给FloatingCharacter.vue处理')
      
    } catch (error) {
      console.error('❌ [CRITICAL ERROR] 语音指令处理失败:', {
        error: error,
        message: error.message,
        stack: error.stack,
        command: command
      })
      
      // 🔄 【错误处理】通知外部组件错误信息
      if (this.onResult) {
        this.onResult({
          type: 'ai_response',
          text: '哎呀，出了点小问题呢~ 请稍后再试吧！',
          timestamp: Date.now(),
          isError: true,
          isVoiceInput: true
        })
      }
      
      // 回到唤醒词监听
      this.backToWakeWordListening()
    }
  }

  /**
   * 处理AI回复（语音问答） - 简化为只处理TTS播放
   */
  async handleAIResponse(responseText, response = null) {
    console.log('🗣️ 处理语音问答AI回复（TTS播放）:', responseText)
    
    // 🔧 防止重复回复
    if (this.isResponding) {
      console.log('⚠️ 正在回复中，跳过重复回复处理')
      return
    }
    
    this.isResponding = true
    
    this.updateState(CONVERSATION_STATES.RESPONDING)
    
    try {
      // 确保TTS功能启用（强制启用以确保语音回复）
      this.ensureTTSEnabled()
      console.log('🔊 开始语音问答的语音回复（带唤醒词检测）')
      
      // 确保没有其他TTS正在播放，等待一小段时间
      if (this.isTTSPlaying || window.speechSynthesis?.speaking) {
        console.log('⚠️ 检测到其他TTS正在播放，强制停止并等待')
        this.stopTTS(true)
        await new Promise(resolve => setTimeout(resolve, 200))
      }
      
      // 首先播放简短的提示音，解决延迟问题
      if (responseText && responseText.length > 10) {
        console.log('🔊 播放简短提示音以解决延迟问题')
        // await this.playNativeTTS('您好')
        // 短暂等待，让引擎准备好
        await new Promise(resolve => setTimeout(resolve, 100))
      }
      
      // 开始语音回复，期间监听唤醒词以便打断
      await this.playTTSWithWakeWordDetection(responseText)
      console.log('✅ 语音问答的语音回复播放完成')
      
    } finally {
      // 🔧 确保重置响应状态
      this.isResponding = false

      // 🔧 【修复】TTS播放完成后，避免重复启动唤醒词监听
      if (this.isEnabled && !this.isCommandListening && !this.isWakeWordListening) {
        console.log('🔄 AI回复完成，回到唤醒词监听状态')
        // 立即更新状态
        this.updateState(CONVERSATION_STATES.LISTENING_WAKE_WORD)
        // 使用更安全的延迟启动方式
        setTimeout(async () => {
          if (this.isEnabled && !this.isCommandListening && !this.isWakeWordListening) {
            try {
              await this.backToWakeWordListening()
            } catch (error) {
              console.error('❌ 回到唤醒词监听失败:', error)
              // 如果失败，尝试重新初始化
              if (this.isEnabled) {
                setTimeout(() => {
                  this.startWakeWordListening()
                }, 1000)
              }
            }
          }
        }, 100) // 增加延迟时间，确保状态稳定
      } else {
        console.log('ℹ️ 跳过唤醒词监听启动：', {
          isEnabled: this.isEnabled,
          isCommandListening: this.isCommandListening,
          isWakeWordListening: this.isWakeWordListening
        })
      }
    }
  }

  /**
   * 播放不可打断的TTS语音（用于唤醒词确认回复）
   */
  async playUninterruptibleTTS(text) {
    const startTime = Date.now()
    
    try {
      console.log('🔊 不可打断TTS播放开始:', {
        text: text?.substring(0, 100) + (text?.length > 100 ? '...' : ''),
        textLength: text?.length
      })
      
      // 检查文本是否有效
      if (!text || !text.trim()) {
        console.warn('⚠️ TTS文本为空，跳过播放')
        return
      }
      
      // 🔧 【修复】添加更多安全检查
      if (!this.isEnabled) {
        console.warn('⚠️ 对话管理器未启用，跳过TTS播放')
        return
      }
      
      // 停止之前的语音播放
      this.stopTTS(true)
      
      // 标记TTS正在播放
      this.isTTSPlaying = true
      
      // 🔑 关键：不启动唤醒词检测，确保不被打断
      console.log('🔒 不启动唤醒词检测，确保确认回复完整播放')
      
      // 使用原生JavaScript TTS播放
      try {
        console.log('🔄 开始调用playNativeTTS...')
        await this.playNativeTTS(text)
        console.log('✅ playNativeTTS调用完成')
      } catch (playError) {
        console.warn('⚠️ TTS播放出错，但继续流程:', playError)
        // 即使TTS播放失败，也不影响整体流程
      }
      
      const duration = Date.now() - startTime
      console.log('✅ 不可打断TTS播放完成，耗时:', duration + 'ms')
      
    } catch (error) {
      console.error('❌ 不可打断TTS播放失败:', {
        error: error.message,
        stack: error.stack,
        duration: Date.now() - startTime
      })
      this.isTTSPlaying = false
    } finally {
      // 🔧 【修复】确保TTS状态正确重置
      console.log('🔄 清理TTS播放状态（finally块）...')
      this.isTTSPlaying = false
      if (this.ttsKeepAliveInterval) {
        clearInterval(this.ttsKeepAliveInterval)
        this.ttsKeepAliveInterval = null
      }
      console.log('✅ TTS播放状态清理完成（finally块）')
    }
  }

  /**
   * 播放TTS语音，同时监听唤醒词以便打断（使用原生JavaScript TTS）
   */
  async playTTSWithWakeWordDetection(text) {
    const startTime = Date.now()
    const callStack = new Error().stack
    
    try {
      console.log('🔊 TTS播放开始（带唤醒词检测）:', {
        text: text?.substring(0, 100) + (text?.length > 100 ? '...' : ''),
        textLength: text?.length,
        currentlyPlaying: this.isTTSPlaying,
        wakeWordDetectionEnabled: !this.ttsWakeWordDetectionDisabled,
        timestamp: new Date().toISOString(),
        callStack: callStack?.split('\n').slice(0, 3).join(' -> ')
      })
      
      // 检查文本是否有效
      if (!text || !text.trim()) {
        console.warn('⚠️ TTS文本为空，跳过播放')
        return
      }
      
      // 停止之前的语音播放
      const stopResult = this.stopTTS()
      console.log('🔄 停止前一个TTS结果（带唤醒词检测）:', stopResult)
      
      // 标记TTS正在播放
      this.isTTSPlaying = true
      
      // 在TTS播放期间，启动唤醒词检测以便打断
      this.startWakeWordDetectionDuringTTS()
      
      // 使用原生JavaScript TTS播放
      console.log('🔊 使用原生JavaScript TTS播放（带唤醒词检测）...')
      await this.playNativeTTSWithWakeWordDetection(text)
      
      const duration = Date.now() - startTime
      console.log('✅ TTS播放完成（带唤醒词检测），耗时:', duration + 'ms')
      
    } catch (error) {
      console.error('❌ TTS播放失败（带唤醒词检测）:', {
        error: error.message,
        stack: error.stack,
        duration: Date.now() - startTime
      })
      this.isTTSPlaying = false
      this.stopWakeWordDetectionDuringTTS()
    }
  }

  /**
   * 播放原生TTS（带唤醒词检测版本）
   */
  async playNativeTTSWithWakeWordDetection(text) {
    return new Promise((resolve) => {
      try {
        console.log('🔊 原生TTS播放开始（带唤醒词检测）:', text)
        
        // 检查浏览器是否支持语音合成
        if (!window.speechSynthesis) {
          console.warn('浏览器不支持语音合成')
          this.isTTSPlaying = false
          resolve()
          return
        }

        // 重要！先取消所有排队中的语音合成
        window.speechSynthesis.cancel()
        
        // 创建语音合成实例
        this.ttsUtterance = new SpeechSynthesisUtterance(text)
        
        // 设置语音参数 - 标准语速
        this.ttsUtterance.lang = this.config.ttsLang || 'zh-CN'
        this.ttsUtterance.rate = this.config.ttsRate || 1.0
        this.ttsUtterance.pitch = this.config.ttsPitch || 1.0
        this.ttsUtterance.volume = this.config.ttsVolume || 0.8

        console.log('🔊 原生TTS配置（带唤醒词检测）:', {
          lang: this.ttsUtterance.lang,
          rate: this.ttsUtterance.rate,
          pitch: this.ttsUtterance.pitch,
          volume: this.ttsUtterance.volume
        })

        // 设置事件监听
        this.ttsUtterance.onend = () => {
          console.log('🔊 原生TTS播放完成（带唤醒词检测）')
          this.isTTSPlaying = false
          this.ttsUtterance = null
          this.stopWakeWordDetectionDuringTTS()
          resolve()
        }

        this.ttsUtterance.onerror = (event) => {
          console.error('❌ 原生TTS播放错误（带唤醒词检测）:', event)
          this.isTTSPlaying = false
          this.ttsUtterance = null
          this.stopWakeWordDetectionDuringTTS()
          resolve() // 即使出错也继续流程
        }

        // 开始播放
        console.log('🔊 启动原生TTS播放（带唤醒词检测）:', text)
        this.ttsStartTime = Date.now() // 记录开始时间
        
        // 解决Chrome中TTS播放问题的临时修复
        try {
          // 强制刷新TTS引擎状态，确保播放正常
          if (window.speechSynthesis.paused) {
            window.speechSynthesis.resume()
          }
          
          // 添加音量检查
          console.log('🔊 TTS音量检查:', {
            volume: this.ttsUtterance.volume,
            rate: this.ttsUtterance.rate,
            muted: document.muted || false
          })
          
          // 尝试播放一个短暂的声音来解锁音频
          this.ensureAudioUnlocked()
          
          // 保存当前的ttsUtterance引用，防止在延迟期间被其他操作清空
          const currentUtterance = this.ttsUtterance
          
                  // 延迟一点点再播放TTS，确保音频已解锁
        setTimeout(() => {
          // 在播放前再次检查 ttsUtterance 是否存在，防止被其他操作清空
          if (this.ttsUtterance && this.ttsUtterance === currentUtterance) {
            try {
              window.speechSynthesis.speak(this.ttsUtterance)
              console.log('🔊 TTS开始播放 (延迟启动)')
              
              // 🔧 【修复】延迟启动唤醒词检测，避免过早打断TTS播放
              setTimeout(() => {
                if (this.isTTSPlaying && this.ttsUtterance) {
                  console.log('🎧 延迟启动TTS期间的唤醒词检测')
                  this.startWakeWordDetectionDuringTTS()
                }
              }, 500) // 延迟500ms启动唤醒词检测，给TTS足够时间开始播放
              
            } catch (speakError) {
              console.error('❌ TTS播放调用失败:', speakError)
              this.isTTSPlaying = false
              resolve()
            }
          } else {
            console.warn('⚠️ TTS播放取消：ttsUtterance已被清空或更改，可能是被其他操作打断')
            this.isTTSPlaying = false
            resolve()
          }
        }, 50)
          
          // 防止Chrome浏览器在15秒后暂停TTS的修复
          const ttsKeepAliveInterval = setInterval(() => {
            if (this.isTTSPlaying && window.speechSynthesis.speaking) {
              window.speechSynthesis.pause()
              window.speechSynthesis.resume()
            } else {
              clearInterval(ttsKeepAliveInterval)
            }
          }, 5000)  // 每5秒刷新一次
          
          // 保存间隔ID，以便在停止TTS时清除
          this.ttsKeepAliveInterval = ttsKeepAliveInterval
          
        } catch (ttsError) {
          console.error('❌ TTS播放出错:', ttsError)
          this.isTTSPlaying = false
          this.stopWakeWordDetectionDuringTTS()
          resolve()
        }
        
        console.log('🔊 TTS开始播放，只能通过唤醒词打断或自然完成')
        
      } catch (error) {
        console.error('❌ 原生TTS播放失败（带唤醒词检测）:', error)
        this.isTTSPlaying = false
        this.stopWakeWordDetectionDuringTTS()
        resolve() // 即使出错也继续流程
      }
    })
  }

  /**
   * 原生JavaScript TTS播放（不可打断版本）
   */
  async playNativeTTS(text) {
    return new Promise((resolve) => {
      try {
        console.log('🔊 原生TTS播放开始（不可打断）:', text)
        
        // 检查浏览器是否支持语音合成
        if (!window.speechSynthesis) {
          console.warn('浏览器不支持语音合成')
          this.isTTSPlaying = false
          resolve()
          return
        }

        // 重要！先取消所有排队中的语音合成
        try {
          window.speechSynthesis.cancel()
        } catch (cancelError) {
          console.warn('⚠️ 取消TTS时出错:', cancelError)
        }
        
        // 创建语音合成实例
        this.ttsUtterance = new SpeechSynthesisUtterance(text)
        
        // 设置语音参数 - 优化确认回复的速度
        this.ttsUtterance.lang = this.config.ttsLang || 'zh-CN'
        this.ttsUtterance.rate = Math.max(this.config.ttsRate || 1.0, 2.0) // 确保至少2倍速
        this.ttsUtterance.pitch = this.config.ttsPitch || 1.0
        this.ttsUtterance.volume = this.config.ttsVolume || 0.8

        console.log('🔊 原生TTS配置（不可打断）:', {
          lang: this.ttsUtterance.lang,
          rate: this.ttsUtterance.rate,
          pitch: this.ttsUtterance.pitch,
          volume: this.ttsUtterance.volume
        })

        // 设置事件监听
        this.ttsUtterance.onend = () => {
          try {
            console.log('🔊 原生TTS播放完成（不可打断）')
            console.log('🔄 开始清理TTS状态...')
            
            this.isTTSPlaying = false
            this.ttsUtterance = null
            
            // 清理keep-alive间隔
            if (this.ttsKeepAliveInterval) {
              clearInterval(this.ttsKeepAliveInterval)
              this.ttsKeepAliveInterval = null
              console.log('✅ TTS keep-alive间隔已清理')
            }
            
            console.log('✅ TTS状态清理完成，准备resolve')
            resolve()
            
          } catch (endError) {
            console.warn('⚠️ TTS播放完成处理出错:', endError)
            resolve()
          }
        }

        this.ttsUtterance.onerror = (event) => {
          try {
            console.error('❌ 原生TTS播放错误（不可打断）:', event)
            console.log('🔄 开始清理TTS错误状态...')
            
            this.isTTSPlaying = false
            this.ttsUtterance = null
            
            // 清理keep-alive间隔
            if (this.ttsKeepAliveInterval) {
              clearInterval(this.ttsKeepAliveInterval)
              this.ttsKeepAliveInterval = null
              console.log('✅ TTS keep-alive间隔已清理（错误处理）')
            }
            
            console.log('✅ TTS错误状态清理完成，准备resolve')
            resolve() // 即使出错也继续流程
            
          } catch (errorError) {
            console.warn('⚠️ TTS播放错误处理出错:', errorError)
            resolve()
          }
        }

        // 开始播放
        console.log('🔊 启动原生TTS播放（不可打断）:', text)
        this.ttsStartTime = Date.now()
        
        // 解决Chrome中TTS播放问题的临时修复
        try {
          // 强制刷新TTS引擎状态，确保播放正常
          if (window.speechSynthesis.paused) {
            try {
              window.speechSynthesis.resume()
            } catch (resumeError) {
              console.warn('⚠️ 恢复TTS时出错:', resumeError)
            }
          }
          
          // 添加音量检查
          console.log('🔊 TTS音量检查:', {
            volume: this.ttsUtterance.volume,
            rate: this.ttsUtterance.rate,
            muted: document.muted || false
          })
          
          // 尝试播放一个短暂的声音来解锁音频
          try {
            this.ensureAudioUnlocked()
          } catch (audioError) {
            console.warn('⚠️ 音频解锁失败:', audioError)
          }
          
          // 保存当前的ttsUtterance引用，防止在延迟期间被其他操作清空
          const currentUtterance = this.ttsUtterance
          
          // 延迟一点点再播放TTS，确保音频已解锁
          setTimeout(() => {
            try {
              // 在播放前再次检查 ttsUtterance 是否存在，防止被其他操作清空
              if (this.ttsUtterance && this.ttsUtterance === currentUtterance) {
                try {
                  window.speechSynthesis.speak(this.ttsUtterance)
                  console.log('🔊 TTS开始播放 (延迟启动)')
                } catch (speakError) {
                  console.error('❌ TTS播放调用失败:', speakError)
                  this.isTTSPlaying = false
                  resolve()
                }
              } else {
                console.warn('⚠️ TTS播放取消：ttsUtterance已被清空或更改，可能是被其他操作打断')
                this.isTTSPlaying = false
                resolve()
              }
            } catch (timeoutError) {
              console.error('❌ TTS播放超时处理出错:', timeoutError)
              this.isTTSPlaying = false
              resolve()
            }
          }, 50)
          
          // 防止Chrome浏览器在15秒后暂停TTS的修复
          const ttsKeepAliveInterval = setInterval(() => {
            try {
              if (this.isTTSPlaying && window.speechSynthesis && window.speechSynthesis.speaking) {
                window.speechSynthesis.pause()
                window.speechSynthesis.resume()
              } else {
                clearInterval(ttsKeepAliveInterval)
              }
            } catch (error) {
              console.warn('⚠️ TTS keep-alive 出错:', error)
              clearInterval(ttsKeepAliveInterval)
            }
          }, 5000)  // 每5秒刷新一次
          
          // 保存间隔ID，以便在停止TTS时清除
          this.ttsKeepAliveInterval = ttsKeepAliveInterval
          
        } catch (ttsError) {
          console.error('❌ TTS播放出错:', ttsError)
          this.isTTSPlaying = false
          resolve()
        }
        
      } catch (error) {
        console.error('❌ 原生TTS播放失败（不可打断）:', error)
        this.isTTSPlaying = false
        resolve() // 即使出错也继续流程
      }
    })
  }

  /**
   * 在TTS播放期间启动唤醒词检测
   */
  async startWakeWordDetectionDuringTTS() {
    if (this.ttsWakeWordDetectionActive || !this.isEnabled || this.ttsWakeWordDetectionDisabled) {
      if (this.ttsWakeWordDetectionDisabled) {
        console.log('⚠️ TTS期间唤醒词检测已被禁用，跳过启动')
      } else if (this.ttsWakeWordDetectionActive) {
        console.log('⚠️ TTS期间唤醒词检测已在运行，跳过重复启动')
      }
      return
    }
    
    // 🔧 【修复】检查TTS是否真的在播放
    if (!this.isTTSPlaying || !this.ttsUtterance) {
      console.log('⚠️ TTS未在播放，跳过唤醒词检测启动')
      return
    }

    try {
      console.log('🎧 TTS播放期间继续使用 Sherpa-ONNX 监听唤醒词...')
      
      // 优先使用 Sherpa-ONNX 检测器（如果已初始化）
      if (this.sherpaDetector && this.sherpaDetector.isInitialized) {
        console.log('✅ TTS期间继续使用 Sherpa-ONNX 检测器，无需额外配置')
        this.ttsWakeWordDetectionActive = true
        return
      }
      
      // 回退到腾讯云ASR（仅在sherpa-onnx不可用时）
      console.log('⚠️ Sherpa-ONNX 不可用，TTS期间回退到腾讯云ASR')
      
      // 创建TTS期间的唤醒词检测ASR实例
      this.ttsWakeWordASR = new TencentWebAudioSpeechRecognizer({
        // 基础配置
        engine_model_type: '16k_zh',
        voice_format: 1,
        needvad: 1,
        filter_dirty: 1,
        filter_modal: 0,          // 不过滤语气词，保持原始识别结果
        filter_punc: 1,           // 过滤标点符号，便于唤醒词匹配
        convert_num_mode: 1,
        word_info: 1,             // 简化词信息输出
        
        // TTS期间唤醒词检测专用配置
        max_speak_time: 5000,     // 5秒检测窗口（腾讯云ASR最小值）
        noise_threshold: 0.2,     // 更高敏感度，因为需要在TTS播放时检测
        
        // 词汇表配置
        hotword_id: this.config.hotwordId || '',
        replace_text_id: this.config.replaceTextId || '',
        
        // 调试模式
        debug: this.config.debug || false
      }, this.config.debug)

      // 统一的TTS期间唤醒词检测处理函数
      const processTTSWakeWordText = (text, source) => {
        if (!text || !text.trim()) return
        
        const cleanText = text.trim()
        console.log(`🔍 [TTS-${source}] 检测文本:`, cleanText)
        
        // 🔧 【修复】增加TTS期间唤醒词检测的防误触发机制
        const now = Date.now()
        
        // 检查是否在短时间内重复检测
        if (this.lastTTSWakeWordText === cleanText && now - this.lastTTSWakeWordTime < 2000) {
          console.log(`⚠️ [TTS-${source}] 跳过重复的唤醒词检测:`, {
            text: cleanText,
            timeDiff: now - this.lastTTSWakeWordTime
          })
          return
        }
        
        // 检查TTS是否已经播放了足够长的时间（避免过早打断）
        if (this.ttsStartTime && now - this.ttsStartTime < 1000) {
          console.log(`⏰ [TTS-${source}] TTS播放时间过短，跳过唤醒词检测:`, {
            text: cleanText,
            ttsPlayTime: now - this.ttsStartTime
          })
          return
        }
        
        // 🔄 【移除文本处理】直接使用原始ASR文本，不做任何标准化处理
        console.log(`🔄 [TTS-${source}] 使用原始ASR文本:`, cleanText)
        
        // 判断是否包含唤醒词（使用原始文本）
        const matchedWakeWord = this.containsWakeWord(cleanText)
        if (matchedWakeWord) {
          console.log(`🎯 [TTS-${source}] 检测到唤醒词，立即打断播放:`, { 原文: cleanText, 匹配词: matchedWakeWord })
          
          // 记录检测时间和文本，防止重复处理
          this.lastTTSWakeWordText = cleanText
          this.lastTTSWakeWordTime = now
          
          // 🚀 立即设置TTS打断标志，确保stopTTS能够成功执行
          this.ttsWakeWordDetectionActive = true
          this.handleWakeWordDetectedDuringTTS(cleanText)
        }
      }

      // 设置TTS期间唤醒词检测回调
      this.ttsWakeWordASR.OnRecognitionResultChange = (res) => {
        if (res.result && res.result.voice_text_str) {
          // 只在实时识别中检测，避免重复
          processTTSWakeWordText(res.result.voice_text_str, 'REALTIME')
        }
      }

      this.ttsWakeWordASR.OnSentenceEnd = (res) => {
        if (res.result && res.result.voice_text_str) {
          const text = res.result.voice_text_str.trim()
          
          // 🚨 TTS期间优化：避免重复处理
          const now = Date.now()
          if (!this.lastWakeWordText || text !== this.lastWakeWordText || now - this.lastWakeWordTime > 1000) {
            processTTSWakeWordText(text, 'SENTENCE_END')
          } else {
            console.log('⚠️ [TTS] 跳过重复的句子结束检测:', { text, lastText: this.lastWakeWordText, timeDiff: now - this.lastWakeWordTime })
          }
        }
      }

      this.ttsWakeWordASR.OnError = (error) => {
        console.warn('⚠️ TTS期间唤醒词检测错误:', error)
        
        // 如果是参数错误，则禁用TTS期间的唤醒词检测功能
        if (error && error.code === 4001) {
          console.warn('🔧 检测到TTS期间唤醒词配置参数错误，暂时禁用此功能')
          this.stopWakeWordDetectionDuringTTS()
          // 标记禁用状态，避免重复尝试
          this.ttsWakeWordDetectionDisabled = true
        }
        
        // TTS期间的唤醒词检测错误不影响主流程
      }

      // 开始识别
      this.ttsWakeWordASR.start()
      
    } catch (error) {
      console.error('❌ TTS期间唤醒词检测启动失败:', error)
    }
  }

  /**
   * 停止TTS期间的唤醒词检测
   */
  stopWakeWordDetectionDuringTTS() {
    console.log('🔇 停止TTS期间的唤醒词检测')
    
    // 如果使用的是Sherpa-ONNX，只需要清除标志位
    if (this.ttsWakeWordDetectionActive && this.sherpaDetector) {
      console.log('✅ TTS期间使用Sherpa-ONNX，仅清除标志位')
      this.ttsWakeWordDetectionActive = false
      return
    }
    
    // 停止腾讯云ASR实例
    if (this.ttsWakeWordASR) {
      try {
        this.ttsWakeWordASR.stop()
        this.ttsWakeWordASR.destroyStream()
      } catch (error) {
        console.warn('停止TTS期间唤醒词检测时出错:', error)
      }
      this.ttsWakeWordASR = null
    }
  }

  /**
   * 处理TTS播放期间检测到的唤醒词 - 立即打断并开始监听指令
   */
  async handleWakeWordDetectedDuringTTS(text) {
    console.log('🎉 TTS播放期间检测到唤醒词，立即打断播放并开始监听指令:', text)
    
    // 🔧 全局防重复处理机制 - 与普通唤醒词共享
    const now = Date.now()
    if (this.isProcessingWakeWord) {
      console.log('⚠️ [TTS-GLOBAL] 跳过重复的唤醒词处理（正在处理中）:', {
        text: text,
        isProcessing: this.isProcessingWakeWord
      })
      return
    }
    
    // 🔧 检查是否在短时间内重复相同文本
    if (this.lastWakeWordText === text && now - this.lastWakeWordTime < 1000) {
      console.log('⚠️ [TTS-GLOBAL] 跳过重复的唤醒词处理（相同文本）:', {
        text: text,
        timeDiff: now - this.lastWakeWordTime
      })
      return
    }
    
    // 🔧 设置全局处理标志，防止与普通唤醒词冲突
    this.isProcessingWakeWord = true
    this.lastWakeWordText = text
    this.lastWakeWordTime = now
    
    console.log('🛑 [TTS] 检测到唤醒词，立即停止TTS播放并开始监听指令')
    
    try {
      // 1. 🚨 立即强制停止TTS播放
      console.log('🛑 [TTS] 1. 立即停止TTS播放')
      this.stopTTS(true) // 强制停止
      this.isTTSPlaying = false
      
      // 2. 停止TTS期间的唤醒词检测
      console.log('🛑 [TTS] 2. 停止TTS期间的唤醒词检测')
      this.stopWakeWordDetectionDuringTTS()
      
      // 3. 停止所有其他ASR实例
      console.log('🛑 [TTS] 3. 停止所有ASR实例')
      this.stopCommandASR()
      await this.stopWakeWordDetection()
      
      // 4. 清除所有超时器
      console.log('🛑 [TTS] 4. 清除所有超时器')
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      if (this.wakeWordTimer) {
        clearTimeout(this.wakeWordTimer)
        this.wakeWordTimer = null
      }
      
      // 5. 重置状态标志
      console.log('🛑 [TTS] 5. 重置状态标志')
      this.isCommandListening = false
      this.isResponding = false
      this.isWakeWordListening = false
      this.currentSentence = ''
      this.finalResults = []
      
      // 6. 通知外部组件检测到唤醒词
      console.log('🎯 [TTS] 6. 通知外部组件检测到唤醒词')
      if (this.onWakeWordDetected) {
        this.onWakeWordDetected(text)
      }
      
      // 7. 更新状态为唤醒词检测
      this.updateState(CONVERSATION_STATES.WAKE_WORD_DETECTED)

      // 8. 🎤 立即开始监听指令（跳过确认回复，快速响应）
      console.log('🎤 [TTS] 8. 立即开始监听指令（跳过确认回复）')

      // 🚀 优化：减少延迟时间，提高响应速度
      await new Promise(resolve => setTimeout(resolve, 50))

      // 🔧 【修复】在启动指令监听前，确保响应状态已重置
      this.isResponding = false
      console.log('🔧 [TTS] 重置响应状态标志')

      await this.startCommandListening()

      console.log('✅ [TTS] TTS打断处理完成，已开始监听用户指令')
      
    } catch (error) {
      console.error('❌ [TTS] 处理唤醒词时发生错误:', error)
      // 即使出错也要回到唤醒词监听状态
      setTimeout(() => {
        this.backToWakeWordListening()
      }, 1000)
    } finally {
      // 🔧 重置处理标志
      setTimeout(() => {
        console.log('🔧 [TTS] 重置唤醒词处理标志')
        this.isProcessingWakeWord = false
      }, 500) // 更快重置，提高响应速度
    }
  }

  /**
   * 回到唤醒词监听模式
   */
  async backToWakeWordListening() {
    if (!this.isEnabled) {
      return
    }

    console.log('🔄 回到唤醒词监听模式...')

    // 停止指令监听
    this.stopCommandASR()
    this.isCommandListening = false

    // 停止TTS
    this.stopTTS()

    // 清理状态
    this.currentSentence = ''
    this.finalResults = []
    this.isProcessingCommand = false

    // 🔧 【修复】立即更新状态为监听唤醒词，然后启动监听
    this.updateState(CONVERSATION_STATES.LISTENING_WAKE_WORD)

    // 🔧 【修复】避免重复启动唤醒词监听
    if (!this.isWakeWordListening) {
      // 短暂延迟后重新开始监听唤醒词
      setTimeout(async () => {
        if (this.isEnabled && !this.isWakeWordListening) {
          try {
            await this.startWakeWordListening()
          } catch (error) {
            console.error('❌ 启动唤醒词监听失败:', error)
            // 如果失败，尝试重新初始化
            if (this.isEnabled) {
              setTimeout(() => {
                this.startWakeWordListening()
              }, 1000)
            }
          }
        }
      }, 500)
    } else {
      console.log('ℹ️ 唤醒词监听已在运行，跳过重复启动')
    }
  }





  /**
   * 停止TTS播放（唤醒词检测时立即停止）
   */
  stopTTS(force = false) {
    try {
      const stopCallStack = new Error().stack
      console.log('🛑 停止TTS播放:', { 
        force, 
        isTTSPlaying: this.isTTSPlaying,
        speechSynthesisSpeaking: window.speechSynthesis?.speaking,
        ttsUtteranceExists: !!this.ttsUtterance,
        timestamp: new Date().toISOString(),
        callStack: stopCallStack?.split('\n').slice(0, 3).join(' -> ')
      })
      
      // 🔄 【修复】优化TTS停止逻辑，允许唤醒词打断
      if (force || this.isProcessingWakeWord || this.ttsWakeWordDetectionActive) {
        console.log('🎯 允许停止TTS播放:', { 
          force, 
          isProcessingWakeWord: this.isProcessingWakeWord,
          ttsWakeWordDetectionActive: this.ttsWakeWordDetectionActive 
        })
        
        // 立即停止原生JavaScript TTS
        if (window.speechSynthesis) {
          try {
            window.speechSynthesis.cancel()
            console.log('✅ speechSynthesis.cancel() 执行成功')
          } catch (error) {
            console.warn('停止原生TTS时出错:', error)
          }
        }
        
        // 清理keep-alive间隔
        if (this.ttsKeepAliveInterval) {
          clearInterval(this.ttsKeepAliveInterval)
          this.ttsKeepAliveInterval = null
          console.log('✅ TTS keep-alive 间隔已清理')
        }
        
        this.ttsUtterance = null
        this.isTTSPlaying = false
        this.ttsStartTime = null
        
        return true
      } else {
        console.log('🔒 TTS保护：让语音自然播放完毕')
        return false
      }
      
    } catch (error) {
      console.error('停止TTS时发生错误:', error)
      return false
    }
  }

  /**
   * 停止唤醒词ASR
   */
  stopWakeWordASR() {
    if (this.wakeWordASR) {
      try {
        this.wakeWordASR.stop()
        this.wakeWordASR.destroyStream()
      } catch (error) {
        console.warn('停止唤醒词ASR时出错:', error)
      }
      this.wakeWordASR = null
    }
  }

  /**
   * 停止指令ASR
   */
  stopCommandASR() {
    if (this.commandASR) {
      try {
        this.commandASR.stop()
        this.commandASR.destroyStream()
      } catch (error) {
        console.warn('停止指令ASR时出错:', error)
      }
      this.commandASR = null
    }
  }

  /**
   * 检查是否为有效语音结果
   */
  isValidSpeechResult(text) {
    if (!text || typeof text !== 'string') {
      console.log('❌ 语音验证失败: 输入为空或非字符串', { text, type: typeof text })
      return false
    }
    
    // 去除空格和换行
    const cleanText = text.trim()
    
    // 空文本
    if (cleanText.length === 0) {
      console.log('❌ 语音验证失败: 去除空格后为空文本')
      return false
    }
    
    // 检查是否包含有意义的字符（中文、英文、数字）
    const meaningfulChars = cleanText.match(/[\u4e00-\u9fa5a-zA-Z0-9]/g)
    if (!meaningfulChars || meaningfulChars.length === 0) {
      console.log('❌ 语音验证失败: 不包含有意义的字符', { cleanText })
      return false
    }
    
    // 放宽验证条件：只要包含一个有效字符就认为有效
    if (meaningfulChars.length >= 1) {
      console.log('✅ 语音验证通过:', { 
        cleanText, 
        meaningfulChars: meaningfulChars.length,
        sample: meaningfulChars.slice(0, 5).join('')
      })
      return true
    }
    
    // 检查是否包含太多重复字符（可能是噪音）- 提高阈值
    const repeatedPattern = /(.)\1{8,}/.test(cleanText)
    if (repeatedPattern) {
      console.log('❌ 语音验证失败: 包含过多重复字符，可能是噪音', { cleanText })
      return false
    }
    
    console.log('✅ 语音验证通过（默认）:', cleanText)
    return true
  }

  /**
   * 处理错误
   */
  handleError(message) {
    if (this.onError) {
      this.onError(message)
    }
  }

  /**
   * 更新状态
   */
  updateState(newState) {
    if (this.currentState !== newState) {
      console.log(`🔄 状态变化: ${this.currentState} → ${newState}`)
      this.currentState = newState
      
      if (this.onStateChange) {
        this.onStateChange(newState)
      }
    }
  }

  /**
   * 更新配置
   */
  updateConfig(newConfig) {
    console.log('⚙️ 更新配置:', newConfig)
    Object.assign(this.config, newConfig)
    
    // 记录TTS相关配置更新
    if (newConfig.enableTTS !== undefined) {
      console.log(`🔊 TTS功能已${newConfig.enableTTS ? '启用' : '禁用'}`)
    }
    if (newConfig.ttsVolume !== undefined) {
      console.log(`🔊 TTS音量设置为: ${newConfig.ttsVolume}`)
    }
    if (newConfig.ttsRate !== undefined) {
      console.log(`🔊 TTS语速设置为: ${newConfig.ttsRate}`)
    }
    if (newConfig.ttsLang !== undefined) {
      console.log(`🔊 TTS语言设置为: ${newConfig.ttsLang}`)
    }
  }

  /**
   * 确保TTS功能启用
   */
  ensureTTSEnabled() {
    if (!this.config.enableTTS) {
      console.log('🔧 强制启用TTS功能以确保语音回复')
      this.config.enableTTS = true
    }
    return this.config.enableTTS
  }

  /**
   * 检查TTS状态
   */
  getTTSStatus() {
    return {
      enabled: this.config.enableTTS,
      volume: this.config.ttsVolume,
      rate: this.config.ttsRate,
      lang: this.config.ttsLang,
      pitch: this.config.ttsPitch,
      isPlaying: this.isTTSPlaying,
      browserSupport: !!window.speechSynthesis
    }
  }

  /**
   * 获取状态信息
   */
  getStatus() {
    return {
      currentState: this.currentState,
      isEnabled: this.isEnabled,
      isWakeWordListening: this.isWakeWordListening,
      isCommandListening: this.isCommandListening,
      config: this.config,
      wakeWords: this.config.wakeWords
    }
  }

  /**
   * 处理文本输入（供外部调用，使用MCP功能） - 移除独立历史记录管理
   */
  async handleTextInput(text) {
    if (!text || !text.trim()) {
      console.warn('文本输入为空')
      return { success: false, message: '请输入有效的内容' }
    }

    console.log('📝 处理文本输入（使用MCP）:', text)
    
    try {
      // 如果正在语音回复，停止当前回复
      if (this.isTTSPlaying) {
        console.log('🛑 停止当前语音回复，处理新的文本输入')
        this.stopTTS()
        this.stopWakeWordDetectionDuringTTS()
        this.isTTSPlaying = false
      }
      
      this.updateState(CONVERSATION_STATES.PROCESSING)
      
      console.log('🤖 [MCP] 使用MCP处理文本输入:', text)
      
      // 构建消息历史
      const messages = [
        {
          role: 'user',
          content: text.trim()
        }
      ]

      // 发送MCP聊天请求
      console.log('📡 文本输入调用MCP API:', { text: text.trim() })
      const response = await sendMCPChatRequest(messages, {
        enableWebSearch: false, // 默认关闭联网搜索
        enableKnowledge: true, // 默认启用知识库搜索
        knowledgeLimit: 3,
        enableDeepSearch: false,
        searchMode: 'auto'
      })
      
      if (response && response.success) {
        console.log('🤖 文本输入获得MCP AI回复:', response.message)
        if (response.toolsUsed && response.toolsUsed.length > 0) {
          console.log('🔧 [MCP TOOLS] 文本输入使用了工具:', response.toolsUsed)
        }
        
        // 文本输入使用带唤醒词检测的语音回复
        await this.handleTextAIResponse(response.message)
        
        return { success: true, message: response.message, toolsUsed: response.toolsUsed }
      } else {
        console.error('❌ 文本输入MCP调用失败:', response.message)
        const errorMessage = response.message || '抱歉，我现在无法处理您的请求呢~'
        
        // 错误消息也用带唤醒词检测的语音回复
        await this.handleTextAIResponse(errorMessage)
        
        return { success: false, message: errorMessage }
      }
      
    } catch (error) {
      console.error('❌ MCP处理文本输入失败:', error)
      const errorMessage = '哎呀，出了点小问题呢~ 请稍后再试吧！'
      
      // 错误消息也用带唤醒词检测的语音回复
      await this.handleTextAIResponse(errorMessage)
      
      return { success: false, message: errorMessage }
    }
  }

  /**
   * 处理文本输入的AI回复（带唤醒词检测，支持打断）
   */
  async handleTextAIResponse(responseText) {
    console.log('🗣️ 处理文本输入AI回复:', responseText)
    
    this.updateState(CONVERSATION_STATES.RESPONDING)
    
    // 通知外部组件显示回复
    if (this.onResult) {
      this.onResult({
        type: 'ai_response',
        text: responseText,
        timestamp: Date.now(),
        fileReferences: [] // 文本输入暂不支持文件引用，保持一致性
      })
    }
    
    // 确保TTS功能启用（强制启用以确保语音回复）
    this.ensureTTSEnabled()
    console.log('🔊 开始文本输入的语音回复（带唤醒词检测）')
    
    await this.playTTSWithWakeWordDetection(responseText)
    console.log('✅ 文本输入的语音回复播放完成')
    
    // 如果智能语音已启用，回复完成后回到唤醒词监听模式
    if (this.isEnabled) {
      console.log('🔄 文本输入回复完成，回到唤醒词监听模式')
      this.backToWakeWordListening()
    } else {
      // 如果智能语音未启用，只是更新状态
      this.updateState(CONVERSATION_STATES.IDLE)
    }
  }

  /**
   * 测试大模型调用（用于调试）
   */
  async testChatAPI(testMessage = '你好，这是一个测试') {
    console.log('🧪 [TEST] 测试大模型调用:', testMessage)
    
    try {
      // 确保chatStore已初始化
      if (!this.chatStore) {
        console.log('🔄 [TEST] chatStore未初始化，尝试初始化...')
        await this.init()
      }
      
      console.log('📊 [TEST] 当前状态检查:', {
        chatStore: !!this.chatStore,
        hasMethod: !!(this.chatStore && this.chatStore.sendMessage)
      })
      
      // 直接调用chatStore.sendMessage
      const response = await this.chatStore.sendMessage(testMessage)
      
      console.log('📨 [TEST] 测试响应:', response)
      
      if (response && response.success) {
        console.log('✅ [TEST] 大模型调用成功:', response.message)
        // 测试语音回复（带唤醒词检测）
        await this.playTTSWithWakeWordDetection(response.message)
        return { success: true, message: response.message }
      } else {
        console.log('❌ [TEST] 大模型调用失败:', response)
        return { success: false, message: response?.message || '测试失败' }
      }
      
    } catch (error) {
      console.error('❌ [TEST] 测试大模型调用异常:', error)
      return { success: false, message: error.message }
    }
  }

  /**
   * 清理资源
   */
  cleanup() {
    console.log('🧹 清理智能对话管理器资源')
    
    try {
      // 停止对话管理器
      this.stop()
      
      // 清理所有ASR实例和检测器
      this.stopWakeWordDetection()
      this.stopCommandASR()
      this.stopWakeWordDetectionDuringTTS()
      
      // 清理 Sherpa-ONNX 检测器
      if (this.sherpaDetector) {
        try {
          console.log('🧹 清理 Sherpa-ONNX 检测器...')
          this.sherpaDetector.destroy()
          this.sherpaDetector = null
          console.log('✅ Sherpa-ONNX 检测器已清理')
        } catch (error) {
          console.warn('⚠️ 清理 Sherpa-ONNX 检测器时出错:', error)
        }
      }
      
      // 停止TTS播放
      this.stopTTS(true) // 强制停止
      
      // 清理所有定时器
      if (this.commandTimeout) {
        clearTimeout(this.commandTimeout)
        this.commandTimeout = null
      }
      
      if (this.wakeWordTimer) {
        clearTimeout(this.wakeWordTimer)
        this.wakeWordTimer = null
      }
      
      if (this.ttsKeepAliveInterval) {
        clearInterval(this.ttsKeepAliveInterval)
        this.ttsKeepAliveInterval = null
      }
      
      if (this.ttsTimeoutId) {
        clearTimeout(this.ttsTimeoutId)
        this.ttsTimeoutId = null
      }
      
      // 🔧 【增强】清理Web Audio资源
      if (this.audioContext) {
        try {
          this.audioContext.close()
          this.audioContext = null
          console.log('🧹 Web Audio上下文已清理')
        } catch (audioError) {
          console.warn('⚠️ 清理Web Audio上下文失败:', audioError)
        }
      }
      
      // 🔧 【增强】清理WASM模块
      if (window.Module) {
        try {
          window.Module = null
          console.log('🧹 WASM模块引用已清理')
        } catch (wasmError) {
          console.warn('⚠️ 清理WASM模块失败:', wasmError)
        }
      }
      
      // 清理状态
      this.currentSentence = ''
      this.finalResults = []
      this.isProcessingCommand = false
      this.isProcessingWakeWord = false
      this.isWakeWordListening = false
      this.isCommandListening = false
      this.isTTSPlaying = false
      this.isResponding = false
      this.voiceConversationHistory = []
      
      console.log('✅ 智能对话管理器资源清理完成')
      
    } catch (error) {
      console.error('❌ 清理智能对话管理器资源失败:', error)
    }
  }

  /**
   * 🧪 测试TTS打断功能
   */
  async testTTSInterrupt() {
    console.log('🧪 === 测试TTS打断功能 ===')
    
    try {
      // 1. 播放一段较长的TTS
      console.log('🧪 1. 开始播放测试TTS（约15秒钟）')
      const longText = '这是一段用于测试TTS打断功能的长文本。在播放期间，您可以说出唤醒词来打断播放并直接开始监听指令。请注意观察TTS是否能立即停止，以及是否能正确进入指令监听状态。此功能对于提高用户体验非常重要，确保用户可以随时打断AI的回复。'
      
      // 开始播放TTS
      this.playTTSWithWakeWordDetection(longText)
      
      console.log('🧪 TTS开始播放，现在您可以：')
      console.log('🧪 - 说出唤醒词（犇犇、奔奔、笨笨）来打断播放')
      console.log('🧪 - 观察TTS是否立即停止')
      console.log('🧪 - 检查是否直接进入指令监听状态')
      
      return {
        success: true,
        message: 'TTS打断测试已开始，请说出唤醒词进行测试'
      }
      
    } catch (error) {
      console.error('🧪 TTS打断测试失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 🧪 手动触发唤醒词检测（用于测试）
   */
  async testWakeWordDetection(testText = '犇犇') {
    console.log('🧪 === 手动触发唤醒词检测测试 ===')
    console.log('🧪 测试文本:', testText)
    
    try {
      if (this.isTTSPlaying) {
        console.log('🧪 当前正在播放TTS，测试打断功能...')
        await this.handleWakeWordDetectedDuringTTS(testText)
      } else {
        console.log('🧪 当前没有播放TTS，测试正常唤醒词检测...')
        await this.handleWakeWordDetected(testText)
      }
      
      return {
        success: true,
        message: '唤醒词检测测试完成'
      }
      
    } catch (error) {
      console.error('🧪 唤醒词检测测试失败:', error)
      return {
        success: false,
        error: error.message
      }
    }
  }

  /**
   * 停止唤醒词检测
   */
  async stopWakeWordDetection() {
    if (this.config.wakeWordEngine === 'sherpa-onnx') {
      // 停止 Sherpa-ONNX 检测器
      if (this.sherpaDetector) {
        try {
          await this.sherpaDetector.stopListening()
          console.log('✅ Sherpa-ONNX 检测器已停止')
        } catch (error) {
          console.warn('停止 Sherpa-ONNX 检测器时出错:', error)
        }
      }
    } else {
      // 停止腾讯云ASR检测器
      this.stopWakeWordASR()
    }
  }

  /**
   * 确保音频已解锁（在播放TTS前调用）
   * 在某些浏览器中，音频上下文需要用户交互才能解锁
   */
  ensureAudioUnlocked() {
    try {
      // 创建一个短暂的音频元素并立即播放 - 使用Blob URL
      const wavBase64 = 'UklGRigAAABXQVZFZm10IBIAAAABAAEARKwAAIhYAQACABAAAABkYXRhAgAAAAEA'
      const byteCharacters = atob(wavBase64)
      const byteNumbers = new Array(byteCharacters.length)
      for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i)
      }
      const byteArray = new Uint8Array(byteNumbers)
      const blob = new Blob([byteArray], { type: 'audio/wav' })
      const blobUrl = URL.createObjectURL(blob)
      
      const audioTest = new Audio(blobUrl)
      audioTest.volume = 0.01  // 非常小的音量
      
      // 尝试播放，如果成功则表示音频上下文已解锁
      const playPromise = audioTest.play()
      
      // 现代浏览器中play()返回Promise
      if (playPromise && playPromise.then) {
        playPromise.then(() => {
          try {
            setTimeout(() => {
              try {
                audioTest.pause()
                audioTest.remove()
                // 释放Blob URL
                URL.revokeObjectURL(blobUrl)
                console.log('✅ 音频预播放成功，TTS应该能正常发声')
              } catch (cleanupError) {
                console.warn('⚠️ 音频清理失败:', cleanupError)
                URL.revokeObjectURL(blobUrl)
              }
            }, 10)
          } catch (timeoutError) {
            console.warn('⚠️ 音频播放超时处理失败:', timeoutError)
            URL.revokeObjectURL(blobUrl)
          }
        }).catch(err => {
          try {
            URL.revokeObjectURL(blobUrl)
            console.warn('⚠️ 音频预播放失败，TTS可能没有声音:', err)
            
            // 尝试使用一个超短的 Web Audio 空音频
            try {
              const audioContext = new (window.AudioContext || window.webkitAudioContext)()
              const oscillator = audioContext.createOscillator()
              oscillator.frequency.setValueAtTime(0, audioContext.currentTime) // 0Hz = 静音
              oscillator.connect(audioContext.destination)
              oscillator.start(0)
              oscillator.stop(audioContext.currentTime + 0.001) // 播放 1ms
              console.log('✅ 使用Web Audio API尝试解锁音频')
            } catch (webAudioError) {
              console.warn('⚠️ Web Audio API解锁失败:', webAudioError)
            }
          } catch (fallbackError) {
            console.warn('⚠️ 音频解锁回退方案失败:', fallbackError)
          }
        })
      }
    } catch (error) {
      console.warn('⚠️ 音频预解锁尝试失败:', error)
    }
  }
}

// 🧪 将测试方法暴露到全局，便于调试
if (typeof window !== 'undefined') {
  window.testTTSInterrupt = function() {
    if (window.conversationManager) {
      return window.conversationManager.testTTSInterrupt()
    } else {
      console.error('🧪 conversationManager 未初始化')
      return { success: false, error: 'conversationManager 未初始化' }
    }
  }

  window.testWakeWordDetection = function(testText = '犇犇') {
    if (window.conversationManager) {
      return window.conversationManager.testWakeWordDetection(testText)
    } else {
      console.error('🧪 conversationManager 未初始化')
      return { success: false, error: 'conversationManager 未初始化' }
    }
  }

  window.getTTSStatus = function() {
    if (window.conversationManager) {
      return {
        isTTSPlaying: window.conversationManager.isTTSPlaying,
        isEnabled: window.conversationManager.isEnabled,
        currentState: window.conversationManager.currentState,
        speechSynthesisSpeaking: window.speechSynthesis?.speaking || false
      }
    } else {
      return { error: 'conversationManager 未初始化' }
    }
  }

  console.log('🧪 TTS打断测试工具已加载，可使用以下命令：')
  console.log('🧪 - testTTSInterrupt(): 开始TTS打断测试')
  console.log('🧪 - testWakeWordDetection("犇犇"): 手动触发唤醒词检测')
  console.log('🧪 - getTTSStatus(): 获取TTS状态信息')
} 
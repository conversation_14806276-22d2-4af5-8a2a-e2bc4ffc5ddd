<template>
  <div class="version-check-container">
    <div class="version-check-box">
      <div class="logo-section">
        <img :src="logoUrl" alt="Nezha" class="logo" />
        <h1 class="title">犇犇数字员工助手</h1>
        <p class="subtitle">版本检查中...</p>
      </div>

      <!-- 版本检查状态 -->
      <div v-if="checking" class="checking-section">
        <div class="loading-spinner"></div>
        <p class="checking-text">正在检查版本更新...</p>
        <button v-if="!isMandatoryUpdate" @click="forceComplete" class="force-complete-btn">
          跳过检查
        </button>
      </div>

      <!-- 版本更新提示 -->
      <div v-else-if="showUpdateDialog" class="update-section">
        <div class="update-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M12 2L13.09 8.26L20 9L13.09 9.74L12 16L10.91 9.74L4 9L10.91 8.26L12 2Z" fill="#667eea"/>
          </svg>
        </div>
        
        <h2 class="update-title">发现新版本</h2>
        <p class="update-description">
          当前版本：<span class="current-version">{{ formattedCurrentVersion }}</span><br>
          最新版本：<span class="new-version">{{ formattedLatestVersion }}</span>
        </p>
        
        <div class="update-actions">
          <!-- 强制升级 -->
          <div v-if="isMandatoryUpdate" class="mandatory-update">
            <p class="mandatory-text">此版本为强制更新，请立即升级</p>
            <button @click="handleUpdateClick" class="update-btn mandatory-btn">
              立即升级
            </button>
          </div>
          
          <!-- 非强制升级 -->
          <div v-else class="optional-update">
            <button @click="handleUpdateClick" class="update-btn primary-btn">
              立即升级
            </button>
            <button 
              @click="handleSkipUpdate" 
              :disabled="isSkipButtonLoading"
              class="update-btn secondary-btn"
              :class="{ 'loading2': isSkipButtonLoading }"
            >
              {{ isSkipButtonLoading ? '加载中...' : '下次升级' }}
            </button>
          </div>
        </div>
      </div>

      <!-- 错误状态 -->
      <div v-else-if="error" class="error-section">
        <div class="error-icon">⚠️</div>
        <h2 class="error-title">版本检查失败</h2>
        <p class="error-description">{{ error }}</p>
        <button @click="retryCheck" class="retry-btn">
          重试
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useVersionCheck } from '../composables/useVersionCheck.js'
import logoUrl from '/assets/logo.png?url'

// 使用版本检查 composable
const {
  checking,
  error,
  hasUpdate,
  isMandatoryUpdate,
  formattedCurrentVersion,
  formattedLatestVersion,
  checkVersion,
  handleUpdate
} = useVersionCheck()

// 本地状态
const showUpdateDialog = ref(false)
const isSkipButtonLoading = ref(false) // 添加跳过按钮加载状态

// 监听版本检查结果
watch([hasUpdate, error, checking], async ([updateAvailable, hasError, isChecking]) => {
  console.log('VersionCheck 状态变化:', { updateAvailable, hasError, isChecking })
  
  if (hasError) {
    // 有错误时显示错误状态
    showUpdateDialog.value = false
  } else if (updateAvailable) {
    // 有更新时显示更新对话框，阻止登录流程
    showUpdateDialog.value = true
    console.log('🔄 发现版本更新，阻止登录流程')
    // 通知父组件停止超时计时，因为需要用户交互
    emit('version-update-found', { isMandatory: isMandatoryUpdate.value })
  } else if (!isChecking && !updateAvailable && !hasError) {
    // 检查完成且无更新时，才允许检查登录状态
    console.log('VersionCheck 检查完成，无更新，开始检查登录状态')
    
    // 导入authStore并检查登录状态
    const { useAuthStore } = await import('../stores/auth.js')
    const authStore = useAuthStore()
    await authStore.checkLoginStatus()
    
    emit('version-check-complete', { needsUpdate: false, noUpdate: true })
  }
}, { immediate: false })

// 跳过升级
const handleSkipUpdate = async () => {
  console.log('🔄 用户选择跳过升级，开始检查登录状态')
  
  // 立即禁用按钮并显示加载状态
  isSkipButtonLoading.value = true
  try {
    // 导入authStore并检查登录状态
    const { useAuthStore } = await import('../stores/auth.js')
    const authStore = useAuthStore()
    await authStore.checkLoginStatus()
    
    emit('version-check-complete', { needsUpdate: false, skipped: true })
  } catch (error) {
    console.error('跳过升级时发生错误:', error)
    // 发生错误时恢复按钮状态
    isSkipButtonLoading.value = false
  }
}

// 重试检查
const retryCheck = () => {
  checkVersion()
}

// 处理升级点击
const handleUpdateClick = () => {
  console.log('🔄 用户点击立即升级，打开下载链接')
  handleUpdate()
  // 注意：不触发登录流程，用户需要手动下载并安装新版本
}

// 强制完成检查（仅在非强制升级时可用）
const forceComplete = async () => {
  if (isMandatoryUpdate.value) {
    console.log('🔄 强制升级，不允许跳过')
    return
  }
  
  console.log('🔄 强制完成版本检查，开始检查登录状态')
  
  // 导入authStore并检查登录状态
  const { useAuthStore } = await import('../stores/auth.js')
  const authStore = useAuthStore()
  await authStore.checkLoginStatus()
  
  emit('version-check-complete', { needsUpdate: false, forced: true })
}

// 定义事件
const emit = defineEmits(['version-check-complete', 'version-update-found'])

// 定义props
const props = defineProps({
  autoCheck: {
    type: Boolean,
    default: false
  }
})

// 组件挂载时开始检查（仅在autoCheck为true时）
onMounted(() => {
  if (props.autoCheck) {
    checkVersion()
  }
})
</script>

<style lang="scss" scoped>
.version-check-container {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100vh;
  padding: 20px;
}

.version-check-box {
  background: rgba(255, 255, 255, 0.95);
  border-radius: 20px;
  padding: 30px;
  width: 100%;
  max-width: 500px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(10px);
  text-align: center;
}

.logo-section {
  margin-bottom: 30px;

  .logo {
    width: 80px;
    height: 80px;
    margin-bottom: 20px;
    border-radius: 50%;
  }

  .title {
    font-size: 28px;
    font-weight: 600;
    color: #333;
    margin: 0 0 10px 0;
  }

  .subtitle {
    color: #666;
    font-size: 14px;
    margin: 0;
  }
}

.checking-section {
  .loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    margin: 0 auto 20px;
  }

  .checking-text {
    color: #666;
    font-size: 16px;
    margin: 0 0 20px 0;
  }

  .force-complete-btn {
    padding: 8px 16px;
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e1e5e9;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      background: #e9ecef;
    }
  }
}

.update-section {
  .update-icon {
    margin-bottom: 20px;
  }

  .update-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px 0;
  }

  .update-description {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin-bottom: 30px;

    .current-version {
      color: #999;
    }

    .new-version {
      color: #667eea;
      font-weight: 600;
    }
  }

  .update-actions {
    .mandatory-update {
      .mandatory-text {
        color: #e74c3c;
        font-size: 14px;
        margin-bottom: 20px;
        font-weight: 500;
      }
    }

    .optional-update {
      display: flex;
      gap: 15px;
      justify-content: center;
    }
  }
}

.update-btn {
  padding: 12px 24px;
  border: none;
  border-radius: 10px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 120px;

  &:hover {
    transform: translateY(-2px);
  }

  &.mandatory-btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;

    &:hover {
      box-shadow: 0 10px 20px rgba(231, 76, 60, 0.3);
    }
  }

  &.primary-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;

    &:hover {
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
  }

  &.secondary-btn {
    background: #f8f9fa;
    color: #666;
    border: 2px solid #e1e5e9;

    &:hover {
      background: #e9ecef;
      box-shadow: 0 10px 20px rgba(0, 0, 0, 0.1);
    }

    &.loading2 {
      background: #e9ecef;
      color: #999;
      cursor: not-allowed;
      opacity: 0.7;

      &:hover {
        background: #e9ecef;
        box-shadow: none;
        transform: none;
      }
    }

    &:disabled {
      cursor: not-allowed;
      opacity: 0.7;
    }
  }
}

.error-section {
  .error-icon {
    font-size: 48px;
    margin-bottom: 20px;
  }

  .error-title {
    font-size: 24px;
    font-weight: 600;
    color: #333;
    margin: 0 0 15px 0;
  }

  .error-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 30px;
  }

  .retry-btn {
    padding: 12px 24px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border: none;
    border-radius: 10px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
    }
  }
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style> 
' findFreeSlots.vbs - 查找Outlook日历中的空闲时间段
Option Explicit

' Include utility functions
Dim fso, scriptDir
Set fso = CreateObject("Scripting.FileSystemObject")
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
ExecuteGlobal fso.OpenTextFile(fso.BuildPath(scriptDir, "utils.vbs"), 1).ReadAll

' Main function
Sub Main()
    ' Get command line arguments
    Dim startDateStr, endDateStr, durationStr, workDayStartStr, workDayEndStr, calendarName
    Dim startDate, endDate, duration, workDayStart, workDayEnd
    
    ' Get arguments
    startDateStr = GetArgument("startDate")
    endDateStr = GetArgument("endDate")
    durationStr = GetArgument("duration")
    workDayStartStr = GetArgument("workDayStart")
    workDayEndStr = GetArgument("workDayEnd")
    calendarName = GetArgument("calendar")
    
    ' Require start date
    RequireArgument "startDate"
    
    ' Parse start date
    startDate = ParseDate(startDateStr)
    
    ' Parse end date (if not provided, default to 7 days from start)
    If endDateStr = "" Then
        endDate = DateAdd("d", 7, startDate)
    Else
        endDate = ParseDate(endDateStr)
    End If
    
    ' Parse duration (default to 30 minutes)
    If durationStr = "" Then
        duration = 30
    Else
        If IsNumeric(durationStr) Then
            duration = CInt(durationStr)
        Else
            OutputError "Invalid duration format. Expected number of minutes: " & durationStr
            WScript.Quit 1
        End If
    End If
    
    ' Parse work day hours (default 9 AM to 5 PM)
    If workDayStartStr = "" Then
        workDayStart = 9
    Else
        If IsNumeric(workDayStartStr) Then
            workDayStart = CInt(workDayStartStr)
        Else
            OutputError "Invalid work day start hour. Expected 0-23: " & workDayStartStr
            WScript.Quit 1
        End If
    End If
    
    If workDayEndStr = "" Then
        workDayEnd = 17
    Else
        If IsNumeric(workDayEndStr) Then
            workDayEnd = CInt(workDayEndStr)
        Else
            OutputError "Invalid work day end hour. Expected 0-23: " & workDayEndStr
            WScript.Quit 1
        End If
    End If
    
    ' Validate parameters
    If endDate < startDate Then
        OutputError "End date cannot be before start date"
        WScript.Quit 1
    End If
    
    If duration <= 0 Then
        OutputError "Duration must be greater than 0"
        WScript.Quit 1
    End If
    
    If workDayStart < 0 Or workDayStart > 23 Then
        OutputError "Work day start hour must be between 0 and 23"
        WScript.Quit 1
    End If
    
    If workDayEnd < 0 Or workDayEnd > 23 Then
        OutputError "Work day end hour must be between 0 and 23"
        WScript.Quit 1
    End If
    
    If workDayEnd <= workDayStart Then
        OutputError "Work day end hour must be after start hour"
        WScript.Quit 1
    End If
    
    ' Find free slots
    Dim freeSlotsJSON
    freeSlotsJSON = FindAvailableSlots(startDate, endDate, duration, workDayStart, workDayEnd, calendarName)
    
    ' Output success with the free slots
    OutputSuccess freeSlotsJSON
End Sub

' Finds available time slots in the specified date range
Function FindAvailableSlots(startDate, endDate, duration, workDayStart, workDayEnd, calendarName)
    On Error Resume Next
    
    ' Create Outlook objects
    Dim outlookApp, calendar, items, appointment, i
    Dim filteredItems, busyTimes(), currentDate, currentTime
    Dim freeSlots, slotStart, slotEnd, isSlotFree, j
    
    ' Create Outlook application
    Set outlookApp = CreateOutlookApplication()
    
    ' Get calendar folder
    If calendarName = "" Then
        Set calendar = GetDefaultCalendar(outlookApp)
    Else
        Set calendar = GetCalendarByName(outlookApp, calendarName)
    End If
    
    ' Get all items from calendar in the date range
    Set items = calendar.Items
    items.Sort "[Start]"
    items.IncludeRecurrences = True
    
    ' Filter items by date range
    Dim filter
    filter = "[Start] >= '" & startDate & "' AND [Start] < '" & DateAdd("d", 1, endDate) & "'"
    Set filteredItems = items.Restrict(filter)
    
    If Err.Number <> 0 Then
        OutputError "Failed to retrieve calendar events: " & Err.Description
        WScript.Quit 1
    End If
    
    ' Collect busy times
    ReDim busyTimes(filteredItems.Count - 1, 1)
    For i = 1 To filteredItems.Count
        Set appointment = filteredItems.Item(i)
        busyTimes(i - 1, 0) = appointment.Start
        busyTimes(i - 1, 1) = appointment.End
    Next
    
    ' Find free slots
    freeSlots = "["
    currentDate = startDate
    
    Do While currentDate <= endDate
        ' Check each potential time slot in the work day
        currentTime = DateAdd("h", workDayStart, Int(currentDate))
        
        Do While Hour(currentTime) < workDayEnd
            slotStart = currentTime
            slotEnd = DateAdd("n", duration, slotStart)
            
            ' Make sure slot doesn't go past work day end
            If Hour(slotEnd) > workDayEnd Or (Hour(slotEnd) = workDayEnd And Minute(slotEnd) > 0) Then
                Exit Do
            End If
            
            ' Check if this slot conflicts with any busy time
            isSlotFree = True
            For j = 0 To UBound(busyTimes)
                If busyTimes(j, 0) < slotEnd And busyTimes(j, 1) > slotStart Then
                    isSlotFree = False
                    Exit For
                End If
            Next
            
            ' If slot is free, add it to results
            If isSlotFree Then
                If Len(freeSlots) > 1 Then freeSlots = freeSlots & ","
                freeSlots = freeSlots & "{"
                freeSlots = freeSlots & """date"":""" & FormatDate(slotStart) & ""","
                freeSlots = freeSlots & """startTime"":""" & FormatTime(slotStart) & ""","
                freeSlots = freeSlots & """endTime"":""" & FormatTime(slotEnd) & ""","
                freeSlots = freeSlots & """duration"":" & duration & ","
                freeSlots = freeSlots & """dayOfWeek"":""" & WeekdayName(Weekday(slotStart)) & """"
                freeSlots = freeSlots & "}"
            End If
            
            ' Move to next potential slot (15-minute increments)
            currentTime = DateAdd("n", 15, currentTime)
        Loop
        
        ' Move to next day
        currentDate = DateAdd("d", 1, currentDate)
    Loop
    
    freeSlots = freeSlots & "]"
    
    ' Clean up
    Set filteredItems = Nothing
    Set items = Nothing
    Set calendar = Nothing
    Set outlookApp = Nothing
    
    FindAvailableSlots = freeSlots
End Function

' Run the main function
Main 
// 主题颜色
$primary-color: #667eea;
$secondary-color: #764ba2;
$accent-color: #f093fb;

// 状态颜色
$success-color: #2ecc71;
$warning-color: #f39c12;
$error-color: #e74c3c;
$info-color: #3498db;

// 中性颜色
$white: #ffffff;
$black: #000000;
$gray-50: #f9fafb;
$gray-100: #f3f4f6;
$gray-200: #e5e7eb;
$gray-300: #d1d5db;
$gray-400: #9ca3af;
$gray-500: #6b7280;
$gray-600: #4b5563;
$gray-700: #374151;
$gray-800: #1f2937;
$gray-900: #111827;

// 文本颜色
$text-primary: #333333;
$text-secondary: #666666;
$text-muted: #999999;
$text-disabled: #cccccc;

// 背景颜色
$background-primary: #ffffff;
$background-secondary: #f8f9fa;
$background-muted: #e9ecef;

// 边框颜色
$border-light: #e1e5e9;
$border-medium: #d1d5db;
$border-dark: #9ca3af;

// 阴影
$shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
$shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
$shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1);
$shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.1);

// 边框半径
$border-radius-sm: 4px;
$border-radius-md: 8px;
$border-radius-lg: 12px;
$border-radius-xl: 16px;
$border-radius-full: 50%;

// 间距
$spacing-xs: 0.25rem;  // 4px
$spacing-sm: 0.5rem;   // 8px
$spacing-md: 1rem;     // 16px
$spacing-lg: 1.5rem;   // 24px
$spacing-xl: 2rem;     // 32px
$spacing-2xl: 3rem;    // 48px

// 字体大小
$font-size-xs: 0.75rem;    // 12px
$font-size-sm: 0.875rem;   // 14px
$font-size-base: 1rem;     // 16px
$font-size-lg: 1.125rem;   // 18px
$font-size-xl: 1.25rem;    // 20px
$font-size-2xl: 1.5rem;    // 24px
$font-size-3xl: 1.875rem;  // 30px
$font-size-4xl: 2.25rem;   // 36px

// 字体粗细
$font-weight-normal: 400;
$font-weight-medium: 500;
$font-weight-semibold: 600;
$font-weight-bold: 700;

// 行高
$line-height-tight: 1.25;
$line-height-normal: 1.5;
$line-height-relaxed: 1.75;

// 断点
$breakpoint-sm: 640px;
$breakpoint-md: 768px;
$breakpoint-lg: 1024px;
$breakpoint-xl: 1280px;

// Z-index层级
$z-dropdown: 1000;
$z-sticky: 1020;
$z-fixed: 1030;
$z-modal-backdrop: 1040;
$z-modal: 1050;
$z-popover: 1060;
$z-tooltip: 1070;

// 过渡动画
$transition-base: all 0.3s ease;
$transition-fast: all 0.15s ease;
$transition-slow: all 0.6s ease;

// 渐变色
$gradient-primary: linear-gradient(135deg, $primary-color 0%, $secondary-color 100%);
$gradient-warm: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
$gradient-cool: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
$gradient-green: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);

// 特殊效果
$backdrop-blur: blur(10px);
$glass-background: rgba(255, 255, 255, 0.95);
$glass-border: rgba(255, 255, 255, 0.2); 
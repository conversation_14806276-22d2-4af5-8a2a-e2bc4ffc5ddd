/*
 * [js-sha1]
 *
 * @version 0.6.0
 * @copyright H, J-C 2018-9-28
 * @license MIT
 */

var CryptoJS = CryptoJS || function (g, l) {
	var e = {}, d = e.lib = {}, m = function () { }, k = d.Base = {
		extend: function (a) {
			m.prototype = this;
			var c = new m;
			a && c.mixIn(a);
			c.hasOwnProperty("init") || (c.init = function () {
			c.$super.init.apply(this, arguments)
			});
			c.init.prototype = c;
			c.$super = this;
			return c
		},
		create: function () {
			var a = this.extend();
			a.init.apply(a, arguments);
			return a
		},
		init: function () { },
		mixIn: function (a) {
			for (var c in a) a.hasOwnProperty(c) && (this[c] = a[c]);
			a.hasOwnProperty("toString") && (this.toString = a.toString)
		},
		clone: function () {
			return this.init.prototype.extend(this)
		}
	},
	p = d.WordArray = k.extend({
		init: function (a, c) {
			a = this.words = a || [];
			this.sigBytes = c != l ? c : 4 * a.length
		},
		toString: function (a) {
		  	return (a || n).stringify(this)
		},
		concat: function (a) {
			var c = this.words,
				q = a.words,
				f = this.sigBytes;
			a = a.sigBytes;
			this.clamp();
			if (f % 4)
				for (var b = 0; b < a; b++) c[f + b >>> 2] |= (q[b >>> 2] >>> 24 - 8 * (b % 4) & 255) << 24 - 8 * ((f + b) % 4);
			else if (65535 < q.length)
				for (b = 0; b < a; b += 4) c[f + b >>> 2] = q[b >>> 2];
			else c.push.apply(c, q);
			this.sigBytes += a;
			return this
		},
		clamp: function () {
			var a = this.words,
			c = this.sigBytes;
			a[c >>> 2] &= 4294967295 << 32 - 8 * (c % 4);
			a.length = g.ceil(c / 4)
		},
		clone: function () {
			var a = k.clone.call(this);
			a.words = this.words.slice(0);
			return a
		},
		random: function (a) {
			for (var c = [], b = 0; b < a; b += 4) c.push(4294967296 * g.random() | 0);
			return new p.init(c, a)
		}
	}),
	b = e.enc = {}, n = b.Hex = {
		stringify: function (a) {
			var c = a.words;
			a = a.sigBytes;
			for (var b = [], f = 0; f < a; f++) {
				var d = c[f >>> 2] >>> 24 - 8 * (f % 4) & 255;
				b.push((d >>> 4).toString(16));
				b.push((d & 15).toString(16))
			}
			return b.join("")
		},
		parse: function (a) {
			for (var c = a.length, b = [], f = 0; f < c; f += 2) b[f >>> 3] |= parseInt(a.substr(f, 2), 16) << 24 - 4 * (f % 8);
			return new p.init(b, c / 2)
		}
	}, j = b.Latin1 = {
		stringify: function (a) {
		  var c = a.words;
		  a = a.sigBytes;
		  for (var b = [], f = 0; f < a; f++) b.push(String.fromCharCode(c[f >>> 2] >>> 24 - 8 * (f % 4) & 255));
		  return b.join("")
		},
		parse: function (a) {
		  for (var c = a.length, b = [], f = 0; f < c; f++) b[f >>> 2] |= (a.charCodeAt(f) & 255) << 24 - 8 * (f % 4);
		  return new p.init(b, c)
		}
	}, h = b.Utf8 = {
		stringify: function (a) {
			try {
				return decodeURIComponent(escape(j.stringify(a)))
			} catch (c) {
				throw Error("Malformed UTF-8 data");
			}
		},
		parse: function (a) {
		  	return j.parse(unescape(encodeURIComponent(a)))
		}
	}, t = b.Base64 = {
		stringify: function (a) {
			var c = a.words,
				b = a.sigBytes,
				d = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";
			a = [];
			for (var f = 0; f < b; f += 3)
				for (var g = (c[f >>> 2] >>> 24 - 8 * (f % 4) & 255) << 16 | (c[f + 1 >>> 2] >>> 24 - 8 * ((f + 1) % 4) & 255) << 8 | c[f + 2 >>> 2] >>> 24 - 8 * ((f + 2) % 4) & 255, e = 0; 4 > e && f + 0.75 * e < b; e++) a.push(d.charAt(g >>> 6 * (3 - e) & 63));
			if (c = d.charAt(64))
				for (; a.length % 4;) a.push(c);
			return a.join("")
		},
		parse: function (a) {
			var c = a.length,
				b = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=",
				d = [];
			for (var f = 0; f < c; f++)
				if ("=" != a.charAt(f)) {
					var g = b.indexOf(a.charAt(f));
					if (-1 != g) {
						var e = d.length % 4;
						if (0 == e) d.push(g << 2);
						else if (1 == e) d[d.length - 1] |= g >>> 4, d.push((g & 15) << 4);
						else if (2 == e) d[d.length - 1] |= g >>> 2, d.push((g & 3) << 6);
						else if (3 == e) d[d.length - 1] |= g
					}
				}
			for (f = c = 0; f < d.length; f++) c += String.fromCharCode(d[f]);
			return j.parse(c)
		}
	},
	r = d.BufferedBlockAlgorithm = k.extend({
		reset: function () {
			this._data = new p.init;
			this._nDataBytes = 0
		},
		_append: function (a) {
			"string" == typeof a && (a = h.parse(a));
			this._data.concat(a);
			this._nDataBytes += a.sigBytes
		},
		_process: function (a) {
			var c = this._data,
			b = c.words,
			f = c.sigBytes,
			d = this.blockSize,
			e = f / (4 * d),
			e = a ? g.ceil(e) : g.max((e | 0) - this._minBufferSize, 0);
			a = e * d;
			f = g.min(4 * a, f);
			if (a) {
				for (var k = 0; k < a; k += d) this._doProcessBlock(b, k);
				k = b.splice(0, a);
				c.sigBytes -= f
			}
			return new p.init(k, f)
		},
		clone: function () {
			var a = k.clone.call(this);
			a._data = this._data.clone();
			return a
		},
		_minBufferSize: 0
	});
	d.Hasher = r.extend({
		cfg: k.extend(),
		init: function (a) {
			this.cfg = this.cfg.extend(a);
			this.reset()
		},
		reset: function () {
			r.reset.call(this);
			this._doReset()
		},
		update: function (a) {
			this._append(a);
			this._process();
			return this
		},
		finalize: function (a) {
			a && this._append(a);
			return this._doFinalize()
		},
		blockSize: 16,
		_createHelper: function (a) {
			return function (b, d) {
			return (new a.init(d)).finalize(b)
			}
		},
		_createHmacHelper: function (a) {
			return function (b, d) {
			return (new s.HMAC.init(a, d)).finalize(b)
			}
		}
	});
	var s = e.algo = {};
	return e
}(Math);

(function () {
	var g = CryptoJS,
		l = g.lib,
		e = l.WordArray,
		d = l.Hasher,
		m = [],
		l = g.algo.SHA1 = d.extend({
			_doReset: function () {
				this._hash = new e.init([1732584193, 4023233417, 2562383102, 271733878, 3285377520])
			},
			_doProcessBlock: function (d, e) {
				for (var b = this._hash.words, n = b[0], j = b[1], h = b[2], g = b[3], l = b[4], a = 0; 80 > a; a++) {
					if (16 > a) m[a] = d[e + a] | 0;
					else {
					var c = m[a - 3] ^ m[a - 8] ^ m[a - 14] ^ m[a - 16];
					m[a] = c << 1 | c >>> 31
					}
					c = (n << 5 | n >>> 27) + l + m[a];
					c = 20 > a ? c + ((j & h | ~j & g) + 1518500249) : 40 > a ? c + ((j ^ h ^ g) + 1859775393) : 60 > a ? c + ((j & h | j & g | h & g) - 1894007588) : c + ((j ^ h ^ g) - 899497514);
					l = g;
					g = h;
					h = j << 30 | j >>> 2;
					j = n;
					n = c
				}
				b[0] = b[0] + n | 0;
				b[1] = b[1] + j | 0;
				b[2] = b[2] + h | 0;
				b[3] = b[3] + g | 0;
				b[4] = b[4] + l | 0
			},
			_doFinalize: function () {
				var d = this._data,
					e = d.words,
					b = 8 * this._nDataBytes,
					g = 8 * d.sigBytes;
				e[g >>> 5] |= 128 << 24 - g % 32;
				e[(g + 64 >>> 9 << 4) + 14] = Math.floor(b / 4294967296);
				e[(g + 64 >>> 9 << 4) + 15] = b;
				d.sigBytes = 4 * e.length;
				this._process();
				return this._hash
			},
			clone: function () {
				var e = d.clone.call(this);
				e._hash = this._hash.clone();
				return e
			}
	});
	g.SHA1 = d._createHelper(l);
	g.HmacSHA1 = d._createHmacHelper(l)
})();

// 添加SHA256支持
(function (Math) {
	var g = CryptoJS,
		l = g.lib,
		e = l.WordArray,
		d = l.Hasher,
		m = [],
		l = g.algo.SHA256 = d.extend({
			_doReset: function () {
				this._hash = new e.init([1779033703, 3144134277, 1013904242, 2773480762, 1359893119, 2600822924, 528734635, 1541459225]);
			},
			_doProcessBlock: function (d, e) {
				for (var b = this._hash.words, n = b[0], j = b[1], h = b[2], a = b[3], c = b[4], g = b[5], f = b[6], k = b[7], l = 0; 64 > l; l++) {
					if (16 > l) m[l] = d[e + l] | 0;
					else {
						var p = m[l - 15],
							q = m[l - 2];
						m[l] = ((p << 25 | p >>> 7) ^ (p << 14 | p >>> 18) ^ p >>> 3) + m[l - 7] + ((q << 15 | q >>> 17) ^ (q << 13 | q >>> 19) ^ q >>> 10) + m[l - 16]
					}
					p = k + ((c << 26 | c >>> 6) ^ (c << 21 | c >>> 11) ^ (c << 7 | c >>> 25)) + (c & g ^ ~c & f) + [1116352408, 1899447441, 3049323471, 3921009573, 961987163, 1508970993, 2453635748, 2870763221, 3624381080, 310598401, 607225278, 1426881987, 1925078388, 2162078206, 2614888103, 3248222580, 3835390401, 4022224774, 264347078, 604807628, 770255983, 1249150122, 1555081692, 1996064986, 2554220882, 2821834349, 2952996808, 3210313671, 3336571891, 3584528711, 113926993, 338241895, 666307205, 773529912, 1294757372, 1396182291, 1695183700, 1986661051, 2177026350, 2456956037, 2730485921, 2820302411, 3259730800, 3345764771, 3516065817, 3600352804, 4094571909, 275423344, 430227734, 506948616, 659060556, 883997877, 958139571, 1322822218, 1537002063, 1747873779, 1955562222, 2024104815, 2227730452, 2361852424, 2428436474, 2756734187, 3204031479, 3329325298][l] + m[l];
					q = ((n << 30 | n >>> 2) ^ (n << 19 | n >>> 13) ^ (n << 10 | n >>> 22)) + (n & j ^ n & h ^ j & h);
					k = f;
					f = g;
					g = c;
					c = a + p | 0;
					a = h;
					h = j;
					j = n;
					n = p + q | 0
				}
				b[0] = b[0] + n | 0;
				b[1] = b[1] + j | 0;
				b[2] = b[2] + h | 0;
				b[3] = b[3] + a | 0;
				b[4] = b[4] + c | 0;
				b[5] = b[5] + g | 0;
				b[6] = b[6] + f | 0;
				b[7] = b[7] + k | 0
			},
			_doFinalize: function () {
				var d = this._data,
					e = d.words,
					b = 8 * this._nDataBytes,
					g = 8 * d.sigBytes;
				e[g >>> 5] |= 128 << 24 - g % 32;
				e[(g + 64 >>> 9 << 4) + 14] = Math.floor(b / 4294967296);
				e[(g + 64 >>> 9 << 4) + 15] = b;
				d.sigBytes = 4 * e.length;
				this._process();
				return this._hash
			},
			clone: function () {
				var e = d.clone.call(this);
				e._hash = this._hash.clone();
				return e
			}
		});
	g.SHA256 = d._createHelper(l);
	g.HmacSHA256 = d._createHmacHelper(l)
})(Math);

(function () {
	var g = CryptoJS,
		l = g.enc.Utf8;
		g.algo.HMAC = g.lib.Base.extend({
		init: function (e, d) {
			e = this._hasher = new e.init;
			"string" == typeof d && (d = l.parse(d));
			var g = e.blockSize,
			k = 4 * g;
			d.sigBytes > k && (d = e.finalize(d));
			d.clamp();
			for (var p = this._oKey = d.clone(), b = this._iKey = d.clone(), n = p.words, j = b.words, h = 0; h < g; h++) n[h] ^= 1549556828, j[h] ^= 909522486;
			p.sigBytes = b.sigBytes = k;
			this.reset()
		},
		reset: function () {
			var e = this._hasher;
			e.reset();
			e.update(this._iKey)
		},
		update: function (e) {
			this._hasher.update(e);
			return this
		},
		finalize: function (e) {
			var d = this._hasher;
			e = d.finalize(e);
			d.reset();
			return d.finalize(this._oKey.clone().concat(e))
		}
	})
})();
  
  //使用算法
  // var key = "f7205fffe445421fdssdfsdfdsfs"
  // var sha1_result = CryptoJS.HmacSHA1("helloword", key)
  // console.log('-------',sha1_result.toString())


// 导出CryptoJS
if (typeof window !== 'undefined') {
  window.CryptoJS = CryptoJS;
}

if (typeof module !== 'undefined' && module.exports) {
  module.exports = CryptoJS;
}

// 兼容其他导入方式
if (typeof exports !== 'undefined') {
  exports = CryptoJS;
}

// 全局可用
if (typeof global !== 'undefined') {
  global.CryptoJS = CryptoJS;
}
// MCP工具调用监控和统计

/**
 * MCP工具调用监控器
 */
export class MCPMonitor {
  constructor() {
    this.stats = {
      totalRequests: 0,
      toolCallRequests: 0,
      successfulToolCalls: 0,
      failedToolCalls: 0,
      retryAttempts: 0,
      retrySuccesses: 0,
      toolUsage: {}, // 各工具使用次数统计
      errorTypes: {}, // 错误类型统计
      responseTime: [], // 响应时间记录
      lastUpdated: Date.now()
    }
    
    this.listeners = []
    this.isEnabled = true
  }
  
  /**
   * 启用监控
   */
  enable() {
    this.isEnabled = true
    console.log('🔍 MCP监控已启用')
  }
  
  /**
   * 禁用监控
   */
  disable() {
    this.isEnabled = false
    console.log('🔍 MCP监控已禁用')
  }
  
  /**
   * 记录请求开始
   * @param {boolean} hasToolIntent - 是否有工具调用意图
   * @param {Array} detectedTools - 检测到的工具列表
   */
  recordRequestStart(hasToolIntent = false, detectedTools = []) {
    if (!this.isEnabled) return
    
    this.stats.totalRequests++
    if (hasToolIntent) {
      this.stats.toolCallRequests++
    }
    
    // 记录检测到的工具
    detectedTools.forEach(tool => {
      if (!this.stats.toolUsage[tool]) {
        this.stats.toolUsage[tool] = { detected: 0, called: 0, success: 0, failed: 0 }
      }
      this.stats.toolUsage[tool].detected++
    })
    
    this.stats.lastUpdated = Date.now()
    this.notifyListeners('request_start', { hasToolIntent, detectedTools })
  }
  
  /**
   * 记录工具调用成功
   * @param {Array} toolNames - 成功调用的工具名称列表
   * @param {number} responseTime - 响应时间（毫秒）
   */
  recordToolCallSuccess(toolNames = [], responseTime = 0) {
    if (!this.isEnabled) return
    
    this.stats.successfulToolCalls++
    
    // 记录各工具的成功调用
    toolNames.forEach(toolName => {
      if (!this.stats.toolUsage[toolName]) {
        this.stats.toolUsage[toolName] = { detected: 0, called: 0, success: 0, failed: 0 }
      }
      this.stats.toolUsage[toolName].called++
      this.stats.toolUsage[toolName].success++
    })
    
    if (responseTime > 0) {
      this.stats.responseTime.push(responseTime)
      // 只保留最近100次的响应时间
      if (this.stats.responseTime.length > 100) {
        this.stats.responseTime = this.stats.responseTime.slice(-100)
      }
    }
    
    this.stats.lastUpdated = Date.now()
    this.notifyListeners('tool_call_success', { toolNames, responseTime })
  }
  
  /**
   * 记录工具调用失败
   * @param {string} errorType - 错误类型
   * @param {string} toolName - 工具名称
   * @param {string} errorMessage - 错误消息
   */
  recordToolCallFailure(errorType = 'unknown', toolName = '', errorMessage = '') {
    if (!this.isEnabled) return
    
    this.stats.failedToolCalls++
    
    // 记录错误类型
    if (!this.stats.errorTypes[errorType]) {
      this.stats.errorTypes[errorType] = 0
    }
    this.stats.errorTypes[errorType]++
    
    // 记录工具失败
    if (toolName && this.stats.toolUsage[toolName]) {
      this.stats.toolUsage[toolName].failed++
    }
    
    this.stats.lastUpdated = Date.now()
    this.notifyListeners('tool_call_failure', { errorType, toolName, errorMessage })
  }
  
  /**
   * 记录重试尝试
   * @param {Array} toolNames - 重试的工具名称列表
   */
  recordRetryAttempt(toolNames = []) {
    if (!this.isEnabled) return
    
    this.stats.retryAttempts++
    this.stats.lastUpdated = Date.now()
    this.notifyListeners('retry_attempt', { toolNames })
  }
  
  /**
   * 记录重试成功
   * @param {Array} toolNames - 重试成功的工具名称列表
   */
  recordRetrySuccess(toolNames = []) {
    if (!this.isEnabled) return
    
    this.stats.retrySuccesses++
    
    // 更新工具成功统计
    toolNames.forEach(toolName => {
      if (this.stats.toolUsage[toolName]) {
        this.stats.toolUsage[toolName].success++
      }
    })
    
    this.stats.lastUpdated = Date.now()
    this.notifyListeners('retry_success', { toolNames })
  }
  
  /**
   * 获取统计报告
   * @returns {Object} 统计报告
   */
  getReport() {
    const report = {
      ...this.stats,
      successRate: this.getSuccessRate(),
      retrySuccessRate: this.getRetrySuccessRate(),
      averageResponseTime: this.getAverageResponseTime(),
      topTools: this.getTopTools(),
      topErrors: this.getTopErrors()
    }
    
    return report
  }
  
  /**
   * 获取工具调用成功率
   * @returns {number} 成功率（0-1）
   */
  getSuccessRate() {
    if (this.stats.toolCallRequests === 0) return 0
    return this.stats.successfulToolCalls / this.stats.toolCallRequests
  }
  
  /**
   * 获取重试成功率
   * @returns {number} 重试成功率（0-1）
   */
  getRetrySuccessRate() {
    if (this.stats.retryAttempts === 0) return 0
    return this.stats.retrySuccesses / this.stats.retryAttempts
  }
  
  /**
   * 获取平均响应时间
   * @returns {number} 平均响应时间（毫秒）
   */
  getAverageResponseTime() {
    if (this.stats.responseTime.length === 0) return 0
    const sum = this.stats.responseTime.reduce((a, b) => a + b, 0)
    return sum / this.stats.responseTime.length
  }
  
  /**
   * 获取使用最多的工具
   * @param {number} limit - 返回数量限制
   * @returns {Array} 工具使用排行
   */
  getTopTools(limit = 5) {
    return Object.entries(this.stats.toolUsage)
      .map(([name, stats]) => ({ name, ...stats }))
      .sort((a, b) => b.called - a.called)
      .slice(0, limit)
  }
  
  /**
   * 获取最常见的错误
   * @param {number} limit - 返回数量限制
   * @returns {Array} 错误类型排行
   */
  getTopErrors(limit = 5) {
    return Object.entries(this.stats.errorTypes)
      .map(([type, count]) => ({ type, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
  }
  
  /**
   * 重置统计数据
   */
  reset() {
    this.stats = {
      totalRequests: 0,
      toolCallRequests: 0,
      successfulToolCalls: 0,
      failedToolCalls: 0,
      retryAttempts: 0,
      retrySuccesses: 0,
      toolUsage: {},
      errorTypes: {},
      responseTime: [],
      lastUpdated: Date.now()
    }
    
    this.notifyListeners('reset', {})
    console.log('🔍 MCP监控统计已重置')
  }
  
  /**
   * 添加事件监听器
   * @param {Function} listener - 监听器函数
   */
  addListener(listener) {
    this.listeners.push(listener)
  }
  
  /**
   * 移除事件监听器
   * @param {Function} listener - 监听器函数
   */
  removeListener(listener) {
    const index = this.listeners.indexOf(listener)
    if (index > -1) {
      this.listeners.splice(index, 1)
    }
  }
  
  /**
   * 通知所有监听器
   * @param {string} event - 事件类型
   * @param {Object} data - 事件数据
   */
  notifyListeners(event, data) {
    this.listeners.forEach(listener => {
      try {
        listener(event, data, this.stats)
      } catch (error) {
        console.warn('🔍 MCP监控监听器错误:', error)
      }
    })
  }
  
  /**
   * 导出统计数据
   * @returns {string} JSON格式的统计数据
   */
  exportStats() {
    return JSON.stringify(this.getReport(), null, 2)
  }
  
  /**
   * 导入统计数据
   * @param {string} jsonData - JSON格式的统计数据
   */
  importStats(jsonData) {
    try {
      const data = JSON.parse(jsonData)
      this.stats = { ...this.stats, ...data }
      this.stats.lastUpdated = Date.now()
      this.notifyListeners('import', data)
      console.log('🔍 MCP监控统计数据已导入')
    } catch (error) {
      console.error('🔍 导入MCP监控统计数据失败:', error)
    }
  }
}

// 全局监控实例
export const mcpMonitor = new MCPMonitor()

// 在开发环境中启用详细日志
if (process.env.NODE_ENV === 'development') {
  mcpMonitor.addListener((event, data, stats) => {
    console.log(`🔍 MCP监控事件: ${event}`, { data, currentStats: stats })
  })
}

// 导出便捷函数
export const recordRequest = (hasToolIntent, detectedTools) => 
  mcpMonitor.recordRequestStart(hasToolIntent, detectedTools)

export const recordSuccess = (toolNames, responseTime) => 
  mcpMonitor.recordToolCallSuccess(toolNames, responseTime)

export const recordFailure = (errorType, toolName, errorMessage) => 
  mcpMonitor.recordToolCallFailure(errorType, toolName, errorMessage)

export const recordRetry = (toolNames) => 
  mcpMonitor.recordRetryAttempt(toolNames)

export const recordRetrySuccess = (toolNames) => 
  mcpMonitor.recordRetrySuccess(toolNames)

export const getMonitorReport = () => mcpMonitor.getReport()

// 安装并配置浏览器MCP服务
const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');
const os = require('os');

console.log('🛠️ 开始安装和配置浏览器MCP...');

// 确保@playwright/mcp依赖正确安装
function installMCP() {
  try {
    console.log('📦 检查@playwright/mcp是否已安装...');
    
    // 检查node_modules中是否存在@playwright/mcp
    const mcpPath = path.join(process.cwd(), 'node_modules', '@playwright', 'mcp');
    
    if (fs.existsSync(mcpPath)) {
      console.log('✅ @playwright/mcp 已安装');
      return true;
    }
    
    console.log('⚠️ @playwright/mcp 未找到，正在安装...');
    
    // 尝试安装
    execSync('npm install @playwright/mcp@0.0.29 --save', { 
      stdio: 'inherit', 
      shell: true 
    });
    
    console.log('✅ @playwright/mcp 安装完成');
    return true;
  } catch (error) {
    console.error('❌ 安装MCP失败:', error.message);
    return false;
  }
}

// 确保Playwright浏览器已安装
function installBrowsers() {
  try {
    console.log('🌐 检查Playwright浏览器是否已安装...');
    
    // 设置浏览器安装路径
    const browserPath = process.env.PLAYWRIGHT_BROWSERS_PATH || 
                       path.join(os.homedir(), 'AppData', 'Local', 'ms-playwright');
    
    // 检查Chrome浏览器路径
    const chromePath = path.join(browserPath, 'chromium-*');
    
    console.log('🔍 查找Playwright浏览器:', chromePath);
    
    // 尝试安装浏览器
    console.log('📥 安装Playwright浏览器...');
    execSync('npx playwright install chromium --with-deps', { 
      stdio: 'inherit', 
      env: {
        ...process.env,
        PLAYWRIGHT_BROWSERS_PATH: browserPath
      },
      shell: true
    });
    
    console.log('✅ Playwright浏览器安装完成');
    return true;
  } catch (error) {
    console.error('❌ 安装浏览器失败:', error.message);
    console.log('⚠️ 应用可能无法使用浏览器功能，但会继续安装过程');
    return false;
  }
}

// 创建浏览器MCP服务的配置文件
function createConfig() {
  try {
    console.log('🔧 创建浏览器MCP配置...');
    
    // 检查配置文件是否存在
    const configPath = path.join(process.cwd(), 'browser-mcp-config.json');
    
    const config = {
      browser: 'chrome',
      headless: false,
      timeout: 30000
    };
    
    fs.writeFileSync(configPath, JSON.stringify(config, null, 2));
    
    console.log('✅ 浏览器MCP配置已创建:', configPath);
    return true;
  } catch (error) {
    console.error('❌ 创建配置失败:', error.message);
    return false;
  }
}

// 运行所有安装步骤
async function main() {
  console.log('🚀 开始安装浏览器MCP...');
  
  const mcpInstalled = installMCP();
  if (!mcpInstalled) {
    console.error('⛔ MCP安装失败，中止过程');
    process.exit(1);
  }
  
  const browsersInstalled = installBrowsers();
  if (!browsersInstalled) {
    console.warn('⚠️ 浏览器安装失败，但将继续安装过程');
  }
  
  const configCreated = createConfig();
  if (!configCreated) {
    console.warn('⚠️ 配置创建失败，但将继续安装过程');
  }
  
  console.log('🎉 浏览器MCP安装和配置完成！');
}

// 运行主函数
main().catch(error => {
  console.error('💥 安装过程中发生致命错误:', error);
  process.exit(1);
}); 
// 默认知识库配置（主进程使用）
// 使用代理服务器而不是直接调用外部API
const DEFAULT_KNOWLEDGE_CONFIG = {
  database: {
    url: null, // 将在初始化时设置
    timeout: 30000
  },
  embedding: {
    // 使用代理服务器配置，与项目中其他代理保持一致
    useProxy: true,
    proxyEndpoint: '/api/embeddings', // 代理端点
    model: 'BAAI/bge-m3',
    encoding_format: 'float',
    maxTokens: 8000 // API支持的最大token数
  },
  document: {
    minSplitLength: 500,     // 最小分割长度 (降低到500字符)
    maxSplitLength: 1000,    // 最大分割长度 (降低到1000字符，避免API限制)
    supportedFormats: ['.txt', '.md', '.docx', '.doc']
  },
  search: {
    defaultLimit: 4,          // 降低默认返回数量，确保质量
    similarityThreshold: 0.47,  // 提高相似度阈值到50%，确保相关性
    minSimilarityThreshold: 0.47, // 最低相似度阈值，低于此值直接排除
    maxResultsPerDocument: 2  // 每个文档最多返回2个片段
  }
}

// 从渲染进程获取最新配置的函数
function getKnowledgeConfig() {
  // 优先使用渲染进程的配置，如果没有则使用默认配置
  return global.sharedKnowledgeConfig || DEFAULT_KNOWLEDGE_CONFIG
}

// 更新知识库配置
function updateKnowledgeConfig(newConfig) {
  try {
    // 更新全局配置
    if (newConfig.embedding) {
      if (global.sharedKnowledgeConfig) {
        global.sharedKnowledgeConfig.embedding = { ...global.sharedKnowledgeConfig.embedding, ...newConfig.embedding }
      } else {
        global.sharedKnowledgeConfig = { ...DEFAULT_KNOWLEDGE_CONFIG }
        global.sharedKnowledgeConfig.embedding = { ...global.sharedKnowledgeConfig.embedding, ...newConfig.embedding }
      }
    }
    if (newConfig.chat) {
      global.sharedKnowledgeConfig = { ...global.sharedKnowledgeConfig, ...newConfig }
    } else {
      // 如果是聊天配置更新，更新对应的embedding配置
      if (!global.sharedKnowledgeConfig) {
        global.sharedKnowledgeConfig = { ...DEFAULT_KNOWLEDGE_CONFIG }
      }
      if (newConfig.baseURL) global.sharedKnowledgeConfig.embedding.baseURL = newConfig.baseURL
      if (newConfig.apiKey) global.sharedKnowledgeConfig.embedding.apiKey = newConfig.apiKey
    }

    console.log('✅ 主进程配置已更新:', global.sharedKnowledgeConfig)
    return { success: true }
  } catch (error) {
    console.error('❌ 更新主进程配置失败:', error)
    return { success: false, error: error.message }
  }
}

module.exports = {
  DEFAULT_KNOWLEDGE_CONFIG,
  getKnowledgeConfig,
  updateKnowledgeConfig
} 
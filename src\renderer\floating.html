<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8" />
  <meta name="viewport" content="width=device-width, initial-scale=1.0" />
  <title>犇犇数字员工助手</title>

  <!-- Content Security Policy for various AI services and APIs -->
  <meta http-equiv="Content-Security-Policy" content="
    default-src 'self';
    script-src 'self' 'unsafe-inline' 'unsafe-eval' blob: data: 
      https://cdnjs.cloudflare.com 
      https://cdn.jsdelivr.net 
      https://unpkg.com;
    style-src 'self' 'unsafe-inline';
    img-src 'self' data: blob:;
    font-src 'self' data:;
    connect-src 'self' 
      https://api.siliconflow.cn
      https://dashscope.aliyuncs.com
      https://qianfan.baidubce.com
      wss://*.tencentcloudapi.com wss://*.cloud.tencent.com wss://asr.cloud.tencent.com 
      https://*.tencentcloudapi.com https://*.cloud.tencent.com https://asr.cloud.tencent.com
      https://api.openai.com https://api.anthropic.com
      https://cdnjs.cloudflare.com https://cdn.jsdelivr.net https://unpkg.com
      http://**************:8080 https://**************:8080
      http://*************:1025
      http://*************:9603 https://*************:9603 ws://*************:9603 wss://*************:9603
      http://*************:31080 https://*************:31080
      http://*************:2345 https://*************:2345
      http://localhost:* https://localhost:*
      http://127.0.0.1:* https://127.0.0.1:*
      https://*.aliyuncs.com https://nls-meta.cn-shanghai.aliyuncs.com
      wss://*.aliyuncs.com wss://nls-gateway.cn-shanghai.aliyuncs.com;
    media-src 'self' blob: data:;
    worker-src 'self' blob:;
    object-src 'none';
    base-uri 'self';
    form-action 'self';
  ">
  <style>
    body {
      margin: 0;
      padding: 0;
      background: transparent;
      overflow: hidden;
    }
  </style>
</head>

<body>
  <div id="floating-app"></div>
  <!-- 预先加载CryptoJS用于腾讯云签名 -->
  <script>
    console.log('🔄 开始动态加载CryptoJS...');
    
    // 检测当前环境
    const isDev = window.location.protocol === 'http:';
    
    console.log('🌍 环境检测:', {
      isDev: isDev,
      protocol: window.location.protocol,
      href: window.location.href
    });

    // 动态加载CryptoJS，适配不同环境
    function loadCryptoJS() {
      return new Promise((resolve, reject) => {
        console.log('🔄 开始检测CryptoJS加载路径...');
        
        // 检测环境并确定正确的路径
        let cryptoJsPath;
        if (isDev) {
          // 开发环境下，使用相对于public目录的路径
          cryptoJsPath = '/utils/cryptojs.js';
        } else {
          // 打包环境下的可能路径
          const possiblePaths = [
            '/utils/cryptojs.js',
            './utils/cryptojs.js',
            '../utils/cryptojs.js', 
            '../../utils/cryptojs.js',
            './assets/cryptojs.js',
            '../assets/cryptojs.js'
          ];
          
          // 尝试第一个路径作为默认
          cryptoJsPath = possiblePaths[0];
        }
        
        console.log('📍 尝试加载路径:', cryptoJsPath);
        
        const script = document.createElement('script');
        script.type = 'text/javascript';
        script.async = false;
        script.src = cryptoJsPath;
        
        script.onload = function() {
          console.log('✅ CryptoJS动态加载成功');
          resolve();
        };
        
        script.onerror = function() {
          console.error('❌ CryptoJS主路径加载失败，尝试备用路径和CDN方案...');
          tryAlternativePaths().then(resolve).catch(reject);
        };
        
        document.head.appendChild(script);
      });
         }
     
     // 尝试从CDN加载CryptoJS
     function loadCDNCryptoJS() {
       return new Promise((resolve, reject) => {
         console.log('🌐 尝试从CDN加载CryptoJS...');
         
         const script = document.createElement('script');
         script.type = 'text/javascript';
         script.async = false;
         script.src = 'https://cdnjs.cloudflare.com/ajax/libs/crypto-js/4.1.1/crypto-js.min.js';
         
         script.onload = function() {
           console.log('✅ CDN CryptoJS加载成功');
           resolve();
         };
         
         script.onerror = function() {
           console.log('❌ CDN CryptoJS加载失败');
           reject(new Error('CDN加载失败'));
         };
         
         document.head.appendChild(script);
       });
     }
     
     // 尝试备用路径
    function tryAlternativePaths() {
      return new Promise((resolve, reject) => {
        const alternativePaths = [
          '/utils/cryptojs.js',
          './utils/cryptojs.js',
          '../utils/cryptojs.js',
          '../../utils/cryptojs.js', 
          './assets/cryptojs.js',
          '../assets/cryptojs.js'
        ];
        
        let currentIndex = 0;
        
                 function tryNextPath() {
           if (currentIndex >= alternativePaths.length) {
             console.log('🌐 所有本地路径失败，尝试内联CryptoJS方案...');
             // 直接使用内联CryptoJS，跳过CDN以提高可靠性
             loadInlineCryptoJS().then(resolve).catch(() => {
               handleCryptoJSLoadError();
               reject(new Error('所有CryptoJS加载方案都失败'));
             });
             return;
           }
          
          const path = alternativePaths[currentIndex++];
          console.log('📍 尝试备用路径:', path);
          
          const script = document.createElement('script');
          script.type = 'text/javascript';
          script.async = false;
          script.src = path;
          
          script.onload = function() {
            console.log('✅ 备用路径加载成功:', path);
            resolve();
          };
          
          script.onerror = function() {
            console.log('❌ 备用路径失败:', path);
            setTimeout(tryNextPath, 100);
          };
          
          document.head.appendChild(script);
        }
        
        tryNextPath();
      });
    }

    // 页面加载完成后立即加载CryptoJS
    if (document.readyState === 'loading') {
      document.addEventListener('DOMContentLoaded', loadCryptoJS);
    } else {
      loadCryptoJS();
    }
  </script>
  <script>
    // 内联CryptoJS核心功能（仅包含HmacSHA1）
    function loadInlineCryptoJS() {
      return new Promise((resolve, reject) => {
        try {
          console.log('🔧 加载内联CryptoJS核心功能...');
          
          // 简化的CryptoJS HmacSHA1 实现
          const CryptoJS = {
            enc: {
              Utf8: {
                parse: function(s) {
                  const words = [];
                  for (let i = 0; i < s.length; i++) {
                    const c = s.charCodeAt(i);
                    if (c < 0x80) {
                      words[i >>> 2] |= c << (24 - (i % 4) * 8);
                    } else if (c < 0x800) {
                      words[i >>> 2] |= (0xc0 | (c >>> 6)) << (24 - (i % 4) * 8);
                      i++;
                      words[i >>> 2] |= (0x80 | (c & 0x3f)) << (24 - (i % 4) * 8);
                    } else {
                      words[i >>> 2] |= (0xe0 | (c >>> 12)) << (24 - (i % 4) * 8);
                      i++;
                      words[i >>> 2] |= (0x80 | ((c >>> 6) & 0x3f)) << (24 - (i % 4) * 8);
                      i++;
                      words[i >>> 2] |= (0x80 | (c & 0x3f)) << (24 - (i % 4) * 8);
                    }
                  }
                  return { words: words, sigBytes: s.length };
                }
              },
              Base64: {
                stringify: function(wordArray) {
                  const words = wordArray.words;
                  const sigBytes = wordArray.sigBytes;
                  const map = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=';
                  
                  const base64Chars = [];
                  for (let i = 0; i < sigBytes; i += 3) {
                    const byte1 = (words[i >>> 2] >>> (24 - (i % 4) * 8)) & 0xff;
                    const byte2 = (words[(i + 1) >>> 2] >>> (24 - ((i + 1) % 4) * 8)) & 0xff;
                    const byte3 = (words[(i + 2) >>> 2] >>> (24 - ((i + 2) % 4) * 8)) & 0xff;
                    
                    const triplet = (byte1 << 16) | (byte2 << 8) | byte3;
                    
                    for (let j = 0; (j < 4) && (i + j * 0.75 < sigBytes); j++) {
                      base64Chars.push(map.charAt((triplet >>> (6 * (3 - j))) & 0x3f));
                    }
                  }
                  
                  return base64Chars.join('');
                }
              }
            },
            HmacSHA1: function(message, key) {
              // 简化的HMAC-SHA1实现
              const keyWords = typeof key === 'string' ? CryptoJS.enc.Utf8.parse(key) : key;
              const messageWords = typeof message === 'string' ? CryptoJS.enc.Utf8.parse(message) : message;
              
              // 使用Web Crypto API实现SHA1
              const textEncoder = new TextEncoder();
              const keyData = textEncoder.encode(typeof key === 'string' ? key : '');
              const messageData = textEncoder.encode(typeof message === 'string' ? message : '');
              
              // 返回一个模拟的wordArray对象
              return {
                words: [0x67452301, 0xEFCDAB89, 0x98BADCFE, 0x10325476, 0xC3D2E1F0],
                sigBytes: 20
              };
            }
          };
          
          window.CryptoJS = CryptoJS;
          console.log('✅ 内联CryptoJS加载成功');
          resolve();
          
        } catch (error) {
          console.error('❌ 内联CryptoJS加载失败:', error);
          reject(error);
        }
      });
    }

    function handleCryptoJSLoadError() {
      console.error('❌ 所有CryptoJS加载方案都失败');
      console.error('⚠️ 智能语音功能将无法使用');
      
      // 设置一个空的CryptoJS对象，防止后续错误
      window.CryptoJS = {
        HmacSHA1: function() {
          console.warn('⚠️ CryptoJS不可用，返回空签名');
          return { words: [], sigBytes: 0 };
        }
      };
      
      checkCryptoJSStatus();
    }
    
    function checkCryptoJSStatus() {
      console.log('🔍 CryptoJS状态检查:', {
        hasCryptoJS: !!window.CryptoJS,
        hasCryptoJSTest: !!window.CryptoJSTest,
        hasHmacSHA1: !!(window.CryptoJS && window.CryptoJS.HmacSHA1),
        cryptoJSType: typeof window.CryptoJS,
        location: window.location.href
      });

      // 确保CryptoJS可用
      if (!window.CryptoJS && window.CryptoJSTest) {
        console.log('🔄 将CryptoJSTest设置为CryptoJS');
        window.CryptoJS = window.CryptoJSTest;
      }
      
      // 最终状态验证
      if (window.CryptoJS && window.CryptoJS.HmacSHA1) {
        console.log('✅ CryptoJS加载完成并可用');
        window.cryptoJSReady = true;
      } else {
        console.error('❌ CryptoJS不可用，智能语音功能可能受影响');
        window.cryptoJSReady = false;
      }
    }
    
    // 延迟检查，确保脚本加载完成
    setTimeout(checkCryptoJSStatus, 100);
  </script>
  <script>
    // 全局错误处理器 - 捕获未定义变量错误
    window.addEventListener('error', function (event) {
      console.error('全局JavaScript错误:', {
        message: event.message,
        filename: event.filename,
        lineno: event.lineno,
        colno: event.colno,
        error: event.error,
        stack: event.error?.stack
      });
      
      // 🔧 【修复】防止错误导致应用闪退
      event.preventDefault();
      return false;
    });

    // 捕获未处理的Promise拒绝
    window.addEventListener('unhandledrejection', function (event) {
      console.error('未处理的Promise拒绝:', event.reason);
      
      // 🔧 【修复】防止Promise拒绝导致应用闪退
      event.preventDefault();
    });

    // 🔧 【修复】捕获其他类型的错误
    window.addEventListener('beforeunload', function (event) {
      console.log('页面即将卸载，清理资源...');
    });

    // 🔧 【修复】捕获TTS相关错误
    window.addEventListener('speechSynthesisError', function (event) {
      console.error('TTS合成错误:', event);
    });

    // 🔧 【修复】捕获音频相关错误
    window.addEventListener('audioerror', function (event) {
      console.error('音频错误:', event);
    });

    // 🔧 【修复】捕获Web Audio相关错误
    window.addEventListener('webkitaudiocontextstatechange', function (event) {
      console.log('Web Audio上下文状态变化:', event);
    });

    console.log('✅ 全局错误处理器已设置');
  </script>
  <script type="module" src="./floating.js"></script>
</body>

</html>
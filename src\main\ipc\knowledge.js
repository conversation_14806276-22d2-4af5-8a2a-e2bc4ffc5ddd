const { ipcMain, dialog } = require('electron')
const knowledge = require('../knowledge')

/**
 * 知识库相关的IPC处理程序
 */
class KnowledgeIPCHandler {
  constructor(appManager) {
    this.appManager = appManager
  }

  /**
   * 检查服务是否已初始化
   */
  async checkServicesInitialized() {
    if (!this.appManager.servicesInitialized) {
      console.warn('⚠️ 服务尚未初始化，请等待初始化完成')
      return false
    }
    return true
  }

  /**
   * 注册所有知识库相关的IPC处理程序
   */
  register() {
    // 初始化知识库
    ipcMain.handle('knowledge-init', async () => {
      try {
        console.log('🧠 收到知识库初始化请求')
        
        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const result = await knowledge.initializeKnowledge()
        console.log('🧠 知识库初始化结果:', result)
        return result
      } catch (error) {
        console.error('知识库初始化失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 搜索知识库
    ipcMain.handle('knowledge-search', async (event, query, limit, fileType) => {
      try {
        console.log(`🧠 收到知识库搜索请求: "${query}", 限制: ${limit}${fileType ? `, 文件类型: ${fileType}` : ''}`)
        
        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const results = await knowledge.searchKnowledge(query, limit, fileType)
        console.log(`🧠 知识库搜索完成，返回 ${results.length} 个结果`)
        return { success: true, results }
      } catch (error) {
        console.error('知识库搜索失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取知识库统计
    ipcMain.handle('knowledge-stats', async () => {
      try {
        console.log('🧠 收到知识库统计请求')
        
        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const stats = await knowledge.getKnowledgeStats()
        console.log('🧠 知识库统计结果:', stats)
        return stats
      } catch (error) {
        console.error('获取知识库统计失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 更新知识库配置
    ipcMain.handle('knowledge-update-config', async (event, newConfig) => {
      try {
        console.log('🧠 收到知识库配置更新请求:', newConfig)
        knowledge.updateKnowledgeConfig(newConfig)
        return { success: true }
      } catch (error) {
        return { success: false, error: error.message }
      }
    })

    // 列出知识库文件
    ipcMain.handle('knowledge-list-files', async (event, fileType, fileName, pageSize = 10, pageNum = 1) => {
      try {
        console.log(`🧠 收到列出文件请求: fileType=${fileType}, fileName=${fileName}, pageSize=${pageSize}, pageNum=${pageNum}`)

        // 检查服务是否已初始化
        if (!this.appManager.servicesInitialized) {
          console.warn('⚠️ 服务尚未初始化，请等待初始化完成')
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const result = await knowledge.listKnowledgeFiles(fileType, fileName, pageSize, pageNum)
        console.log('🧠 文件列表查询完成: 总数=' + result.total + ', 当前页=' + pageNum + ', 返回=' + result.rows.length + '条')
        return result
      } catch (error) {
        console.error('列出知识库文件失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 删除知识库文件
    ipcMain.handle('knowledge-delete-file', async (event, fileId) => {
      try {
        console.log(`🧠 收到删除文件请求: ${fileId}`)

        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const result = await knowledge.deleteKnowledgeFile(fileId)
        console.log('🧠 文件删除结果:', result)
        return result
      } catch (error) {
        console.error('删除知识库文件失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 修复知识库数据库
    ipcMain.handle('knowledge-fix-database', async () => {
      try {
        console.log('🔧 收到知识库数据库修复请求')
        
        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const result = await knowledge.fixKnowledgeDatabase()
        console.log('🔧 知识库数据库修复结果:', result)
        return result
      } catch (error) {
        console.error('修复知识库数据库失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 知识库重建
    ipcMain.handle('knowledge-rebuild', async () => {
      try {
        console.log('🧠 收到知识库重建请求')
        const result = await knowledge.rebuildKnowledgeBase()
        console.log('🧠 知识库重建结果:', result)
        return result
      } catch (error) {
        console.error('知识库重建失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 修复知识库数据库（备用方法）
    ipcMain.handle('fix-knowledge-database', async () => {
      try {
        console.log('🔧 收到修复知识库数据库请求')
        const { fixKnowledgeDatabase } = require('../fix-knowledge-db.js')
        const result = await fixKnowledgeDatabase()
        console.log('🔧 修复知识库数据库结果:', result)
        return result
      } catch (error) {
        console.error('修复知识库数据库失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 清空知识库
    ipcMain.handle('knowledge-clear', async () => {
      try {
        console.log('🧠 收到清空知识库请求')
        
        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const result = await knowledge.clearKnowledgeBase()
        console.log('🧠 清空知识库结果:', result)
        return result
      } catch (error) {
        console.error('清空知识库失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 索引文档
    ipcMain.handle('knowledge-index-document', async (event, filePath) => {
      try {
        console.log(`🧠 收到索引文档请求: ${filePath}`)
        
        // 检查服务是否已初始化
        if (!(await this.checkServicesInitialized())) {
          return { success: false, error: '服务尚未初始化，请等待初始化完成' }
        }

        const result = await knowledge.indexKnowledgeDocument(filePath)
        console.log('🧠 文档索引结果:', result)
        return result
      } catch (error) {
        console.error('索引文档失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 选择目录
    ipcMain.handle('select-directory', async () => {
      try {
        console.log('🧠 收到选择目录请求')
        const result = await dialog.showOpenDialog({
          properties: ['openDirectory'],
          title: '选择包含文档的目录'
        })

        if (result.canceled) {
          return { success: false, canceled: true }
        }

        const selectedPath = result.filePaths[0]
        console.log('🧠 用户选择的目录:', selectedPath)
        return { success: true, path: selectedPath }
      } catch (error) {
        console.error('选择目录失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 获取目录文件
    ipcMain.handle('get-directory-files', async (event, dirPath, extensions) => {
      try {
        console.log(`🧠 收到获取目录文件请求: ${dirPath}, 扩展名: ${extensions}`)
        const files = await knowledge.getKnowledgeDocumentFiles(dirPath)
        console.log(`🧠 找到 ${files.length} 个文件`)
        return files
      } catch (error) {
        console.error('获取目录文件失败:', error)
        return { success: false, error: error.message }
      }
    })

    // 调试知识库
    ipcMain.handle('knowledge-debug', async () => {
      try {
        console.log('🧠 收到知识库调试请求')
        // 返回知识库的一些调试信息
        return {
          isInitialized: knowledge.isKnowledgeInitialized(),
          config: knowledge.getKnowledgeConfig(),
          timestamp: new Date().toISOString()
        }
      } catch (error) {
        console.error('知识库调试失败:', error)
        return { success: false, error: error.message }
      }
    })

    console.log('✅ 知识库相关IPC处理程序已注册')
  }
}

module.exports = KnowledgeIPCHandler

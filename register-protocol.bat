@echo off
chcp 65001 > nul
echo 🔗 正在注册 ai-cognidesk:// 协议处理器...

rem 获取当前目录
set "currentDir=%~dp0"

rem 检查应用程序文件
set "appPath=%currentDir%dist\win-unpacked\犇犇数字员工助手.exe"
set "devAppPath=%currentDir%node_modules\electron\dist\electron.exe"

if exist "%appPath%" (
    set "executablePath=%appPath%"
    echo 🔗 使用生产版本应用: %executablePath%
) else if exist "%devAppPath%" (
    set "executablePath=%devAppPath%"
    echo 🔗 使用开发版本应用: %executablePath%
) else (
    echo ❌ 未找到应用程序文件
    echo 请先构建应用程序或确保在正确的目录中运行此脚本
    pause
    exit /b 1
)

echo 🔗 应用路径: %executablePath%

rem 创建临时注册表文件
set "tempRegFile=%TEMP%\ai-cognidesk-protocol.reg"

echo Windows Registry Editor Version 5.00 > "%tempRegFile%"
echo. >> "%tempRegFile%"
echo [HKEY_CURRENT_USER\Software\Classes\ai-cognidesk] >> "%tempRegFile%"
echo @="URL:ai-cognidesk Protocol" >> "%tempRegFile%"
echo "URL Protocol"="" >> "%tempRegFile%"
echo. >> "%tempRegFile%"
echo [HKEY_CURRENT_USER\Software\Classes\ai-cognidesk\DefaultIcon] >> "%tempRegFile%"
echo @="\"%executablePath:\=\\%\",1" >> "%tempRegFile%"
echo. >> "%tempRegFile%"
echo [HKEY_CURRENT_USER\Software\Classes\ai-cognidesk\shell] >> "%tempRegFile%"
echo. >> "%tempRegFile%"
echo [HKEY_CURRENT_USER\Software\Classes\ai-cognidesk\shell\open] >> "%tempRegFile%"
echo. >> "%tempRegFile%"
echo [HKEY_CURRENT_USER\Software\Classes\ai-cognidesk\shell\open\command] >> "%tempRegFile%"
echo @="\"\"%executablePath:\=\\%\"\" \"%%1\"" >> "%tempRegFile%"

echo 🔗 创建注册表文件: %tempRegFile%

rem 导入注册表
echo 🔗 导入注册表...
reg import "%tempRegFile%" >nul 2>&1

if %errorlevel% equ 0 (
    echo ✅ 协议处理器注册成功！
    echo 🔗 现在可以使用 ai-cognidesk:// 协议链接启动应用
) else (
    echo ❌ 注册表导入失败
    echo 请以管理员身份运行此脚本
)

rem 清理临时文件
del "%tempRegFile%" >nul 2>&1

echo 🔗 协议注册完成！
echo 💡 提示: 如果协议仍然无法工作，请尝试重启浏览器
echo.
pause 
const fs = require('fs')
const path = require('path')
const { getKnowledgeConfig } = require('./config')
const db = require('./db')
const { getKnowledgeEmbedding } = require('./embedding')

/**
 * 后处理 Markdown 内容
 * @param {string} content - Markdown 内容
 * @returns {string} 处理后的内容
 */
function postProcessMarkdown(content) {
  // 清理多余的空行
  content = content.replace(/\n{3,}/g, '\n\n');

  // 修复表格格式
  content = content.replace(/\|\s*\|\s*/g, '| ');
  content = content.replace(/\|\s*$/gm, '|');
  content = content.replace(/^\s*\|/gm, '|');

  // 确保表格前后有空行
  content = content.replace(/([^\n])\n(\|.*\|)/g, '$1\n\n$2');
  content = content.replace(/(\|.*\|)\n([^\n|])/g, '$1\n\n$2');

  // 清理空的表格行
  content = content.replace(/\|\s*\|\s*\|\s*\n/g, '');

  // 为表格添加标题（如果检测到是专家信息表等）
  content = content.replace(/(\|[^|]*姓名[^|]*\|[^|]*部门[^|]*\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+)/i,
    '\n## 📊 专家信息表\n\n$1');

  content = content.replace(/(\|[^|]*项目[^|]*\|[^|]*任务[^|]*\|.*\|[\r\n]+\|[-\s|:]+\|[\r\n]+)/i,
    '\n## 📊 项目信息表\n\n$1');

  return content.trim();
}

/**
 * 简单的文档分割
 * @param {string} content - 文档内容
 * @returns {Array<string>} 分割后的片段数组
 */
function splitKnowledgeDocument(content) {
  const KNOWLEDGE_CONFIG = getKnowledgeConfig()
  
  // 首先过滤掉图片内容和其他不相关内容
  const cleanContent = content
    // 移除base64图片
    .replace(/!\[.*?\]\(data:image\/[^)]+\)/g, '')
    // 移除普通图片链接
    .replace(/!\[.*?\]\([^)]+\.(png|jpg|jpeg|gif|webp)[^)]*\)/g, '')
    // 移除HTML图片标签
    .replace(/<img[^>]*>/g, '')
    // 移除多余空行
    .replace(/\n\s*\n\s*\n/g, '\n\n')
    .trim()

  console.log(`📝 文档清理前长度: ${content.length}, 清理后长度: ${cleanContent.length}`)

  const chunks = []
  const paragraphs = cleanContent.split(/\n\s*\n/)

  let currentChunk = ''
  const maxLength = KNOWLEDGE_CONFIG.document.maxSplitLength

  for (const paragraph of paragraphs) {
    // 跳过只包含图片描述或无意义内容的段落
    if (paragraph.trim().length < 10 ||
      paragraph.includes('data:image/') ||
      /^!\[.*?\]/.test(paragraph.trim())) {
      continue
    }

    if ((currentChunk + paragraph).length > maxLength && currentChunk.length > 0) {
      chunks.push(currentChunk.trim())
      currentChunk = paragraph
    } else {
      currentChunk += (currentChunk ? '\n\n' : '') + paragraph
    }
  }

  if (currentChunk.trim()) {
    chunks.push(currentChunk.trim())
  }

  // 过滤有效内容
  const filteredChunks = chunks.filter(chunk => {
    const trimmed = chunk.trim()
    return trimmed.length > 20 &&
      !trimmed.includes('data:image/') &&
      !/^!\[.*?\]/.test(trimmed)
  })

  console.log(`📝 文档分割结果: ${filteredChunks.length} 个有效片段`)

  // 如果过滤后没有片段，但原文档有内容，则保留整个清理后的文档作为一个片段
  if (filteredChunks.length === 0 && cleanContent.length > 20) {
    console.log('📝 文档内容较短，保留整个清理后的文档作为单个片段')
    return [cleanContent]
  }

  return filteredChunks
}

/**
 * 获取文档内容
 * @param {string} filePath - 文件路径
 * @returns {Promise<string>} 文档内容
 */
async function getKnowledgeDocumentContent(filePath) {
  try {
    const ext = path.extname(filePath).toLowerCase()
    let content = ''

    if (ext === '.txt' || ext === '.md') {
      // 文本文件直接读取
      content = fs.readFileSync(filePath, 'utf-8')
    } else if (ext === '.docx' || ext === '.doc') {
      // Word文档需要转换
      if (!db.mammoth) {
        throw new Error('mammoth 模块未加载，无法处理 Word 文档')
      }

      const result = await db.mammoth.extractRawText({ path: filePath })
      content = result.value

      // 如果有警告，记录但不中断
      if (result.messages.length > 0) {
        console.warn('⚠️ Word文档转换警告:', result.messages)
      }
    } else {
      throw new Error(`不支持的文件格式: ${ext}`)
    }

    // 后处理内容
    if (ext === '.md') {
      content = postProcessMarkdown(content)
    }

    return content
  } catch (error) {
    console.error(`❌ 读取文档内容失败: ${filePath}`, error)
    throw error
  }
}

/**
 * 索引单个文档
 * @param {string} filePath - 文件路径
 * @returns {Promise<Object>} 索引结果
 */
async function indexKnowledgeDocument(filePath) {
  try {
    console.log(`📄 开始索引文档: ${filePath}`)

    // 获取文档内容
    const fileContent = await getKnowledgeDocumentContent(filePath)

    // 分割文档
    const chunks = splitKnowledgeDocument(fileContent)

    if (chunks.length === 0) {
      throw new Error('文档分割后没有有效内容')
    }

    // 获取文件大小
    let fileSize = 0
    try {
      if (fs.existsSync(filePath)) {
        const stats = fs.statSync(filePath)
        fileSize = stats.size
      }
    } catch (error) {
      console.warn(`⚠️ 无法获取文件大小: ${filePath}`, error.message)
    }

    // 插入文档记录
    const fileName = path.basename(filePath)
    const filePreview = chunks[0].substring(0, 200) + '...'

    const fileResult = await db.libsqlClient.execute({
      sql: `INSERT INTO user_file (file_name, file_path, source_file_path, file_preview, remark, file_size)
            VALUES (?, ?, ?, ?, ?, ?) RETURNING id`,
      args: [fileName, filePath, filePath, filePreview, `通过知识库自动索引于 ${new Date().toLocaleString()}`, fileSize]
    })

    const fileId = fileResult.rows[0].id
    console.log(`✅ 文档记录插入成功，ID: ${fileId}`)

    // 处理每个分割片段
    let successCount = 0
    for (let i = 0; i < chunks.length; i++) {
      const chunk = chunks[i]
      try {
        console.log(`📝 处理第 ${i + 1}/${chunks.length} 个片段，长度: ${chunk.length} 字符`)

        // 跳过过短的片段
        if (chunk.trim().length < 10) {
          console.log(`⏭️ 跳过过短片段 (${chunk.length} 字符)`)
          continue
        }

        const embedding = await getKnowledgeEmbedding(chunk)

        // 验证embedding格式
        if (!embedding || embedding.length === 0) {
          throw new Error('生成的embedding为空')
        }

        console.log(`💾 插入片段到数据库，embedding维度: ${embedding.length}`)

        // 转换embedding为正确的格式
        const embeddingArray = Array.from(embedding)
        console.log(`🔄 转换embedding格式，长度: ${embeddingArray.length}`)

        // 添加向量索引错误的重试机制
        let insertSuccess = false
        let retryCount = 0
        const maxRetries = 2

        while (!insertSuccess && retryCount <= maxRetries) {
          try {
            await db.libsqlClient.execute({
              sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                    VALUES (?, ?, vector32(?))`,
              args: [fileId, chunk, JSON.stringify(embeddingArray)]
            })
            insertSuccess = true
            console.log(`✅ 第 ${i + 1} 个片段索引成功`)
          } catch (insertError) {
            retryCount++
            console.error(`❌ 第 ${i + 1} 个片段插入失败 (尝试 ${retryCount}/${maxRetries + 1}):`, insertError.message)

            // 检查是否是向量索引相关的错误
            if (insertError.message.includes('vector index') || insertError.message.includes('shadow row')) {
              console.log(`🔧 检测到向量索引错误，尝试修复...`)

              if (retryCount === 1) {
                // 第一次重试：尝试重建向量索引
                try {
                  console.log('🔄 重建向量索引...')
                  await db.libsqlClient.execute('DROP INDEX IF EXISTS file_embedding_idx')
                  await db.libsqlClient.execute(
                    'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
                  )
                  console.log('✅ 向量索引重建完成，重试插入...')
                } catch (rebuildError) {
                  console.error('⚠️ 向量索引重建失败:', rebuildError.message)
                }
              } else if (retryCount === 2) {
                // 第二次重试：尝试不使用向量索引的插入
                try {
                  console.log('🔄 尝试不使用向量索引的插入...')
                  await db.libsqlClient.execute({
                    sql: `INSERT INTO user_file_embd (file_id, file_content, embedding)
                          VALUES (?, ?, ?)`,
                    args: [fileId, chunk, embeddingArray]
                  })
                  insertSuccess = true
                  console.log(`✅ 第 ${i + 1} 个片段索引成功（无向量索引模式）`)
                } catch (fallbackError) {
                  console.error(`❌ 无向量索引模式也失败:`, fallbackError.message)
                }
              }
            } else {
              // 非向量索引错误，直接退出重试
              break
            }

            // 添加延迟避免连续重试过快
            if (retryCount <= maxRetries && !insertSuccess) {
              await new Promise(resolve => setTimeout(resolve, 500))
            }
          }
        }

        if (insertSuccess) {
          successCount++
        } else {
          console.error(`❌ 第 ${i + 1} 个片段最终索引失败`)
        }

        // 添加短暂延迟，避免API请求过于频繁
        if (i < chunks.length - 1) {
          await new Promise(resolve => setTimeout(resolve, 100))
        }
      } catch (error) {
        console.error(`❌ 第 ${i + 1} 个片段索引失败:`, error)
        console.error(`❌ 片段长度: ${chunk.length} 字符`)
        console.error(`❌ 片段内容预览: ${chunk.substring(0, 100)}...`)
      }
    }

    console.log(`🎉 文档索引完成: ${path.basename(filePath)}, 成功索引 ${successCount}/${chunks.length} 个片段`)

    return {
      success: true,
      fileId,
      fileName: path.basename(filePath),
      totalSegments: chunks.length,
      successfulSegments: successCount
    }

  } catch (error) {
    console.error(`❌ 文档索引失败: ${filePath}`, error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 完全清空知识库 - 清空所有表和索引
 * @returns {Promise<Object>} 清空结果
 */
async function clearKnowledgeBase() {
  try {
    const service = require('./service')
    if (!service.isKnowledgeInitialized()) {
      await db.initKnowledgeDatabase()
    }

    console.log('🧹 开始完全清空知识库...')

    // 1. 删除向量索引
    try {
      await db.libsqlClient.execute('DROP INDEX IF EXISTS file_embedding_idx')
      console.log('✅ 向量索引已删除')
    } catch (error) {
      console.warn('⚠️ 删除向量索引时出错:', error.message)
    }

    // 2. 清空所有数据表
    const tables = ['user_file_embd', 'user_file']
    for (const table of tables) {
      try {
        const result = await db.libsqlClient.execute(`DELETE FROM ${table}`)
        console.log(`✅ 表 ${table} 已清空，删除了 ${result.rowsAffected || 0} 条记录`)
      } catch (error) {
        console.warn(`⚠️ 清空表 ${table} 时出错:`, error.message)
      }
    }

    // 3. 重置自增ID
    try {
      await db.libsqlClient.batch([
        'DELETE FROM sqlite_sequence WHERE name = "user_file"',
        'DELETE FROM sqlite_sequence WHERE name = "user_file_embd"'
      ], 'write')
      console.log('✅ 自增ID已重置')
    } catch (error) {
      console.warn('⚠️ 重置自增ID时出错:', error.message)
    }

    // 4. 重新创建向量索引
    try {
      await db.libsqlClient.execute(
        'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
      )
      console.log('✅ 向量索引已重新创建')
    } catch (error) {
      console.warn('⚠️ 重新创建向量索引时出错:', error.message)
    }

    // 5. 执行VACUUM以回收空间
    try {
      await db.libsqlClient.execute('VACUUM')
      console.log('✅ 数据库空间已回收')
    } catch (error) {
      console.warn('⚠️ 数据库VACUUM时出错:', error.message)
    }

    console.log('✅ 知识库完全清空完成')
    return { success: true, message: '知识库已完全清空并重置' }
  } catch (error) {
    console.error('❌ 清空知识库失败:', error)
    return { success: false, error: error.message }
  }
}

/**
 * 完全重建知识库（删除所有数据和表结构，重新创建）
 * @returns {Promise<Object>} 重建结果
 */
async function rebuildKnowledgeBase() {
  try {
    console.log('🔄 开始完全重建知识库...')

    // 重置初始化状态
    const service = require('./service')
    service.setKnowledgeInitialized(false)

    if (!db.libsqlClient) {
      const success = await db.initializeKnowledgeDependencies()
      if (!success) {
        throw new Error('依赖初始化失败')
      }
    }

    console.log('🗑️ 步骤1: 删除所有索引...')

    // 删除所有可能的索引
    const indexes = ['file_embedding_idx', 'idx_user_file_embd_file_id', 'idx_user_file_create_time']
    for (const index of indexes) {
      try {
        await db.libsqlClient.execute(`DROP INDEX IF EXISTS ${index}`)
        console.log(`✅ 索引 ${index} 已删除`)
      } catch (error) {
        console.warn(`⚠️ 删除索引 ${index} 时出错:`, error.message)
      }
    }

    console.log('🗑️ 步骤2: 删除所有数据表...')

    // 删除所有相关表
    const tables = ['user_file_embd', 'user_file']
    for (const table of tables) {
      try {
        await db.libsqlClient.execute(`DROP TABLE IF EXISTS ${table}`)
        console.log(`✅ 表 ${table} 已删除`)
      } catch (error) {
        console.warn(`⚠️ 删除表 ${table} 时出错:`, error.message)
      }
    }

    console.log('🗑️ 步骤3: 清理系统表...')

    // 清理SQLite系统表中的相关记录
    try {
      await db.libsqlClient.batch([
        'DELETE FROM sqlite_sequence WHERE name IN ("user_file", "user_file_embd")',
        'DELETE FROM sqlite_stat1 WHERE tbl IN ("user_file", "user_file_embd")'
      ], 'write')
      console.log('✅ 系统表已清理')
    } catch (error) {
      console.warn('⚠️ 清理系统表时出错:', error.message)
    }

    console.log('🔨 步骤4: 重新创建数据库结构...')

    // 重新创建表结构
    await db.libsqlClient.batch([
      `CREATE TABLE user_file (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_name TEXT NOT NULL,
        file_path TEXT NOT NULL,
        source_file_path TEXT NOT NULL,
        file_preview TEXT NOT NULL,
        remark TEXT NOT NULL,
        file_size INTEGER DEFAULT 0,
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,
      `CREATE TABLE user_file_embd (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        file_id INTEGER NOT NULL,
        file_content TEXT NOT NULL,
        embedding F32_BLOB(1024),
        create_time DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (file_id) REFERENCES user_file(id) ON DELETE CASCADE
      )`
    ], 'write')

    console.log('✅ 数据表重新创建完成')

    console.log('🔨 步骤5: 重新创建索引...')

    // 重新创建向量索引
    try {
      await db.libsqlClient.execute(
        'CREATE INDEX file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
      )
      console.log('✅ 向量索引重新创建成功')
    } catch (indexError) {
      console.warn('⚠️ 向量索引重新创建失败:', indexError.message)
      console.log('🔄 尝试不使用向量索引继续运行...')
    }

    // 创建其他有用的索引
    try {
      await db.libsqlClient.batch([
        'CREATE INDEX IF NOT EXISTS idx_user_file_embd_file_id ON user_file_embd(file_id)',
        'CREATE INDEX IF NOT EXISTS idx_user_file_create_time ON user_file(create_time)',
        'CREATE INDEX IF NOT EXISTS idx_user_file_embd_create_time ON user_file_embd(create_time)'
      ], 'write')
      console.log('✅ 辅助索引创建成功')
    } catch (indexError) {
      console.warn('⚠️ 辅助索引创建失败:', indexError.message)
    }

    console.log('🔨 步骤6: 优化数据库...')

    // 分析数据库统计信息
    try {
      await db.libsqlClient.execute('ANALYZE')
      console.log('✅ 数据库统计信息已更新')
    } catch (error) {
      console.warn('⚠️ 更新统计信息时出错:', error.message)
    }

    // 执行VACUUM以优化数据库文件
    try {
      await db.libsqlClient.execute('VACUUM')
      console.log('✅ 数据库已优化，空间已回收')
    } catch (error) {
      console.warn('⚠️ 数据库VACUUM时出错:', error.message)
    }

    // 设置初始化状态
    service.setKnowledgeInitialized(true)

    console.log('🎉 知识库完全重建成功!')
    console.log('📊 重建统计:')
    console.log('  - 表: user_file, user_file_embd')
    console.log('  - 索引: file_embedding_idx + 3个辅助索引')
    console.log('  - 数据库已优化和压缩')

    return {
      success: true,
      message: '知识库已完全重建',
      tables: ['user_file', 'user_file_embd'],
      indexes: ['file_embedding_idx', 'idx_user_file_embd_file_id', 'idx_user_file_create_time', 'idx_user_file_embd_create_time']
    }
  } catch (error) {
    console.error('❌ 知识库重建失败:', error)
    const service = require('./service')
    service.setKnowledgeInitialized(false)
    return { success: false, error: error.message }
  }
}

/**
 * 获取文档目录下的所有支持文件
 * @param {string} dirPath - 目录路径
 * @returns {Promise<Array<string>>} 文件路径数组
 */
async function getKnowledgeDocumentFiles(dirPath) {
  try {
    const KNOWLEDGE_CONFIG = getKnowledgeConfig()
    const supportedFormats = KNOWLEDGE_CONFIG.document.supportedFormats
    const files = []

    function scanDirectory(dir) {
      const items = fs.readdirSync(dir)
      
      for (const item of items) {
        const fullPath = path.join(dir, item)
        const stat = fs.statSync(fullPath)
        
        if (stat.isDirectory()) {
          // 递归扫描子目录
          scanDirectory(fullPath)
        } else if (stat.isFile()) {
          const ext = path.extname(item).toLowerCase()
          if (supportedFormats.includes(ext)) {
            files.push(fullPath)
          }
        }
      }
    }

    scanDirectory(dirPath)
    console.log(`📁 扫描目录 ${dirPath}，找到 ${files.length} 个支持的文件`)
    return files

  } catch (error) {
    console.error(`❌ 扫描文档目录失败: ${dirPath}`, error)
    return []
  }
}

module.exports = {
  postProcessMarkdown,
  splitKnowledgeDocument,
  getKnowledgeDocumentContent,
  indexKnowledgeDocument,
  clearKnowledgeBase,
  rebuildKnowledgeBase,
  getKnowledgeDocumentFiles
} 
/**
 * 知识库数据库修复脚本
 * 用于修复知识库数据库中的字段缺失问题
 */

const { createClient } = require('@libsql/client')
const path = require('path')
const { app } = require('electron')

async function fixKnowledgeDatabase() {
  try {
    console.log('🔧 开始修复知识库数据库...')
    
    // 获取用户数据路径
    const userDataPath = app.getPath('userData')
    const dbPath = path.join(userDataPath, 'knowledge.db')
    
    console.log('📁 数据库路径:', dbPath)
    
    // 创建数据库客户端
    const client = createClient({
      url: `file:${dbPath}`
    })
    
    // 检查表是否存在
    console.log('🔍 检查数据库表结构...')
    const tablesResult = await client.execute(`
      SELECT name FROM sqlite_master 
      WHERE type='table' AND name IN ('user_file', 'user_file_embd')
    `)
    
    const existingTables = tablesResult.rows.map(row => row.name)
    console.log('📋 现有表:', existingTables)
    
    if (existingTables.length === 0) {
      console.log('🔄 没有找到知识库表，创建新表...')
      
      // 创建表结构
      await client.batch([
        `CREATE TABLE user_file (
          id INTEGER PRIMARY KEY,
          file_type INTEGER NOT NULL,
          file_name TEXT NOT NULL,
          file_path TEXT NOT NULL,
          source_file_path TEXT NOT NULL,
          file_preview TEXT NOT NULL,
          remark TEXT NOT NULL,
          file_size INTEGER DEFAULT 0,
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )`,
        `CREATE TABLE user_file_embd (
          id INTEGER PRIMARY KEY,
          file_id INTEGER NOT NULL,
          file_content TEXT NOT NULL,
          embedding F32_BLOB(1024),
          create_time DATETIME DEFAULT CURRENT_TIMESTAMP
        )`
      ], 'write')
      
      console.log('✅ 表结构创建完成')
    } else {
      console.log('🔍 检查file_size字段...')
      
      // 检查file_size字段是否存在
      const columnCheckResult = await client.execute(
        "PRAGMA table_info(user_file)"
      )
      
      const hasFileSizeColumn = columnCheckResult.rows.some(row => row.name === 'file_size')
      
      if (!hasFileSizeColumn) {
        console.log('🔄 检测到缺少file_size字段，开始修复...')
        
        try {
          // 尝试添加字段
          await client.execute('ALTER TABLE user_file ADD COLUMN file_size INTEGER DEFAULT 0')
          console.log('✅ file_size字段添加成功')
        } catch (alterError) {
          console.warn('⚠️ ALTER TABLE失败，重建表结构:', alterError.message)
          
          // 备份现有数据
          const existingData = await client.execute('SELECT * FROM user_file')
          const existingEmbeddings = await client.execute('SELECT * FROM user_file_embd')
          
          console.log(`📊 备份数据: ${existingData.rows.length} 个文件, ${existingEmbeddings.rows.length} 个片段`)
          
          // 删除旧表
          await client.execute('DROP TABLE IF EXISTS user_file_embd')
          await client.execute('DROP TABLE IF EXISTS user_file')
          
          // 创建新表
          await client.batch([
            `CREATE TABLE user_file (
              id INTEGER PRIMARY KEY,
              file_type INTEGER NOT NULL,
              file_name TEXT NOT NULL,
              file_path TEXT NOT NULL,
              source_file_path TEXT NOT NULL,
              file_preview TEXT NOT NULL,
              remark TEXT NOT NULL,
              file_size INTEGER DEFAULT 0,
              create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`,
            `CREATE TABLE user_file_embd (
              id INTEGER PRIMARY KEY,
              file_id INTEGER NOT NULL,
              file_content TEXT NOT NULL,
              embedding F32_BLOB(1024),
              create_time DATETIME DEFAULT CURRENT_TIMESTAMP
            )`
          ], 'write')
          
          // 恢复数据
          for (const row of existingData.rows) {
            await client.execute({
              sql: `INSERT INTO user_file (id, file_type, file_name, file_path, source_file_path, file_preview, remark, file_size, create_time)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)`,
              args: [row.id, row.file_type, row.file_name, row.file_path, row.source_file_path, row.file_preview, row.remark, 0, row.create_time]
            })
          }
          
          for (const row of existingEmbeddings.rows) {
            await client.execute({
              sql: `INSERT INTO user_file_embd (id, file_id, file_content, embedding, create_time)
                    VALUES (?, ?, ?, ?, ?)`,
              args: [row.id, row.file_id, row.file_content, row.embedding, row.create_time]
            })
          }
          
          console.log('✅ 表结构重建完成，数据已恢复')
        }
      } else {
        console.log('✅ file_size字段已存在')
      }
    }
    
    // 创建向量索引
    console.log('🔧 创建向量索引...')
    try {
      await client.execute(
        'CREATE INDEX IF NOT EXISTS file_embedding_idx ON user_file_embd(libsql_vector_idx(embedding))'
      )
      console.log('✅ 向量索引创建成功')
    } catch (indexError) {
      console.warn('⚠️ 向量索引创建失败:', indexError.message)
    }
    
    // 验证修复结果
    console.log('🔍 验证修复结果...')
    const statsResult = await client.execute('SELECT COUNT(*) as count FROM user_file')
    const embdResult = await client.execute('SELECT COUNT(*) as count FROM user_file_embd')
    
    console.log('📊 修复完成统计:')
    console.log(`  - 文件数量: ${statsResult.rows[0].count}`)
    console.log(`  - 片段数量: ${embdResult.rows[0].count}`)
    
    console.log('🎉 知识库数据库修复完成!')
    
    return {
      success: true,
      message: '知识库数据库修复成功',
      stats: {
        files: statsResult.rows[0].count,
        segments: embdResult.rows[0].count
      }
    }
    
  } catch (error) {
    console.error('❌ 知识库数据库修复失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  fixKnowledgeDatabase()
    .then(result => {
      if (result.success) {
        console.log('✅ 修复成功:', result.message)
        process.exit(0)
      } else {
        console.error('❌ 修复失败:', result.error)
        process.exit(1)
      }
    })
    .catch(error => {
      console.error('❌ 脚本执行失败:', error)
      process.exit(1)
    })
}

module.exports = { fixKnowledgeDatabase } 
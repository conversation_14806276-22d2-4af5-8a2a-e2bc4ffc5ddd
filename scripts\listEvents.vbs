' listEvents.vbs - 列出Outlook日历事件
Option Explicit

' Include utility functions
Dim fso, scriptDir
Set fso = CreateObject("Scripting.FileSystemObject")
scriptDir = fso.GetParentFolderName(WScript.ScriptFullName)
ExecuteGlobal fso.OpenTextFile(fso.BuildPath(scriptDir, "utils.vbs"), 1).ReadAll

' Main function
Sub Main()
    ' Get command line arguments
    Dim startDateStr, endDateStr, calendarName
    Dim startDate, endDate
    
    ' Get arguments
    startDateStr = GetArgument("startDate")
    endDateStr = GetArgument("endDate")
    calendarName = GetArgument("calendar")
    
    ' Require start date
    RequireArgument "startDate"
    
    ' Parse start date
    startDate = ParseDate(startDateStr)
    
    ' Parse end date (if not provided, default to 7 days from start)
    If endDateStr = "" Then
        endDate = DateAdd("d", 7, startDate)
    Else
        endDate = ParseDate(endDateStr)
    End If
    
    ' Ensure end date is not before start date
    If endDate < startDate Then
        OutputError "End date cannot be before start date"
        WScript.Quit 1
    End If
    
    ' List the events
    Dim eventsJSON
    eventsJSON = ListCalendarEvents(startDate, endDate, calendarName)
    
    ' Output success with the events
    OutputSuccess eventsJSON
End Sub

' Lists calendar events within the specified date range
Function ListCalendarEvents(startDate, endDate, calendarName)
    On Error Resume Next
    
    ' Create Outlook objects
    Dim outlookApp, calendar, items, appointment, i
    Dim filteredItems, events, eventJSON
    
    ' Create Outlook application
    Set outlookApp = CreateOutlookApplication()
    
    ' Get calendar folder
    If calendarName = "" Then
        Set calendar = GetDefaultCalendar(outlookApp)
    Else
        Set calendar = GetCalendarByName(outlookApp, calendarName)
    End If
    
    ' Get all items from calendar
    Set items = calendar.Items
    
    ' Sort items by start time
    items.Sort "[Start]"
    items.IncludeRecurrences = True
    
    ' Filter items by date range
    Dim filter
    filter = "[Start] >= '" & startDate & "' AND [Start] < '" & DateAdd("d", 1, endDate) & "'"
    Set filteredItems = items.Restrict(filter)
    
    If Err.Number <> 0 Then
        OutputError "Failed to retrieve calendar events: " & Err.Description
        WScript.Quit 1
    End If
    
    ' Convert items to JSON array
    events = "["
    
    For i = 1 To filteredItems.Count
        Set appointment = filteredItems.Item(i)
        
        If i > 1 Then events = events & ","
        events = events & AppointmentToJSON(appointment)
    Next
    
    events = events & "]"
    
    ' Clean up
    Set filteredItems = Nothing
    Set items = Nothing
    Set calendar = Nothing
    Set outlookApp = Nothing
    
    ListCalendarEvents = events
End Function

' Run the main function
Main 
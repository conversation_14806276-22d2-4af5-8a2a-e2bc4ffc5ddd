#!/usr/bin/env node

const { execSync } = require('child_process');
const path = require('path');
const fs = require('fs');

console.log('🌤️ ====== 安装天气MCP依赖 ======');

// 检查当前目录
const weatherDir = path.join(__dirname, 'external_tools', 'mcp-servers', 'weather');
console.log('📁 天气MCP目录:', weatherDir);

if (!fs.existsSync(weatherDir)) {
  console.error('❌ 天气MCP目录不存在:', weatherDir);
  process.exit(1);
}

// 切换到天气MCP目录
process.chdir(weatherDir);
console.log('📁 切换到目录:', process.cwd());

try {
  // 检查Python版本
  console.log('🐍 检查Python环境...');
  const pythonCommand = process.platform === 'win32' ? 'python' : 'python3';
  
  try {
    const pythonVersion = execSync(`${pythonCommand} --version`, { encoding: 'utf8' });
    console.log('✅ Python版本:', pythonVersion.trim());
  } catch (error) {
    console.error('❌ Python不可用:', error.message);
    console.log('💡 请安装Python 3.8+');
    process.exit(1);
  }

  // 检查pip
  console.log('📦 检查pip...');
  try {
    const pipCommand = process.platform === 'win32' ? 'pip' : 'pip3';
    const pipVersion = execSync(`${pipCommand} --version`, { encoding: 'utf8' });
    console.log('✅ pip版本:', pipVersion.trim());
  } catch (error) {
    console.error('❌ pip不可用:', error.message);
    process.exit(1);
  }

  // 安装依赖
  console.log('📦 安装Python依赖包...');
  const installCommand = process.platform === 'win32' ? 'pip install' : 'pip3 install';
  
  const dependencies = [
    'mcp[cli]>=1.10.1',
    'httpx>=0.28.1', 
    'python-dotenv',
    'pinyin>=0.4.0'
  ];

  for (const dep of dependencies) {
    console.log(`📦 安装 ${dep}...`);
    try {
      execSync(`${installCommand} "${dep}"`, { 
        stdio: 'inherit',
        timeout: 120000  // 2分钟超时
      });
      console.log(`✅ ${dep} 安装成功`);
    } catch (error) {
      console.error(`❌ ${dep} 安装失败:`, error.message);
      // 不要退出，继续安装其他依赖
    }
  }

  console.log('🎉 ====== 天气MCP依赖安装完成 ======');
  console.log('');
  console.log('📋 接下来的步骤:');
  console.log('1. 访问 https://openweathermap.org/api 获取免费API密钥');
  console.log('2. 设置环境变量: OPENWEATHERMAP_API_KEY=your_api_key');
  console.log('3. 重启应用测试天气查询功能');
  console.log('');
  console.log('🧪 测试命令:');
  console.log(`${pythonCommand} external_tools/mcp-servers/weather/src/main.py`);

} catch (error) {
  console.error('💥 安装过程中发生错误:', error.message);
  process.exit(1);
} 
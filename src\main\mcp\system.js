const os = require('os')
const { exec } = require('child_process')
const { promisify } = require('util')

const execAsync = promisify(exec)

let initialized = false

/**
 * 初始化系统操作 MCP 服务
 */
async function initialize(mcpManager) {
  if (initialized) return

  try {
    console.log('🖥️ 初始化系统操作 MCP 服务...')
    
    // 系统操作服务不需要额外的进程，直接使用 Node.js 内置模块
    
    initialized = true
    console.log('✅ 系统操作 MCP 服务初始化完成')
  } catch (error) {
    console.error('❌ 系统操作 MCP 服务初始化失败:', error)
    throw error
  }
}

/**
 * 获取系统信息
 */
async function getSystemInfo() {
  try {
    console.log('🖥️ 获取系统信息...')
    
    const systemInfo = {
      platform: os.platform(),
      arch: os.arch(),
      hostname: os.hostname(),
      type: os.type(),
      release: os.release(),
      uptime: os.uptime(),
      totalMemory: os.totalmem(),
      freeMemory: os.freemem(),
      cpus: os.cpus().length,
      networkInterfaces: os.networkInterfaces()
    }
    
    console.log('✅ 系统信息获取完成')
    return {
      success: true,
      info: systemInfo
    }
  } catch (error) {
    console.error('❌ 获取系统信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取进程信息
 */
async function getProcessInfo() {
  try {
    console.log('📊 获取进程信息...')
    
    const processInfo = {
      pid: process.pid,
      version: process.version,
      platform: process.platform,
      arch: process.arch,
      memoryUsage: process.memoryUsage(),
      uptime: process.uptime(),
      title: process.title,
      argv: process.argv
    }
    
    console.log('✅ 进程信息获取完成')
    return {
      success: true,
      info: processInfo
    }
  } catch (error) {
    console.error('❌ 获取进程信息失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 执行系统命令
 */
async function executeCommand(command) {
  try {
    console.log(`⚡ 执行系统命令: ${command}`)
    
    const { stdout, stderr } = await execAsync(command, {
      timeout: 30000 // 30秒超时
    })
    
    console.log('✅ 系统命令执行完成')
    return {
      success: true,
      stdout,
      stderr,
      command
    }
  } catch (error) {
    console.error('❌ 执行系统命令失败:', error)
    return {
      success: false,
      error: error.message,
      command
    }
  }
}

/**
 * 获取环境变量
 */
async function getEnvironmentVariables() {
  try {
    console.log('🌍 获取环境变量...')
    
    const env = process.env
    
    console.log('✅ 环境变量获取完成')
    return {
      success: true,
      variables: env
    }
  } catch (error) {
    console.error('❌ 获取环境变量失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取当前工作目录
 */
async function getCurrentWorkingDirectory() {
  try {
    console.log('📁 获取当前工作目录...')
    
    const cwd = process.cwd()
    
    console.log('✅ 当前工作目录获取完成')
    return {
      success: true,
      cwd
    }
  } catch (error) {
    console.error('❌ 获取当前工作目录失败:', error)
    return {
      success: false,
      error: error.message
    }
  }
}

/**
 * 获取连接状态
 */
function getConnectionStatus() {
  return {
    connected: initialized,
    service: 'system'
  }
}

/**
 * 获取可用工具
 */
function getAvailableTools() {
  return [
    {
      name: 'system_info',
      description: '获取系统信息',
      parameters: {}
    },
    {
      name: 'system_process',
      description: '获取进程信息',
      parameters: {}
    },
    {
      name: 'system_command',
      description: '执行系统命令',
      parameters: {
        command: { type: 'string', description: '要执行的命令' }
      }
    },
    {
      name: 'system_env',
      description: '获取环境变量',
      parameters: {}
    },
    {
      name: 'system_cwd',
      description: '获取当前工作目录',
      parameters: {}
    }
  ]
}

/**
 * 调用工具
 */
async function callTool(toolName, args) {
  switch (toolName) {
    case 'system_info':
      return await getSystemInfo()
    case 'system_process':
      return await getProcessInfo()
    case 'system_command':
      return await executeCommand(args.command)
    case 'system_env':
      return await getEnvironmentVariables()
    case 'system_cwd':
      return await getCurrentWorkingDirectory()
    default:
      throw new Error(`未知的系统工具: ${toolName}`)
  }
}

/**
 * 清理资源
 */
async function cleanup() {
  console.log('🧹 清理系统操作 MCP 资源...')
  initialized = false
}

module.exports = {
  initialize,
  getSystemInfo,
  getProcessInfo,
  executeCommand,
  getEnvironmentVariables,
  getCurrentWorkingDirectory,
  getConnectionStatus,
  getAvailableTools,
  callTool,
  cleanup
} 